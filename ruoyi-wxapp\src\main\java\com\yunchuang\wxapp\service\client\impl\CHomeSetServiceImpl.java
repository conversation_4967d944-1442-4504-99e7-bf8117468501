package com.yunchuang.wxapp.service.client.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yunchuang.wxapp.mapper.HomeSetMapper;
import com.yunchuang.wxapp.model.domain.HomeSet;
import com.yunchuang.wxapp.model.enums.HomeSetType;
import com.yunchuang.wxapp.model.enums.common.CommonEnableStatus;
import com.yunchuang.wxapp.model.resp.CCommonHomeSetReq;
import com.yunchuang.wxapp.service.client.ICHomeSetService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 首页设置 Service 实现类
 */
@Service
@Transactional
public class CHomeSetServiceImpl extends ServiceImpl<HomeSetMapper, HomeSet> implements ICHomeSetService {

    @Resource
    private HomeSetMapper homeSetMapper;

    /**
     * 获取轮播图列表
     *
     * @return 轮播图列表
     */
    @Override
    public List<HomeSet> getCarouselList() {
        LambdaQueryWrapper<HomeSet> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HomeSet::getType, HomeSetType.BANNER.getValue());
        queryWrapper.eq(HomeSet::getStatus, CommonEnableStatus.ENABLE.getValue());
        queryWrapper.orderByAsc(HomeSet::getSort);
        return homeSetMapper.selectList(queryWrapper);
    }

    /**
     * 获取首页设置列表
     *
     * @return 首页设置列表
     */
    @Override
    public List<CCommonHomeSetReq> getHomeSetList() {
        // 查询启用的首页设置列表，并按sort字段升序排序
        LambdaQueryWrapper<HomeSet> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HomeSet::getStatus, CommonEnableStatus.ENABLE.getValue());
        queryWrapper.orderByAsc(HomeSet::getSort);
        List<HomeSet> homeSetList = homeSetMapper.selectList(queryWrapper);

        // 使用Java 8 Stream API进行分组
        Map<Integer, List<HomeSet>> homeSetMap = homeSetList.stream().collect(Collectors.groupingBy(HomeSet::getType));

        // 封装首页设置列表
        List<CCommonHomeSetReq> reqList = new ArrayList<>();
        homeSetMap.forEach((type, homeSets) -> {
            CCommonHomeSetReq req = new CCommonHomeSetReq();
            req.setType(type);
            req.setHomeSetList(homeSets);
            reqList.add(req);
        });

        return reqList;
    }

}
