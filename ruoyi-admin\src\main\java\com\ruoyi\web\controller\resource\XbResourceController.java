package com.ruoyi.web.controller.resource;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.QueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.oss.ALY_OSS;
import com.ruoyi.device.domain.CameraSysImage;
import com.ruoyi.dto.Result;
import com.ruoyi.photo.domain.DeviceResourceRelation;
import com.ruoyi.photo.service.DeviceResourceRelationService;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.system.service.ISysDictDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.photo.domain.XbResource;
import com.ruoyi.photo.service.IXbResourceService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

import static com.ruoyi.common.utils.SecurityUtils.getLoginUser;


/**
 * 素材管理Controller
 *
 * <AUTHOR>
 * @date 2024-05-29
 */
@RestController
@RequestMapping("/resource/xb_resource")
public class XbResourceController extends BaseController {
    @Autowired
    private IXbResourceService xbResourceService;
    @Autowired
    private DeviceResourceRelationService deviceResourceRelationService;
    @Autowired
    private ISysDictDataService dictDataService;
    @Resource
    private ISysDeptService deptService;

    /**
     * 查询素材管理列表
     */
    @GetMapping("/list")
    public TableDataInfo list(XbResource xbResource, int pageNum, int pageSize, @RequestParam(required = false) String deviceId) {
        return xbResourceService.selectXbResourceList(xbResource, pageNum, pageSize, deviceId);
    }

    /**
     * 导出素材管理列表
     */
    @PreAuthorize("@ss.hasPermi('xb_resource:xb_resource:export')")
    @Log(title = "素材管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, XbResource xbResource) {
//        List<XbResource> list = (List<XbResource>) (xbResourceService.selectXbResourceList(xbResource).getRows());
//        ExcelUtil<XbResource> util = new ExcelUtil<XbResource>(XbResource.class);
//        util.exportExcel(response, list, "素材管理数据");
    }

    /**
     * 获取素材管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('xb_resource:xb_resource:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(xbResourceService.getById(id));
    }

    /**
     * 新增素材管理
     */
    @PreAuthorize("@ss.hasPermi('xb_resource:xb_resource:add')")
    @Log(title = "素材管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(XbResource xbResource, @RequestParam(value = "image", required = false) MultipartFile image) {
        LoginUser loginUser = getLoginUser();
        if (loginUser == null) {
            return AjaxResult.error(401, "用户未登录");
        }
        xbResource.setCreateBy(loginUser.getUsername());
        xbResource.setDeptId(loginUser.getDeptId());
        if (image != null)
            return toAjax(xbResourceService.insertXbResource(image, xbResource));
        return AjaxResult.error("请添加图片");
    }

    /**
     * 修改素材管理
     */
    @PreAuthorize("@ss.hasPermi('xb_resource:xb_resource:edit')")
    @Log(title = "素材管理", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/update")
    public AjaxResult edit(XbResource xbResource, @RequestParam(value = "image", required = false) MultipartFile image) {

        if (xbResource == null || xbResource.getId() == null)
            return AjaxResult.error("数据错误");

        if (getLoginUser().getUser() == null) {
            return AjaxResult.error("用户未登录");
        }

        if (!getLoginUser().getUser().isAdmin() &&
                !"pdl_admin".equals(getLoginUser().getUser().getUserName()) &&
                !getLoginUser().getUser().getUserName().equals(xbResource.getCreateBy())) {
            return AjaxResult.error("无权修改该资源");
        }

        return xbResourceService.updateXbResource(xbResource, image);
    }

    /**
     * 删除素材管理
     */
    @PreAuthorize("@ss.hasPermi('xb_resource:xb_resource:remove')")
    @Log(title = "素材管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(xbResourceService.deleteXbResourceByIds(ids));
    }


    /**
     * 查询素材管理列表
     */
    @GetMapping("/listResource_choose")
    public TableDataInfo listResource_choose(String deviceId, int pageNum, int pageSize, @RequestParam(required = false) String people, @RequestParam(required = false) String style, @RequestParam(required = false) String title, @RequestParam(required = false) String remark) {

        LoginUser loginUser = getLoginUser();

        if (loginUser == null) {
            TableDataInfo tableDataInfo = new TableDataInfo();
            tableDataInfo.setMsg("用户未登录");
            tableDataInfo.setCode(401);
            return tableDataInfo;
        }


        List<DeviceResourceRelation> chooseResources = deviceResourceRelationService.query().eq("device_id", deviceId).list();
        QueryChainWrapper<XbResource> query = xbResourceService.query();


        {
            /**
             * 用户层级权限判断筛选
             */
            boolean isLeader = loginUser.getUser().getDept().getLeader().equals(loginUser.getUsername()) || loginUser.getUser().isAdmin();
            String[] deptIds = loginUser.getUser().getDept().getAncestors().split(",");//上级代理的id
            List<String> deptIdList = new ArrayList<>(Arrays.asList(deptIds));
            deptIdList.add(String.valueOf(loginUser.getDeptId()));//加上本级代理的id
            List<SysDept> sysDepts = deptService.listByIds(deptIdList);//获取上述级别代理的dept信息
            List<String> leadersName = new ArrayList<>();//上述获取的dept的负责人的username
            leadersName.add(loginUser.getUsername());
            for (SysDept sysDept : sysDepts) {
                leadersName.add(sysDept.getLeader());
            }

            List<Long> childDeptIds = new ArrayList<>();
            if (isLeader) {
                List<SysDept> depts = deptService.query().like("ancestors", loginUser.getDeptId()).list();
                for (SysDept dept : depts) {
                    childDeptIds.add(dept.getDeptId());
                }
                childDeptIds.add(loginUser.getDeptId());
                System.out.println(childDeptIds);

            }
            query.and(qr -> {
                qr.in("create_by", leadersName);
                if (childDeptIds.size() > 0)
                    qr.or().in("dept_id", childDeptIds);
            });
        }


//        if (!loginUser.getUser().isAdmin()) {
//            query.and(qr -> {
//                qr.eq("create_by", loginUser.getUsername()).or().eq("create_by", "pdl_admin");
//            });
//        }


        List<Long> resourceIds = new ArrayList<>();
        for (DeviceResourceRelation chooseResource : chooseResources) {
            resourceIds.add(chooseResource.getResourceId());
        }
        query.eq("parent_id", 0);
        if (resourceIds.size() > 0)
            query.notIn("id", resourceIds);

        if (people != null)
            query.eq("people", people);
        if (style != null)
            query.eq("style", style);
        if (title != null)
            query.like("title", title);
        if (remark != null)
            query.like("remark", remark);
        Page<XbResource> page = query.orderByDesc("create_time").page(new Page<>(pageNum, pageSize));
        List<XbResource> records = page.getRecords();

        return new TableDataInfo(records, page.getTotal());
    }

    @GetMapping("/listResource_not")
    public TableDataInfo listResource_not(String deviceId, int pageNum, int pageSize, @RequestParam(required = false) String people, @RequestParam(required = false) String style, @RequestParam(required = false) String title, @RequestParam(required = false) String remark) {

        List<DeviceResourceRelation> chooseResources = deviceResourceRelationService.query().eq("device_id", deviceId).list();

        if (chooseResources.size() == 0) return new TableDataInfo(new ArrayList<>(), 0L);
        QueryChainWrapper<XbResource> query = xbResourceService.query();
        query.eq("parent_id", 0);
        if (people != null)
            query.eq("people", people);
        if (style != null)
            query.eq("style", style);
        if (title != null)
            query.like("title", title);
        if (remark != null)
            query.like("remark", remark);

        List<Long> resourceIds = new ArrayList<>();
        for (DeviceResourceRelation chooseResource : chooseResources) {
            resourceIds.add(chooseResource.getResourceId());
        }
        query.in("id", resourceIds);

        Page<XbResource> page = query.orderByDesc("create_time").page(new Page<>(pageNum, pageSize));
        List<XbResource> records = page.getRecords();

        return new TableDataInfo(records, page.getTotal());
    }

    @GetMapping("/chooseResource")
    public AjaxResult chooseResource(String deviceId, Long resourceId) {
        DeviceResourceRelation deviceResourceRelation = new DeviceResourceRelation();
        deviceResourceRelation.setResourceId(resourceId);
        deviceResourceRelation.setDeviceId(deviceId);
        boolean save = deviceResourceRelationService.save(deviceResourceRelation);

        List<XbResource> xbResources = xbResourceService.query().eq("parent_id", resourceId).list();
        for (XbResource xbResource : xbResources) {
            DeviceResourceRelation deviceResourceRelationChild = new DeviceResourceRelation();
            deviceResourceRelationChild.setResourceId(xbResource.getId());
            deviceResourceRelationChild.setDeviceId(deviceId);
            save = deviceResourceRelationService.save(deviceResourceRelationChild) && save;
        }

        return save ? AjaxResult.success("选择成功") : AjaxResult.error("选择失败");
    }

    @GetMapping("/notResource")
    public AjaxResult notResource(String deviceId, Long resourceId) {
        QueryWrapper<DeviceResourceRelation> query = new QueryWrapper<>();
        query.eq("device_id", deviceId);
        List<XbResource> xbResources = xbResourceService.query().eq("parent_id", resourceId).list();
        query.and(qr -> {
            for (XbResource xbResource : xbResources) {
                qr.eq("resource_id",xbResource.getId()).or();
            }
           qr.eq("resource_id",resourceId);
        });

        boolean remove = deviceResourceRelationService.remove(query);
        return remove ? AjaxResult.success("移除成功") : AjaxResult.error("移除失败");
    }

    /**
     * 查询素材管理列表  设备端调用
     */
    @GetMapping("/listByDevice")
    public TableDataInfo listByDevice(@RequestParam(required = false) String people,
                                      @RequestParam(required = false) String style,
                                      @RequestParam String deviceId) {
        List<DeviceResourceRelation> resourceRelationList = deviceResourceRelationService.query().eq("device_id", deviceId).list();
        if (resourceRelationList.size() == 0) return new TableDataInfo(new ArrayList<>(), 0L);
        QueryChainWrapper<XbResource> query = xbResourceService.query();
        if (people != null)
            query.eq("people", people);
        if (style != null)
            query.eq("style", style);

        query.eq("status", 1);
        query.and(qr -> {
            for (DeviceResourceRelation relation : resourceRelationList) {
                qr.eq("id", relation.getResourceId()).or();
            }
        });
        List<XbResource> list = query.orderByDesc("create_time").list();
        SysDictData dictData = dictDataService.query().eq("dict_type", "style").eq("dict_value", style).one();


        if (people != null && style != null) {
            SysDictData dictData1 = dictDataService.query().eq("dict_type", "style").eq("dict_value", style).one();
            if (dictData1 != null) {
                for (XbResource record : list) {
                    record.setStyleTitle(dictData1.getDictLabel());
                }
            }
        } else if (people != null) {

            for (XbResource record : list) {
                SysDictData dictData2 = dictDataService.query().eq("dict_type", "style").eq("dict_value", record.getStyle()).one();
                if (dictData2 != null)
                    record.setStyleTitle(dictData2.getDictLabel());
            }
        }

        TableDataInfo tableDataInfo = new TableDataInfo(list, (long) list.size());
        if (dictData != null)
            tableDataInfo.setMsg(dictData.getDictLabel());
        return tableDataInfo;
    }

    @GetMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestParam Integer id, @RequestParam Integer status) {
        boolean update = xbResourceService.update().set("status", status).eq("id", id).update();
        return update ? AjaxResult.success("操作成功") : AjaxResult.error("操作失败");
    }
}
