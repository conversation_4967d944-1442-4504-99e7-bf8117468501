package com.ruoyi.order.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 订单管理对象 order_camera
 * 
 * <AUTHOR>
 * @date 2023-07-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("order_camera")
public class OrderCamera extends BaseEntity
{
    /** 未支付*/
    private static final long nonPayment = 0L;
    /** 已支付*/
    private static final long serialVersionUID = 1L;
    /** 已取消*/
    private static final long canceled = 2L;
    /** 已退款*/
    private static final long refunded = 3L;
    /** 已打印*/
    private static final long printed = 4L;


    /** 订单id */
    @Excel(name = "订单id")
    @JsonSerialize(using = ToStringSerializer.class)
    @TableId(value = "order_id", type = IdType.INPUT)
    private String orderId;

    /** 设备id */
    @Excel(name = "设备id")
    private String deviceId;

    /** 设备名称 */
    @Excel(name = "设备名称")
    private String deviceName;

    /** 设备主人的userid */
    private Long userId;

    /** 微信用户openid */
    private String openid;

    /** 手机号 */
    @Excel(name = "手机号")
    private String phone;

    /** 三方订单id */
    private String transactionId;

    /** 商户id */
    private String mchid;

    /** 小程序appid */
    private String appid;

    /** 订单状态 0未支付 1已支付 2已取消 3已退款 4已打印 5退款中 6退款失败*/
    @Excel(name = "订单状态")
    private Long orderStatus;

    private String refundId;
    private Date refundTime;

    /** 支付方式 1微信 2支付宝 3现金 4优惠券 5乐瑶瑶*/
    @Excel(name = "支付方式")
    private Long payWay;

    @Excel(name = "优惠券码")
    private Long voucherCode;

    /** 商品数量 */
    @Excel(name = "商品数量")
    private Long productQuantity;

    /** 照片类型  大头贴—1 写真—2  证件照—3 上传打印—4 漫画风—5 手翻书—6   7-换背景，合拍  加印—10  AI—100 点券充值-101   */
    @Excel(name = "照片类型")
    private Integer photoType;

    /** 商品描述 */
    @Excel(name = "商品描述")
    private String productDescription;

    @Excel(name = "相框id")
    private Long modelId;

    @Excel(name = "接口费类型")
    private Long interfaceType;



    @Excel(name = "订单金额,单位：分")
    private Long orderPrice;

    @Excel(name = "实收")
    private Long moneyReceived;

    @Excel(name = "手续费")
    private Long commission;

    @Excel(name = "接口费")
    private Long interfaceFee;

    @Excel(name = "到账")
    private Long account;




    /** 支付时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "支付时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date payTime;

    @TableField(exist = false)
    private String myNickName;

    @TableField(exist = false)
    private Date startTime;

    @TableField(exist = false)
    private Date endTime;

    /**
     * 订单代理商   todo 可以配合数据库 删除字段
     */
    private Long roleId;

    /**
     * 隐藏标志0正常 1隐藏
     */
    private Integer hide;

    @TableField(exist = false)
    private String photoUrl;

    @TableField(exist = false)
    private String gifUrl;

    @JsonSerialize(using = ToStringSerializer.class)
    @TableField(exist = false)
    private Long photoId;

    @TableField(exist = false)
    private Long priceCount;
    @TableField(exist = false)
    private Long receivedCount;
    @TableField(exist = false)
    private Long commissionCount;
    @TableField(exist = false)
    private Long interfaceFeeCount;
    @TableField(exist = false)
    private Long accountCount;
    @TableField(exist = false)
    private Long productQuantityCount;

}
