package com.yunchuang.wxapp.aspect;

import cn.hutool.json.JSONUtil;
import com.yunchuang.wxapp.util.IpUtils;
import com.yunchuang.wxapp.util.ServletUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * API日志切面
 */
@Aspect
@Component
@Order(1)
public class ApiLog {

    private static final Logger log = LoggerFactory.getLogger(ApiLog.class); // 日志记录

    private static final String LINE_SEPARATOR = System.lineSeparator(); // 换行符

    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    @Value("${api.log.path-patterns:/client/wxapp/**}")
    private List<String> pathPatterns; // 拦截路径

    @Value("${api.log.exclude-path-patterns:/client/wxapp/test/**}")
    private List<String> excludePathPatterns; // 排除路径

    public ApiLog() {
    }

    @Around("@within(org.springframework.web.bind.annotation.RestController)||@within(org.springframework.stereotype.Controller)")
    public Object doAround(ProceedingJoinPoint joinPoint) throws Throwable {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            return joinPoint.proceed();
        }
        HttpServletRequest request = attributes.getRequest();
        String requestURI = request.getRequestURI();

        if (isExcludedPath(requestURI)) {
            return joinPoint.proceed();
        }

        if (isInterceptedPath(requestURI)) {
            long startTime = System.currentTimeMillis();
            StringBuilder content = new StringBuilder(LINE_SEPARATOR);
            content.append("========================================== Start ==========================================").append(LINE_SEPARATOR);
            content.append("URL            : {}").append(LINE_SEPARATOR);
            content.append("HTTP Method    : {}").append(LINE_SEPARATOR);
            content.append("Class Method   : {}").append(LINE_SEPARATOR);
            content.append("IP             : {}").append(LINE_SEPARATOR);
            content.append("Request Headers: {}").append(LINE_SEPARATOR);

            Object result = joinPoint.proceed(joinPoint.getArgs());
            String resultStr = "";
            try {
                resultStr = JSONUtil.toJsonStr(result);
            } catch (Exception e) {
                log.warn("转换JSON失败", e);
            }
            content.append("Response Args  : {}").append(LINE_SEPARATOR);
            content.append("Time-Consuming : {}ms").append(LINE_SEPARATOR);
            content.append("=========================================== End ===========================================").append(LINE_SEPARATOR);
            log.info(content.toString(), request.getRequestURL(), request.getMethod(),
                    joinPoint.getSignature().getDeclaringTypeName() + "." + joinPoint.getSignature().getName(),
                    IpUtils.getClientIpAddr(), getHeaders(request), resultStr, System.currentTimeMillis() - startTime);
            return result;
        }
        return joinPoint.proceed(joinPoint.getArgs());
    }

    // 判断请求路径是否匹配拦截路径
    private boolean isExcludedPath(String requestURI) {
        return excludePathPatterns.stream().anyMatch(pattern -> pathMatcher.match(pattern, requestURI));
    }

    // 判断请求路径是否匹配排除路径
    private boolean isInterceptedPath(String requestURI) {
        return pathPatterns.stream().anyMatch(pattern -> pathMatcher.match(pattern, requestURI));
    }

    // 获取请求头
    private StringBuilder getHeaders(HttpServletRequest request) {
        StringBuilder headers = new StringBuilder();
        String token = ServletUtils.getHeader(request, "Authorization");
        headers.append("Authorization").append("=").append(token);
        return headers;
    }
}