package com.yunchuang.wxapp.model.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 意见反馈对象 wxapp_feedback
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("wxapp_feedback")
public class Feedback extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 反馈ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @Excel(name = "用户ID")
    private Long userId;

    /**
     * 用户昵称
     */
    @Excel(name = "用户昵称")
    private String nickname;

    /**
     * 反馈内容
     */
    @Excel(name = "反馈内容")
    private String content;

    /**
     * 状态
     */
    @Excel(name = "状态")
    private Integer status;

    /**
     * 反馈类型 1-自助打印机
     */
    @Excel(name = "反馈类型")
    private Integer feedbackType;

}