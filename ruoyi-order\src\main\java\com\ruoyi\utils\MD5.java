package com.ruoyi.utils;

import java.security.MessageDigest;

/**
 * 
 * @project entry
 * @description 
 * <AUTHOR>
 * @creation 2017年3月14日
 * @email 
 * @version
 */
public class MD5 {

    private final static String[] hexDigits = { "0", "1", "2", "3", "4", "5",
        "6", "7", "8", "9", "a", "b", "c", "d", "e", "f" };
    
    /**
     * 通过特定编码格式加密字符串
     * @param origin 需加密的字符串
     * @param charsetName 编码格式
     * @return String 加密后的字符串
     */
    public static String MD5Encode(String origin, String charsetName) {
        origin =origin.trim();
        String resultString = null;
        try {
            resultString = new String(origin);
            MessageDigest md = MessageDigest.getInstance("MD5");
            resultString = byteArrayToHexString(md.digest(resultString.getBytes(charsetName)));
        } catch (Exception ex) {
        }
        return resultString;
    }
    
    public static String byteArrayToHexString(byte[] b) {
        StringBuffer resultSb = new StringBuffer();
        for (int i = 0; i < b.length; i++) {
            resultSb.append(byteToHexString(b[i]));
        }
        return resultSb.toString();
    }
    
    /**
     * Java 转换byte到16进制
     * @param b
     * @return
     */
    private static String byteToHexString(byte b) {
        int n = b;
        if (n < 0) {
            n = 256 + n;
        }
        int d1 = n / 16;
        int d2 = n % 16;
        return hexDigits[d1] + hexDigits[d2];
    }
}
