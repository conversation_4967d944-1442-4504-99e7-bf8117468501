package com.ruoyi.dto;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;

/**
 * 万岁山支付 创建付款码请求类
 */
public class WSSPayCreatePayCodeReq {

    /**
     * 付款金额（单位分）
     */
    @Min(value = 0, message = "付款金额不能小于0")
    private Long amount;

    /**
     * 支付源 0：微信；1：支付宝；4：银联；6：数字人民币
     */
    @Min(value = 0, message = "支付源不合法")
    private Integer paySource;

    /**
     * 订单号
     */
    @NotEmpty(message = "订单号不能为空")
    private String orderNo;


    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public Integer getPaySource() {
        return paySource;
    }

    public void setPaySource(Integer paySource) {
        this.paySource = paySource;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    @Override
    public String toString() {
        return "WSSPayCreatePayCodeReq{" +
                ", amount=" + amount +
                ", paySource=" + paySource +
                ", orderNo='" + orderNo + '\'' +
                '}';
    }
}
