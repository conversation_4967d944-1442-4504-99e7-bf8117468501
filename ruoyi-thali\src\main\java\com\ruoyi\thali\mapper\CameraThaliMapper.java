package com.ruoyi.thali.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.thali.domain.CameraThali;

/**
 * 套餐Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-07-27
 */
public interface CameraThaliMapper  extends BaseMapper<CameraThali>
{
    /**
     * 查询套餐
     * 
     * @param id 套餐主键
     * @return 套餐
     */
    public CameraThali selectCameraThaliById(Long id);

    /**
     * 查询套餐列表
     * 
     * @param cameraThali 套餐
     * @return 套餐集合
     */
    public List<CameraThali> selectCameraThaliList(CameraThali cameraThali);

    /**
     * 新增套餐
     * 
     * @param cameraThali 套餐
     * @return 结果
     */
    public int insertCameraThali(CameraThali cameraThali);

    /**
     * 修改套餐
     * 
     * @param cameraThali 套餐
     * @return 结果
     */
    public int updateCameraThali(CameraThali cameraThali);

    /**
     * 删除套餐
     * 
     * @param id 套餐主键
     * @return 结果
     */
    public int deleteCameraThaliById(Long id);

    /**
     * 批量删除套餐
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCameraThaliByIds(Long[] ids);

    List<CameraThali> selectPackageByDeciceId(String deviceId);

    public int updateCameraThaliById(CameraThali cameraThaliList);


    CameraThali updatePrice(String deviceId);
}
