package com.ruoyi.web.controller.wxapp.admin;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.yunchuang.wxapp.model.domain.PhotoFrameSeries;
import com.yunchuang.wxapp.service.admin.IPhotoFrameSeriesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 相框系列Controller
 *
 * <AUTHOR>
 * @date 2025-02-06
 */
@RestController
@RequestMapping("/wxapp/series")
public class PhotoFrameSeriesController extends BaseController {
    @Autowired
    private IPhotoFrameSeriesService photoFrameSeriesService;

    /**
     * 查询相框系列列表
     */
    @PreAuthorize("@ss.hasPermi('wxapp:series:list')")
    @GetMapping("/list")
    public TableDataInfo list(PhotoFrameSeries photoFrameSeries) {
        startPage();
        List<PhotoFrameSeries> list = photoFrameSeriesService.selectPhotoFrameSeriesList(photoFrameSeries);
        return getDataTable(list);
    }

    /**
     * 导出相框系列列表
     */
    @PreAuthorize("@ss.hasPermi('wxapp:series:export')")
    @Log(title = "相框系列", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PhotoFrameSeries photoFrameSeries) {
        List<PhotoFrameSeries> list = photoFrameSeriesService.selectPhotoFrameSeriesList(photoFrameSeries);
        ExcelUtil<PhotoFrameSeries> util = new ExcelUtil<PhotoFrameSeries>(PhotoFrameSeries.class);
        util.exportExcel(response, list, "相框系列数据");
    }

    /**
     * 获取相框系列详细信息
     */
    @PreAuthorize("@ss.hasPermi('wxapp:series:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(photoFrameSeriesService.selectPhotoFrameSeriesById(id));
    }

    /**
     * 新增相框系列
     */
    @PreAuthorize("@ss.hasPermi('wxapp:series:add')")
    @Log(title = "相框系列", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PhotoFrameSeries photoFrameSeries) {
        return toAjax(photoFrameSeriesService.insertPhotoFrameSeries(photoFrameSeries));
    }

    /**
     * 修改相框系列
     */
    @PreAuthorize("@ss.hasPermi('wxapp:series:edit')")
    @Log(title = "相框系列", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PhotoFrameSeries photoFrameSeries) {
        return toAjax(photoFrameSeriesService.updatePhotoFrameSeries(photoFrameSeries));
    }

    /**
     * 删除相框系列
     */
    @PreAuthorize("@ss.hasPermi('wxapp:series:remove')")
    @Log(title = "相框系列", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(photoFrameSeriesService.deletePhotoFrameSeriesByIds(ids));
    }
}
