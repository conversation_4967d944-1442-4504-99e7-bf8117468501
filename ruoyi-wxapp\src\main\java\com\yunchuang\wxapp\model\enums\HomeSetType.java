package com.yunchuang.wxapp.model.enums;

import lombok.Getter;

/**
 * 首页设置 - 类型
 */
@Getter
public enum HomeSetType {

    // 轮播图
    BANNER(1, "轮播图"),

    // 新品主题
    NEW_PRODUCT_THEME(2, "新品主题");

    private final Integer value;
    private final String name;

    HomeSetType(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public static HomeSetType getHomeSetType(Integer value) {
        for (HomeSetType homeSetType : HomeSetType.values()) {
            if (homeSetType.getValue().equals(value)) {
                return homeSetType;
            }
        }
        return null;
    }

}
