package com.ruoyi.generator.service;

import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

@Service
public class ImageDownloaderService {

    private final RestTemplate restTemplate;

    public ImageDownloaderService(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    public File downloadImageToFile(String imageUrl, String saveFilePath) throws IOException {
        ResponseEntity<byte[]> response = restTemplate.exchange(imageUrl, HttpMethod.GET, null, byte[].class);

        if (response.getStatusCode() == HttpStatus.OK) {
            byte[] imageBytes = response.getBody();

            File imageFile = new File(saveFilePath);
            try (FileOutputStream fos = new FileOutputStream(imageFile)) {
                fos.write(imageBytes);
            }

            return imageFile;
        } else {
            throw new RuntimeException("Failed to download image from the URL: " + imageUrl);
        }
    }
}
