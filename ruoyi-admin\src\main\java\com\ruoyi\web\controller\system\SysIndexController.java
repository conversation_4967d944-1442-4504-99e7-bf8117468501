package com.ruoyi.web.controller.system;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.device.domain.DeviceCamera;
import com.ruoyi.device.service.IDeviceCameraService;
import com.ruoyi.dto.DeviceCount;
import com.ruoyi.dto.Result;
import com.ruoyi.order.domain.OrderCamera;
import com.ruoyi.order.domain.OrderCollect;
import com.ruoyi.order.dto.DatStatisticsMobileDTO;
import com.ruoyi.order.service.IOrderCameraService;
import com.ruoyi.order.service.IOrderCollectService;
import com.ruoyi.socket.WebSocketServer;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 首页
 *
 * <AUTHOR>
 */
@RestController
public class SysIndexController extends BaseController {
    /**
     * 系统基础配置
     */
    @Autowired
    private RuoYiConfig ruoyiConfig;
    @Autowired
    private IOrderCameraService orderCameraService;
    @Autowired
    private ISysUserService userService;
    @Autowired
    private IDeviceCameraService deviceCameraService;
    @Autowired
    private IOrderCollectService orderCollectService;
    /**
     * 首页订单类型汇总概括
     */
    @GetMapping("/monthCount")
    public AjaxResult monthCount() {
        List<Long> monthAccounts = new ArrayList<>();
        List<String> dates = getAllMonthStartDatesUpToNextMonth();
        for (int i = 0; i < dates.size() - 1; i++) {
            Long account = orderCameraService.countMonthIncome(getUserId(), dates.get(i), dates.get(i + 1));
            if (account != null)
            monthAccounts.add(account);
        }
        return success(monthAccounts);
    }


    /**
     * 首页订单类型汇总概括
     */
    @GetMapping("/indexCount")
    public Result indexCount(@RequestParam(required = false) String merchantId) {

        // 获取今天的日期
        LocalDate today = LocalDate.now();
        // 获取昨天的日期
        LocalDate yesterday = today.minusDays(1);
        // 获取明天的日期
        LocalDate tomorrow = today.plusDays(1);
        // 将昨天、今天、明天的日期转为00:00的LocalDateTime
        LocalDateTime yesterdayMidnight = yesterday.atStartOfDay();
        LocalDateTime todayMidnight = today.atStartOfDay();
        LocalDateTime tomorrowMidnight = tomorrow.atStartOfDay();
        // 转换为Date对象
        Date yesterdayDate = Date.from(yesterdayMidnight.atZone(ZoneId.systemDefault()).toInstant());
        Date todayDate = Date.from(todayMidnight.atZone(ZoneId.systemDefault()).toInstant());
        Date tomorrowDate = Date.from(tomorrowMidnight.atZone(ZoneId.systemDefault()).toInstant());

        OrderCamera orderCamera = new OrderCamera();
        orderCamera.setStartTime(todayDate);
        orderCamera.setEndTime(tomorrowDate);

        TableDataInfo todayDataInfo = orderCameraService.selectOrderCameraList(orderCamera, 1, 10000);
        List<OrderCamera> todayOrderList = (List<OrderCamera>) todayDataInfo.getRows();

        //昨日订单统计
        orderCamera.setStartTime(yesterdayDate);
        orderCamera.setEndTime(todayDate);
        TableDataInfo yesterdayDataInfo = orderCameraService.selectOrderCameraList(orderCamera, 1, 10000);
        List<OrderCamera> yesterdayOrderList = (List<OrderCamera>) yesterdayDataInfo.getRows();

        DeviceCount deviceCount = new DeviceCount();

        int todayRefundCount = 0;
        int yesterdayRefundCount = 0;
        for (OrderCamera camera : todayOrderList) {
            if (camera.getOrderStatus() == 3 || camera.getOrderStatus() == 5 || camera.getOrderStatus() == 6)
                todayRefundCount++;
        }
        for (OrderCamera camera : yesterdayOrderList) {
            if (camera.getOrderStatus() == 3 || camera.getOrderStatus() == 5 || camera.getOrderStatus() == 6)
                yesterdayRefundCount++;
        }

        Long historyTotalIncome = orderCameraService.getHistoryTotalIncome(getUserId());


        deviceCount.setRevenueCount(historyTotalIncome);
        deviceCount.setTodayOrderCount(todayOrderList.size());
        deviceCount.setYesterdayOrderCount(yesterdayOrderList.size());
        deviceCount.setTodayCount(((OrderCamera)todayDataInfo.getData()).getAccountCount());
        deviceCount.setYesterdayCount(((OrderCamera)yesterdayDataInfo.getData()).getAccountCount());
        deviceCount.setTodayRefundCount(todayRefundCount);
        deviceCount.setYesterdayRefundCount(yesterdayRefundCount);
        deviceCount.setTodayOrderList(todayOrderList);
        deviceCount.setYesterdayOrderList(yesterdayOrderList);

        //设备列表 统计设备在线状态 总量
        int onlineCount = 0;
        List<DeviceCamera> deviceList = (List<DeviceCamera>) deviceCameraService.selectDeviceCameraList(new DeviceCamera(), 1, 10000).getRows();
        for (DeviceCamera device : deviceList) {
            String deviceId = device.getDeviceId();
            if (WebSocketServer.checkOnline(deviceId)) {
                onlineCount++;
            }
        }
        deviceCount.setOnLineCount((long) onlineCount);
        deviceCount.setDeviceCount((long) deviceList.size());

        return Result.ok(200, "查询成功", "", deviceCount);
    }

    /**
     * 首页设备统计
     */
    @GetMapping("/indexCountDevice")
    public Result indexCountDevice(@RequestParam(required = false) String userId) {
        DeviceCount deviceCount = new DeviceCount();
        //设备列表 统计设备在线状态 总量
        int onlineCount = 0;
        List<DeviceCamera> deviceList = (List<DeviceCamera>) deviceCameraService.selectDeviceCameraList(new DeviceCamera(), 1, 10000).getRows();
        for (DeviceCamera device : deviceList) {
            String deviceId = device.getDeviceId();
            if (WebSocketServer.checkOnline(deviceId)) {
                onlineCount++;
            }
        }
        deviceCount.setOnLineCount((long) onlineCount);
        deviceCount.setDeviceCount((long) deviceList.size());
        return Result.ok(200, "查询成功", "", deviceCount);
    }

    /**
     * 首页订单统计
     */
    @GetMapping("/indexCountOrder")
    public Result indexCountOrder(@RequestParam(required = false) String userId) {
        return null;
    }

    /**
     * 首页汇总统计
     */
    @GetMapping("/indexCollectOrder")
    public Result indexCollectOrder(@RequestParam(required = false) String userId) {
        return null;
    }

    /**
     * 移动端首页数据
     */
    @GetMapping("/indexMobile")
    public Result indexMobile(@RequestParam(required = false) String deviceId) {
        LoginUser loginUser = getLoginUser();
        if (loginUser == null) return null;
        return Result.ok(200, "查询成功", "", orderCameraService.getIndexMobile(loginUser, deviceId));
    }

    /**
     * 移动端数据统计
     */
    @GetMapping("/dataStatisticsMobile")
    public Result dataStatisticsMobile(@RequestParam Integer dateFilter) {
        LoginUser loginUser = getLoginUser();
        if (loginUser == null) return null;
        SysUser user = loginUser.getUser();
        if (user == null) return null;
        // 商户号
        String merchantId = (user.getMerchantId() == null || user.getMerchantId().isEmpty()) ? String.valueOf(user.getUserId()) : user.getMerchantId();

        DatStatisticsMobileDTO datStatisticsMobileDTO = orderCameraService.getDataStatisticsMobile(merchantId, loginUser, dateFilter);
        long onlineDevice = 0;
        for (String deviceId : datStatisticsMobileDTO.getDeviceIdList()) { // 统计在线设备数
            if (WebSocketServer.checkOnline(deviceId)) onlineDevice += 1;
        }
        datStatisticsMobileDTO.setOnlineDevice(onlineDevice);
        return Result.ok(200, "查询成功", "", datStatisticsMobileDTO);
    }

    public static List<String> getAllMonthStartDatesUpToNextMonth() {
        List<String> monthDates = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        LocalDate now = LocalDate.now();

        // 当前月份
        int currentYear = now.getYear();
        int currentMonth = now.getMonthValue();

        // 生成从1月到当前月+1的月份列表（处理跨年）
        int totalMonths = currentMonth + 1;
        for (int i = 1; i <= totalMonths; i++) {
            int year = currentYear;
            int month = i;
            if (i > 12) {
                year += 1;
                month = i - 12;
            }
            LocalDate firstDay = LocalDate.of(year, month, 1);
            String formattedDate = firstDay.atStartOfDay(ZoneId.systemDefault()).format(formatter);
            monthDates.add(formattedDate);
        }

        return monthDates;
    }


}