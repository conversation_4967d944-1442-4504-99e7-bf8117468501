package com.ruoyi.web.controller.wxapp.client;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.utils.MyResultUtil;
import com.yunchuang.wxapp.model.req.CConsultationLikeOrUnlikeReq;
import com.yunchuang.wxapp.service.client.ICConsultationService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 咨询Controller
 */
@RestController
@RequestMapping("/client/wxapp/consultation")
public class CConsultationController extends BaseController {

    @Resource
    private ICConsultationService cConsultationService;

    /**
     * 咨询问题点赞或点踩
     */
    @PostMapping("/like_or_unlike")
    public Map<String, Object> likeOrUnlike(@RequestBody CConsultationLikeOrUnlikeReq req) {
        boolean isSuccess = cConsultationService.likeOrUnlike(req);
        return isSuccess ? MyResultUtil.success() : MyResultUtil.error();
    }
}
