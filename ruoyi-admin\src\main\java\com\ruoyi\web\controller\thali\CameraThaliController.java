package com.ruoyi.web.controller.thali;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.thali.domain.CameraThali;
import com.ruoyi.thali.dto.Result;
import com.ruoyi.thali.service.ICameraThaliService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 套餐Controller
 * 
 * <AUTHOR>
 * @date 2023-07-27
 */
@RestController
@RequestMapping("/thali/camera_thali")
public class CameraThaliController extends BaseController
{
    @Autowired
    private ICameraThaliService cameraThaliService;

    /**
     * 查询套餐列表
     */
    @PreAuthorize("@ss.hasPermi('thali:camera_thali:list')")
    @GetMapping("/list")
    public TableDataInfo list(CameraThali cameraThali)
    {
        startPage();
        List<CameraThali> list = cameraThaliService.selectCameraThaliList(cameraThali);
        return getDataTable(list);
    }

    /**
     * 导出套餐列表
     */
    @PreAuthorize("@ss.hasPermi('thali:camera_thali:export')")
    @Log(title = "套餐", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CameraThali cameraThali)
    {
        List<CameraThali> list = cameraThaliService.selectCameraThaliList(cameraThali);
        ExcelUtil<CameraThali> util = new ExcelUtil<CameraThali>(CameraThali.class);
        util.exportExcel(response, list, "套餐数据");
    }

    /**
     * 获取套餐详细信息
     */
    @PreAuthorize("@ss.hasPermi('thali:camera_thali:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(cameraThaliService.selectCameraThaliById(id));
    }

    /**
     * 新增套餐
     */
    @PreAuthorize("@ss.hasPermi('thali:camera_thali:add')")
    @Log(title = "套餐", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CameraThali cameraThali)
    {
        return toAjax(cameraThaliService.insertCameraThali(cameraThali));
    }

    /**
     * 修改套餐
     */
    @PreAuthorize("@ss.hasPermi('thali:camera_thali:edit')")
    @Log(title = "套餐", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CameraThali cameraThali)
    {
        return toAjax(cameraThaliService.updateCameraThali(cameraThali));
    }

    /**
     * 删除套餐
     */
    @PreAuthorize("@ss.hasPermi('thali:camera_thali:remove')")
    @Log(title = "套餐", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(cameraThaliService.deleteCameraThaliByIds(ids));
    }

	@GetMapping("/byDevice")
    public Result getByDevice(@RequestParam String deviceId)
    {
        return cameraThaliService.queryCameraThaliByDevice(deviceId);
    }

    @GetMapping("/packageType")
    public AjaxResult getPackageType(@RequestParam String deviceId)
    {
        CameraThali cameraThalis = cameraThaliService.queryPackageType(deviceId);
        return success(cameraThalis);
    }
    @PostMapping("/updatePrice")
    public AjaxResult updatePrice(CameraThali cameraThali)
    {
        System.out.println("cameraThali");
        System.out.println(cameraThali);
        CameraThali cameraThali1 = cameraThaliService.updatePrice(cameraThali);
        return success(cameraThali1);
    }
}
