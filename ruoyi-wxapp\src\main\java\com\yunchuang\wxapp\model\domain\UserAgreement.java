package com.yunchuang.wxapp.model.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 用户协议对象 wxapp_user_agreement
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("wxapp_user_agreement")
public class UserAgreement extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 协议ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 发布日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "发布日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date releaseDate;

    /**
     * 生效日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "生效日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date effectiveDate;

    /**
     * 协议内容
     */
    @Excel(name = "协议内容")
    private String agreementContent;

    /**
     * 是否启用 0：是 1：否
     */
    @Excel(name = "是否启用 0：是 1：否")
    private Integer beEnabled;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setReleaseDate(Date releaseDate) {
        this.releaseDate = releaseDate;
    }

    public Date getReleaseDate() {
        return releaseDate;
    }

    public void setEffectiveDate(Date effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    public Date getEffectiveDate() {
        return effectiveDate;
    }

    public void setAgreementContent(String agreementContent) {
        this.agreementContent = agreementContent;
    }

    public String getAgreementContent() {
        return agreementContent;
    }

    public void setBeEnabled(Integer beEnabled) {
        this.beEnabled = beEnabled;
    }

    public Integer getBeEnabled() {
        return beEnabled;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("releaseDate", getReleaseDate())
                .append("effectiveDate", getEffectiveDate())
                .append("agreementContent", getAgreementContent())
                .append("beEnabled", getBeEnabled())
                .toString();
    }
}
