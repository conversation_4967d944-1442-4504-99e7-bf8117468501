package com.ruoyi;


import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 启动程序
 *
 * <AUTHOR>
 */

@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@ComponentScan(basePackages = {"com.ruoyi", "com.yunchuang"})
@EnableScheduling
public class RuoYiApplication {

    public static void main(String[] args) {
        // System.setProperty("spring.devtools.restart.enabled", "false");
        SpringApplication app = new SpringApplication(RuoYiApplication.class);
        app.run(args);
        System.out.println(" ________  ________  ________ \n" +
                "|\\_____  \\|\\_____  \\|\\  _____\\\n" +
                " \\|___/  /|\\|___/  /\\ \\  \\__/ \n" +
                "     /  / /    /  / /\\ \\   __\\\n" +
                "    /  /_/__  /  /_/__\\ \\  \\_|\n" +
                "   |\\________\\\\________\\ \\__\\ \n" +
                "    \\|_______|\\|_______|\\|__| \n" +
                "\n");
    }
}
