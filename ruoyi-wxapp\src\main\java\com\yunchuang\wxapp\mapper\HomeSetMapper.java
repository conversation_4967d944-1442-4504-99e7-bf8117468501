package com.yunchuang.wxapp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yunchuang.wxapp.model.domain.HomeSet;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 首页设置Mapper接口
 *
 * <AUTHOR>
 * @date 2025-02-07
 */
@Mapper
public interface HomeSetMapper extends BaseMapper<HomeSet> {
    /**
     * 查询首页设置
     *
     * @param id 首页设置主键
     * @return 首页设置
     */
    public HomeSet selectHomeSetById(Long id);

    /**
     * 查询首页设置列表
     *
     * @param homeSet 首页设置
     * @return 首页设置集合
     */
    public List<HomeSet> selectHomeSetList(HomeSet homeSet);

    /**
     * 新增首页设置
     *
     * @param homeSet 首页设置
     * @return 结果
     */
    public int insertHomeSet(HomeSet homeSet);

    /**
     * 修改首页设置
     *
     * @param homeSet 首页设置
     * @return 结果
     */
    public int updateHomeSet(HomeSet homeSet);

    /**
     * 删除首页设置
     *
     * @param id 首页设置主键
     * @return 结果
     */
    public int deleteHomeSetById(Long id);

    /**
     * 批量删除首页设置
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHomeSetByIds(Long[] ids);
}
