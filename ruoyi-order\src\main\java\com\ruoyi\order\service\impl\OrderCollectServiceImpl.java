package com.ruoyi.order.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.order.domain.OrderCamera;
import com.ruoyi.order.domain.OrderCollect;
import com.ruoyi.order.mapper.OrderCameraMapper;
import com.ruoyi.order.mapper.OrderCollectMapper;
import com.ruoyi.order.service.IOrderCollectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


/**
 * collectService业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-03
 */
@Service
public class OrderCollectServiceImpl extends ServiceImpl<OrderCollectMapper, OrderCollect> implements IOrderCollectService {

    @Resource
    private OrderCollectMapper orderCollectMapper;

    @Override
    public Long getCountMon(String time) {
        return orderCollectMapper.countPrice(time);
    }
}
