package com.yunchuang.wxapp.model.enums.exception;

import lombok.Getter;

/**
 * 微信小程序 - 认证异常枚举
 */
@Getter
public enum WxappAuthenticationExceptionCode {

    /**
     * 认证失败
     */
    AUTHENTICATION_FAILED(60401, "认证失败"),

    /**
     * 认证标识不能为空
     */
    AUTHENTICATION_ID_NULL(60402, "认证标识不能为空"),

    /**
     * 认证已过期
     */
    AUTHENTICATION_EXPIRED(60403, "认证已过期");


    private final int code;
    private final String message;

    WxappAuthenticationExceptionCode(int code, String message) {
        this.code = code;
        this.message = message;
    }
}
