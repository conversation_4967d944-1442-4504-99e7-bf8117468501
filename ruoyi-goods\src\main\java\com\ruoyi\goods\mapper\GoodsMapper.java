package com.ruoyi.goods.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.goods.domain.Goods;

/**
 * goodsMapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
public interface GoodsMapper extends BaseMapper<Goods>
{
    /**
     * 查询goods
     * 
     * @param id goods主键
     * @return goods
     */
    public Goods selectGoodsById(Long id);

    /**
     * 查询goods列表
     * 
     * @param goods goods
     * @return goods集合
     */
    public List<Goods> selectGoodsList(Goods goods);

    /**
     * 新增goods
     * 
     * @param goods goods
     * @return 结果
     */
    public int insertGoods(Goods goods);

    /**
     * 修改goods
     * 
     * @param goods goods
     * @return 结果
     */
    public int updateGoods(Goods goods);

    /**
     * 删除goods
     * 
     * @param id goods主键
     * @return 结果
     */
    public int deleteGoodsById(Long id);

    /**
     * 批量删除goods
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteGoodsByIds(Long[] ids);
}
