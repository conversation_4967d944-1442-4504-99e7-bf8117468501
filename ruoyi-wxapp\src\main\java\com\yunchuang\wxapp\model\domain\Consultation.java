package com.yunchuang.wxapp.model.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 咨询对象 wxapp_consultation
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("wxapp_consultation")
public class Consultation extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 咨询ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 咨询类型
     */
    @Excel(name = "咨询类型")
    private Integer type;

    /**
     * 问题
     */
    @Excel(name = "问题")
    private String question;

    /**
     * 回答
     */
    @Excel(name = "回答")
    private String answer;

    /**
     * 已解决数量
     */
    @Excel(name = "已解决数量")
    private Long resolvedNum;

    /**
     * 未解决数量
     */
    @Excel(name = "未解决数量")
    private Long unresolvedNum;

}
