package com.ruoyi.resource.service.impl;

import java.util.List;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.resource.mapper.ModelPackageMapper;
import com.ruoyi.resource.domain.ModelPackage;
import com.ruoyi.resource.service.IModelPackageService;

/**
 * 模板库Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-10
 */
@Service
public class ModelPackageServiceImpl implements IModelPackageService 
{
    @Autowired
    private ModelPackageMapper modelPackageMapper;

    /**
     * 查询模板库
     * 
     * @param id 模板库主键
     * @return 模板库
     */
    @Override
    public ModelPackage selectModelPackageById(Long id)
    {
        return modelPackageMapper.selectModelPackageById(id);
    }

    /**
     * 查询模板库列表
     * 
     * @param modelPackage 模板库
     * @return 模板库
     */
    @Override
    public List<ModelPackage> selectModelPackageList(ModelPackage modelPackage)
    {

        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser == null) return null;
        SysUser sysUser = loginUser.getUser();
        if (sysUser == null) return null;
        modelPackage.setCreateBy(sysUser.getUserName());
        return modelPackageMapper.selectModelPackageList(modelPackage);
    }

    /**
     * 新增模板库
     * 
     * @param modelPackage 模板库
     * @return 结果
     */
    @Override
    public int insertModelPackage(ModelPackage modelPackage)
    {
        modelPackage.setCreateTime(DateUtils.getNowDate());
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser == null) return 0;
        SysUser sysUser = loginUser.getUser();
        if (sysUser == null) return 0;
        modelPackage.setCreateBy(sysUser.getUserName());
        return modelPackageMapper.insertModelPackage(modelPackage);
    }

    /**
     * 修改模板库
     * 
     * @param modelPackage 模板库
     * @return 结果
     */
    @Override
    public int updateModelPackage(ModelPackage modelPackage)
    {
        modelPackage.setUpdateTime(DateUtils.getNowDate());
        return modelPackageMapper.updateModelPackage(modelPackage);
    }

    /**
     * 批量删除模板库
     * 
     * @param ids 需要删除的模板库主键
     * @return 结果
     */
    @Override
    public int deleteModelPackageByIds(Long[] ids)
    {
        return modelPackageMapper.deleteModelPackageByIds(ids);
    }

    /**
     * 删除模板库信息
     * 
     * @param id 模板库主键
     * @return 结果
     */
    @Override
    public int deleteModelPackageById(Long id)
    {
        return modelPackageMapper.deleteModelPackageById(id);
    }
}
