package com.ruoyi.beauty.mapper;

import java.util.List;
import com.ruoyi.beauty.domain.BeautyValue;

/**
 * 美颜强度Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-10-15
 */
public interface BeautyValueMapper 
{
    /**
     * 查询美颜强度
     * 
     * @param id 美颜强度主键
     * @return 美颜强度
     */
    public BeautyValue selectBeautyValueById(Long id);

    /**
     * 查询美颜强度列表
     * 
     * @param beautyValue 美颜强度
     * @return 美颜强度集合
     */
    public List<BeautyValue> selectBeautyValueList(BeautyValue beautyValue);

    /**
     * 新增美颜强度
     * 
     * @param beautyValue 美颜强度
     * @return 结果
     */
    public int insertBeautyValue(BeautyValue beautyValue);

    /**
     * 修改美颜强度
     * 
     * @param beautyValue 美颜强度
     * @return 结果
     */
    public int updateBeautyValue(BeautyValue beautyValue);

    /**
     * 删除美颜强度
     * 
     * @param id 美颜强度主键
     * @return 结果
     */
    public int deleteBeautyValueById(Long id);

    /**
     * 批量删除美颜强度
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBeautyValueByIds(Long[] ids);
}
