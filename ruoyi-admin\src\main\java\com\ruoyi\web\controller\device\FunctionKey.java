package com.ruoyi.web.controller.device;

import com.ruoyi.common.AndroidBeauty.XiangXinKey;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.device.domain.DeviceCamera;
import com.ruoyi.device.service.IDeviceCameraService;
import com.ruoyi.dto.Result;
import com.ruoyi.socket.WebSocketServer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;

/**
 * 获取相关功能密钥
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@Slf4j
@RestController
@RequestMapping("/function")
public class FunctionKey {
    @Autowired
    private IDeviceCameraService deviceCameraService;

    /**
     * 获取相芯密钥
     */
    @GetMapping("/XiangXin")
    public Result getXiangXinKey(String deviceId) {
        DeviceCamera deviceCamera = deviceCameraService.getById(deviceId);
        if (deviceCamera == null){
            return Result.fail(400,"设备不存在", String.valueOf(System.currentTimeMillis()));
        }
        byte[] bytes = XiangXinKey.A();
        try {
            String encrypt = encrypt(bytes);
            return Result.ok(200,"获取成功", String.valueOf(System.currentTimeMillis()),encrypt);
        } catch (Exception e) {
            log.error("getXiangXinKey",e.getMessage());
        }
        return Result.fail(500,"获取失败", String.valueOf(System.currentTimeMillis()));
    }


//    public static void main(String[] args) throws Exception {
//        byte[] bytes = XiangXinKey.A();
//        byte[] encrypt = encrypt(bytes);
//        System.out.println(Arrays.toString(encrypt));
//        System.out.println(Result.ok(200, "获取成功", String.valueOf(System.currentTimeMillis()), encrypt));
//    }

    /**
     * 加密 byte[]
     */
    private static final String SECRET_KEY = "yunchuangzhineng"; // 16 字节
    public static String encrypt(byte[] data) throws Exception {
        SecretKeySpec keySpec = new SecretKeySpec(SECRET_KEY.getBytes(), "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(Cipher.ENCRYPT_MODE, keySpec);
        byte[] bytes = cipher.doFinal(data);
        return Base64.getEncoder().encodeToString(bytes);
    }

}
