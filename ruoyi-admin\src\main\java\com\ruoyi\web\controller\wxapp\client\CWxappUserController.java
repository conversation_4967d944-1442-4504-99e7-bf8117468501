package com.ruoyi.web.controller.wxapp.client;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.RandomUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.message.service.AliSmsService;
import com.ruoyi.order.service.IOrderPrinterService;
import com.yunchuang.wxapp.model.constant.RedisKeyGroup;
import com.yunchuang.wxapp.model.domain.WxappLoginUser;
import com.yunchuang.wxapp.model.req.CUserLoginOrRegisterReq;
import com.yunchuang.wxapp.service.WxAppAuthService;
import com.yunchuang.wxapp.service.client.ICUserService;
import com.yunchuang.wxapp.util.UserContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 用户Controller
 */
@RestController
@RequestMapping("/client/wxapp/user")
public class CWxappUserController extends BaseController {

    private static final Logger log = LoggerFactory.getLogger(CWxappUserController.class);

    @Resource
    private ICUserService cUserService;

    @Resource
    private AliSmsService aliSmsService;

    @Resource
    private WxAppAuthService wxAppAuthService;

    @Resource
    private IOrderPrinterService orderPrinterService;

    @Resource
    private RedisCache redisCache;

    /**
     * 登录或注册
     */
    @PostMapping("/login_or_register")
    public Map<String, Object> loginOrRegister(@RequestBody CUserLoginOrRegisterReq req) {
        return success(cUserService.loginOrRegister(req));
    }

    /**
     * 发送短信验证码（微信小程序专用）
     */
    @PostMapping("/sendSmsCode")
    public Map<String, Object> sendSmsCode(HttpServletRequest request, @RequestBody Map<String, String> requestBody) {

        // 从请求中获取手机号
        String phoneNumber = requestBody.get("phoneNumber");

        log.info("=== 微信小程序发送验证码请求 ===");
        log.info("手机号: {}", phoneNumber);

        // 参数验证
        if (StringUtils.isEmpty(phoneNumber)) {
            log.error("参数验证失败: 手机号不能为空");
            return error("手机号不能为空");
        }

        // 手机号格式验证
        if (!phoneNumber.matches("^1[3-9]\\d{9}$")) {
            log.error("参数验证失败: 手机号格式不正确");
            return error("手机号格式不正确");
        }

        // 获取当前用户信息（如果已登录）
        String openid = getCurrentUserOpenid(request);
        if (StringUtils.isNotEmpty(openid)) {
            // 用户已登录，检查手机号是否与当前绑定的手机号一致
            String checkResult = cUserService.checkMobileForSms(openid, phoneNumber);
            if (!"可以发送".equals(checkResult)) {
                log.warn("手机号检查失败 - openid: {}, mobile: {}, reason: {}", openid, phoneNumber, checkResult);
                return error(checkResult);
            }
        } else {
            log.info("用户未登录，跳过手机号一致性检查");
        }

        try {
            // 生成4位随机验证码
            String code = RandomUtil.getFourBitRandom();
            log.info("生成验证码: {} (手机号: {})", code, phoneNumber);

            // 发送短信验证码（模拟模式）
            boolean isSuccess = aliSmsService.sendSmsVerifyCodeMock(phoneNumber, code);

            if (isSuccess) {
                log.info("验证码发送成功 - 手机号: {}", phoneNumber);
                return success("验证码发送成功");
            } else {
                log.error("验证码发送失败 - 手机号: {}", phoneNumber);
                return error("验证码发送失败，请稍后重试");
            }

        } catch (Exception e) {
            log.error("发送验证码异常 - 手机号: {}, error: {}", phoneNumber, e.getMessage(), e);
            return error("系统异常，请稍后重试");
        }
    }

    /**
     * 校验验证码并绑定手机号
     */
    @PostMapping("/bindMobile")
    public Map<String, Object> bindMobile(
            HttpServletRequest request,
            @RequestParam("mobile") String mobile,
            @RequestParam("code") String code) {

        log.info("=== 开始处理手机号绑定请求 ===");
        log.info("手机号: {}, 验证码: {}", mobile, code);

        // 基础参数验证
        if (StringUtils.isEmpty(mobile)) {
            log.error("参数验证失败: 手机号不能为空");
            return error("手机号不能为空");
        }

        if (StringUtils.isEmpty(code)) {
            log.error("参数验证失败: 验证码不能为空");
            return error("验证码不能为空");
        }

        try {
            // 校验验证码（控制器层负责验证码校验，因为需要访问message模块）
            boolean isCodeValid = aliSmsService.checkSmsVerifyCode(mobile, code);
            if (!isCodeValid) {
                log.error("验证码校验失败: 验证码不正确或已过期");
                return error("验证码不正确");
            }

            // 获取当前用户openid
            String openid = getCurrentUserOpenid(request);
            if (StringUtils.isEmpty(openid)) {
                log.error("获取用户openid失败: 用户未登录");
                return error("用户未登录，请先登录");
            }

            // 调用业务服务进行绑定（业务逻辑在服务层处理）
            String bindResult = cUserService.bindMobileWithValidation(openid, mobile);

            // 根据业务服务返回的结果判断成功或失败
            if ("绑定成功".equals(bindResult)) {
                log.info("手机号绑定成功: openid={}, mobile={}", openid, mobile);
                return success(bindResult);
            } else {
                log.error("手机号绑定失败: openid={}, mobile={}, reason={}", openid, mobile, bindResult);
                return error(bindResult);
            }

        } catch (Exception e) {
            log.error("手机号绑定异常: {}", e.getMessage(), e);
            return error("系统异常，请稍后重试");
        }
    }

    /**
     * 从请求中获取当前用户的openid
     * 优先级：1. 从ThreadLocal中获取 2. 从JWT token中获取 3. 返回null
     */
    private String getCurrentUserOpenid(HttpServletRequest request) {
        try {
            // 方法1：尝试从ThreadLocal中获取（如果经过了认证拦截器）
            WxappLoginUser currentUser = UserContext.getCurrentUser();
            if (currentUser != null && StringUtils.isNotEmpty(currentUser.getOpenid())) {
                log.info("从ThreadLocal获取到openid: {}", currentUser.getOpenid());
                return currentUser.getOpenid();
            }

            // 方法2：尝试从JWT token中获取
            WxappLoginUser loginUser = wxAppAuthService.getClientUser(request);
            if (loginUser != null && StringUtils.isNotEmpty(loginUser.getOpenid())) {
                log.info("从JWT token获取到openid: {}", loginUser.getOpenid());
                return loginUser.getOpenid();
            }
        } catch (Exception e) {
            log.warn("获取用户openid失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 注销用户账号
     */
    @PostMapping("/deleteAccount")
    public Map<String, Object> deleteAccount(HttpServletRequest request) {

        log.info("=== 开始处理用户注销请求 ===");

        try {
            // 获取当前用户openid
            String openid = getCurrentUserOpenid(request);
            if (StringUtils.isEmpty(openid)) {
                log.error("获取用户openid失败: 用户未登录");
                return error("用户未登录，请先登录");
            }

            log.info("开始注销用户账号 - openid: {}", openid);

            // 1. 删除用户的打印订单数据（逻辑删除）
            boolean orderDeleteResult = deleteUserOrders(openid);
            if (!orderDeleteResult) {
                log.warn("删除用户订单数据失败，但继续执行用户删除 - openid: {}", openid);
            }

            // 2. 删除用户账号
            String userDeleteResult = cUserService.deleteUserAccount(openid);

            // 根据结果返回响应
            if ("注销成功".equals(userDeleteResult)) {
                log.info("用户账号注销成功 - openid: {}", openid);
                return success("注销成功");
            } else {
                log.error("用户账号注销失败 - openid: {}, reason: {}", openid, userDeleteResult);
                return error(userDeleteResult);
            }

        } catch (Exception e) {
            log.error("用户账号注销异常: {}", e.getMessage(), e);
            return error("系统异常，请稍后重试");
        }
    }

    /**
     * 退出登录
     */
    @PostMapping("/logout")
    public Map<String, Object> logout(HttpServletRequest request) {

        log.info("=== 开始处理用户退出登录请求 ===");

        try {
            // 获取当前用户信息
            WxappLoginUser currentUser = UserContext.getCurrentUser();
            String openid = null;
            Long userId = null;

            if (currentUser != null) {
                openid = currentUser.getOpenid();
                userId = currentUser.getId();
                log.info("从ThreadLocal获取到用户信息 - openid: {}, userId: {}", openid, userId);
            } else {
                // 如果ThreadLocal中没有，尝试从JWT token中获取
                try {
                    WxappLoginUser loginUser = wxAppAuthService.getClientUser(request);
                    if (loginUser != null) {
                        openid = loginUser.getOpenid();
                        userId = loginUser.getId();
                        log.info("从JWT token获取到用户信息 - openid: {}, userId: {}", openid, userId);
                    }
                } catch (Exception e) {
                    log.warn("从JWT token获取用户信息失败: {}", e.getMessage());
                }
            }

            // 如果获取到了用户ID，清除Redis中的登录信息
            if (userId != null) {
                String redisKey = RedisKeyGroup.LOGIN_USER + ":" + userId;
                boolean deleteResult = redisCache.deleteObject(redisKey);
                log.info("清除Redis登录信息 - key: {}, result: {}", redisKey, deleteResult);
            } else {
                log.warn("未获取到用户ID，跳过Redis清理");
            }

            // 清除ThreadLocal中的用户信息
            UserContext.removeCurrentUser();
            log.info("已清除ThreadLocal中的用户信息");

            log.info("用户退出登录成功 - openid: {}", openid);
            return success("退出登录成功");

        } catch (Exception e) {
            log.error("用户退出登录异常: {}", e.getMessage(), e);
            // 即使出现异常，也要尝试清除ThreadLocal
            try {
                UserContext.removeCurrentUser();
            } catch (Exception clearException) {
                log.error("清除ThreadLocal异常: {}", clearException.getMessage());
            }
            return error("退出登录失败，请稍后重试");
        }
    }

    /**
     * 删除用户的打印订单数据（逻辑删除）
     *
     * @param openid 用户openid
     * @return 删除结果
     */
    private boolean deleteUserOrders(String openid) {
        try {
            log.info("开始删除用户订单数据 - openid: {}", openid);

            // 调用订单服务删除用户的所有订单数据
            boolean result = orderPrinterService.deleteUserOrdersByOpenid(openid);

            if (result) {
                log.info("用户订单数据删除成功 - openid: {}", openid);
            } else {
                log.warn("用户订单数据删除失败 - openid: {}", openid);
            }

            return result;
        } catch (Exception e) {
            log.error("删除用户订单数据异常 - openid: {}, error: {}", openid, e.getMessage(), e);
            return false;
        }
    }

}
