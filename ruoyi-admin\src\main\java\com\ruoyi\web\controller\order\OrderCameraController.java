package com.ruoyi.web.controller.order;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.GeneralSecurityException;
import java.security.PrivateKey;
import java.util.Date;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.device.domain.DeviceCamera;
import com.ruoyi.device.service.IDeviceCameraService;
import com.ruoyi.order.domain.OrderCollect;
import com.ruoyi.order.dto.CountPriceDto;
import com.ruoyi.order.dto.Result;
import com.ruoyi.order.service.IOrderCollectService;
import com.ruoyi.photo.domain.UserPhoto;
import com.ruoyi.photo.service.IUserPhotoService;
import com.ruoyi.socket.WebSocketServer;
import com.ruoyi.system.service.ISysRoleService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.utils.HttpUtils;
import com.wechat.pay.contrib.apache.httpclient.auth.PrivateKeySigner;
import com.wechat.pay.contrib.apache.httpclient.auth.Verifier;
import com.wechat.pay.contrib.apache.httpclient.auth.WechatPay2Credentials;
import com.wechat.pay.contrib.apache.httpclient.cert.CertificatesManager;
import com.wechat.pay.contrib.apache.httpclient.constant.WechatPayHttpHeaders;
import com.wechat.pay.contrib.apache.httpclient.exception.HttpCodeException;
import com.wechat.pay.contrib.apache.httpclient.exception.NotFoundException;
import com.wechat.pay.contrib.apache.httpclient.exception.ParseException;
import com.wechat.pay.contrib.apache.httpclient.exception.ValidationException;
import com.wechat.pay.contrib.apache.httpclient.notification.Notification;
import com.wechat.pay.contrib.apache.httpclient.notification.NotificationHandler;
import com.wechat.pay.contrib.apache.httpclient.notification.NotificationRequest;
import com.wechat.pay.contrib.apache.httpclient.util.AesUtil;
import com.wechat.pay.contrib.apache.httpclient.util.PemUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.order.domain.OrderCamera;
import com.ruoyi.order.service.IOrderCameraService;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.web.servlet.ModelAndView;

import static com.ruoyi.utils.OrderConstant.*;

/**
 * 订单管理Controller
 *
 * <AUTHOR>
 * @date 2023-07-13
 */
@Slf4j
@RestController
@RequestMapping("/order/camera_order")
public class OrderCameraController extends BaseController {

    @Autowired
    private IOrderCameraService orderCameraService;

    @Autowired
    private IDeviceCameraService deviceCameraService;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private IUserPhotoService userPhotoService;

    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private IOrderCollectService orderCollectService;

    /**
     * 查询订单管理列表
     */
    @PreAuthorize("@ss.hasPermi('order:camera_order:list')")
    @GetMapping("/list")
    public TableDataInfo list(OrderCamera orderCamera, int pageNum, int pageSize) {
        return orderCameraService.selectOrderCameraList(orderCamera, pageNum, pageSize);
    }

    /**
     * 导出订单管理列表
     */
//    @PreAuthorize("@ss.hasPermi('order:camera_order:export')")
    @Log(title = "订单管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OrderCamera orderCamera, int pageNum, int pageSize) {
        TableDataInfo tableDataInfo = orderCameraService.selectOrderCameraList(orderCamera, pageNum, pageSize);
        List<OrderCamera> list = (List<OrderCamera>) tableDataInfo.getRows();
        ExcelUtil<OrderCamera> util = new ExcelUtil<OrderCamera>(OrderCamera.class);
        util.exportExcel(response, list, "订单管理数据");
    }

    /**
     * 获取订单管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('order:camera_order:query')")
    @GetMapping(value = "/{orderId}")
    public AjaxResult getInfo(@PathVariable("orderId") String orderId) {
        return success(orderCameraService.selectOrderCameraByOrderId(orderId));
    }

    /**
     * 修改订单管理
     */
    @PreAuthorize("@ss.hasPermi('order:camera_order:edit')")
    @Log(title = "订单管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OrderCamera orderCamera) {
        return toAjax(orderCameraService.updateOrderCamera(orderCamera));
    }

    /**
     * 删除订单管理
     */
    @PreAuthorize("@ss.hasPermi('order:camera_order:remove')")
    @Log(title = "订单管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{orderIds}")
    public AjaxResult remove(@PathVariable String[] orderIds) {
        return toAjax(orderCameraService.deleteOrderCameraByOrderIds(orderIds));
    }

    /**
     * 订单预创建微信
     */
    @Log(title = "订单预创建", businessType = BusinessType.OTHER)
    @GetMapping("/preOrder")
    public Result getPreOrderId(@RequestParam String deviceId,
                                @RequestParam Integer pay,
                                @RequestParam Integer productQuantity) {
        if (deviceId == null) {
            return Result.fail(500, "设备id不能为空", String.valueOf(System.currentTimeMillis()));
        }
        DeviceCamera camera = deviceCameraService.selectDeviceCameraByDeviceId(deviceId);
        if (camera == null) {
            return Result.fail(500, "设备id不存在", String.valueOf(System.currentTimeMillis()));
        }
        if (camera.getDeviceStatus() == 0) {
            return Result.fail(500, "设备已停用", String.valueOf(System.currentTimeMillis()));
        }
        if (camera.getUserId() == null) {
            return Result.fail(500, "设备无负责人", String.valueOf(System.currentTimeMillis()));
        }
        OrderCamera orderCamera = new OrderCamera();
        orderCamera.setDeviceId(deviceId);
        orderCamera.setUserId(camera.getUserId());
        orderCamera.setDeviceName(camera.getDeviceName());

        orderCamera.setProductDescription("拍照机打印照片交易");
        orderCamera.setCreateTime(DateUtils.getNowDate());

        orderCamera.setMchid(MERCHANT_ID);
        orderCamera.setAppid(APP_ID);
        orderCamera.setProductQuantity(Long.valueOf(productQuantity));
        orderCamera.setOrderPrice(Long.valueOf(pay));
        return orderCameraService.insertOrderCamera(orderCamera, pay);
    }

    /**
     * 点券充值订单预创建微信
     */
    @Log(title = "点券充值订单预创建微信", businessType = BusinessType.OTHER)
    @GetMapping("/preStampsOrder")
    public Result getPreOrderId(@RequestParam Integer stamps) {

        OrderCamera orderCamera = new OrderCamera();
        orderCamera.setUserId(getUserId());
        orderCamera.setPhotoType(101);

        orderCamera.setProductDescription("系统点券充值");
        orderCamera.setCreateTime(DateUtils.getNowDate());
//        orderCamera.setUserId(1L);
        orderCamera.setMchid(MERCHANT_ID);
        orderCamera.setAppid(APP_ID);
        orderCamera.setProductQuantity(Long.valueOf(stamps));
        orderCamera.setOrderPrice(Long.valueOf(stamps));
        return orderCameraService.insertOrderCamera(orderCamera, stamps);
    }

    /**
     * 订单预创建     汇联
     */
    @Log(title = "订单创建", businessType = BusinessType.OTHER)
    @GetMapping("/create")
    public Result createOrderId(@RequestParam String deviceId,
                                @RequestParam String orderId,
                                @RequestParam Long totalAmount,
                                @RequestParam Integer photoType,
                                @RequestParam String body,
                                @RequestParam(required = false) Long productQuantity
    ) {
        if (deviceId == null) {
            return Result.fail(500, "设备id不能为空", String.valueOf(System.currentTimeMillis()));
        }
        DeviceCamera camera = deviceCameraService.selectDeviceCameraByDeviceId(deviceId);
        if (camera == null) {
            return Result.fail(500, "设备id不存在", String.valueOf(System.currentTimeMillis()));
        }
        if (camera.getDeviceStatus() == 0) {
            return Result.fail(500, "设备已停用", String.valueOf(System.currentTimeMillis()));
        }

        SysUser sysUser = sysUserService.selectUserById(camera.getUserId());
        if (sysUser == null) return Result.fail(500, "设备无负责人", String.valueOf(System.currentTimeMillis()));
        if (sysUser.getMerchantId() == null || sysUser.getMerchantId().equals(""))
            return Result.fail(500, "商家商户号未设置", String.valueOf(System.currentTimeMillis()));

        OrderCamera order = new OrderCamera();
        order.setDeviceName(camera.getDeviceName());
        order.setOrderId(orderId);
        order.setDeviceId(deviceId);
        order.setOrderPrice(totalAmount);
        order.setPhotoType(photoType);
        order.setProductDescription(body);
        order.setMchid(sysUser.getMerchantId());
        order.setUserId(camera.getUserId());
        order.setProductQuantity(productQuantity);
        return orderCameraService.createOrderHL(order);
    }

    //支付回调（微信）
    @PostMapping("/record")
    public ResponseEntity<?> record(HttpServletRequest request, HttpServletResponse response) throws GeneralSecurityException, ValidationException, ParseException, NotFoundException, IOException, HttpCodeException {

        String readData = HttpUtils.readData(request);
        NotificationRequest notificationRequest = new NotificationRequest.Builder()
                .withSerialNumber(request.getHeader(WechatPayHttpHeaders.WECHAT_PAY_SERIAL))
                .withNonce(request.getHeader(WechatPayHttpHeaders.WECHAT_PAY_NONCE))
                .withTimestamp(request.getHeader(WechatPayHttpHeaders.WECHAT_PAY_TIMESTAMP))
                .withSignature(request.getHeader(WechatPayHttpHeaders.WECHAT_PAY_SIGNATURE))
                .withBody(readData)
                .build();
        InputStream is = null;
        try {
            is = new ClassPathResource("/cert/apiclient_key.pem").getInputStream();
        } catch (IOException e) {
            e.printStackTrace();
        }
        PrivateKey merchantPrivateKey = PemUtil.loadPrivateKey(is);
        CertificatesManager certificatesManager = CertificatesManager.getInstance();
        certificatesManager.putMerchant(MERCHANT_ID, new WechatPay2Credentials(MERCHANT_ID, new PrivateKeySigner(MERCHANT_SERIAL_NUMBER, merchantPrivateKey)), API_V3_KEY.getBytes(StandardCharsets.UTF_8));
        Verifier verifier = certificatesManager.getVerifier(MERCHANT_ID);
        NotificationHandler handler = new NotificationHandler(verifier, API_V3_KEY.getBytes(StandardCharsets.UTF_8));
        // 验签和解析请求体
        Notification notification = handler.parse(notificationRequest);
        log.info("====================验签和解析请求体=================>>{}", notification.toString());
        AesUtil util = new AesUtil(API_V3_KEY.getBytes(StandardCharsets.UTF_8));
        Notification.Resource resource = notification.getResource();
        String ciphertext = resource.getCiphertext();
        String associatedData = resource.getAssociatedData();
        String nonce = resource.getNonce();
        String plainText = util.decryptToString(associatedData.getBytes(StandardCharsets.UTF_8), nonce.getBytes(StandardCharsets.UTF_8), ciphertext);
        log.info("===明文==={}", plainText);
        response.setStatus(200);
        JSONObject jsonObject = JSONObject.parseObject(plainText);
        String orderId = jsonObject.getString("out_trade_no");
        String transactionId = jsonObject.getString("transaction_id");
        Date payTime = jsonObject.getDate("success_time");
        String openId = jsonObject.getJSONObject("payer").getString("openid");

        OrderCamera orderCamera = orderCameraService.getById(orderId);

        orderCamera.setPayTime(payTime);
        orderCamera.setOpenid(openId);
        orderCamera.setTransactionId(transactionId);
        orderCamera.setOrderStatus(1L);
        orderCameraService.updateOrderCamera(orderCamera);

        if (orderCamera.getDeviceId() != null && !orderCamera.getDeviceId().equals(""))
            WebSocketServer.sendInfo("zfcg", orderCamera.getDeviceId()); // 主推支付成功

        if (orderCamera.getPhotoType() == 101)
            sysUserService.update().eq("user_id",orderCamera.getUserId()).setSql("stamps = stamps + " + orderCamera.getOrderPrice()).update();

        return ResponseEntity.ok().build();

    }


    @GetMapping("/payStatus")
    public Result PayStatus(@RequestParam String orderId) {
        OrderCamera order = orderCameraService.selectOrderCameraByOrderId(orderId);
        if (order == null) {
            return Result.fail(500, "订单号不存在", String.valueOf(System.currentTimeMillis()));
        }
        Long orderStatus = order.getOrderStatus();
        String message = orderStatus == 0 ? "订单未支付" : orderStatus == 1 ? "订单已支付" : orderStatus == 2 ? "订单已取消" : "订单异常" + orderStatus;
        return Result.ok(200, message, String.valueOf(System.currentTimeMillis()), orderStatus);
    }

    //订单创建（现金）
    @Log(title = "现金订单创建", businessType = BusinessType.OTHER)
    @GetMapping("/cash")
    public Result cash(@RequestParam String deviceId,
                       @RequestParam Integer pay,
                       @RequestParam String orderId,
                       @RequestParam Integer photoType) {
        if (deviceId == null) {
            return Result.fail(500, "设备id不能为空", String.valueOf(System.currentTimeMillis()));
        }

        DeviceCamera camera = deviceCameraService.selectDeviceCameraByDeviceId(deviceId);
        if (camera == null) {
            return Result.fail(500, "设备id不存在", String.valueOf(System.currentTimeMillis()));
        }
        if (camera.getDeviceStatus() == 0) {
            return Result.fail(500, "设备已停用", String.valueOf(System.currentTimeMillis()));
        }

        OrderCamera orderCamera = new OrderCamera();
        orderCamera.setOrderStatus(1L);
        orderCamera.setPayWay(3L);
        orderCamera.setOrderId(orderId);
        orderCamera.setOrderPrice(Long.valueOf(pay));
        orderCamera.setMoneyReceived(Long.valueOf(pay));
        orderCamera.setAccount(Long.valueOf(pay));
        orderCamera.setDeviceId(deviceId);

        orderCamera.setUserId(camera.getUserId());
        orderCamera.setDeviceName(camera.getDeviceName());
        orderCamera.setPhotoType(photoType);
        orderCamera.setCreateTime(DateUtils.getNowDate());
        orderCamera.setPayTime(DateUtils.getNowDate());

        //汇总(现金订单)

//        OrderCollect orderCollect = new OrderCollect();
//        orderCollect.setTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM, orderCamera.getCreateTime()));
//        orderCollect.setMerchantId(orderCamera.getMchid());
//        Long count = orderCollectService.query().eq("merchant_id", orderCollect.getMerchantId()).eq("time", orderCollect.getTime()).count();
//        if (count > 0) {
//            orderCollectService.update()
//                    .setSql("count = count + " + orderCamera.getMoneyReceived())
//                    .eq("merchant_id", orderCollect.getMerchantId())
//                    .eq("time", orderCollect.getTime()).update();
//        } else {
//            orderCollect.setCount(orderCamera.getMoneyReceived());
//            orderCollectService.save(orderCollect);
//        }

        //更新设备表中的设备总收入
        deviceCameraService.update().eq("device_id", orderCamera.getDeviceId()).setSql("count_price = count_price + " + orderCamera.getOrderPrice()).update();


        orderCamera.setProductDescription(photoType == 1 ? "大头贴" : photoType == 2 ? "写真照" : photoType == 3 ? "证件照" : photoType == 4 ? "手机上传" : photoType == 5 ? "漫画风" : "其他");
        return orderCameraService.createOrderCash(orderCamera);
    }


    /**
     * author 建宏
     *
     * @param startTime
     * @param endTime
     * @param deviceId
     * @return
     */
    @GetMapping("/CountPriceByDate")
    public Result CountPriceByDate(@RequestParam String startTime,
                                   @RequestParam String endTime,
                                   @RequestParam String deviceId) {
        if (getLoginUser().getUser() == null) return null;
        List<OrderCamera> list = orderCameraService.query()
                .between("create_time", startTime, endTime)
                .eq("mchid", getLoginUser().getUser().getMerchantId())
                .eq("device_id", deviceId)
                .and(qr -> {
                    qr.eq("order_status", 1).or().eq("order_status", 4);
                }).list();
        CountPriceDto countPriceDtos = new CountPriceDto();
        countPriceDtos.setTotalNum(0L);
        countPriceDtos.setProfit(0L);
        countPriceDtos.setTurnover(0);

        for (OrderCamera orderCamera : list) {
            countPriceDtos.setTurnover(countPriceDtos.getTurnover() + 1);
            countPriceDtos.setTotalNum(countPriceDtos.getTotalNum() + 1);
            countPriceDtos.setProfit(countPriceDtos.getProfit() + orderCamera.getOrderPrice());
        }
        countPriceDtos.setOrderCameraList(list);

        return Result.ok(200, "成功", "", countPriceDtos);
    }

    @GetMapping("/changeType")
    public AjaxResult changeType(@RequestParam String orderId, @RequestParam Integer type, @RequestParam(required = false) Integer modelId) {
        int camera = orderCameraService.updateOrderCameraTypeById(orderId, type, modelId);
        return AjaxResult.success(camera);
    }

    // 退款接口！！！
    @PostMapping("/orderRefund")
    public AjaxResult orderRefund(OrderCamera orderCamera) {
        try {
            return orderCameraService.refundsHL(orderCamera);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error(e.getMessage());
        }
    }

    // 退款查询接口！！！
    @PostMapping("/refundQuery")
    public Result refundQuery(OrderCamera orderCamera) {
        List<OrderCamera> list = orderCameraService.query().eq("order_status", 5).list();
        for (OrderCamera camera : list) {
            if (camera.getOrderId().contains("_") || camera.getOrderId().equals(orderCamera.getOrderId())) continue;
            try {
                orderCameraService.refundsQuery(camera);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        try {
            return orderCameraService.refundsQuery(orderCamera);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.fail(400, e.getMessage(), "");
        }

    }

}
