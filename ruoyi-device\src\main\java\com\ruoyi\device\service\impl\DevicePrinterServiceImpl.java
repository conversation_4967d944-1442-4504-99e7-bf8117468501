package com.ruoyi.device.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.device.domain.DevicePrinter;
import com.ruoyi.device.mapper.DevicePrinterMapper;
import com.ruoyi.device.service.IDevicePrinterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 打印机设备Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-24
 */
@Service
public class DevicePrinterServiceImpl extends ServiceImpl<DevicePrinterMapper, DevicePrinter> implements IDevicePrinterService
{
    @Autowired
    private DevicePrinterMapper devicePrinterMapper;

    /**
     * 查询打印机设备
     * 
     * @param deviceId 打印机设备ID
     * @return 打印机设备
     */
    @Override
    public DevicePrinter selectDevicePrinterByDeviceId(String deviceId)
    {
        return devicePrinterMapper.selectDevicePrinterByDeviceId(deviceId);
    }

    /**
     * 查询打印机设备列表
     * 
     * @param devicePrinter 打印机设备
     * @return 打印机设备
     */
    @Override
    public List<DevicePrinter> selectDevicePrinterList(DevicePrinter devicePrinter)
    {
        return devicePrinterMapper.selectDevicePrinterList(devicePrinter);
    }

    /**
     * 新增打印机设备
     * 
     * @param devicePrinter 打印机设备
     * @return 结果
     */
    @Override
    public int insertDevicePrinter(DevicePrinter devicePrinter)
    {
        devicePrinter.setCreateTime(DateUtils.getNowDate());
        return devicePrinterMapper.insertDevicePrinter(devicePrinter);
    }

    /**
     * 修改打印机设备
     * 
     * @param devicePrinter 打印机设备
     * @return 结果
     */
    @Override
    public int updateDevicePrinter(DevicePrinter devicePrinter)
    {
        devicePrinter.setUpdateTime(DateUtils.getNowDate());
        return devicePrinterMapper.updateDevicePrinter(devicePrinter);
    }

    /**
     * 批量删除打印机设备
     * 
     * @param deviceIds 需要删除的打印机设备ID
     * @return 结果
     */
    @Override
    public int deleteDevicePrinterByDeviceIds(String[] deviceIds)
    {
        return devicePrinterMapper.deleteDevicePrinterByDeviceIds(deviceIds);
    }

    /**
     * 删除打印机设备信息
     * 
     * @param deviceId 打印机设备ID
     * @return 结果
     */
    @Override
    public int deleteDevicePrinterByDeviceId(String deviceId)
    {
        return devicePrinterMapper.deleteDevicePrinterByDeviceId(deviceId);
    }
    
    /**
     * 更新设备在线状态
     * 
     * @param deviceId 设备ID
     * @param onlineStatus 在线状态
     * @return 结果
     */
    @Override
    public int updateDeviceOnlineStatus(String deviceId, Integer onlineStatus)
    {
        return devicePrinterMapper.updateDeviceOnlineStatus(deviceId, onlineStatus);
    }
    
    /**
     * 更新设备耗材信息
     * 
     * @param devicePrinter 打印机设备
     * @return 结果
     */
    @Override
    public int updateDeviceConsumables(DevicePrinter devicePrinter)
    {
        return devicePrinterMapper.updateDeviceConsumables(devicePrinter);
    }
} 