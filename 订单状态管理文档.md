# 打印机订单状态管理文档

## 概述

本文档详细描述打印机订单系统中的各种状态定义、流转规则和管理机制。

## 订单状态定义

### **主要状态码**

| 状态码 | 状态名称 | 英文名称 | 说明 | 可操作性 |
|--------|----------|----------|------|----------|
| **0** | 未支付 | UNPAID | 订单创建后的初始状态 | 可支付、可取消、可修改 |
| **1** | 已支付 | PAID | 支付成功，等待打印 | 可打印、可退款 |
| **2** | 已取消 | CANCELLED | 订单被取消 | 终态，不可操作 |
| **3** | 已退款 | REFUNDED | 订单已退款 | 终态，不可操作 |
| **4** | 已完成 | COMPLETED | 打印完成 | 终态，可查看 |

### **扩展状态（可选）**

| 状态码 | 状态名称 | 说明 | 使用场景 |
|--------|----------|------|----------|
| **5** | 打印中 | 设备正在执行打印任务 | 实时状态跟踪 |
| **6** | 打印失败 | 打印过程中出现错误 | 异常处理 |
| **7** | 部分完成 | 多任务订单部分完成 | 复杂订单管理 |

## 状态流转图

```mermaid
stateDiagram-v2
    [*] --> 未支付(0) : 创建订单
    
    未支付(0) --> 已支付(1) : 支付成功
    未支付(0) --> 已取消(2) : 用户取消/超时
    
    已支付(1) --> 打印中(5) : 开始打印
    已支付(1) --> 已退款(3) : 申请退款
    
    打印中(5) --> 已完成(4) : 打印成功
    打印中(5) --> 打印失败(6) : 打印异常
    
    打印失败(6) --> 打印中(5) : 重新打印
    打印失败(6) --> 已退款(3) : 退款处理
    
    已完成(4) --> [*] : 订单结束
    已取消(2) --> [*] : 订单结束
    已退款(3) --> [*] : 订单结束
```

## 状态详细说明

### **1. 未支付 (0)**

**特征**：
- 订单刚创建，用户尚未支付
- 可以修改订单内容（打印参数、文件等）
- 有超时机制，超时后自动取消

**允许操作**：
- ✅ 发起支付
- ✅ 修改订单信息
- ✅ 取消订单
- ✅ 查看订单详情

**业务规则**：
```java
// 超时自动取消（通常30分钟）
if (System.currentTimeMillis() - order.getCreateTime() > 30 * 60 * 1000) {
    order.setOrderStatus(2); // 自动取消
}
```

### **2. 已支付 (1)**

**特征**：
- 用户已完成支付，资金已到账
- 等待设备开始打印
- 可以申请退款（有时间限制）

**允许操作**：
- ✅ 开始打印
- ✅ 申请退款
- ✅ 查看订单详情
- ❌ 修改订单内容

**业务规则**：
```java
// 支付成功后通知设备
WebSocketServer.sendInfo("zfcg", order.getDeviceId());

// 退款时间限制（如24小时内）
if (System.currentTimeMillis() - order.getPayTime() < 24 * 60 * 60 * 1000) {
    // 允许退款
}
```

### **3. 已取消 (2)**

**特征**：
- 订单被取消，无法恢复
- 如果已支付，需要退款
- 终态，不可再操作

**触发条件**：
- 用户主动取消
- 超时自动取消
- 系统异常取消

**允许操作**：
- ✅ 查看订单详情
- ❌ 其他所有操作

### **4. 已退款 (3)**

**特征**：
- 资金已退回用户账户
- 订单流程结束
- 终态，不可再操作

**退款场景**：
- 用户主动申请退款
- 打印失败后退款
- 设备故障退款
- 客服处理退款

**允许操作**：
- ✅ 查看订单详情
- ✅ 查看退款记录
- ❌ 其他所有操作

### **5. 已完成 (4)**

**特征**：
- 打印任务全部完成
- 用户已取得打印成果
- 订单流程正常结束

**完成条件**：
- 所有打印任务状态为"已完成"
- 设备确认打印成功
- 用户确认收到打印品（可选）

**允许操作**：
- ✅ 查看订单详情
- ✅ 下载打印文件
- ✅ 评价服务（可选）
- ❌ 修改或退款

## 打印任务状态

### **任务状态定义**

| 状态码 | 状态名称 | 说明 |
|--------|----------|------|
| **0** | 待打印 | 等待设备处理 |
| **1** | 打印中 | 设备正在打印 |
| **2** | 已完成 | 打印成功 |
| **3** | 打印失败 | 打印出错 |

### **订单与任务状态关系**

```java
// 订单状态计算逻辑
public int calculateOrderStatus(List<OrderPrinterTask> tasks) {
    if (tasks.isEmpty()) return 0; // 未支付
    
    boolean allCompleted = tasks.stream().allMatch(t -> t.getPrintStatus() == 2);
    boolean anyFailed = tasks.stream().anyMatch(t -> t.getPrintStatus() == 3);
    boolean anyPrinting = tasks.stream().anyMatch(t -> t.getPrintStatus() == 1);
    
    if (allCompleted) return 4; // 已完成
    if (anyPrinting) return 5;  // 打印中
    if (anyFailed) return 6;    // 打印失败
    
    return 1; // 已支付
}
```

## 状态变更规则

### **允许的状态转换**

| 当前状态 | 可转换状态 | 触发条件 | 操作者 |
|----------|------------|----------|--------|
| 0 → 1 | 未支付 → 已支付 | 支付成功 | 支付系统 |
| 0 → 2 | 未支付 → 已取消 | 用户取消/超时 | 用户/系统 |
| 1 → 3 | 已支付 → 已退款 | 退款申请 | 用户/客服 |
| 1 → 5 | 已支付 → 打印中 | 开始打印 | 设备 |
| 5 → 4 | 打印中 → 已完成 | 打印成功 | 设备 |
| 5 → 6 | 打印中 → 打印失败 | 打印异常 | 设备 |
| 6 → 5 | 打印失败 → 打印中 | 重新打印 | 设备/客服 |
| 6 → 3 | 打印失败 → 已退款 | 退款处理 | 客服 |

### **禁止的状态转换**

❌ **不允许的转换**：
- 已完成 → 其他状态
- 已取消 → 其他状态  
- 已退款 → 其他状态
- 已支付 → 未支付

## 状态管理API

### **查询订单状态**

```java
@GetMapping("/order/status/{orderId}")
public AjaxResult getOrderStatus(@PathVariable String orderId) {
    OrderPrinter order = orderPrinterService.selectOrderPrinterByOrderId(orderId);
    return AjaxResult.success(order.getOrderStatus());
}
```

### **更新订单状态**

```java
@PostMapping("/order/updateStatus")
public AjaxResult updateOrderStatus(@RequestBody OrderStatusUpdateRequest request) {
    // 验证状态转换是否合法
    if (!isValidStatusTransition(request.getCurrentStatus(), request.getNewStatus())) {
        return AjaxResult.error("不允许的状态转换");
    }
    
    // 更新状态
    orderPrinterService.updateOrderStatus(request.getOrderId(), request.getNewStatus());
    return AjaxResult.success();
}
```

### **批量状态查询**

```java
@PostMapping("/order/batchStatus")
public AjaxResult getBatchOrderStatus(@RequestBody List<String> orderIds) {
    Map<String, Integer> statusMap = orderPrinterService.getBatchOrderStatus(orderIds);
    return AjaxResult.success(statusMap);
}
```

## 状态监控与告警

### **状态统计**

```sql
-- 各状态订单数量统计
SELECT 
    order_status,
    COUNT(*) as count,
    COUNT(*) * 100.0 / (SELECT COUNT(*) FROM order_printer) as percentage
FROM order_printer 
WHERE DATE(create_time) = CURDATE()
GROUP BY order_status;
```

### **异常状态监控**

| 监控指标 | 阈值 | 告警条件 |
|----------|------|----------|
| 未支付订单超时率 | > 20% | 30分钟内未支付 |
| 支付成功率 | < 95% | 支付失败过多 |
| 打印成功率 | < 90% | 打印失败过多 |
| 退款率 | > 5% | 退款订单过多 |

### **状态异常处理**

```java
// 定时任务：处理异常状态
@Scheduled(fixedRate = 300000) // 5分钟执行一次
public void handleAbnormalStatus() {
    // 1. 处理超时未支付订单
    List<OrderPrinter> timeoutOrders = orderPrinterService.getTimeoutUnpaidOrders();
    timeoutOrders.forEach(order -> {
        order.setOrderStatus(2); // 设为已取消
        orderPrinterService.updateOrderPrinter(order);
    });
    
    // 2. 处理长时间打印中的订单
    List<OrderPrinter> longPrintingOrders = orderPrinterService.getLongPrintingOrders();
    longPrintingOrders.forEach(order -> {
        // 检查设备状态，决定是否标记为失败
    });
}
```

## 最佳实践

### **1. 状态设计原则**
- **单一职责**：每个状态有明确的业务含义
- **状态完整**：覆盖所有可能的业务场景
- **转换清晰**：状态转换规则明确且合理
- **终态明确**：明确哪些是终态，避免无限循环

### **2. 状态管理建议**
- **原子操作**：状态变更使用数据库事务
- **日志记录**：记录所有状态变更的详细日志
- **权限控制**：不同角色有不同的状态操作权限
- **异常处理**：对异常状态有完善的处理机制

### **3. 性能优化**
- **索引优化**：在 order_status 字段上建立索引
- **缓存策略**：热点订单状态可以缓存
- **批量操作**：支持批量状态查询和更新
- **异步处理**：状态变更通知可以异步处理

## 总结

订单状态管理是打印机订单系统的核心组件，通过合理的状态设计和严格的转换规则，确保订单流程的完整性和一致性。本文档提供了完整的状态定义、流转规则和管理机制，为系统开发和维护提供了重要参考。
