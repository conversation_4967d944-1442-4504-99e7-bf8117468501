package com.ruoyi.device.handler;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.device.domain.WarningRecipients;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

@Slf4j
@MappedJdbcTypes(JdbcType.VARCHAR) // 数据库中该字段存储的类型
@MappedTypes(WarningRecipients.class) // 需要转换的对象
public class WarningRecipientsTypeHandler extends BaseTypeHandler<WarningRecipients> {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, WarningRecipients parameter, JdbcType jdbcType) throws SQLException {
        try {
            String json = objectMapper.writeValueAsString(parameter);
            ps.setString(i, json);
        } catch (JsonProcessingException e) {
            log.error("Error serializing WarningRecipients to JSON", e);
            throw new SQLException("Error serializing WarningRecipients to JSON", e);
        }
    }

    @Override
    public WarningRecipients getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String json = rs.getString(columnName);
        if (json != null && !json.trim().isEmpty()) {
            try {
                return objectMapper.readValue(json, WarningRecipients.class);
            } catch (JsonProcessingException e) {
                log.error("Error deserializing JSON from column '{}'", columnName, e);
                throw new SQLException("Error deserializing JSON from column '" + columnName + "'", e);
            }
        }
        return null;
    }

    @Override
    public WarningRecipients getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String json = rs.getString(columnIndex);
        if (json != null && !json.trim().isEmpty()) {
            try {
                return objectMapper.readValue(json, WarningRecipients.class);
            } catch (JsonProcessingException e) {
                log.error("Error deserializing JSON from columnIndex '{}'", columnIndex, e);
                throw new SQLException("Error deserializing JSON from columnIndex '" + columnIndex + "'", e);
            }
        }
        return null;
    }

    @Override
    public WarningRecipients getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String json = cs.getString(columnIndex);
        if (json != null && !json.trim().isEmpty()) {
            try {
                return objectMapper.readValue(json, WarningRecipients.class);
            } catch (JsonProcessingException e) {
                log.error("Error deserializing JSON from CallableStatement columnIndex '{}'", columnIndex, e);
                throw new SQLException("Error deserializing JSON from CallableStatement columnIndex '" + columnIndex + "'", e);
            }
        }
        return null;
    }
}