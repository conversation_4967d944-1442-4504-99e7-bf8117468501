package com.yunchuang.wxapp.service.client.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.device.domain.CameraSysImage;
import com.ruoyi.device.mapper.CameraSysImageMapper;
import com.yunchuang.wxapp.exception.WxappBusinessException;
import com.yunchuang.wxapp.mapper.WishListMapper;
import com.yunchuang.wxapp.model.domain.WishList;
import com.yunchuang.wxapp.model.enums.exception.WxappBusinessExceptionCode;
import com.yunchuang.wxapp.model.req.CWishListAddReq;
import com.yunchuang.wxapp.model.req.CWishListDeleteReq;
import com.yunchuang.wxapp.model.resp.CWishListDetailListResp;
import com.yunchuang.wxapp.service.client.ICWishListService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 心愿单 Service 实现类
 */
@Service
@Transactional
public class CWishListServiceImpl extends ServiceImpl<WishListMapper, WishList> implements ICWishListService {

    @Resource
    private WishListMapper wishListMapper;

    @Resource
    private CameraSysImageMapper cameraSysImageMapper;

    /**
     * 查询心愿单列表
     *
     * @param userId 用户ID
     * @return 心愿单列表
     */
    @Override
    public List<WishList> getWishListList(Long userId) {
        // 查询心愿单列表
        LambdaQueryWrapper<WishList> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WishList::getUserId, userId);
        return wishListMapper.selectList(queryWrapper);
    }

    /**
     * 新增心愿单
     *
     * @param userId 用户ID
     * @param addReq 新增心愿单请求
     * @return 是否成功
     */
    @Override
    public boolean addWishList(Long userId, CWishListAddReq addReq) {
        // 查询是否已经存在
        LambdaQueryWrapper<WishList> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WishList::getUserId, userId);
        queryWrapper.eq(WishList::getPhotoFrameId, addReq.getPhotoFrameId());
        WishList existWishList = wishListMapper.selectOne(queryWrapper);
        if (existWishList != null) {
            throw new WxappBusinessException(WxappBusinessExceptionCode.EC_60305);
        }

        // 新增心愿单
        existWishList = new WishList();
        existWishList.setUserId(userId);
        existWishList.setPhotoFrameId(addReq.getPhotoFrameId());
        // TODO: 查询之前是否已经用过该相框 如果有则设置打卡时间
        return wishListMapper.insert(existWishList) > 0;
    }

    /**
     * 删除心愿单
     *
     * @param userId    用户ID
     * @param deleteReq 删除心愿单请求
     * @return 是否成功
     */
    @Override
    public boolean deleteWishList(Long userId, CWishListDeleteReq deleteReq) {
        // 删除心愿单
        LambdaQueryWrapper<WishList> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WishList::getUserId, userId);
        queryWrapper.eq(WishList::getPhotoFrameId, deleteReq.getPhotoFrameId());
        return wishListMapper.delete(queryWrapper) > 0;
    }

    /**
     * 查询心愿单详情列表
     *
     * @param userId 用户ID
     * @return 心愿单详情列表
     */
    @Override
    public List<CWishListDetailListResp> getWishListDetailList(Long userId) {
        // 查询心愿单详情列表
        List<WishList> wishListList = getWishListList(userId);

        // 查询相框列表
        List<Long> photoFrameIdList = wishListList.stream().map(WishList::getPhotoFrameId).collect(Collectors.toList());
        List<CameraSysImage> cameraSysImageList = cameraSysImageMapper.selectBatchIds(photoFrameIdList);

        // 组装心愿单详情列表
        List<CWishListDetailListResp> wishListDetailList = new ArrayList<>();
        wishListList.forEach(wishList -> {
            CWishListDetailListResp wishListDetail = new CWishListDetailListResp();
            BeanUtils.copyProperties(wishList, wishListDetail);
            CameraSysImage cameraSysImage = cameraSysImageList.stream()
                    .filter(item -> item.getId().equals(wishList.getPhotoFrameId()))
                    .findFirst().orElse(null);
            wishListDetail.setPhotoFrame(cameraSysImage);
            wishListDetailList.add(wishListDetail);
        });

        return wishListDetailList;
    }
}
