package com.ruoyi.photo.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.photo.domain.UserPhoto;
import com.ruoyi.photo.domain.XbResource;
import org.springframework.web.multipart.MultipartFile;

/**
 * 素材管理Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-29
 */
public interface IXbResourceService extends IService<XbResource>
{
    /**
     * 查询素材管理
     * 
     * @param id 素材管理主键
     * @return 素材管理
     */
    public XbResource selectXbResourceById(Long id);

    /**
     * 查询素材管理列表
     * 
     * @param xbResource 素材管理
     * @return 素材管理集合
     */
    public TableDataInfo selectXbResourceList(XbResource xbResource, int pageNum, int pageSize,String deviceId);

    /**
     * 新增素材管理
     * 
     * @param xbResource 素材管理
     * @return 结果
     */
    public int insertXbResource(MultipartFile image, XbResource xbResource);

    /**
     * 修改素材管理
     * 
     * @param xbResource 素材管理
     * @return 结果
     */
    public AjaxResult updateXbResource(XbResource xbResource, MultipartFile image);

    /**
     * 批量删除素材管理
     * 
     * @param ids 需要删除的素材管理主键集合
     * @return 结果
     */
    public int deleteXbResourceByIds(Long[] ids);

    /**
     * 删除素材管理信息
     * 
     * @param id 素材管理主键
     * @return 结果
     */
    public int deleteXbResourceById(Long id);
}
