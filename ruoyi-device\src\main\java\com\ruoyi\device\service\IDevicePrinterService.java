package com.ruoyi.device.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.device.domain.DevicePrinter;

import java.util.List;

/**
 * 打印机设备Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-24
 */
public interface IDevicePrinterService extends IService<DevicePrinter>
{
    /**
     * 查询打印机设备
     * 
     * @param deviceId 打印机设备ID
     * @return 打印机设备
     */
    public DevicePrinter selectDevicePrinterByDeviceId(String deviceId);

    /**
     * 查询打印机设备列表
     * 
     * @param devicePrinter 打印机设备
     * @return 打印机设备集合
     */
    public List<DevicePrinter> selectDevicePrinterList(DevicePrinter devicePrinter);

    /**
     * 新增打印机设备
     * 
     * @param devicePrinter 打印机设备
     * @return 结果
     */
    public int insertDevicePrinter(DevicePrinter devicePrinter);

    /**
     * 修改打印机设备
     * 
     * @param devicePrinter 打印机设备
     * @return 结果
     */
    public int updateDevicePrinter(DevicePrinter devicePrinter);

    /**
     * 批量删除打印机设备
     * 
     * @param deviceIds 需要删除的打印机设备ID
     * @return 结果
     */
    public int deleteDevicePrinterByDeviceIds(String[] deviceIds);

    /**
     * 删除打印机设备信息
     * 
     * @param deviceId 打印机设备ID
     * @return 结果
     */
    public int deleteDevicePrinterByDeviceId(String deviceId);
    
    /**
     * 更新设备在线状态
     * 
     * @param deviceId 设备ID
     * @param onlineStatus 在线状态
     * @return 结果
     */
    public int updateDeviceOnlineStatus(String deviceId, Integer onlineStatus);
    
    /**
     * 更新设备耗材信息
     * 
     * @param devicePrinter 打印机设备
     * @return 结果
     */
    public int updateDeviceConsumables(DevicePrinter devicePrinter);
} 