package com.ruoyi.po;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/7/27 10:55
 */
@Data
public class CloudWithdrawRecord {
    private Integer id;

    private Integer mchId;
    /**
     * 外部交易流水号
     */
    private String outTradeNo;
    /**
     * 提现总金额
     */
    private String totalAmount;
    /**
     * 平台设置提现手续费用
     */
    private BigDecimal platformFee;
    /**
     * 实际到账金额
     */
    private  BigDecimal realAmount;
    /**
     * 提现状态  0待确认(申请提现后)  1成功   2失败   3待处理（确认提现后） 10待提现（分账统计出来，未调提现申请前）
     */
    private  Integer status;
    /**
     * 网商提现订单号
     */
    private  String orderNo;
    /**
     * 绑定银行卡号
     */
    private  String bankCardNo;
    /**
     * 绑定卡号户名
     */
    private  String bankCertName;
    /**
     * 提现确认时间
     */
    private Date withdrawApplyDate;
    /**
     * 提现完成时间
     */
    private  Date withdrawFinishDate;
    /**
     * 失败原因
     */
    private  String failReason;
    /**
     * 备注
     */
    private  String memo;
    private  Date createTime;
    private  Date updateTim;
    /**
     * 汇联商户号
     */
    private String hlMerchantId;
    /**
     *  动态验证码
     */
    private String smsCode;
    private String merchantid;
    private String isvOrgId;

    public String relateOrderNo;

    /**
     * 退回原因
     * */
    public String refundReason;

    /**
     * 回调地址
     */
    private String  notifyUrl;
}
