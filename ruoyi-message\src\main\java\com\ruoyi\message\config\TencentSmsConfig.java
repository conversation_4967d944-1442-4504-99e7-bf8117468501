package com.ruoyi.message.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 短信配置类 - 腾讯云
 * <p>
 * 该类用于读取腾讯云短信相关的配置属性
 * </p>
 * <p>
 * 注意：请确保在 application.yml 或 application.properties 中配置了以下属性：
 * <ul>
 *     <li>tencent.sms.app-id</li>
 *     <li>tencent.sms.secret-id</li>
 *     <li>tencent.sms.secret-key</li>
 * </ul>
 * </p>
 */
@Data
@Component
@ConfigurationProperties(prefix = "tencent.sms")
public class TencentSmsConfig {

    /**
     * 短信应用ID
     */
    private String appId;

    /**
     * 短信应用密钥
     */
    private String secretId;

    /**
     * 短信应用密钥
     */
    private String secretKey;
}
