package com.ruoyi.wxservice.model.enums.exception;

import lombok.Getter;

/**
 * 消息模板id枚举
 */
@Getter
public enum MessageTemplateIdEnum {

    CONSUMABLES_QUANTITY_WARNING("e8NJ3fJwGn9F9900ZEbUy_TTKGKIN5kSBpkH4pIlIws", "耗材余量预警消息模板");

    private final String id;
    private final String name;

    MessageTemplateIdEnum(String id, String name) {
        this.id = id;
        this.name = name;
    }

    public static MessageTemplateIdEnum getById(String id) {
        for (MessageTemplateIdEnum value : values()) {
            if (value.id.equals(id)) {
                return value;
            }
        }
        return null;
    }
}
