package com.ruoyi.web.controller.resource;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.oss.ALY_OSS;
import com.ruoyi.device.domain.StickerMessage;
import com.ruoyi.dto.Result;
import com.ruoyi.order.service.IOrderCameraService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.device.domain.CameraSysImage;
import com.ruoyi.device.service.ICameraSysImageService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * camera_sys_imageController
 *
 * <AUTHOR>
 * @date 2023-07-19
 */
@RestController
@RequestMapping("/device/image")
public class CameraSysImageController extends BaseController {
    @Autowired
    private ICameraSysImageService cameraSysImageService;
    @Autowired
    private IOrderCameraService orderCameraService;

    /**
     * 查询camera_sys_image列表
     */
    @PreAuthorize("@ss.hasPermi('device:image:list')")
    @GetMapping("/list")
    public TableDataInfo list(CameraSysImage cameraSysImage, int pageNum, int pageSize, @RequestParam(required = false) Boolean isOwn) {
        return cameraSysImageService.selectCameraSysImageList(cameraSysImage, pageNum, pageSize, isOwn);
    }

    /**
     * 查询私人camera_sys_image列表   ！！！！！！！！！！！！！！！！！！！！！  旧AI设备
     */
    @GetMapping("/myList")
    public TableDataInfo myList(CameraSysImage cameraSysImage) {
        if (cameraSysImage.getCreateBy() == null || cameraSysImage.getCreateBy().equals("")) {
            if (getLoginUser() != null) {
                cameraSysImage.setCreateBy(getLoginUser().getUsername());
            }
        }
        if (cameraSysImage.getCreateBy() == null) return null;
        startPage();
        List<CameraSysImage> list = cameraSysImageService.selectMyCameraSysImageList(cameraSysImage);
        return getDataTable(list);
    }

    /**
     * 导出camera_sys_image列表
     */
    @PreAuthorize("@ss.hasPermi('device:image:export')")
    @Log(title = "camera_sys_image", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CameraSysImage cameraSysImage, int pageNum, int pageSize, @RequestParam(required = false) Boolean isOwn) {
        List<CameraSysImage> list = (List<CameraSysImage>) cameraSysImageService.selectCameraSysImageList(cameraSysImage, pageNum, pageSize, isOwn).getRows();
        ExcelUtil<CameraSysImage> util = new ExcelUtil<CameraSysImage>(CameraSysImage.class);
        util.exportExcel(response, list, "camera_sys_image数据");
    }

    /**
     * 获取camera_sys_image详细信息
     */
    @PreAuthorize("@ss.hasPermi('device:image:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(cameraSysImageService.selectCameraSysImageById(id));
    }

    /**
     * 新增camera_sys_image
     */
    @PreAuthorize("@ss.hasPermi('device:image:add')")
    @Log(title = "camera_sys_image", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(CameraSysImage cameraSysImage, @RequestParam(value = "image", required = false) MultipartFile image, @RequestParam(value = "iconImage", required = false) MultipartFile iconImage) {
        if (image != null) {
            String originalFilename = image.getOriginalFilename();
            String imageName = "mlf/" + System.currentTimeMillis();
            if (originalFilename.contains(".png")) {
                imageName += ".png";
            } else {
                imageName += ".jpg";
            }
            String url = ALY_OSS.uploadImage(image, imageName);
            url = url.substring(0, url.indexOf("?"));
            cameraSysImage.setUrl(url);
            cameraSysImage.setObjectName(imageName);
        } else {
            return AjaxResult.error("请添加照片");
        }
        if (iconImage != null){
            String originalFilename = iconImage.getOriginalFilename();
            String imageName = "model_icon/" + System.currentTimeMillis();
            if (originalFilename.contains(".png")) {
                imageName += ".png";
            } else {
                imageName += ".jpg";
            }
            String url = ALY_OSS.uploadImage(iconImage, imageName);
            url = url.substring(0, url.indexOf("?"));
            cameraSysImage.setIcon(url);
            cameraSysImage.setIconObjectName(imageName);
        }
        return toAjax(cameraSysImageService.insertCameraSysImage(cameraSysImage));
    }

    /**
     * 修改camera_sys_image
     */
    @PreAuthorize("@ss.hasPermi('device:image:edit')")
    @Log(title = "camera_sys_image", businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    public AjaxResult edit(CameraSysImage cameraSysImage, @RequestParam(value = "image", required = false) MultipartFile image, @RequestParam(value = "iconImage", required = false) MultipartFile iconImage) {
        System.out.println(cameraSysImage);
        if (getLoginUser() == null) {
            return AjaxResult.error("用户未登录");
        } else {
            if (!SecurityUtils.isAdmin(getUserId()) &&
                    !getLoginUser().getUsername().equals(cameraSysImage.getCreateBy()))
                return AjaxResult.error("无权修改该资源");

            if (image != null) {
                String objectName = cameraSysImage.getObjectName();
                if (objectName == null || objectName.equals("")) {
                    String originalFilename = image.getOriginalFilename();
                    objectName = "mlf/" + System.currentTimeMillis();
                    if (originalFilename.contains(".png")) {
                        objectName += ".png";
                    } else {
                        objectName += ".jpg";
                    }
                    cameraSysImage.setObjectName(objectName);
                }

                String url = ALY_OSS.uploadImage(image, objectName);
                url = url.substring(0, url.indexOf("?"));
                cameraSysImage.setUrl(url);
            }
            if (iconImage != null) {
                String iconObjectName = cameraSysImage.getIconObjectName();
                if (iconObjectName == null || iconObjectName.equals("")) {
                    String originalFilename = iconImage.getOriginalFilename();
                    iconObjectName = "model_icon/" + System.currentTimeMillis();
                    if (originalFilename.contains(".png")) {
                        iconObjectName += ".png";
                    } else {
                        iconObjectName += ".jpg";
                    }
                    cameraSysImage.setIconObjectName(iconObjectName);
                }

                String url = ALY_OSS.uploadImage(iconImage, iconObjectName);
                url = url.substring(0, url.indexOf("?"));
                cameraSysImage.setIcon(url);
            }

            return toAjax(cameraSysImageService.updateCameraSysImage(cameraSysImage));
        }
    }
        /*if (image != null){
            if (cameraSysImage.getObjectName()!=null){
                cameraSysImageService.deleteImage(cameraSysImage.getObjectName());
            }
            cameraSysImage = cameraSysImageService.uploadImage(image,cameraSysImage);
        }
        return toAjax(cameraSysImageService.updateCameraSysImage(cameraSysImage));*/

    /**
     * 删除camera_sys_image
     */
    @PreAuthorize("@ss.hasPermi('device:image:remove')")
    @Log(title = "camera_sys_image", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(cameraSysImageService.deleteCameraSysImageByIds(ids));
    }

    @GetMapping("/background")
    public Result background() {
        List<CameraSysImage> list = cameraSysImageService.selectCameraSysBackgroundList();
        return Result.ok(200, "查询成功", String.valueOf(System.currentTimeMillis()), list);
    }


    @GetMapping("/userModelType")
    public TableDataInfo userModelType(String deviceId) {
        startPage();
        List<CameraSysImage> list = cameraSysImageService.userModelType(deviceId);
        return getDataTable(list);
    }

    @GetMapping("/sticker")
    public List<StickerMessage> getSticker(@RequestParam(required = false) String deviceId, @RequestParam(required = false) Integer who) {

        return cameraSysImageService.getSticker(deviceId,who);
    }

    @GetMapping("/model")
    public List<CameraSysImage> getModel(@RequestParam(required = false) String deviceId, @RequestParam String type, @RequestParam int who) {
        if (deviceId == null || deviceId.equals("")) deviceId = "test01";
        return cameraSysImageService.getModel(deviceId, type, who);
    }


    /**
     * get 相框 by ids
     */
    @GetMapping("/byIds/{ids}")
    public AjaxResult getByIds(@PathVariable Long[] ids) {
        List<CameraSysImage> models = cameraSysImageService.listByIds(Arrays.asList(ids));
        return new AjaxResult(200,"查询成功",models);
    }
}
