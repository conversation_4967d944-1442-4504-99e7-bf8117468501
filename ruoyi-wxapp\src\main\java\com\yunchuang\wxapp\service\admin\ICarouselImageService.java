package com.yunchuang.wxapp.service.admin;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yunchuang.wxapp.model.domain.CarouselImage;

import java.util.List;

/**
 * 轮播图Service接口
 *
 * <AUTHOR>
 * @date 2024-12-21
 */
public interface ICarouselImageService extends IService<CarouselImage> {
    /**
     * 查询轮播图
     *
     * @param id 轮播图主键
     * @return 轮播图
     */
    public CarouselImage selectCarouselImageById(Long id);

    /**
     * 查询轮播图列表
     *
     * @param carouselImage 轮播图
     * @return 轮播图集合
     */
    public List<CarouselImage> selectCarouselImageList(CarouselImage carouselImage);

    /**
     * 新增轮播图
     *
     * @param carouselImage 轮播图
     * @return 结果
     */
    public int insertCarouselImage(CarouselImage carouselImage);

    /**
     * 修改轮播图
     *
     * @param carouselImage 轮播图
     * @return 结果
     */
    public int updateCarouselImage(CarouselImage carouselImage);

    /**
     * 批量删除轮播图
     *
     * @param ids 需要删除的轮播图主键集合
     * @return 结果
     */
    public int deleteCarouselImageByIds(Long[] ids);

    /**
     * 删除轮播图信息
     *
     * @param id 轮播图主键
     * @return 结果
     */
    public int deleteCarouselImageById(Long id);
}
