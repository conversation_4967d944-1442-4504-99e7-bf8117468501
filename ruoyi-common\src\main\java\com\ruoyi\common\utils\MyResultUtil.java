package com.ruoyi.common.utils;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.HashMap;
import java.util.Map;

/**
 * 返回结果工具类 (只负责构建响应体)
 */
public class MyResultUtil {

    /**
     * 自定义 全参数
     *
     * @param success   是否成功
     * @param code      状态码
     * @param message   消息
     * @param result    数据
     * @param timestamp 时间戳
     * @return Map<String, Object> 响应体
     */
    public static Map<String, Object> customResult(Boolean success, Integer code, String message, Object result, String timestamp) {
        Map<String, Object> map = new HashMap<>();
        map.put("success", success);
        map.put("message", message);
        map.put("code", code);
        map.put("result", result);
        map.put("timestamp", timestamp);
        return map;
    }

    /**
     * 操作成功
     *
     * @return Map<String, Object> 响应体
     */
    public static Map<String, Object> success() {
        return customResult(true, 200, "操作成功", null, java.lang.String.valueOf(System.currentTimeMillis()));
    }

    /**
     * 操作成功
     *
     * @param result 数据
     * @return Map<String, Object> 响应体
     */
    public static Map<String, Object> success(Object result) {
        return customResult(true, 200, "操作成功", result, java.lang.String.valueOf(System.currentTimeMillis()));
    }

    /**
     * 操作成功
     *
     * @param message 消息
     * @param result  数据
     * @return Map<String, Object> 响应体
     */
    public static Map<String, Object> success(String message, Object result) {
        return customResult(true, 200, message, result, java.lang.String.valueOf(System.currentTimeMillis()));
    }

    /**
     * 操作成功
     *
     * @param code    状态码
     * @param message 消息
     * @param result  数据
     * @return Map<String, Object> 响应体
     */
    public static Map<String, Object> success(Integer code, String message, Object result) {
        return customResult(true, code, message, result, java.lang.String.valueOf(System.currentTimeMillis()));
    }

    /**
     * 操作失败
     *
     * @return Map<String, Object> 响应体
     */
    public static Map<String, Object> error() {
        return customResult(false, 500, "操作失败", null, java.lang.String.valueOf(System.currentTimeMillis()));
    }

    /**
     * 操作失败
     *
     * @param message 消息
     * @return Map<String, Object> 响应体
     */
    public static Map<String, Object> error(String message) {
        return customResult(false, 500, message, null, java.lang.String.valueOf(System.currentTimeMillis()));
    }

    /**
     * 操作失败
     *
     * @param code    状态码
     * @param message 消息
     * @return Map<String, Object> 响应体
     */
    public static Map<String, Object> error(Integer code, String message) {
        return customResult(false, code, message, null, java.lang.String.valueOf(System.currentTimeMillis()));
    }

    /**
     * 操作失败
     *
     * @param code    状态码
     * @param message 消息
     * @param result  数据
     * @return Map<String, Object> 响应体
     */
    public static Map<String, Object> error(Integer code, String message, Object result) {
        return customResult(false, code, message, result, java.lang.String.valueOf(System.currentTimeMillis()));
    }

    /**
     * 分页数据
     */
    public static Map<String, Object> pageResult(Object records, long total, Long size, Long current, String[] orders, Long pages) {
        Map<String, Object> map = new HashMap<>();
        map.put("records", records);
        map.put("total", total);
        map.put("size", size);
        map.put("current", current);
        map.put("orders", orders);
        map.put("pages", pages);
        return map;
    }

    /**
     * 分页数据
     */
    public static Map<String, Object> pageResult(Page pagem, String[] orders) {
        Map<String, Object> map = new HashMap<>();
        map.put("records", pagem.getRecords());
        map.put("total", pagem.getTotal());
        map.put("size", pagem.getSize());
        map.put("current", pagem.getCurrent());
        map.put("orders", orders);
        map.put("pages", pagem.getPages());
        return map;
    }
}