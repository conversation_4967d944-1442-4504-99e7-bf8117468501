<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.photo.mapper.XbResourceMapper">
    
    <resultMap type="XbResource" id="XbResourceResult">
        <result property="id"    column="id"    />
        <result property="projectId"    column="project_id"    />
        <result property="modelId"    column="model_id"    />
        <result property="url"    column="url"    />
        <result property="title"    column="title"    />
        <result property="content"    column="content"    />
        <result property="pay"    column="pay"    />
        <result property="people"    column="people"    />
        <result property="style"    column="style"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectXbResourceVo">
        select id, project_id, model_id, url, title, content, pay, people, style, status, create_by, create_time, update_by, update_time, remark from xb_resource
    </sql>

    <select id="selectXbResourceList" parameterType="XbResource" resultMap="XbResourceResult">
        <include refid="selectXbResourceVo"/>
        <where>  
            <if test="id != null "> and id = #{id}</if>
            <if test="projectId != null  and projectId != ''"> and project_id = #{projectId}</if>
            <if test="modelId != null  and modelId != ''"> and model_id = #{modelId}</if>
            <if test="url != null  and url != ''"> and url = #{url}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="pay != null "> and pay = #{pay}</if>
            <if test="people != null "> and people = #{people}</if>
            <if test="style != null "> and style = #{style}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectXbResourceById" parameterType="Long" resultMap="XbResourceResult">
        <include refid="selectXbResourceVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertXbResource" parameterType="XbResource">
        insert into xb_resource
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="projectId != null">project_id,</if>
            <if test="modelId != null">model_id,</if>
            <if test="url != null">url,</if>
            <if test="title != null">title,</if>
            <if test="content != null">content,</if>
            <if test="pay != null">pay,</if>
            <if test="people != null">people,</if>
            <if test="style != null">style,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="projectId != null">#{projectId},</if>
            <if test="modelId != null">#{modelId},</if>
            <if test="url != null">#{url},</if>
            <if test="title != null">#{title},</if>
            <if test="content != null">#{content},</if>
            <if test="pay != null">#{pay},</if>
            <if test="people != null">#{people},</if>
            <if test="style != null">#{style},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateXbResource" parameterType="XbResource">
        update xb_resource
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="modelId != null">model_id = #{modelId},</if>
            <if test="url != null">url = #{url},</if>
            <if test="title != null">title = #{title},</if>
            <if test="content != null">content = #{content},</if>
            <if test="pay != null">pay = #{pay},</if>
            <if test="people != null">people = #{people},</if>
            <if test="style != null">style = #{style},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteXbResourceById" parameterType="Long">
        delete from xb_resource where id = #{id}
    </delete>

    <delete id="deleteXbResourceByIds" parameterType="String">
        delete from xb_resource where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>