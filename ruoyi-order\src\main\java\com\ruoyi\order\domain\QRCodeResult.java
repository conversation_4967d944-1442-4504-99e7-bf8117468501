package com.ruoyi.order.domain;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

public class QRCodeResult {

    @JsonSerialize(using = ToStringSerializer.class)
    private String QRCode;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long OrderId;

    public QRCodeResult(String QRCode, Long orderId) {
        this.QRCode = QRCode;
        OrderId = orderId;
    }

    public String getQRCode() {
        return QRCode;
    }

    public void setQRCode(String QRCode) {
        this.QRCode = QRCode;
    }

    public Long getOrderId() {
        return OrderId;
    }

    public void setOrderId(Long orderId) {
        OrderId = orderId;
    }

    @Override
    public String toString() {
        return "QRCodeResult{" +
                "QRCode='" + QRCode + '\'' +
                ", OrderId=" + OrderId +
                '}';
    }
}
