package com.ruoyi.generator.domain;

import com.alibaba.fastjson.JSONObject;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 微信客服文本消息
 */
public class CustomerServiceText implements Serializable {

    /**
     * 接受用户openid
     */
    private String touser;

    /**
     * 消息类型
     */
    private String msgtype;

    private Map<String,String> text;

    public String getTouser() {
        return touser;
    }

    public void setTouser(String touser) {
        this.touser = touser;
    }

    public String getMsgtype() {
        return msgtype;
    }

    public void setMsgtype(String msgtype) {
        this.msgtype = msgtype;
    }

    public Map<String, String> getText() {
        return text;
    }

    public void setText(Map<String, String> text) {
        this.text = text;
    }

    public static void main(String[] args) {
        CustomerServiceText text = new CustomerServiceText();
        text.setTouser("qqq");
        text.setMsgtype("text");

        Map<String,String> text1 = new HashMap<>();
        text1.put("content","你好，欢迎您的加入！");
        text.setText(text1);

        String json =  JSONObject.toJSONString(text);
        System.out.print(json);
    }
}
