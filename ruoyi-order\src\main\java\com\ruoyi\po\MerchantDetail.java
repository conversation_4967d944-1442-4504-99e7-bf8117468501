package com.ruoyi.po;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: 少凡
 * @Date 2018/7/30 9:31
 */
@Data
@NoArgsConstructor
public class MerchantDetail {

    /**
     * 商户简称
     */
    private String alias;

    /**
     * 联系人手机号。为商户常用联系人联系手机号
     */
    private String contactMobile;

    /**
     * 联系人姓名。为商户常用联系人姓名
     */
    private String contactName;

    /**
     * 省份（按照附录5.6国标省市区号录入）。
     * 备注：Province、City、District、Address须同时出现。
     * 可选填，一旦填写会做正确性校验
     */
    private String province;

    private String city;

    private String district;

    private String address;

    /**
     * 商户客服电话
     */
    private String servicePhoneNo;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 企业法人名称（注意：是法人名称，非法人代表名称）
     * 自然人或个人工商户无须上送
     */
    private String legalPerson;

    /**
     * 负责人手机号。自然人或个体工商户上送，企业可不上送。
     * 备注：若商户后续开通二类户及余利宝功能，该手机号作为客户在网商的预留手机号，企业可不填)。
     * 开户发送的短信验证码的手机号需要和负责人手机号一致。
     */
    private String principalMobile;


    private String principalCertNo;

    /**
     * 负责人名称或企业法人代表姓名。
     * 备注：若商户类型为“自然人”填写责任人本人，若为“个体工商户”填写经营者本人，若为“企业”填写企业法人代表名称
     */
    private String principalPerson;

    /**
     * 营业执照工商注册号。若商户类型为“自然人”无需上送
     */
    private String bussAuthNum;

    /**
     * 组织机构代码证号
     */
    private String certOrgCode;

    /**
     * 负责人或企业法人代表的身份证图片正面。
     * 备注：需要先调商户图片上传接口上传图片获取链接，并将该链接填到本字段。涉及图片上传的接口处理逻辑一致。
     * 对应PhotoType字段（图片类型）：01
     */
    private String certPhotoA;

    /**
     * 负责人或企业法人代表的身份证图片反面
     * 对应PhotoType字段（图片类型）：02
     */
    private String certPhotoB;

    /**
     * 营业执照图片(若商户类型为“自然人”无需上送)
     * 对应PhotoType字段（图片类型）：03
     */
    private String licensePhoto;

    /**
     * 组织机构代码证图片(若商户类型为“自然人”无需上送、“个体工商户”可选)
     * 对应PhotoType字段（图片类型）：04
     */
    private String prgPhoto;

    /**
     * 开户许可证照片(若商户类型为“自然人”无需上送、“个体工商户”可选、企业必送)
     * 对应PhotoType字段（图片类型）：05
     */
    private String industryLicensePhoto;

    /**
     * 门头照(店铺门头照，可选)
     * 对应PhotoType字段（图片类型）：06
     */
    private String shopPhoto;
    /**
     * 证件类型 01，身份证
     */
    private String principalCertType;

    /**
     * 其他图片
     * 对应PhotoType字段（图片类型）：07
     */
    private String otherPhoto;
    /**
     * 法人身份证有效期开始时间
     */
    private String principalCertNoStartDate;
    /**
     * 法人身份证有效期
     */
    private String principalCertNoExpDate;
    /**
     * 营业执照有效期开始时间
     */
    private String bussStartDate;
    /**
     * 营业执照有效期
     */
    private String bussExpDate;
    /**
     * 开户许可证编号
     */
    private String industryLicenseNum;
    /**
     * 租赁合同照
     */
    private String contractPhoto;
    /**
     * 门店内景照
     */
    private String shopEntrancePhoto;
    /**
     * 收银台照
     */
    private String checkStandPhoto;
    /**
     * 手持身份证照
     */
    private String handHoldIdCard;

    /**
     * 负责人微信号
     */
    private String weChatID;
    /**
     * 负责人QQ号
     */
    private String qqID;


    public MerchantDetail(String legalPerson, String bussAuthNum, String principalPerson, String principalMobile,
                          String principalCertType, String principalCertNo, String province, String city, String district,
                          String address, String email, String servicePhoneNo, String weChatID, String qqID,
                          String certPhotoA, String certPhotoB, String licensePhoto, String industryLicensePhoto,
                          String shopPhoto, String shopEntrancePhoto, String contractPhoto, String checkStandPhoto) {
        this.legalPerson = legalPerson;
        this.bussAuthNum = bussAuthNum;
        this.principalPerson = principalPerson;
        this.principalMobile = principalMobile;
        this.principalCertType = principalCertType;
        this.principalCertNo = principalCertNo;
        this.province = province;
        this.city = city;
        this.district = district;
        this.address = address;
        this.email = email;
        this.servicePhoneNo = servicePhoneNo;
        this.weChatID = weChatID;
        this.qqID = qqID;
        this.certPhotoA = certPhotoA;
        this.certPhotoB = certPhotoB;
        this.licensePhoto = licensePhoto;
        this.industryLicensePhoto = industryLicensePhoto;
        this.shopPhoto = shopPhoto;
        this.shopEntrancePhoto = shopEntrancePhoto;
        this.contractPhoto = contractPhoto;
        this.checkStandPhoto = checkStandPhoto;
    }
}
