package com.yunchuang.wxapp.service;

import com.ruoyi.common.utils.WeixinRequestUtil;
import com.yunchuang.wxapp.exception.WxappBusinessException;
import com.yunchuang.wxapp.model.enums.exception.WxappBusinessExceptionCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 微信开放平台Service
 */
@Service
public class WxAppOpenApiService {

    private static final Logger log = LoggerFactory.getLogger(WxAppOpenApiService.class);

    @Value("${wxapp.wechat.applets.app-id:wx2e5ecfd4e6248e29}")
    private String APP_ID; // 微信小程序的appId

    @Value("${wxapp.wechat.applets.app-secret:89eff162c71fa45f834f8a04ac27d302}")
    private String APP_SECRET;  // 微信小程序的appSecret

    private final WeixinRequestUtil weixinRequestUtil;

    public WxAppOpenApiService(WeixinRequestUtil weixinRequestUtil) {
        this.weixinRequestUtil = weixinRequestUtil;
    }

    /**
     * 获取微信小程序的openid
     *
     * @param code 微信小程序登录凭证
     * @return openid
     */
    public String getOpenid(String code) {
        // 配置请求地址
        String url = "https://api.weixin.qq.com/sns/jscode2session";
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("appid", APP_ID);
        queryParams.put("secret", APP_SECRET);
        queryParams.put("js_code", code);
        queryParams.put("grant_type", "authorization_code");
        Map response = weixinRequestUtil.sendWxGetRequest(url, queryParams, Map.class);
        log.info("获取openid接口返回结果：{}", response);
        if (response.get("openid") == null) {
            log.error("获取openid失败");
            throw new WxappBusinessException(WxappBusinessExceptionCode.EC_60102);
        }
        return (String) response.get("openid");
    }
}
