package com.ruoyi.utils;

import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import java.io.IOException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.SignatureException;
import java.security.spec.InvalidKeySpecException;
import java.util.*;

import static com.ruoyi.utils.OrderConstant.*;

public class ScanpayUtils {

    public static Map<String, String> paraFilter(Map<String, String> map) {

        Map<String, String> result = new HashMap<>();

        if (map == null || map.size() <= 0) {
            return result;
        }

        for (String key : map.keySet()) {//返回map中所有key值的列表
            String value = map.get(key);
            //如果key值等于sign或者以reserved开头的，则跳过这个循环，开始下次循环
            if (key.equalsIgnoreCase("sign") || (key.length() >= 8 && key.substring(0, 8).equalsIgnoreCase("reserved"))) {
                continue;
            }
            //将键值对，放入集合
            result.put(key, value);
        }

        return result;
    }

    public static String createLinkString(Map<String, String> map) {//字典排序算法

        List<String> keys = new ArrayList<>(map.keySet());//取map中的key放进集合
        Collections.sort(keys);//将key按字典排序

        String prestr = "";

        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            String value = map.get(key);//按照key取value

            if (i == keys.size() - 1) {
                //拼接时，不包括最后一个&字符
                prestr = prestr + key + "=" + value;
            } else {
                prestr = prestr + key + "=" + value + "&";
            }
        }
        System.out.println("响应报文签名原文：" + prestr);
        return prestr;
    }

    public static String getSign(Map<String, String> map) throws InvalidKeySpecException, SignatureException, NoSuchAlgorithmException, InvalidKeyException, IOException {

        Map<String, String> mapNew = paraFilter(map);

        String preSignStr = createLinkString(mapNew);

//        System.out.println("==============================待签名字符串==============================\r\n" + preSignStr);

        String sign = Sign.sign(preSignStr, INS_PRIVATE_KEY);

        sign = sign.replace("\r\n", "");

//        System.out.println("==============================签名字符串==============================\r\n" + sign);

//        String sign = URLEncoder.encode(sign);

        return sign;
    }

    public static Boolean verifySign(Map<String, String> map, String sign) throws Exception {

        Map<String, String> mapNew = paraFilter(map);

        String preSignStr = createLinkString(mapNew);
        //比较
        return Sign.verify(preSignStr.getBytes(charset), FY_PUBLIC_KEY, sign);//签名原文，公钥，签名
    }

    public static Map<String,String> xmlStr2Map(String xmlStr){
        Map<String,String> map = new HashMap<String,String>();
        Document doc;
        try {
            doc = DocumentHelper.parseText(xmlStr);
            Element resroot = doc.getRootElement();
            List children = resroot.elements();
            if(children != null && children.size() > 0) {
                for(int i = 0; i < children.size(); i++) {
                    Element child = (Element)children.get(i);
//                    map.put(child.getName(), child.getTextTrim());//会将换行符转换成空格
                    map.put(child.getName(), child.getStringValue().trim());
                }
            }
        } catch (DocumentException e) {
            e.printStackTrace();
        }
        return map;
    }


}
