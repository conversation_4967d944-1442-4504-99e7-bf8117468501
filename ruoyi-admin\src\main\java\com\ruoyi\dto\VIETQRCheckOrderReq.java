package com.ruoyi.dto;

/**
 * VIETQR生成VietQR码请求
 */
public class VIETQRCheckOrderReq {

    /**
     * VietQR账号
     */
    private String bankAccount;


    /**
     * 类型
     */
    private String type;

    /**
     * 值
     */
    private String value;

    /**
     * 编码
     */
    private String checkSum;


    public String getBankAccount() {
        return bankAccount;
    }

    public void setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getCheckSum() {
        return checkSum;
    }

    public void setCheckSum(String checkSum) {
        this.checkSum = checkSum;
    }

    @Override
    public String toString() {
        return "VIETQRGenerateCustomerReq{" +
                ", bankAccount='" + bankAccount + '\'' +
                ", type='" + type + '\'' +
                ", value='" + value + '\'' +
                ", checkSum='" + checkSum + '\'' +
                '}';
    }
}
