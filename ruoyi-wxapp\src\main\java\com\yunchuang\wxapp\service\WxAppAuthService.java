package com.yunchuang.wxapp.service;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.json.JSONUtil;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.StringUtils;
import com.yunchuang.wxapp.exception.WxappAuthenticationException;
import com.yunchuang.wxapp.model.constant.RedisKeyGroup;
import com.yunchuang.wxapp.model.domain.WxappLoginUser;
import com.yunchuang.wxapp.model.enums.exception.WxappAuthenticationExceptionCode;
import com.yunchuang.wxapp.util.MyJWTUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;


/**
 * 微信小程序 - 认证服务
 */
@Service
public class WxAppAuthService {
    private static final Logger log = LoggerFactory.getLogger(WxAppAuthService.class);

    @Value("${wxapp.auth.token-expire-time:43200}")
    private Long tokenExpireTime; // 令牌过期时间

    @Value("${wxapp.auth.token-refresh-time:4320}")
    private Long tokenRefreshTime; // 令牌刷新时间


    @Resource
    private RedisCache redisCache;

    /**
     * 获取用户身份信息
     *
     * @param request 请求
     * @return LotteryLoginUser 登录用户信息
     */
    public WxappLoginUser getClientUser(HttpServletRequest request) {
        // 获取请求携带的令牌
        String token = getToken(request);
        if (StringUtils.isNotEmpty(token)) {
            try {
                // 验证JWT
                if (!MyJWTUtil.verifyJWT(token)) {
                    throw new WxappAuthenticationException(WxappAuthenticationExceptionCode.AUTHENTICATION_FAILED);
                }
                // 解析JWT
                String str = MyJWTUtil.parseJWT(token);
                // 获取用户标识
                String userKey = JSONUtil.parseObj(str).getStr("userId");
                // 从redis中获取用户信息
                return redisCache.getCacheObject(RedisKeyGroup.LOGIN_USER + ":" + userKey);
            } catch (Exception e) {
                log.error("获取用户信息异常'{}'", e.getMessage());
                throw new WxappAuthenticationException(WxappAuthenticationExceptionCode.AUTHENTICATION_FAILED);
            }
        } else {
            throw new WxappAuthenticationException(WxappAuthenticationExceptionCode.AUTHENTICATION_ID_NULL);
        }
    }

    /**
     * 设置用户身份信息
     *
     * @param loginUser 登录用户信息
     * @return token
     */
    public String setClientUser(WxappLoginUser loginUser) {
        // 保存用户信息到redis
        loginUser.setJwtEndTime(System.currentTimeMillis() + tokenExpireTime * 60 * 1000);
        redisCache.setCacheObject(RedisKeyGroup.LOGIN_USER + ":" + loginUser.getId(), loginUser);
        // 创建token
        Map<String, Object> map = new HashMap<>();
        map.put("userId", loginUser.getId());
        return MyJWTUtil.createJWT(map, DateTime.now().offsetNew(DateField.YEAR, 99));
    }

    /**
     * 验证令牌有效期，相差不足20分钟，自动刷新缓存
     *
     * @param loginUser 登录信息
     */
    public void verifyToken(WxappLoginUser loginUser) {
        long expireTime = loginUser.getJwtEndTime();
        long currentTime = System.currentTimeMillis();
        if (expireTime - currentTime <= 20 * 60 * 1000) {
            refreshToken(loginUser);
        }
    }

    /**
     * 刷新令牌有效期
     * <p>
     * 重新设置JWT到期时间<br />
     * 如果距离到期时间不足设定的时间，自动刷新缓存
     * </p>
     *
     * @param loginUser 登录信息
     */
    public void refreshToken(WxappLoginUser loginUser) {
        // 先判断是否需要刷新
        long expireTime = loginUser.getJwtEndTime();
        long currentTime = System.currentTimeMillis();
        if (expireTime - currentTime > tokenRefreshTime * 60 * 1000) {
            return;
        }
        // 重新设置JWT到期时间
        loginUser.setJwtEndTime(System.currentTimeMillis() + tokenExpireTime * 60 * 1000);
        // 根据uuid将loginUser缓存
        String userKey = RedisKeyGroup.LOGIN_USER + ":" + loginUser.getId();
        redisCache.setCacheObject(userKey, loginUser);
    }

    /**
     * 获取请求token
     *
     * @param request 请求
     * @return token
     */
    public String getToken(HttpServletRequest request) {
        return request.getHeader("Authorization");
    }
}
