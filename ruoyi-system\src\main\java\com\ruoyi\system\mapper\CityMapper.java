package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.City;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 城市信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-07-14
 */
@Mapper
public interface CityMapper 
{
    /**
     * 查询城市信息
     * 
     * @param id 城市信息主键
     * @return 城市信息
     */
    public City selectCityById(Long id);

    /**
     * 查询城市信息列表
     * 
     * @param city 城市信息
     * @return 城市信息集合
     */
    public List<City> selectCityList(City city);

    /**
     * 新增城市信息
     * 
     * @param city 城市信息
     * @return 结果
     */
    public int insertCity(City city);

    /**
     * 修改城市信息
     * 
     * @param city 城市信息
     * @return 结果
     */
    public int updateCity(City city);

    /**
     * 删除城市信息
     * 
     * @param id 城市信息主键
     * @return 结果
     */
    public int deleteCityById(Long id);

    /**
     * 批量删除城市信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCityByIds(Long[] ids);

    List<City> queryProvinceByValue(String province);

    List<City> queryCityByProvince(@Param("province") String province, @Param("city") String city);

    List<City> queryDistrictByCity(@Param("city") String city, @Param("district") String district);
}
