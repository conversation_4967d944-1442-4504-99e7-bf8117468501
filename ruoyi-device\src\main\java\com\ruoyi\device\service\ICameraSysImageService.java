package com.ruoyi.device.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.device.domain.CameraSysImage;
import com.ruoyi.device.domain.StickerMessage;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

/**
 * camera_sys_imageService接口
 * 
 * <AUTHOR>
 * @date 2023-07-19
 */

public interface ICameraSysImageService extends IService<CameraSysImage>
{
    /**
     * 查询camera_sys_image
     * 
     * @param id camera_sys_image主键
     * @return camera_sys_image
     */
    public CameraSysImage selectCameraSysImageById(Long id);
    /**
     * 查询camera_sys_image列表
     * 
     * @param cameraSysImage camera_sys_image
     * @return camera_sys_image集合
     */
    public TableDataInfo selectCameraSysImageList(CameraSysImage cameraSysImage, int pageNum, int pageSize, Boolean isOwn);
    /**
     * 查询私人camera_sys_image列表
     *
     * @param cameraSysImage camera_sys_image
     * @return camera_sys_image集合
     */
    public List<CameraSysImage> selectMyCameraSysImageList(CameraSysImage cameraSysImage);
    /**
     * 新增camera_sys_image
     * 
     * @param cameraSysImage camera_sys_image
     * @return 结果
     */
    public int insertCameraSysImage(CameraSysImage cameraSysImage);
    /**
     * 修改camera_sys_image
     * 
     * @param cameraSysImage camera_sys_image
     * @return 结果
     */
    public int updateCameraSysImage(CameraSysImage cameraSysImage);
    /**
     * 批量删除camera_sys_image
     * 
     * @param ids 需要删除的camera_sys_image主键集合
     * @return 结果
     */
    public int deleteCameraSysImageByIds(Long[] ids);

    /**
     * 删除camera_sys_image信息
     * 
     * @param id camera_sys_image主键
     * @return 结果
     */
    public int deleteCameraSysImageById(Long id);


    /**
     * 查询camera_sys全部背景图
     *
     * @return camera_sys_image集合
     */
    public List<CameraSysImage> selectCameraSysBackgroundList();


    /**
     *
     * @param deviceId
     * @return
     */
    List<CameraSysImage> userModelType(String deviceId);

    List<StickerMessage> getSticker(String deviceId,Integer who);

    List<CameraSysImage> getModel(String deviceId, String type, int who);


    /**
     * 修改模版
     * @param cameraSysImage 当前模版
     * @return
     */
}
