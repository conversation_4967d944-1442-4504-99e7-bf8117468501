package com.ruoyi.web.controller.wxapp.admin;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.yunchuang.wxapp.model.domain.UserAgreement;
import com.yunchuang.wxapp.service.admin.IUserAgreementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 用户协议Controller
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@RestController
@RequestMapping("/wxapp/agreement")
public class UserAgreementController extends BaseController {

    @Autowired
    private IUserAgreementService userAgreementService;

    /**
     * 查询用户协议列表
     */
    @PreAuthorize("@ss.hasPermi('wxapp:agreement:list')")
    @GetMapping("/list")
    public TableDataInfo list(UserAgreement userAgreement) {
        startPage();
        List<UserAgreement> list = userAgreementService.selectUserAgreementList(userAgreement);
        return getDataTable(list);
    }

    /**
     * 导出用户协议列表
     */
    @PreAuthorize("@ss.hasPermi('wxapp:agreement:export')")
    @Log(title = "用户协议", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, UserAgreement userAgreement) {
        List<UserAgreement> list = userAgreementService.selectUserAgreementList(userAgreement);
        ExcelUtil<UserAgreement> util = new ExcelUtil<UserAgreement>(UserAgreement.class);
        util.exportExcel(response, list, "用户协议数据");
    }

    /**
     * 获取用户协议详细信息
     */
    @PreAuthorize("@ss.hasPermi('wxapp:agreement:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(userAgreementService.selectUserAgreementById(id));
    }

    /**
     * 新增用户协议
     */
    @PreAuthorize("@ss.hasPermi('wxapp:agreement:add')")
    @Log(title = "用户协议", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody UserAgreement userAgreement) {
        return toAjax(userAgreementService.insertUserAgreement(userAgreement));
    }

    /**
     * 修改用户协议
     */
    @PreAuthorize("@ss.hasPermi('wxapp:agreement:edit')")
    @Log(title = "用户协议", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody UserAgreement userAgreement) {
        return toAjax(userAgreementService.updateUserAgreement(userAgreement));
    }

    /**
     * 删除用户协议
     */
    @PreAuthorize("@ss.hasPermi('wxapp:agreement:remove')")
    @Log(title = "用户协议", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(userAgreementService.deleteUserAgreementByIds(ids));
    }
}
