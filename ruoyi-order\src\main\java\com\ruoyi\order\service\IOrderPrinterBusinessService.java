package com.ruoyi.order.service;

import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 订单打印机业务服务接口
 * 处理复杂的业务逻辑，包括文件上传、用户认证、参数验证等
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface IOrderPrinterBusinessService {
    
    /**
     * 上传文件并创建打印订单（整合接口）
     *
     * @param file 单个文件
     * @param files 多个文件
     * @param deviceId 设备ID
     * @param deviceName 设备名称
     * @param openid 用户openid
     * @param copies 打印份数
     * @param colorMode 颜色模式
     * @param duplexMode 双面模式
     * @param paperType 纸张类型
     * @param pageRange 页码范围
     * @return 处理结果
     */
    Map<String, Object> uploadAndCreateOrder(
            MultipartFile file,
            MultipartFile[] files,
            String deviceId,
            String deviceName,
            String openid,
            Integer copies,
            Integer colorMode,
            Integer duplexMode,
            Integer paperType,
            String pageRange);

    /**
     * 创建订单（第一步：创建订单）
     *
     * @param deviceId 设备ID
     * @param deviceName 设备名称
     * @param openid 用户openid
     * @param phone 用户手机号
     * @return 创建结果
     */
    Map<String, Object> createOrder(
            String deviceId,
            String deviceName,
            String openid,
            String phone);

    /**
     * 创建微信支付订单
     *
     * @param deviceId 设备ID
     * @param deviceName 设备名称
     * @param openid 用户openid
     * @param phone 用户手机号
     * @return 创建结果
     */
    Map<String, Object> createWxPayOrder(
            String deviceId,
            String deviceName,
            String openid,
            String phone);

    /**
     * 上传文件并计算价格（第二步：上传文件）
     *
     * @param orderId 订单ID
     * @param file 文件
     * @param copies 打印份数
     * @param colorMode 颜色模式
     * @param duplexMode 双面模式
     * @param paperType 纸张类型
     * @param pageRange 页码范围
     * @param isPhoto 是否为照片
     * @param sizeSpec 尺寸大小
     * @return 上传结果
     */
    Map<String, Object> uploadFile(
            String orderId,
            MultipartFile file,
            Integer copies,
            Integer colorMode,
            Integer duplexMode,
            Integer paperType,
            String pageRange,
            Integer isPhoto,
            String sizeSpec);
    
    /**
     * 验证基础参数
     * 
     * @param deviceId 设备ID
     * @param file 单个文件
     * @param files 多个文件
     * @return 验证结果，null表示验证通过，否则返回错误信息
     */
    String validateBasicParams(String deviceId, MultipartFile file, MultipartFile[] files);
    
    /**
     * 验证订单相关参数
     * 
     * @param orderId 订单ID
     * @param file 文件
     * @return 验证结果，null表示验证通过，否则返回错误信息
     */
    String validateOrderParams(String orderId, MultipartFile file);
    
    /**
     * 记录请求日志
     * 
     * @param request HTTP请求
     * @param methodName 方法名
     * @param params 参数信息
     */
    void logRequestInfo(HttpServletRequest request, String methodName, Map<String, Object> params);
    
    /**
     * 记录文件信息日志
     *
     * @param file 单个文件
     * @param files 多个文件
     */
    void logFileInfo(MultipartFile file, MultipartFile[] files);

    /**
     * 获取订单详情（包含打印任务和总金额）
     *
     * @param orderId 订单ID
     * @param openid 用户openid（可选，用于权限验证）
     * @return 订单详情
     */
    Map<String, Object> getOrderDetail(String orderId, String openid);
}
