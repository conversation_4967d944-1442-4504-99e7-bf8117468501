package com.ruoyi.order.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.Date;

/**
 * 订单打印任务对象 order_printer_tasks
 * 
 * <AUTHOR>
 * @date 2024-06-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("order_printer_tasks")
public class OrderPrinterTask extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 任务id */
    @Excel(name = "任务id")
    @TableId(value = "task_id", type = IdType.INPUT)
    private String taskId;

    /** 关联订单id */
    @Excel(name = "关联订单id")
    private String orderId;

    /** 打印机设备id */
    @Excel(name = "打印机设备id")
    private String deviceId;

    /** 文件URL */
    @Excel(name = "文件URL")
    private String fileUrl;

    /** 文件名称 */
    @Excel(name = "文件名称")
    private String fileName;

    /** 用户上传的原始文件名 */
    @Excel(name = "原始文件名")
    private String originalFileName;

    /** 文件类型 */
    @Excel(name = "文件类型")
    private String fileType;

    /** 文件大小(字节) */
    @Excel(name = "文件大小")
    private Long fileSize;

    /** 打印状态 0-待打印 1-打印中 2-打印完成 3-打印失败 */
    @Excel(name = "打印状态")
    private Integer printStatus;

    /** 页码范围(如"1-3,5") */
    @Excel(name = "页码范围")
    private String pageRange;

    /** 打印份数 */
    @Excel(name = "打印份数")
    private Integer copies;

    /** 颜色模式 0-黑白 1-彩色 */
    @Excel(name = "颜色模式")
    private Integer colorMode;

    /** 双面模式 0-单面 1-双面 */
    @Excel(name = "双面模式")
    private Integer duplexMode;

    /** 纸张类型 1-A4 2-A5 3-照片纸 */
    @Excel(name = "纸张类型")
    private Integer paperType;

    /** 金额(客户给钱) 单位：元 */
    @Excel(name = "金额(元)")
    private Double taskPrice;

    /** 错误信息 */
    @Excel(name = "错误信息")
    private String errorMsg;

    /** 重试次数 */
    @Excel(name = "重试次数")
    private Integer retryCount;

    /** 开始打印时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始打印时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startTime;

    /** 完成打印时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "完成打印时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endTime;

    /** 任务状态（0-正常 1-删除） */
    @Excel(name = "任务状态")
    private Integer status;

    /** 是否为照片 0-否 1-是 */
    @Excel(name = "是否为照片")
    private Integer isPhoto;

    /** 尺寸大小（如：A4、4寸、6寸等） */
    @Excel(name = "尺寸大小")
    private String sizeSpec;
}