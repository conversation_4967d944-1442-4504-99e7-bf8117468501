package com.ruoyi.photo.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.photo.domain.UserPhoto;
import com.ruoyi.photo.domain.XbResource;

/**
 * 素材管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-29
 */
public interface XbResourceMapper extends BaseMapper<XbResource>
{
    /**
     * 查询素材管理
     * 
     * @param id 素材管理主键
     * @return 素材管理
     */
    public XbResource selectXbResourceById(Long id);

    /**
     * 查询素材管理列表
     * 
     * @param xbResource 素材管理
     * @return 素材管理集合
     */
    public List<XbResource> selectXbResourceList(XbResource xbResource);

    /**
     * 新增素材管理
     * 
     * @param xbResource 素材管理
     * @return 结果
     */
    public int insertXbResource(XbResource xbResource);

    /**
     * 修改素材管理
     * 
     * @param xbResource 素材管理
     * @return 结果
     */
    public int updateXbResource(XbResource xbResource);

    /**
     * 删除素材管理
     * 
     * @param id 素材管理主键
     * @return 结果
     */
    public int deleteXbResourceById(Long id);

    /**
     * 批量删除素材管理
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteXbResourceByIds(Long[] ids);
}
