package com.ruoyi.order.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.order.domain.OrderCollect;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * collectMapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-03
 */
public interface OrderCollectMapper extends BaseMapper<OrderCollect> {


    @Select("SELECT SUM(count) AS totalRevenue FROM order_collect WHERE time = #{time} ")
    Long countPrice(@Param("time") String time);
}
