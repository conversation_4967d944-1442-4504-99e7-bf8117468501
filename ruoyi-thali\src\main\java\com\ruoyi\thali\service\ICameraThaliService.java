package com.ruoyi.thali.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.thali.domain.CameraThali;
import com.ruoyi.thali.dto.Result;

/**
 * 套餐Service接口
 * 
 * <AUTHOR>
 * @date 2023-07-27
 */
public interface ICameraThaliService extends IService<CameraThali>
{
    /**
     * 查询套餐
     * 
     * @param id 套餐主键
     * @return 套餐
     */
    public CameraThali selectCameraThaliById(Long id);

    /**
     * 查询套餐列表
     * 
     * @param cameraThali 套餐
     * @return 套餐集合
     */
    public List<CameraThali> selectCameraThaliList(CameraThali cameraThali);

    /**
     * 新增套餐
     * 
     * @param cameraThali 套餐
     * @return 结果
     */
    public int insertCameraThali(CameraThali cameraThali);

    /**
     * 修改套餐
     * 
     * @param cameraThali 套餐
     * @return 结果
     */
    public int updateCameraThali(CameraThali cameraThali);

    /**
     * 批量删除套餐
     * 
     * @param ids 需要删除的套餐主键集合
     * @return 结果
     */
    public int deleteCameraThaliByIds(Long[] ids);

    /**
     * 删除套餐信息
     * 
     * @param id 套餐主键
     * @return 结果
     */
    public int deleteCameraThaliById(Long id);

    Result queryCameraThaliByDevice(String deviceId);

    /**
     *  套餐详情
     * @param deviceId 套餐id
     * @return 套餐详情
     */
    public CameraThali queryPackageType(String deviceId);

    /**
     * 修改套餐价格
     * @param cameraThali 改动数据
     * @return 改动后的数据
     */
    CameraThali updatePrice(CameraThali cameraThali);
}
