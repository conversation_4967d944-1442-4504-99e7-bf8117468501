/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2017 All Rights Reserved.
 */
package com.ruoyi.enums;

/**
 * 
 * <AUTHOR>
 * @version $Id: PhotoType.java, v 0.1 2017年8月3日 下午2:52:20 simon.xxm Exp $
 *
 */
public class PhotoType {
    public static final String CERT_PHOTO_A           = "01"; //身份证正面
    public static final String CERT_PHOTO_B           = "02"; //身份证反面
    public static final String LICENSE_PHOTO          = "03"; //营业执照图片
    public static final String PRG_PHOTO              = "04"; //组织机构代码证图片
    public static final String INDUSTRY_LICENSE_PHOTO = "05"; //开户许可证照片
    public static final String SHOP_PHOTO             = "06"; //门头照
    public static final String SHOP_ENTRANCE_PHOTO    = "09"; //门店内景照
    public static final String CHECK_STAND_PHOTO      = "08"; //收银台照
    public static final String HAND_HOLDIDCARD_PHOTO  = "11"; //手持身份证照
    public static final String CONTRACT_PHOTO         = "12"; //租赁合同
    public static final String OTHER_PHOTO            = "07"; //其他图片
    public static boolean contains(String value) {
        return value.equals(CERT_PHOTO_A) || value.equals(CERT_PHOTO_B)
               || value.equals(LICENSE_PHOTO) || value.equals(PRG_PHOTO)
               || value.equals(INDUSTRY_LICENSE_PHOTO) || value.equals(SHOP_PHOTO)
               || value.equals(OTHER_PHOTO);
    }

    /**
     * 图片类型。可选值：
     *
     * 01 身份证人像面
     *
     * 02 身份证国徽面
     *
     * 03 营业执照
     *
     * 04组织机构代码证
     *
     * 05 开户许可证
     *
     * 06 门头照
     *
     * 07 其他
     *
     * 08 收银台照片
     *
     * 09 门店内景照片
     *
     * 10 各大餐饮平台入驻照片
     *
     * 11 手持身份证合照
     *
     * 12 租赁协议
     *
     * 13补充材料图片
     *
     * 14 非法人结算授权书图片
     *
     * 15 持卡人二代身份证正面图片
     *
     * 16 持卡人二代身份证反面图片
     * */
}
