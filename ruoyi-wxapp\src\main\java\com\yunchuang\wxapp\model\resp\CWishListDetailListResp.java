package com.yunchuang.wxapp.model.resp;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.device.domain.CameraSysImage;
import lombok.Data;

import java.util.Date;

/**
 * 心愿单 - 详情列表响应
 */
@Data
public class CWishListDetailListResp {

    /**
     * 心愿单ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @Excel(name = "用户ID")
    private Long userId;

    /**
     * 相框ID
     */
    @Excel(name = "相框ID")
    private Long photoFrameId;

    /**
     * 打卡时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "打卡时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date clockinTime;

    /**
     * 相框
     */
    private CameraSysImage photoFrame;
}
