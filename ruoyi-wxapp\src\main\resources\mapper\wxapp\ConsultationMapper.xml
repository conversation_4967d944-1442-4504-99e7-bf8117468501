<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yunchuang.wxapp.mapper.ConsultationMapper">

    <resultMap type="Consultation" id="ConsultationResult">
        <result property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="question" column="question"/>
        <result property="answer" column="answer"/>
        <result property="resolvedNum" column="resolved_num"/>
        <result property="unresolvedNum" column="unresolved_num"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectConsultationVo">
        select id,
               type,
               question,
               answer,
               resolved_num,
               unresolved_num,
               create_by,
               create_time,
               update_by,
               update_time,
               remark
        from wxapp_consultation
    </sql>

    <select id="selectConsultationList" parameterType="Consultation" resultMap="ConsultationResult">
        <include refid="selectConsultationVo"/>
        <where>
            <if test="type != null ">and type = #{type}</if>
            <if test="question != null  and question != ''">and question like concat('%', #{question}, '%')</if>
            <if test="answer != null  and answer != ''">and answer = #{answer}</if>
            <if test="resolvedNum != null ">and resolved_num = #{resolvedNum}</if>
            <if test="unresolvedNum != null ">and unresolved_num = #{unresolvedNum}</if>
        </where>
    </select>

    <select id="selectConsultationById" parameterType="Long" resultMap="ConsultationResult">
        <include refid="selectConsultationVo"/>
        where id = #{id}
    </select>

    <insert id="insertConsultation" parameterType="Consultation" useGeneratedKeys="true" keyProperty="id">
        insert into wxapp_consultation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="type != null">type,</if>
            <if test="question != null and question != ''">question,</if>
            <if test="answer != null">answer,</if>
            <if test="resolvedNum != null">resolved_num,</if>
            <if test="unresolvedNum != null">unresolved_num,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="type != null">#{type},</if>
            <if test="question != null and question != ''">#{question},</if>
            <if test="answer != null">#{answer},</if>
            <if test="resolvedNum != null">#{resolvedNum},</if>
            <if test="unresolvedNum != null">#{unresolvedNum},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateConsultation" parameterType="Consultation">
        update wxapp_consultation
        <trim prefix="SET" suffixOverrides=",">
            <if test="type != null">type = #{type},</if>
            <if test="question != null and question != ''">question = #{question},</if>
            <if test="answer != null">answer = #{answer},</if>
            <if test="resolvedNum != null">resolved_num = #{resolvedNum},</if>
            <if test="unresolvedNum != null">unresolved_num = #{unresolvedNum},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteConsultationById" parameterType="Long">
        delete
        from wxapp_consultation
        where id = #{id}
    </delete>

    <delete id="deleteConsultationByIds" parameterType="String">
        delete from wxapp_consultation where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>