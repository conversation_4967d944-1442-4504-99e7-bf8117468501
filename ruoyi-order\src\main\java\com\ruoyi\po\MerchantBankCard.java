package com.ruoyi.po;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class MerchantBankCard implements Serializable {
    private static final long serialVersionUID = -11933759361910611L;
    
    private Integer id;
    /**
    * 商户id
    */
    private Integer mchId;
    /**
    * 银行卡号
    */
    private String bankCardNo;
    /**
    * 开户总行编码
    */
    private String headBankCode;
    /**
    * 开户支行编码
    */
    private String bankCode;
    /**
    * 银行账户户名
    */
    private String bankCertName;

    /**
     * 账户类型01对公 02对私 03非法人
     */
    private String accountType;
    /**
    * 联行号
    */
    private String contactLine;
    /**
    * 银行名称
    */
    private String bankName;
    /**
    * 开户支行名称
    */
    private String branchName;
    /**
    * 开户支行所在省
    */
    private String branchProvince;
    /**
    * 开户支行所在市
    */
    private String branchCity;
    /**
    * 持卡人证件号码 01 身份证
    */
    private String certType;
    
    private String certNo;
    /**
    * 结算人身份证有效期
    */
    private String certNoExpDate;
    /**
    * 持卡人地址
    */
    private String cardHolderAddress;
    /**
    * 银行卡正面
    */
    private String bankCardA;
    /**
    * 银行卡反面
    */
    private String bankCardB;
    /**
    * 手持银行卡
    */
    private String handHoldBankCard;
    
    private Date createTime;
    
    private Date updateTime;


    public MerchantBankCard(){};
    public MerchantBankCard(String bankCardNo,
                            String bankCertName, String accountType, String contactLine, String bankName,String branchProvince,String branchCity,
                            String certType, String certNo,String cardHolderAddress,
                            String bankCardA, String bankCardB) {
        this.bankCardNo = bankCardNo;
        this.bankCertName = bankCertName;
        this.accountType = accountType;
        this.contactLine = contactLine;
        this.bankName = bankName;
        this.branchProvince = branchProvince;
        this.branchCity = branchCity;
        this.certType = certType;
        this.certNo = certNo;
        this.cardHolderAddress = cardHolderAddress;
        this.bankCardA = bankCardA;
        this.bankCardB = bankCardB;
    }

    public MerchantBankCard(String bankCardNo,String accountType, String bankCertName,  String contactLine, String bankName, String branchProvince, String branchCity,
                            String certNo,  String cardHolderAddress) {
        this.bankCardNo = bankCardNo;
        this.accountType = accountType;
        this.bankCertName = bankCertName;
        this.contactLine = contactLine;
        this.bankName = bankName;
        this.branchProvince = branchProvince;
        this.branchCity = branchCity;
        this.certNo = certNo;
        this.cardHolderAddress = cardHolderAddress;
    }
}