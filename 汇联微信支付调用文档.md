# 汇联微信支付调用文档

## 📋 目录

- [系统概述](#系统概述)
- [微信支付架构](#微信支付架构)
- [调用流程](#调用流程)
- [配置说明](#配置说明)
- [接口详解](#接口详解)
- [实际代码示例](#实际代码示例)
- [前端调用](#前端调用)

## 🏗️ 系统概述

### 微信支付调用链路

本系统通过汇联支付网关调用微信支付，实现了多层架构的支付解决方案：

```
微信小程序/H5 → 系统后端 → 汇联网关 → 支付服务商 → 微信支付API
```

### 核心优势

- ✅ **统一接口**: 一套API支持多个微信支付服务商
- ✅ **智能路由**: 自动选择最优微信支付通道
- ✅ **高可用性**: 多个服务商容灾备份
- ✅ **安全可靠**: RSA签名 + HTTPS双重保障

## 🏗️ 微信支付架构

### 架构流程图

```mermaid
sequenceDiagram
    participant MP as 微信小程序
    participant Backend as 系统后端
    participant <PERSON><PERSON><PERSON> as 汇联网关
    participant PSP as 支付服务商
    participant We<PERSON><PERSON> as 微信支付API

    MP->>Backend: 1. 发起微信支付
    Backend->>Backend: 2. 构建JsPay参数
    Backend->>Backend: 3. RSA签名
    Backend->>HuiLian: 4. 调用汇联JS支付接口
    HuiLian->>PSP: 5. 路由到最优服务商
    PSP->>WeChat: 6. 调用微信统一下单API
    WeChat-->>PSP: 7. 返回prepay_id
    PSP-->>HuiLian: 8. 返回微信支付参数
    HuiLian-->>Backend: 9. 返回payInfo
    Backend-->>MP: 10. 返回微信支付参数
    MP->>WeChat: 11. 调用wx.requestPayment
    WeChat->>PSP: 12. 支付结果回调
    PSP->>HuiLian: 13. 支付成功通知
    HuiLian->>Backend: 14. 回调通知
    Backend->>Backend: 15. 更新订单状态
```

### 微信支付服务商

| 服务商 | 代码 | 特点 | 微信支付能力 |
|--------|------|------|-------------|
| 网商银行 | `mybank` | 阿里系，稳定性高 | JSAPI、Native、APP、H5 |
| 随行付 | `vbill` | 覆盖面广，费率优惠 | JSAPI、Native、APP、H5 |
| 新大陆 | `starpos` | 技术先进，处理速度快 | JSAPI、Native、APP、H5 |
| 汇付天下 | `huifu` | 老牌支付，经验丰富 | JSAPI、Native、APP、H5 |
| 合利宝 | `helipay` | 多通道，高成功率 | JSAPI、Native、APP、H5 |
| 易宝支付 | `yeepay` | 行业专业，定制化强 | JSAPI、Native、APP、H5 |
| 乐刷 | `yeahka` | 移动支付专家 | JSAPI、Native、APP、H5 |

## 🔄 调用流程

### 1. 微信支付发起流程

#### 步骤1: 构建支付参数
```java
// 构建微信JSAPI支付参数
JsPay jsPay = new JsPay();
jsPay.setOutTradeNo(orderId);                    // 商户订单号
jsPay.setWxAppId(MyConfig.WX_APPID);            // 微信公众号AppID
jsPay.setBody("商品描述");                       // 商品描述
jsPay.setHlMerchantId(merchantId);              // 汇联商户号
jsPay.setTotalAmount(String.valueOf(amount));   // 支付金额(分)
jsPay.setChannelType("WX");                     // 指定微信支付
jsPay.setOpenId(userOpenId);                    // 用户微信OpenID
jsPay.setNotifyUrl(notifyUrl);                  // 支付回调地址
```

#### 步骤2: 选择机构和签名
```java
// 根据用户部门选择汇联机构号
String agencyNo = MyConfig.AgencyNo_yc;         // 云创机构号: 11301
String privateKey = MyConfig.PrivateKey_yc;     // 云创私钥

// 潘朵拉用户使用不同机构号
if (isPDLUser) {
    agencyNo = MyConfig.AgencyNo;               // 潘朵拉机构号: 1228026
    privateKey = MyConfig.PrivateKey;           // 潘朵拉私钥
}

// 构建请求参数并RSA签名
String param = HttpsMain.format(jsPay, FunctionEnum.JS_PAY, agencyNo, privateKey);
```

#### 步骤3: 调用汇联网关
```java
// 发送HTTPS请求到汇联网关
String response = HttpsMain.httpReq(MyConfig.PayUrl, param);

// 验证汇联返回签名
if (!RsaUtil.verifyResponseSign(response)) {
    throw new Exception("汇联返回签名验证失败");
}
```

#### 步骤4: 解析微信支付参数
```java
JSONObject jsonObject = JSON.parseObject(response);
if ("S".equals(jsonObject.getString("status")) && "0000".equals(jsonObject.getString("code"))) {
    // 解析微信支付参数
    Map<String, String> result = JSON.parseObject(jsonObject.getString("data"), 
                                                 new TypeReference<Map<String, String>>() {});
    
    // 返回微信支付所需的payInfo
    String payInfo = result.get("payInfo");
    return payInfo;
}
```

### 2. 微信支付参数结构

#### 汇联返回的微信支付参数
```json
{
    "channelType": "WX",
    "data": {
        "appId": "wx8f7a9346308ee56f",
        "timeStamp": "1640995200",
        "nonceStr": "abc123def456",
        "package": "prepay_id=wx20231201123456789",
        "signType": "RSA",
        "paySign": "signature_string"
    }
}
```

#### 微信小程序调用参数
```javascript
// 解析汇联返回的payInfo
const payInfo = JSON.parse(response.data);

// 调用微信支付
wx.requestPayment({
    appId: payInfo.appId,
    timeStamp: payInfo.timeStamp,
    nonceStr: payInfo.nonceStr,
    package: payInfo.package,
    signType: payInfo.signType,
    paySign: payInfo.paySign,
    success: function(res) {
        console.log('支付成功', res);
    },
    fail: function(res) {
        console.log('支付失败', res);
    }
});
```

## ⚙️ 配置说明

### 核心配置参数

#### 微信支付配置
```java
public interface MyConfig {
    // 微信支付公众号AppID (用于支付)
    String WX_APPID = "wx8f7a9346308ee56f";
    
    // 汇联机构号配置
    String AgencyNo = "1228026";        // 潘朵拉机构号
    String AgencyNo_yc = "11301";       // 云创机构号
    
    // 汇联商户号
    String HlMerchant = "*************";
    
    // 微信支付回调地址
    String Notice_URL = "https://fotoboxserve.cameraon.store/pay/notice";
    
    // 汇联支付网关
    String PayUrl = "https://open.huilian.cloud/pay";
    
    // RSA私钥 (用于签名)
    String PrivateKey = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSk...";
    String PrivateKey_yc = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKUwggSh...";
    
    // 汇联公钥 (用于验签)
    String huilian_publicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAs53yaPCd...";
}
```

#### 微信小程序配置
```yaml
# application.yml
wxapp:
  wechat:
    applets:
      app-id: wxb4c2d62fc737311f          # 小程序AppID
      app-secret: f37690c219544053db752bbe423d4080  # 小程序密钥

# 注意: 小程序AppID和支付AppID是不同的
# 小程序AppID: 用于登录和获取用户信息
# 支付AppID: 用于微信支付调用
```

### 商户号配置

| 商户类型 | 汇联商户号 | 微信商户号 | 说明 |
|---------|-----------|-----------|------|
| 潘朵拉云创 | 1386125312629 | 对应微信商户号 | 强哥名下 |
| 云创 | 123802148113840 | 对应微信商户号 | 凤凤名下 |
| 乐高定制 | 1398949712874 | 对应微信商户号 | 乐高专用 |

## 📡 接口详解

### 1. 微信JSAPI支付接口

**接口地址**: `POST /pay/jsPay`

**功能说明**: 创建微信JSAPI支付订单，返回微信支付所需参数

**请求参数**:
```json
{
    "totalAmount": 100,           // 支付金额(分)，必填
    "openId": "oZIa06iUqoN...",   // 微信用户OpenID，必填
    "userId": "user_123",         // 系统用户ID，必填
    "outTradeNo": "order_123",    // 商户订单号，必填
    "mchid": "*************"      // 汇联商户号，必填
}
```

**响应参数**:
```json
{
    "channelType": "WX",
    "data": "{\"appId\":\"wx8f7a9346308ee56f\",\"timeStamp\":\"1640995200\",\"nonceStr\":\"abc123\",\"package\":\"prepay_id=wx123\",\"signType\":\"RSA\",\"paySign\":\"signature\"}"
}
```

### 2. 微信支付回调接口

**接口地址**: `POST /pay/notice`

**功能说明**: 接收汇联转发的微信支付结果通知

**回调参数**:
```json
{
    "status": "S",
    "code": "0000",
    "msg": "成功",
    "data": {
        "outTradeNo": "order_123",
        "channelType": "WX",
        "payChannelOrderNo": "4200001234567890123",  // 微信交易号
        "gmtPayment": "2023-12-01 12:00:00",
        "totalAmount": "100"
    },
    "sign": "汇联签名"
}
```

**回调处理**:
```java
@PostMapping("/notice")
public String wechatPayNotify(HttpServletRequest request) throws IOException {
    // 1. 读取回调数据
    String requestBody = getRequestBody(request);
    
    // 2. 验证汇联签名
    if (!RsaUtil.verifyResponseSign(requestBody)) {
        log.error("微信支付回调签名验证失败");
        return "fail";
    }
    
    // 3. 解析支付结果
    JSONObject data = JSONObject.parseObject(requestBody).getJSONObject("data");
    String orderId = data.getString("outTradeNo");
    String wechatTransactionId = data.getString("payChannelOrderNo");
    
    // 4. 更新订单状态
    OrderPrinter order = orderService.getByOrderId(orderId);
    order.setOrderStatus(1);  // 已支付
    order.setPayWay(1);       // 微信支付
    order.setTransactionId(wechatTransactionId);
    order.setPayTime(new Date());
    orderService.updateOrder(order);
    
    // 5. 通知相关系统
    notifyPaymentSuccess(orderId);
    
    return "success";
}
```

## 💻 实际代码示例

### 1. 系统中的微信支付控制器 (HLPay.java)

```java
@Slf4j
@Controller
@RequestMapping("/pay")
public class HLPay {
    @Autowired
    private IOrderCameraService orderCameraService;
    @Autowired
    private ISysUserService userService;
    @Autowired
    private IDeviceCameraService deviceCameraService;
    @Autowired
    private IFeeService feeService;

    /**
     * 发起JS支付 - 系统实际代码
     */
    @ResponseBody
    @PostMapping("/jsPay")
    public String doJsPay(int totalAmount, String openId, String userId, String outTradeNo, String mchid) throws Exception {

        // 1. 验证订单
        OrderCamera orderCamera = orderCameraService.selectOrderCameraByOrderId(outTradeNo);
        if (orderCamera == null) {
            return "订单号错误";
        }

        // 2. 验证用户
        SysUser user = userService.selectUserById(orderCamera.getUserId());
        if (user == null) {
            return "用户绑定错误";
        }

        // 3. 判断是否为潘朵拉用户
        boolean isPDL = false;
        String ancestors = user.getDept().getAncestors();
        Long deptId = user.getDept().getDeptId();
        if (ancestors.contains("201") || deptId == 201) {
            isPDL = true;
        }

        // 4. 检查订单状态
        if (orderCamera.getOrderStatus() != 0) {
            return "订单状态错误" + orderCamera.getOrderStatus();
        }

        // 5. 构建微信支付参数
        JsPay jsPay = new JsPay();
        jsPay.setOutTradeNo(outTradeNo);                    // 订单号
        jsPay.setWxAppId(MyConfig.WX_APPID);               // 微信支付AppID
        jsPay.setBody("拍照付款");                          // 商品描述
        jsPay.setHlMerchantId(mchid);                      // 汇联商户号
        jsPay.setTotalAmount(String.valueOf(totalAmount)); // 支付金额(分)

        // 6. 自动识别支付渠道 (微信/支付宝)
        boolean aNull = openId == null || openId.equals("") || openId.equals("null");
        jsPay.setOpenId(aNull ? userId : openId);
        orderCamera.setOpenid(aNull ? userId : openId);
        jsPay.setChannelType(aNull ? ChannelTypeEnum.ALI.getCode() : ChannelTypeEnum.WX.getCode());

        // 7. 设置回调地址
        jsPay.setNotifyUrl(MyConfig.Notice_URL);
        jsPay.setSucUrl("https://fotobox.cameraon.store/digitalPhotoTongNiu?orderId=" + outTradeNo);

        // 8. 验证设备
        DeviceCamera device = deviceCameraService.getById(orderCamera.getDeviceId());
        if (device == null)
            return "设备绑定异常";

        // 9. 处理分账逻辑 (系统特有的分账机制)
        String subAccount = device.getSubAccount();
        Fee photoFee = feeService.query().eq("photo_type", orderCamera.getPhotoType()).one();

        // ... 分账处理逻辑 (省略详细代码)

        // 10. 选择汇联机构号和私钥
        String myAgencyNo = MyConfig.AgencyNo_yc;      // 默认云创机构号
        String myPrivateKey = MyConfig.PrivateKey_yc;   // 默认云创私钥
        if (isPDL) {
            myAgencyNo = MyConfig.AgencyNo;             // 潘朵拉机构号
            myPrivateKey = MyConfig.PrivateKey;         // 潘朵拉私钥
        }

        // 11. 构建请求参数并签名
        String param = HttpsMain.format(jsPay, FunctionEnum.JS_PAY, myAgencyNo, myPrivateKey);
        log.info("请求报文{}", param);

        // 12. 发送到汇联网关
        String response = HttpsMain.httpReq(MyConfig.PayUrl, param);
        log.info("响应报文{}", response);

        // 13. 验证汇联返回签名
        if (!RsaUtil.verifyResponseSign(response)) {
            throw new Exception("验签失败");
        }

        // 14. 解析响应并返回支付参数
        JSONObject jsonObject = JSON.parseObject(response);
        String status = jsonObject.getString("status");
        String code = jsonObject.getString("code");

        if ("S".equals(status) && "0000".equals(code)) {
            Map<String, String> res = JSON.parseObject(jsonObject.getString("data"),
                                                      new TypeReference<Map<String, String>>() {});
            Map<String, String> map = new HashMap<>();
            map.put("channelType", jsPay.getChannelType());

            if (jsPay.getChannelType().equals(ChannelTypeEnum.ALI.getCode())) {
                map.put("data", res.get("prePayId"));      // 支付宝
            } else {
                map.put("data", res.get("payInfo"));       // 微信支付
            }

            log.info("返回出去的数据{}", map);
            return JSON.toJSONString(map);
        } else {
            String msg = jsonObject.getString("msg");
            throw new Exception(msg);
        }
    }
}
```

### 2. 系统中的支付回调处理 (HLPay.java)

```java
/**
 * 汇联支付回调处理 - 系统实际代码
 */
@PostMapping("/notice")
public String notice(HttpServletRequest request) throws IOException {
    // 1. 读取回调数据
    BufferedReader reader = request.getReader();
    StringBuffer resultBuffer = new StringBuffer();
    String line = null;
    while ((line = reader.readLine()) != null) {
        resultBuffer.append(line);
    }
    String response = resultBuffer.toString();
    log.info("汇联回调数据: {}", response);

    // 2. 解析回调数据
    JSONObject jsonObject = JSONObject.parseObject(response);
    JSONObject data = jsonObject.getJSONObject("data");

    String orderId = data.getString("outTradeNo");        // 订单号
    String channelType = data.getString("channelType");   // 支付渠道 WX/ALI
    String transactionId = data.getString("payChannelOrderNo"); // 微信交易号
    Date payTime = data.getDate("gmtPayment");           // 支付时间

    // 3. 更新订单状态
    OrderCamera orderCamera = orderCameraService.selectOrderCameraByOrderId(orderId);
    orderCamera.setPayTime(payTime);
    orderCamera.setTransactionId(transactionId);
    orderCamera.setOrderStatus(1L);  // 已支付

    // 4. 设置支付方式
    if (channelType.equals("WX")) {
        orderCamera.setPayWay(1L);   // 微信支付
    } else {
        orderCamera.setPayWay(2L);   // 支付宝
    }

    // 5. 保存订单
    orderCameraService.updateOrderCamera(orderCamera);

    // 6. 通过WebSocket通知设备支付成功
    try {
        WebSocketServer.sendInfo(orderCamera.getDeviceId(), "zfcg");
        log.info("WebSocket通知发送成功 - 设备ID: {}", orderCamera.getDeviceId());
    } catch (Exception e) {
        log.error("WebSocket通知发送失败", e);
    }

    return "success";
}
```

### 3. JsPay实体类 (系统实际代码)

```java
@Data
public class JsPay extends SimplePay {

    /**必填参数**/
    private String channelType;  // 支付渠道 WX/ALI
    private String openId;       // 用户OpenID

    /**可选参数*/
    private String wxAppId;      // 微信AppID
    private String sucUrl;       // 成功跳转地址
}
```

### 4. MyConfig配置类 (系统实际代码)

```java
public interface MyConfig {

    /**
     * 支付公众号(汇联)
     */
    String WX_APPID = "wx8f7a9346308ee56f";

    /**
     * 汇联机构号
     */
    String AgencyNo = "1228026";        // 潘朵拉机构号
    String AgencyNo_yc = "11301";       // 云创机构号

    /**
     * 商户号
     */
    String HlMerchant = "*************";

    /**
     * 回调通知地址
     */
    String Notice_URL = "https://fotoboxserve.cameraon.store/pay/notice";

    /**
     * 汇联公钥 (用于验签)
     */
    String huilian_publicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAs53yaPCdlEQ7vaXwKzM8vujx8TS8vvRl98dXvk7Nbb/w0QciU8IeFwTeuhtomb4W604kbZQMlDFL8csWUr1mhndRmqmA7QvqJwX1CH0+vA+EbuBbp7cIRGjUYp4nepZQ6Wkd2sJmw9LERzcnaODN2QzOyebDzF7+0uWdmoeTu4GP0VKqCVnmugvXR80rkQ/QHDGqFRnwpKLkgCST3gVOC1pGEt7IcYXj2FYbQGLgep6EwnYF2LcIR+wF4Z/hUBHdgpcdpok0+hBQnznEIV5VHokz2le0pjrrb6fhfrsLkNOyR+PbeaBJ9wQkUIrekHOmK+9xLJHjzf7kZqJydtnZgwIDAQAB";

    /**
     * 私钥 (潘朵拉)
     */
    String PrivateKey = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCuC5oltcucmEzv5s72Q8kAiLJQWRtqg1qHHSm6a28BwtEsOVdew9eoR+BEgrlkX20Q3NFqWTTIwCtZRIxLNRy1FJyebxi9JL4w7nJe7+bRE4+ueTGSjKhiDnGMsaLiYgZ/cHNcj/sMaFZe5x1ZSAGIa+0VCqmRVhV02QCWCNYJay8XYDmYCqvMOlcaZPvA5Ekq5TRe5pcs9ZQCXGX5l1yBqMcCMAkhO25100rj1IP9nqeHHo1br0tK+PWY8eQbiHmuIN/8JS5Hn/k0eL2E7ZCryWkTIO7Xnl+uTxQuAw/KtmwCbgafKtdmyIDXdA9XPXT/LAdDhbHd5OiKLQTVHuHJAgMBAAECggEAGQLd0hGAAy73z3nxv/4ZwpPiXB2SDQp3Vfdg6cNKnowqlxpebeXi9fHuTqoijkQQXl39UjUjmr3S/O8W4i/twjAGGdaJTZUcJ9f8Y5xCJUWUXlRYRWElSYId20QjzjicCnBUg7bFMxFDaDv6QbxRTBOGbIGtwI8IYlRU+zql15BsmdoqLhiUqO0Lt1G9OYTBsS5loGLRXWq+QOS6N8xgr/+wY8yVkcUMIikFTxpbH90pqsiGo1UnU7f/5XooPdU9hGhlX4AbssZJE5tLSg2ukngZLKO0+Hm33hhx8dPnnPTwKPgsD6CmNU1bxJOjotWlI04fYeikBdC+xS3ON2bdyQKBgQDrgFkS9882fyP1JeU6i4AmpWwQwnfMNjcIGCnNjKEJ79xd+cZE7hwkLAuOMT0w4I/PR/nmIi9rHlvzMgYoFXmVFGaRDDBbMLkN10QX6zQvyo0tgw9u6vhJUCO+fZtQ6d3ZrGqYEJiF7Z9s2d/KHQDO2xJLkX3r2Q0QT7aiJ2vMZwKBgQC9MdXzWoRW+rvnTM0LpyzsKLnPbzCbcX7JWyOrYmY+qX2uQItvU/D5hl1ceFJwds4cJhN9bCwjIIIp/kTKUlhNUKo4+iLybvjdEb0FkhY3Ed9xatwEIfYMJwkjgPMYgEfTCBnNQ7FrPj3e0wc+lQtXc1Q1y8xbBKi/4BgGqW4CTwKBgQCRJDZBqXcNCEjaAvQHJAjkSJmUrrX0pwk9tN62r1Bk9m6jgJhAzVuMs5Sfmxsyb4RSZPLQcgs1gq168j63H50iU0Nva8q1uQ6kSCLQdwZf2f8uqJKvcqN5U6nj8jfyI2C2kjvT4o6OJEhYZuM1BrEDzCK3bkDkRY52mhpeT9txSwKBgQCznEE6Qz+J94U1o9KxcmcSV+N2i1+A4rFz5SMqzEK20F9xcvMsoGsP6EBvzgatoAwx6u/rzSrlujH4Pdz+mkgcNi0z5DwTHckCzpk9VZ/jbgNLgCAZiEg250QkwYGHTBljvbgJB2/CmRdRl9Oz1Pksb1+Npsl9yyGM7ujAQk8evwKBgG0mWFoQeDHjvkmrugOD3Y+4He2lwkXlnEeDedsDtJWZKNqDqZrso3bjMB2RN3vsGx8KPJS7/bYTUTUC//GI5G9RP1W0WEKyfZwqwpGSDy6LBsOLfihrPBB2HDvkCJA+SfDHQjBxZimTK7RGITNFDuwzWgKQs5eT43qO6QbQRjgs";

    /**
     * 私钥 (云创)
     */
    String PrivateKey_yc = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCd63oRRgnP8aryRK0Hjl+64DMl5TCnldEogi5/HtrXhz5KmftdZoO7gcSfMyJhOGsW+PkpSpKG8h9JyjHPpPVTthbByUjjuzRVOQM9WFoQ1iPgDcWmDalWVEguRl++CzlrpbQFiZQlg4DQICH+kt91CSVHY35gIiG7WTbSqaJzja+/uuF15SRpBNQiDUP4+5VN+SaUwBzezrDnK2eWohuBtOKL2oArp0QQ2Nao4uiC8vY1xBWKs/+oBF17UeyabQ3ITXx9D+2UU+CyLXlWv1W/4s1lxZpfqzJQN7cahZ63PjsmnjTd8sdhH/EeqFK89I95NaWGtTs/Q9CXr5F4ZgD9AgMBAAECggEAaS4ndU3Sxz9M/mOjYz7+n7coGftWtqfgUTyzv1NLqvWXFZUNiJ3463B5BxfFtD8hhIF7YncatOqa0EjteEGEbKcA7T0lYRiUuJZqg/dOtZwK9PX3SKwctzUXhkq1Bhzvt7T7/BwwIfQ+XWCpg6dLUeYvgCbSSzEwilnXW+REIPWX1CcKitzDaBnvO1UxpWdhhgO5pWo3BXrKTmSKKxkbycFdimGImDw53NfN2MekvVdFIOxzjJUkBUPpOzusG101+iwVq9kr4YlIe/uno10JgtIA5NGvnvg1mUlxS9qkwdDkv6KX6mnxAiVllCwvRIbMhjP5DMCzr91Q05yLSrpMOQKBgQDMNFkdsp4crB9qVej5gAo+HO3S/mvgeRyrTSYmIS4QR+6e+eKlmOumYwNhYJY4N8/ifoNTWH/TidL/1lI7yvvkzcM5hl/yT3OQkPZKw4/tgDro0SSOsnLUaM6q6KzA2TluC5uxYi4hJS/g37VHLK7sHPC/mRZxVoMK8krFeV0ABwKBgQDF+bfANZNOgnZ6XTA22F6J0gPPThk7rvvEfrt8CfSDgiPG6KTlV6VON0u2bPIvcvWNKea1B4P3OfsW93D6y4Q/g9XiN7w3lzAuITb5F+oFZ1ESUktemf8KZEQ8jGyKI2DaMVXSAffXTH9m0DkEE2vJ3/GHLvS6ukkyHdlxb4xt2wKBgQCZ8hEI+YKHfR+F69RiXCvcnbSRb2rmZIe/gL702ei/L2tugFZknz9wRY7j14wIP9NCHjxlFf/v+ySN6QY/u4xg9tppwKsOouUtpIt426vi0dwXbqxPw6ocBhNf919dV6YbFzc96BV8uWs57YzdZotMx5ib6Q5pTV4nX3gNHayF+wKBgBSBXOrnoa5MDQF3jXrgxkn4GrTqHer0oNclWT3rugu0+werKQkoHtMPHyRx/FO10IEiiebONHjy5HLohicz1SrO+ORJaZFIc5ETl6INPAv7O4ZuR6gwwx3MbNTopoVYTLvMyFrMIWOHZNq9TmqBqHRsYZPi5S5CBTrPpKn/J8hpAoGBAJk0VITa+ZQonKF8H3iUzaIegZK410eco29bDP4gdtz9SVW9uoMTMLMpeXiftS/SvDvasCHxo/1mWJod4kLdqd72hvzTpXuI6GxI1EhmdJmy7v7AyrAhHkaMeaL4WKLTj/GcfluwbdZdIRrKez7aKCZqQeQz3NAb0100D/9dEyrP";

    /**
     * 汇联支付网关地址
     */
    String PayUrl = "https://open.huilian.cloud/pay";
}
```

## 🌐 前端调用

### 1. 系统中的H5网页支付 (jsPay.html)

```javascript
// 系统实际的前端支付代码
var payInfo;

function doPay() {
    var totalAmount = $('#totalAmount').val();
    var openId = $('#openId').val();
    var userId = $('#userId').val();
    var orderId = $('#orderId').val();
    var mchid = $('#mchid').val();

    $(".safe-pay").css({"pointer-events": "none"});

    $.ajax({
        url: "/pay/jsPay",
        type: "POST",
        dataType: "json",
        data: {
            "totalAmount": Math.round(Number(totalAmount * 100)), // 转换为分
            "openId": openId,
            "userId": userId,
            "outTradeNo": orderId,
            "mchid": mchid,
        },
        success: function (data) {
            console.log("支付接口返回:", data);

            if (data.channelType == "WX") {
                // 微信支付
                payInfo = JSON.parse(data.data);
                onBridgeReady();
            } else {
                // 支付宝支付
                tradePay(data.data);
            }
        },
        error: function (xhr, status, error) {
            $(".safe-pay").css({"pointer-events": "auto"});
            console.error("支付请求失败:", error);
            alert("支付请求失败，请重试");
        }
    });
}

// 微信支付调用
function onBridgeReady() {
    if (typeof WeixinJSBridge == "undefined") {
        if (document.addEventListener) {
            document.addEventListener('WeixinJSBridgeReady', onBridgeReady, false);
        } else if (document.attachEvent) {
            document.attachEvent('WeixinJSBridgeReady', onBridgeReady);
            document.attachEvent('onWeixinJSBridgeReady', onBridgeReady);
        }
    } else {
        WeixinJSBridge.invoke(
            'getBrandWCPayRequest', {
                "appId": payInfo.appId,
                "timeStamp": payInfo.timeStamp,
                "nonceStr": payInfo.nonceStr,
                "package": payInfo.package,
                "signType": payInfo.signType,
                "paySign": payInfo.paySign
            },
            function (res) {
                $(".safe-pay").css({"pointer-events": "auto"});

                if (res.err_msg == "get_brand_wcpay_request:ok") {
                    // 支付成功
                    window.location = "/success";
                } else {
                    // 支付失败或取消
                    alert(JSON.stringify(res));
                }
            }
        );
    }
}

// 支付宝支付调用
function tradePay(data) {
    var form = $('<form method="post" action="https://openapi.alipay.com/gateway.do"></form>');
    form.append('<input type="hidden" name="biz_content" value="' + data + '">');
    form.appendTo('body').submit();
}
```

### 2. 微信小程序支付调用示例

```javascript
// 微信小程序中的支付调用
function wxAppPay(orderData) {
    wx.request({
        url: 'https://fotoboxserve.cameraon.store/pay/jsPay',
        method: 'POST',
        header: {
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        data: {
            totalAmount: Math.round(orderData.amount * 100), // 转换为分
            openId: wx.getStorageSync('openid'),             // 微信用户openid
            userId: wx.getStorageSync('userId'),             // 系统用户ID
            outTradeNo: orderData.orderId,                   // 订单号
            mchid: '*************'                           // 汇联商户号
        },
        success: function(res) {
            console.log('支付接口调用成功:', res.data);

            if (res.data.channelType === 'WX') {
                // 解析微信支付参数
                const payInfo = JSON.parse(res.data.data);

                // 调用微信支付
                wx.requestPayment({
                    appId: payInfo.appId,
                    timeStamp: payInfo.timeStamp,
                    nonceStr: payInfo.nonceStr,
                    package: payInfo.package,
                    signType: payInfo.signType,
                    paySign: payInfo.paySign,
                    success: function(payRes) {
                        console.log('微信支付成功:', payRes);
                        wx.showToast({
                            title: '支付成功',
                            icon: 'success'
                        });

                        // 跳转到成功页面
                        wx.redirectTo({
                            url: '/pages/success/success?orderId=' + orderData.orderId
                        });
                    },
                    fail: function(payRes) {
                        console.log('微信支付失败:', payRes);

                        if (payRes.errMsg.indexOf('cancel') !== -1) {
                            wx.showToast({
                                title: '支付已取消',
                                icon: 'none'
                            });
                        } else {
                            wx.showToast({
                                title: '支付失败',
                                icon: 'error'
                            });
                        }
                    }
                });
            }
        },
        fail: function(res) {
            console.error('支付接口调用失败:', res);
            wx.showToast({
                title: '网络错误，请重试',
                icon: 'error'
            });
        }
    });
}

// 使用示例
const orderData = {
    orderId: 'ORDER_' + Date.now(),
    amount: 9.99
};

wxAppPay(orderData);
```

## 🔍 关键流程说明

### 1. 支付渠道自动识别

系统中的关键逻辑是根据openId自动识别支付渠道：

```java
// 系统实际代码 - 自动识别微信/支付宝
boolean aNull = openId == null || openId.equals("") || openId.equals("null");
jsPay.setOpenId(aNull ? userId : openId);
jsPay.setChannelType(aNull ? ChannelTypeEnum.ALI.getCode() : ChannelTypeEnum.WX.getCode());

// 如果openId存在 → 微信支付 (WX)
// 如果openId为空 → 支付宝 (ALI)
```

### 2. 机构号选择逻辑

系统根据用户部门自动选择不同的汇联机构号：

```java
// 系统实际代码 - 机构号选择
boolean isPDL = false;
String ancestors = user.getDept().getAncestors();
Long deptId = user.getDept().getDeptId();

if (ancestors.contains("201") || deptId == 201) {
    isPDL = true;
}

String myAgencyNo = MyConfig.AgencyNo_yc;      // 默认云创: 11301
String myPrivateKey = MyConfig.PrivateKey_yc;   // 云创私钥

if (isPDL) {
    myAgencyNo = MyConfig.AgencyNo;             // 潘朵拉: 1228026
    myPrivateKey = MyConfig.PrivateKey;         // 潘朵拉私钥
}
```

### 3. 支付参数返回格式

系统返回的支付参数格式：

```java
// 系统实际代码 - 返回格式处理
Map<String, String> map = new HashMap<>();
map.put("channelType", jsPay.getChannelType());

if (jsPay.getChannelType().equals(ChannelTypeEnum.ALI.getCode())) {
    map.put("data", res.get("prePayId"));      // 支付宝返回prePayId
} else {
    map.put("data", res.get("payInfo"));       // 微信支付返回payInfo
}

return JSON.toJSONString(map);
```

### 4. WebSocket通知机制

支付成功后通过WebSocket通知设备：

```java
// 系统实际代码 - WebSocket通知
try {
    WebSocketServer.sendInfo(orderCamera.getDeviceId(), "zfcg");
    log.info("WebSocket通知发送成功 - 设备ID: {}", orderCamera.getDeviceId());
} catch (Exception e) {
    log.error("WebSocket通知发送失败", e);
}
```

## 📊 系统特有功能

### 1. 分账处理

系统包含复杂的分账逻辑（代码中有相关处理，但具体实现较复杂）：

```java
// 系统实际代码片段 - 分账相关
String subAccount = device.getSubAccount();
Fee photoFee = feeService.query().eq("photo_type", orderCamera.getPhotoType()).one();
// ... 分账处理逻辑
```

### 2. 设备绑定验证

每个订单都需要验证设备信息：

```java
// 系统实际代码 - 设备验证
DeviceCamera device = deviceCameraService.getById(orderCamera.getDeviceId());
if (device == null)
    return "设备绑定异常";
```

### 3. 订单状态管理

系统中的订单状态定义：

```java
// 订单状态说明
// 0: 未支付
// 1: 已支付
// 其他状态根据业务需要定义
```

## 🔧 配置要点

### 1. 关键配置参数

| 配置项 | 值 | 说明 |
|--------|----|----- |
| WX_APPID | wx8f7a9346308ee56f | 微信支付公众号AppID |
| AgencyNo | 1228026 | 潘朵拉汇联机构号 |
| AgencyNo_yc | 11301 | 云创汇联机构号 |
| HlMerchant | ************* | 汇联商户号 |
| Notice_URL | https://fotoboxserve.cameraon.store/pay/notice | 支付回调地址 |
| PayUrl | https://open.huilian.cloud/pay | 汇联支付网关 |

### 2. 环境区分

- **生产环境**: 使用正式的汇联网关和配置
- **测试环境**: 可以使用测试商户号进行调试
- **开发环境**: 注意回调地址的配置

---

## 📞 技术支持

### 微信支付相关问题

- **微信支付文档**: https://pay.weixin.qq.com/wiki/doc/api/
- **汇联支付文档**: 内部技术文档
- **问题反馈**: 技术支持团队

### 常见问题

1. **Q: 微信支付参数签名错误？**
   A: 检查汇联私钥配置和签名算法

2. **Q: 微信支付回调未收到？**
   A: 检查回调地址配置和网络连通性

3. **Q: 支付成功但订单状态未更新？**
   A: 检查回调处理逻辑和数据库事务

---

## 📞 技术支持

### 联系方式

- **技术文档**: 内部技术文档系统
- **问题反馈**: 开发团队
- **紧急联系**: 技术支持

### 常见问题

#### Q1: 微信支付参数签名错误？
**A**: 检查以下几点：
1. 确认使用正确的汇联私钥
2. 验证RSA签名算法是否正确
3. 检查请求参数格式是否符合要求

#### Q2: 微信支付回调未收到？
**A**: 检查以下配置：
1. 回调地址是否可以外网访问
2. HTTPS证书是否有效
3. 防火墙是否允许汇联IP访问

#### Q3: 支付成功但订单状态未更新？
**A**: 排查以下问题：
1. 检查回调处理逻辑是否正确
2. 验证数据库事务是否正常提交
3. 查看WebSocket通知是否发送成功

#### Q4: 如何区分微信支付和支付宝？
**A**: 系统通过openId自动识别：
```java
// openId存在 → 微信支付
// openId为空 → 支付宝
boolean aNull = openId == null || openId.equals("") || openId.equals("null");
jsPay.setChannelType(aNull ? ChannelTypeEnum.ALI.getCode() : ChannelTypeEnum.WX.getCode());
```

#### Q5: 潘朵拉和云创用户有什么区别？
**A**: 使用不同的汇联机构号：
- **潘朵拉用户** (部门ID=201): 机构号1228026
- **云创用户** (其他部门): 机构号11301

### 版本信息

- **文档版本**: v2.0
- **最后更新**: 2024-01-15
- **维护团队**: 支付系统开发组

### 更新日志

| 版本 | 日期 | 更新内容 |
|------|------|---------|
| v1.0 | 2024-01-10 | 初始版本，基础微信支付文档 |
| v2.0 | 2024-01-15 | **重大更新：基于系统实际代码重写** |
|      |            | - 使用HLPay.java实际代码示例 |
|      |            | - 添加jsPay.html前端实际代码 |
|      |            | - 包含系统特有的分账和设备验证逻辑 |
|      |            | - 真实的配置参数和机构号选择逻辑 |

### 重要说明

⚠️ **本文档v2.0版本完全基于系统中的实际代码编写，所有代码示例都来自真实的系统实现，确保文档的准确性和可操作性。**

---

**如有问题，请联系技术支持团队。**
