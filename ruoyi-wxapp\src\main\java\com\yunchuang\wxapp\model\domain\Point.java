package com.yunchuang.wxapp.model.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 点位对象 wxapp_point
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("wxapp_point")
public class Point extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 点位ID
     */
    private Long id;

    /**
     * 点位名称
     */
    @Excel(name = "点位名称")
    private String pointName;

    /**
     * 省
     */
    @Excel(name = "省")
    private String province;

    /**
     * 市
     */
    @Excel(name = "市")
    private String city;

    /**
     * 区、县
     */
    @Excel(name = "区、县")
    private String district;

    /**
     * 除省市区外的详细地址
     */
    @Excel(name = "除省市区外的详细地址")
    private String detailAddress;

    /**
     * 经度
     */
    @Excel(name = "经度")
    private Double longitude;

    /**
     * 纬度
     */
    @Excel(name = "纬度")
    private Double latitude;

    /**
     * 营业时间
     */
    @Excel(name = "营业时间")
    private String openingHours;

    /**
     * 收藏人数
     */
    @Excel(name = "收藏人数")
    private Long favoriteCount;

    /**
     * 点位状态
     */
    @Excel(name = "点位状态")
    private Integer status;

}
