package com.ruoyi.web.controller.pay;

import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.crypto.digest.HmacAlgorithm;
import com.alibaba.fastjson2.JSON;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.config.MyConfig;
import com.ruoyi.device.domain.DeviceCamera;
import com.ruoyi.device.service.IDeviceCameraService;
import com.ruoyi.dto.*;
import com.ruoyi.order.domain.OrderCamera;
import com.ruoyi.order.domain.OrderCollect;
import com.ruoyi.order.service.IOrderCameraService;
import com.ruoyi.order.service.IOrderCollectService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.utils.RsaUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Slf4j
@Controller
@RequestMapping("/LYYPay")
public class LYYPay {

    @Resource
    private RestTemplate restTemplate;
    @Resource
    private IOrderCameraService orderCameraService;

    @Resource
    private IDeviceCameraService deviceCameraService;
    @Resource
    private ISysUserService sysUserService;

    @Autowired
    private IOrderCollectService orderCollectService;

    @ResponseBody
    @GetMapping("/createOrder")
    public Result PreCreateOrder(@RequestParam String deviceId,
                                 @RequestParam String orderId,
                                 @RequestParam int paymentAmount,
                                 @RequestParam String productAbilityCode,
                                 @RequestParam String areaCode,
                                 @RequestParam Integer photoType,
                                 @RequestParam String productDescription) {

        if (deviceId == null) {
            return Result.fail(500, "设备id不能为空", String.valueOf(System.currentTimeMillis()));
        }
        DeviceCamera camera = deviceCameraService.selectDeviceCameraByDeviceId(deviceId);
        if (camera == null) {
            return Result.fail(500, "设备id不存在", String.valueOf(System.currentTimeMillis()));
        }
        if (camera.getDeviceStatus() == 0) {
            return Result.fail(500, "设备已停用", String.valueOf(System.currentTimeMillis()));
        }

        SysUser sysUser = sysUserService.selectUserById(camera.getUserId());
        if (sysUser == null)
            return Result.fail(500, "设备无负责人", String.valueOf(System.currentTimeMillis()));
        if (sysUser.getMerchantId() == null || sysUser.getMerchantId().equals("") || sysUser.getPrivateKey() == null)
            return Result.fail(500, "商家商户号未设置或未完善", String.valueOf(System.currentTimeMillis()));
//        List<Long> roleIds = roleService.selectRoleListByUserId(sysUser.getUserId());
        OrderCamera order = new OrderCamera();
//        if (roleIds.size() > 0) order.setRoleId(roleIds.get(0));
        order.setDeviceName(camera.getDeviceName());
        order.setOrderId(orderId);
        order.setDeviceId(deviceId);
        order.setOrderPrice((long) paymentAmount);
        order.setMoneyReceived((long) paymentAmount);
        order.setPhotoType(photoType);
        order.setPayWay(5L);
        order.setProductDescription(productDescription);
        order.setMchid(sysUser.getMerchantId());

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            Date createTime = dateFormat.parse(DateUtils.getTime());
            order.setCreateTime(createTime);
        } catch (Exception e) {
            log.info("时间类型异常");
            log.info(e.getMessage());
        }
        order.setProductQuantity(1L);
        if (!orderCameraService.save(order))
            return Result.fail(500, "data base error", "");


        LYYRequestBody lyyRequestBody = new LYYRequestBody();
        lyyRequestBody.setPayment_amount(String.valueOf(paymentAmount / 100f));
        lyyRequestBody.setArea_code(areaCode);
        lyyRequestBody.setExpired_time_expression("30s");
        Date nowDate = DateUtils.getNowDate();
        Date date = DateUtils.addHours(nowDate, 1);
        String expired_time = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", date);
        lyyRequestBody.setExpired_time(expired_time);
        lyyRequestBody.setIntroduction("拍照订单");
        lyyRequestBody.setOut_trade_number(orderId);
        lyyRequestBody.setProduct_ability_code(productAbilityCode);
        lyyRequestBody.setProduct_number("SERVICE_PROVIDER");
        lyyRequestBody.setSubscription(new Subscription("HTTP", "http://fotoboxserve.yunchuang.store/LYYPay/notice", ""));
        String requestBody = JSON.toJSONString(lyyRequestBody);

        String sign = DigestUtil.hmac(HmacAlgorithm.HmacSHA512,
                        sysUser.getPrivateKey().getBytes())
                .digestHex(requestBody.getBytes(StandardCharsets.UTF_8));

        HttpHeaders requestHeaders = new HttpHeaders();
        requestHeaders.setContentType(MediaType.APPLICATION_JSON);
        requestHeaders.add("resource_version", "v1.0");
        requestHeaders.add("resource_code", "unified.pay.creation");
        requestHeaders.add("resource_merchant", sysUser.getMerchantId());
        requestHeaders.add("secret_code", sysUser.getPrivateKey());
        requestHeaders.add("Authorization", sign);
        HttpEntity<String> r = new HttpEntity<String>(requestBody, requestHeaders);

        String url = "https://gpayment.starthing.com/expose/apis/gateway.do";
        String result = restTemplate.postForObject(url, r, String.class);
        LYYResp lyyResp = JSON.parseObject(result, LYYResp.class);
        return Result.ok(200, "success", "", lyyResp);
    }


    @ResponseBody
    @PostMapping("/notice")
    public String LYYNotice(@RequestBody LYYNotice lyyNotice) {

        String orderId = lyyNotice.getOut_trade_number();
        OrderCamera orderCamera = orderCameraService.getById(orderId);
        orderCamera.setOrderStatus(1L);
        orderCamera.setPayWay(5L);
        orderCamera.setTransactionId(lyyNotice.getBusiness_number());

        orderCameraService.updateById(orderCamera);

        //更新设备表中的设备总收入
        deviceCameraService.update().eq("device_id", orderCamera.getDeviceId()).setSql("count_price = count_price + " + orderCamera.getOrderPrice()).update();


        //汇总（主订单）
        OrderCollect orderCollect = new OrderCollect();
        orderCollect.setTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM, orderCamera.getCreateTime()));
        orderCollect.setMerchantId(orderCamera.getMchid());
        Long count = orderCollectService.query().eq("merchant_id", orderCollect.getMerchantId()).eq("time", orderCollect.getTime()).count();
        if (count > 0) {
            orderCollectService.update()
                    .setSql("count = count + " + orderCamera.getMoneyReceived())
                    .eq("merchant_id", orderCollect.getMerchantId())
                    .eq("time", orderCollect.getTime()).update();
        } else {
            orderCollect.setCount(orderCamera.getMoneyReceived());
            orderCollectService.save(orderCollect);
        }


        System.out.println(lyyNotice);

        return "success";
    }
}
