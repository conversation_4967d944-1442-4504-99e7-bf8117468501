package com.ruoyi.beauty.service.impl;

import java.util.List;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.beauty.mapper.BeautyMapper;
import com.ruoyi.beauty.domain.Beauty;
import com.ruoyi.beauty.service.IBeautyService;

/**
 * 美颜Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-10-15
 */
@Service
public class BeautyServiceImpl implements IBeautyService 
{
    @Autowired
    private BeautyMapper beautyMapper;

    /**
     * 查询美颜
     * 
     * @param useeId 美颜主键
     * @return 美颜
     */
    @Override
    public Beauty selectBeautyByUseeId(Long useeId)
    {
        return beautyMapper.selectBeautyByUseeId(useeId);
    }

    /**
     * 查询美颜列表
     * 
     * @param beauty 美颜
     * @return 美颜
     */
    @Override
    public List<Beauty> selectBeautyList(Beauty beauty)
    {
        return beautyMapper.selectBeautyList(beauty);
    }

    /**
     * 新增美颜
     * 
     * @param beauty 美颜
     * @return 结果
     */
    @Override
    public int insertBeauty(Beauty beauty)
    {
        return beautyMapper.insertBeauty(beauty);
    }

    /**
     * 修改美颜
     * 
     * @param beauty 美颜
     * @return 结果
     */
    @Override
    public int updateBeauty(Beauty beauty)
    {
        return beautyMapper.updateBeauty(beauty);
    }

    /**
     * 批量删除美颜
     * 
     * @param useeIds 需要删除的美颜主键
     * @return 结果
     */
    @Override
    public int deleteBeautyByUseeIds(Long[] useeIds)
    {
        return beautyMapper.deleteBeautyByUseeIds(useeIds);
    }

    /**
     * 删除美颜信息
     * 
     * @param useeId 美颜主键
     * @return 结果
     */
    @Override
    public int deleteBeautyByUseeId(Long useeId)
    {
        return beautyMapper.deleteBeautyByUseeId(useeId);
    }

}
