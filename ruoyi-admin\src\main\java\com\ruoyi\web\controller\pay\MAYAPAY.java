package com.ruoyi.web.controller.pay;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.device.domain.DeviceCamera;
import com.ruoyi.device.service.IDeviceCameraService;
import com.ruoyi.dto.MAYAPayCreateQRPhReq;
import com.ruoyi.dto.Result;
import com.ruoyi.system.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Base64;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/MAYAPay")
public class MAYAPAY {

    @Resource
    private RestTemplate restTemplate;

    @Autowired
    private IDeviceCameraService deviceCameraService;
    @Autowired
    private ISysUserService sysUserService;
    /**
     * API密钥 - 公钥
     */
    private static final String API_KEY_PUBLIC = "pk-rpwb5YR6EfnKiMsldZqY4hgpvJjuy8hhxW2bVAAiz2N";
    /**
     * API密钥 - 私钥
     */
    private static final String API_KEY_SECRET = "sk-6s9dwnYGFJdZOYu1HCUAfUZctWEf9AjtHIG38kezX8W";

    /**
     * 创建支付码
     */
    @GetMapping("/createPayCode")
    public Result createPayCode(double amount, String orderNo, @RequestParam(required = false) String deviceId) {
        log.info("创建Maya付款码");
        String apiKeyPublic = API_KEY_PUBLIC;
        if (deviceId != null){
            DeviceCamera camera = deviceCameraService.selectDeviceCameraByDeviceId(deviceId);
            if (camera == null) {
                return Result.fail(500, "设备id不存在", String.valueOf(System.currentTimeMillis()));
            }

            if (camera.getDeviceStatus() == 0) {
                return Result.fail(500, "设备已停用", String.valueOf(System.currentTimeMillis()));
            }
            SysUser sysUser = sysUserService.selectUserById(camera.getUserId());
            if (sysUser == null) return Result.fail(500, "设备无负责人", String.valueOf(System.currentTimeMillis()));
            if (StrUtil.isEmpty(sysUser.getMerchantId()) || StrUtil.isEmpty(sysUser.getPrivateKey()))
                return Result.fail(500, "请完善账户信息", String.valueOf(System.currentTimeMillis()));
            apiKeyPublic = sysUser.getMerchantId();
        }
        // 创建请求参数
        MAYAPayCreateQRPhReq mayaPayCreateQRPhReq = new MAYAPayCreateQRPhReq();
        mayaPayCreateQRPhReq.setTotalAmount(new MAYAPayCreateQRPhReq.TotalAmount(amount, "PHP"));
        mayaPayCreateQRPhReq.setRequestReferenceNumber(orderNo);
        mayaPayCreateQRPhReq.setMetadata(new MAYAPayCreateQRPhReq.MetadataObj(new MAYAPayCreateQRPhReq.Pf("0721","yunchuang","guangzhou","608","PHL")));
        String mayaPayCreateQRPhReqStr = JSON.toJSONString(mayaPayCreateQRPhReq);
        // 设置请求头
        HttpHeaders requestHeaders = new HttpHeaders();
        requestHeaders.setContentType(MediaType.APPLICATION_JSON);
        // 添加Authorization - 生成签名 公钥转为base64 开头加上"Basic "
        String authorization = "Basic " + Base64.getEncoder().encodeToString(apiKeyPublic.getBytes());
        requestHeaders.add("Authorization", authorization);
        HttpEntity<String> r = new HttpEntity<String>(mayaPayCreateQRPhReqStr, requestHeaders);
        // 请求地址
        String url = "https://pg-sandbox.paymaya.com/payments/v1/qr/payments";
        String result = restTemplate.postForObject(url, r, String.class);
        // 将json字符串转为json对象
        JSONObject jsonObject = JSON.parseObject(result);
        // 如果返回的json对象中 有paymentId 则表示创建支付码成功
        if (jsonObject != null && jsonObject.containsKey("paymentId")) {
            // 获取返回的数据
            String redirectUrl = jsonObject.getString("redirectUrl");
            log.info("redirectUrl:{}", redirectUrl);
            return Result.ok(200, "创建支付码成功", String.valueOf(System.currentTimeMillis()), redirectUrl);
        }else{
            if (jsonObject != null) {
                log.info("创建支付码失败：{}", jsonObject.getString("return_msg"));
            }
            return Result.fail(500, "创建支付码失败", String.valueOf(System.currentTimeMillis()));
        }
    }

    /**
     * 获取支付状态
     */
    @GetMapping("/getPayStatus")
    public Result getOrderStatus(String orderNo) {
        log.info("获取支付状态");
        if (orderNo == null || orderNo.isEmpty()) {
            return Result.fail(500, "商户订单号不能为空", String.valueOf(System.currentTimeMillis()));
        }
        // 设置请求头
        HttpHeaders requestHeaders = new HttpHeaders();
        requestHeaders.setContentType(MediaType.APPLICATION_JSON);
        // 添加Authorization - 生成签名 私钥转为base64 开头加上"Basic "
        String authorization = "Basic " + Base64.getEncoder().encodeToString(API_KEY_SECRET.getBytes());
        requestHeaders.add("Authorization", authorization);
        HttpEntity<String> r = new HttpEntity<String>(requestHeaders);
        // 请求地址
        String url = "https://pg-sandbox.paymaya.com/payments/v1/payment-rrns/" + orderNo;
        String result = restTemplate.exchange(url, HttpMethod.GET, r, String.class).getBody();
        // 将结果 现在是"[{},{}]" 转为json对象 illegal input， offset 1, char [
        List<JSONObject> jsonObjectList = JSON.parseArray(result, JSONObject.class);
        // 如果返回的json对象中 是个列表 则表示获取支付状态成功
        System.out.println(jsonObjectList);
        return Result.ok(200, "获取支付状态成功", String.valueOf(System.currentTimeMillis()), jsonObjectList);
    }
}
