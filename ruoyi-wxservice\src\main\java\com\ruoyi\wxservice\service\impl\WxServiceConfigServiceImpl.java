package com.ruoyi.wxservice.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.wxservice.mapper.WxServiceConfigMapper;
import com.ruoyi.wxservice.model.domain.WxServiceConfig;
import com.ruoyi.wxservice.service.IWxServiceConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 微信服务号配置 Service业务层处理
 */
@Slf4j
@Service
@Transactional
public class WxServiceConfigServiceImpl extends ServiceImpl<WxServiceConfigMapper, WxServiceConfig> implements IWxServiceConfigService {

    @Resource
    private WxServiceConfigMapper wxServiceConfigMapper;

    /**
     * 删除微信服务号配置
     *
     * @param configId 配置ID
     * @param deptId   部门ID
     * @return 是否成功
     */
    public boolean deleteWxService(Long configId, Long deptId) {
        // 通过部门ID查所有子部门ID（没有服务号的部门）

        int result = wxServiceConfigMapper.deleteById(configId);
        if (result > 0) {
            return true;
        } else {
            log.error("Failed to delete WxServiceConfig with ID: {}", configId);
            return false;
        }
    }
}
