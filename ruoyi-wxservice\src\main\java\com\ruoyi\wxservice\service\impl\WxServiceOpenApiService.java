package com.ruoyi.wxservice.service.impl;

import com.ruoyi.common.utils.WeixinRequestUtil;
import com.ruoyi.wxservice.exception.WxserviceBusinessException;
import com.ruoyi.wxservice.model.domain.WxServiceConfig;
import com.ruoyi.wxservice.model.dto.Menu;
import com.ruoyi.wxservice.model.enums.exception.WxserviceBusinessExceptionCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 微信开放平台Service - 服务号
 */
@Slf4j
@Service
public class WxServiceOpenApiService {

    @Value("${wxservice.wechat.service.app-id:wxd81d77088879563b}")
    private String APP_ID; // 服务号的appId

    @Value("${wxservice.wechat.service.app-secret:21aa3e58d60bf810827e335cb4aa8d97}")
    private String APP_SECRET;  // 服务号的appSecret

    private final WeixinRequestUtil weixinRequestUtil;

    public WxServiceOpenApiService(WeixinRequestUtil weixinRequestUtil) {
        this.weixinRequestUtil = weixinRequestUtil;
    }

    /**
     * 获取公众号的access_token
     *
     * @param wxServiceConfig 微信服务号配置
     * @return access_token
     */
    public String getAccessToken(WxServiceConfig wxServiceConfig) {
        // 配置请求地址
        String url = "https://api.weixin.qq.com/cgi-bin/token";
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("appid", wxServiceConfig.getAppId());
        queryParams.put("secret", wxServiceConfig.getAppSecret());
        queryParams.put("grant_type", "client_credential");
        Map response = weixinRequestUtil.sendWxGetRequest(url, queryParams, Map.class);
        log.info("获取access_token接口返回结果：{}", response);
        if (response.get("access_token") == null) {
            log.error("获取access_token失败");
            throw new WxserviceBusinessException(WxserviceBusinessExceptionCode.EC_60103);
        }
        return (String) response.get("access_token");
    }

    /**
     * 发送模板消息
     *
     * @param wxServiceConfig 微信服务号配置
     * @param openid          用户openid
     * @param templateId      模板id
     * @param page            跳转页面
     * @param data            模板数据
     */
    public boolean sendTemplateMessage(WxServiceConfig wxServiceConfig, String openid, String templateId, String page, Object data) {
        // 配置请求地址
        String url = "https://api.weixin.qq.com/cgi-bin/message/template/send";
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("access_token", getAccessToken(wxServiceConfig));
        Map<String, Object> body = new HashMap<>();
        body.put("touser", openid);
        body.put("template_id", templateId);
        body.put("page", page);
        body.put("data", data);
        Map response = weixinRequestUtil.sendWxPostRequest(url, queryParams, body, Map.class);
        log.info("发送模板消息接口返回结果：{}", response);
        return response.get("errcode") != null && (int) response.get("errcode") == 0;
    }

    /**
     * 生成临时二维码
     *
     * @param wxServiceConfig 微信服务号配置
     * @param sceneStr        场景值
     * @param expireSeconds   过期时间（秒）
     * @return 二维码图片地址
     */
    public String createTempQrCode(WxServiceConfig wxServiceConfig, String sceneStr, int expireSeconds) {
        // 配置请求地址
        String url = "https://api.weixin.qq.com/cgi-bin/qrcode/create";
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("access_token", getAccessToken(wxServiceConfig));
        Map<String, Object> body = new HashMap<>();
        body.put("expire_seconds", expireSeconds);
        body.put("action_name", "QR_STR_SCENE");
        Map<String, Object> actionInfo = new HashMap<>();
        Map<String, Object> scene = new HashMap<>();
        scene.put("scene_str", sceneStr);
        actionInfo.put("scene", scene);
        body.put("action_info", actionInfo);
        Map response = weixinRequestUtil.sendWxPostRequest(url, queryParams, body, Map.class);
        log.info("生成临时二维码接口返回结果：{}", response);
        if (response.get("ticket") == null) {
            log.error("生成临时二维码失败");
            throw new WxserviceBusinessException(WxserviceBusinessExceptionCode.EC_60104);
        }
        return "https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=" + response.get("ticket");
    }

    /**
     * 自定义菜单创建
     *
     * @param wxServiceConfig 微信服务号配置
     * @param menu            菜单
     * @return 是否创建成功
     */
    public boolean createMenu(WxServiceConfig wxServiceConfig, Menu menu) {
        // 配置请求地址
        String url = "https://api.weixin.qq.com/cgi-bin/menu/create";
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("access_token", getAccessToken(wxServiceConfig));
        Map response = weixinRequestUtil.sendWxPostRequest(url, queryParams, menu, Map.class);
        log.info("自定义菜单创建接口返回结果：{}", response);
        return response.get("errcode") != null && (int) response.get("errcode") == 0;
    }
}