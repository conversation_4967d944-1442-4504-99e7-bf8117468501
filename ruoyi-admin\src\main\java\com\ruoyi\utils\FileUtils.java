package com.ruoyi.utils;

import java.io.IOException;
import java.nio.file.*;
import java.util.ArrayList;
import java.util.List;

public class FileUtils {


    public static List<String> readTxtFiles(String folderPath) {
        List<String> fileContents = new ArrayList<>();

        try {
            Path directory = Paths.get(folderPath);

            // 检查文件夹是否存在
            if (Files.exists(directory) && Files.isDirectory(directory)) {
                // 获取文件夹中所有txt文件
                DirectoryStream<Path> directoryStream = Files.newDirectoryStream(directory, "*.txt");

                for (Path filePath : directoryStream) {
                    // 读取每个txt文件的内容并添加到列表
                    String fileContent = new String(Files.readAllBytes(filePath));
                    fileContents.add(fileContent);
                }
            } else {
                System.out.println("文件夹不存在或不是一个有效的文件夹路径");
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        return fileContents;
    }

    public static void createFolderAndFile(String folderPath, String fileName) throws Exception {
        // 检查文件夹是否存在，不存在则创建
        Path folder = Paths.get(folderPath);
        if (!Files.exists(folder)) {
            Files.createDirectories(folder);
        }
        // 检查文件是否存在，不存在则创建
        Path file = Paths.get(folderPath, fileName);
        if (!Files.exists(file)) {
            Files.createFile(file);
        }
    }

    public static void writeBase64ToFile(String base64String, String filePath) throws Exception {
        // 清空文件内容
        Files.write(Paths.get(filePath), "".getBytes(), StandardOpenOption.TRUNCATE_EXISTING);
        // 将base64字符串写入文件
        Files.write(Paths.get(filePath), base64String.getBytes(), StandardOpenOption.WRITE);
    }

    //文件数量
    public static int countFiles(String folderPath) {
        int fileCount = 100;

        try {
            // 检查文件夹是否存在，不存在则创建
            Path directory = Paths.get(folderPath);
            if (!Files.exists(directory)) {
                Files.createDirectories(directory);
            }

            // 检查文件夹是否存在
            if (Files.exists(directory) && Files.isDirectory(directory)) {
                // 获取文件夹中的文件数量
                fileCount = (int) Files.list(directory).filter(Files::isRegularFile).count();
            } else {
                System.out.println("文件夹不存在或不是一个有效的文件夹路径");
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        return fileCount;
    }

}
