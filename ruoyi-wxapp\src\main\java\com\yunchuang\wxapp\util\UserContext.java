package com.yunchuang.wxapp.util;

import com.yunchuang.wxapp.model.domain.WxappLoginUser;

/**
 * 微信小程序 - 用户上下文
 */
public class UserContext {

    // ThreadLocal是一个线程内部的数据存储类，可以在当前线程内存储数据，数据存储以后只有当前线程可以得到存储数据
    private static final ThreadLocal<WxappLoginUser> currentUser = new ThreadLocal<>();

    /**
     * 获取当前用户
     *
     * @return 当前用户
     */
    public static WxappLoginUser getCurrentUser() {
        return currentUser.get();
    }

    /**
     * 设置当前用户
     *
     * @param user 用户
     */
    public static void setCurrentUser(WxappLoginUser user) {
        currentUser.set(user);
    }

    /**
     * 清除当前用户
     */
    public static void removeCurrentUser() {
        currentUser.remove(); // 非常重要：在请求结束后清除，防止内存泄漏
    }
}