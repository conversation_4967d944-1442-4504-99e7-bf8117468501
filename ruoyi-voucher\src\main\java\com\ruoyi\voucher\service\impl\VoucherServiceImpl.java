package com.ruoyi.voucher.service.impl;

import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.voucher.mapper.VoucherMapper;
import com.ruoyi.voucher.domain.Voucher;
import com.ruoyi.voucher.service.IVoucherService;

import static com.ruoyi.common.utils.SecurityUtils.getLoginUser;

/**
 * 优惠券Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-04-15
 */
@Service
public class VoucherServiceImpl extends ServiceImpl<VoucherMapper, Voucher> implements IVoucherService
{
    @Autowired
    private VoucherMapper voucherMapper;

    /**
     * 查询优惠券
     * 
     * @param id 优惠券主键
     * @return 优惠券
     */
    @Override
    public Voucher selectVoucherById(Integer id)
    {
        return getById(id);
//        return voucherMapper.selectVoucherById(id);
    }

    /**
     * 查询优惠券列表
     * 
     * @param voucher 优惠券
     * @return 优惠券
     */
    @Override
    public List<Voucher> selectVoucherList(Voucher voucher)
    {
        LoginUser user = SecurityUtils.getLoginUser();
        if (user == null) return null;
        if (user.getUser().isAdmin()) return query().list();
        return query().eq("user_id", user.getUserId()).list();
    }

    /**
     * 新增优惠券
     * 
     * @param voucher 优惠券
     * @return 结果
     */
    @Override
    public int insertVoucher(Voucher voucher)
    {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        voucher.setUserId(user.getUserId());
        voucher.setCreateBy(user.getNickName());
        voucher.setUpdateBy(user.getNickName());
        Date nowDate = DateUtils.getNowDate();
        voucher.setCreateTime(nowDate);
        voucher.setUpdateTime(nowDate);

        System.out.println(voucher);

        return save(voucher) ? 1 : 0;
    }

    /**
     * 修改优惠券
     * 
     * @param voucher 优惠券
     * @return 结果
     */
    @Override
    public int updateVoucher(Voucher voucher)
    {
        voucher.setUpdateTime(DateUtils.getNowDate());
        return updateById(voucher) ? 1 : 0;
    }

    /**
     * 批量删除优惠券
     * 
     * @param ids 需要删除的优惠券主键
     * @return 结果
     */
    @Override
    public int deleteVoucherByIds(Integer[] ids)
    {
        return voucherMapper.deleteVoucherByIds(ids);
    }

    /**
     * 删除优惠券信息
     * 
     * @param id 优惠券主键
     * @return 结果
     */
    @Override
    public int deleteVoucherById(Integer id)
    {
        return voucherMapper.deleteVoucherById(id);
    }
}
