package com.ruoyi.web.controller.wx;


import cn.binarywang.wx.miniapp.api.WxMaService;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.generator.service.WechatService;
import com.ruoyi.generator.util.CheckUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;

@Slf4j
@RestController
@RequestMapping("/wx/official")
public class WxOfficialAccountController {

@Resource
private WechatService wechatService;

    /**
     * 验证微信服务器
     *
     * @param response
     * @param signature
     * @param timestamp
     * @param nonce
     * @param echostr
     */
    @GetMapping("/wechat")
    public void wechatService(PrintWriter out, HttpServletResponse response,
                              @RequestParam(value = "signature", required = false) String signature, @RequestParam String timestamp,
                              @RequestParam String nonce, @RequestParam String echostr) {
        log.info("微信公众号接入,signature:{},timestamp:{},nonce:{},echostr:{}", signature, timestamp, nonce, echostr);
        if (CheckUtil.checkSignature(signature, timestamp, nonce)) {
            out.print(echostr);
        }



    }


    /**
     * 接收来自微信公众号发来的消息
     *
     * @param out
     * @param request
     * @param response
     */
    @PostMapping("/wechat")
    public void wechatServicePost(PrintWriter out, HttpServletRequest request, HttpServletResponse response) throws Exception {
//        String responseMessage = wechatService.processRequest(request);
//        out.print(responseMessage);
//        out.flush();
       wechatService.processRequest2(request, out);

    }


//    /**
//     * 接收微信验证图片信息接口
//     *
//     * @param request
//     * @param response
//     */
//    @RequestMapping("/wxCallBack")
//    public void wxCallBack(HttpServletRequest request, HttpServletResponse response) {
//
//        wechatService.wxCallBack(request, response);
//
////        List<String> urls = new ArrayList<>();
////        List<String> list = iCameraSysImageService.selectCameraSysImageByDeviceId(deviceId);
////        for (String url : list) {
////            urls.add(url);
////        }
////        return Result.ok(true, 200, "获取成功", String.valueOf(System.currentTimeMillis()), urls);
//    }

    @Autowired
    private WxMaService wxMaService;

    @RequestMapping("/wxCallBack")
    public String wxCallBack(String signature, String timestamp, String nonce, String echostr) {
        // 消息合法
        if (wxMaService.checkSignature(timestamp, nonce, signature)) {
            log.info(signature+"----"+timestamp+"----"+nonce+"----"+echostr);

            log.info("-------------微信小程序消息验证通过");
            return echostr;
        }
        // 消息签名不正确，说明不是公众平台发过来的消息
        return null;
    }




}
