package com.ruoyi.utils;

import cn.hutool.core.util.ObjUtil;

import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;

import static cn.hutool.core.date.CalendarUtil.calendar;

/**
 * 日期工具类
 */
public class MyDateUtil {

    /**
     * 获取指定日期所在星期的第一天（星期一）
     *
     * @param date 指定日期
     * @return 星期一的日期
     */
    public static Date getFirstDayOfWeek(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY); // 设置为星期一
        return cal.getTime();
    }

    /**
     * 获取指定日期所在月份的第一天
     *
     * @param date 指定日期
     * @return 月份第一天的日期
     */
    public static Date getFirstDayOfMonth(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.DAY_OF_MONTH, 1); // 设置为月份第一天
        return cal.getTime();
    }

    /**
     * 获取指定日期所在年份的第一天
     *
     * @param date 指定日期
     * @return 年份第一天的日期
     */
    public static Date getFirstDayOfYear(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.DAY_OF_YEAR, 1); // 设置为年份第一天
        return cal.getTime();
    }

    /**
     * 获取当前日期
     *
     * @return 当前日期
     */
    public static Date getNowDate() {
        return new Date();
    }

    /**
     * 在指定日期上增加或减少天数
     *
     * @param date 指定日期
     * @param days 增加或减少的天数
     * @return 修改后的日期
     */
    public static Date addDays(Date date, int days) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DAY_OF_MONTH, days);
        return cal.getTime();
    }

    /**
     * 在指定日期上增加或减少周数
     *
     * @param date  指定日期
     * @param weeks 增加或减少的周数
     * @return 修改后的日期
     */
    public static Date addWeeks(Date date, int weeks) {
        return addDays(date, weeks * 7);
    }

    /**
     * 在指定日期上增加或减少月数
     *
     * @param date   指定日期
     * @param months 增加或减少的月数
     * @return 修改后的日期
     */
    public static Date addMonths(Date date, int months) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MONTH, months);
        return cal.getTime();
    }

    /**
     * 在指定日期上增加或减少年数
     *
     * @param date  指定日期
     * @param years 增加或减少的年数
     * @return 修改后的日期
     */
    public static Date addYears(Date date, int years) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.YEAR, years);
        return cal.getTime();
    }

    /**
     * 获取指定日期的小时数
     * <p>
     * 0-23
     * </p>
     *
     * @param date 指定日期
     * @return 小时数
     */
    public static int getHour(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        return cal.get(Calendar.HOUR_OF_DAY);
    }

    /**
     * 计算指定日期是当月的第几周（按月1号为周一算）
     *
     * @param date 指定日期
     * @return 当月的第几周
     */
    public static int weekOfMonth(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int dayOfMonth = cal.get(Calendar.DAY_OF_MONTH); // 获取当月的第几天
        return (dayOfMonth + 6) / 7; // 计算周数
    }

    /**
     * 计算指定日期是当年的第几月
     *
     * @param date 指定日期
     * @return 当年的第几月
     */
    public static int monthOfYear(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        return cal.get(Calendar.MONTH) + 1;
    }

    /**
     * 比较两个日期是否为同一年
     *
     * @param date1 日期1
     * @param date2 日期2
     * @return 是否为同一年
     * @since 5.4.1
     */
    public static boolean isSameYear(final Date date1, final Date date2) {
        if (date1 == null || date2 == null) {
            throw new IllegalArgumentException("The date must not be null");
        }
        return isSameYear(calendar(date1), calendar(date2));
    }

    /**
     * 比较两个日期是否为同一年<br>
     * 同一年的意思是：ERA（公元）、year（年）都一致。
     *
     * @param cal1 日期1
     * @param cal2 日期2
     * @return 是否为同一年
     * @since 5.4.1
     */
    public static boolean isSameYear(Calendar cal1, Calendar cal2) {
        if (cal1 == null || cal2 == null) {
            throw new IllegalArgumentException("The date must not be null");
        }

        if (ObjUtil.notEqual(cal1.getTimeZone(), cal2.getTimeZone())) {
            // 统一时区
            cal2 = changeTimeZone(cal2, cal1.getTimeZone());
        }

        return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
                // issue#3011@Github
                cal1.get(Calendar.ERA) == cal2.get(Calendar.ERA);
    }

    /**
     * 转换为默认时区的Calendar
     *
     * @param cal 时间
     * @return 默认时区的calendar对象
     */
    private static Calendar changeTimeZone(Calendar cal, TimeZone timeZone) {
        // 转换到统一时区，例如UTC
        cal = (Calendar) cal.clone();
        cal.setTimeZone(timeZone);
        return cal;
    }
}