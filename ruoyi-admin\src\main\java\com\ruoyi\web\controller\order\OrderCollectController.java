package com.ruoyi.web.controller.order;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.device.domain.DeviceCamera;
import com.ruoyi.device.service.IDeviceCameraService;
import com.ruoyi.dto.CollectDto;
import com.ruoyi.order.domain.OrderCamera;
import com.ruoyi.order.domain.OrderCollect;
import com.ruoyi.order.dto.Result;
import com.ruoyi.order.service.IOrderCameraService;
import com.ruoyi.order.service.IOrderCollectService;
import com.ruoyi.socket.WebSocketServer;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.core.controller.BaseController;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * collectController
 *
 * <AUTHOR>
 * @date 2024-01-03
 */
@RestController
@RequestMapping("/collect/collect")
public class OrderCollectController extends BaseController {
    @Autowired
    private IOrderCollectService orderCollectService;
    @Autowired
    private ISysUserService userService;
    @Autowired
    private IOrderCameraService orderCameraService;
    @Autowired
    private IDeviceCameraService deviceCameraService;

//    @GetMapping("/list")
//    public TableDataInfo cash(Integer pageNum, Integer pageSize) {
//        List<CollectDto> collectDtos = new ArrayList<>();
//
//        String YYYY_MM = DateUtils.parseDateToStr(DateUtils.YYYY_MM, DateUtils.getNowDate());
//        Page<OrderCollect> collectPage = orderCollectService.query().eq("time", YYYY_MM).orderByDesc("count").page(new Page<>(pageNum, pageSize));
//        List<OrderCollect> collects = collectPage.getRecords();
//        Long count = orderCollectService.query().eq("time", YYYY_MM).count();
//
//
//        for (OrderCollect orderCollect : collects) {
//            CollectDto collectDto = new CollectDto();
//            String merchantId = orderCollect.getMerchantId();
//            if (userService.query().eq("merchant_id", merchantId).or().eq("user_id", merchantId).count() > 1) continue;
//            collectDto.setSysUser(userService.query().eq("merchant_id", merchantId).or().eq("user_id", merchantId).one());
//            Long countDay = orderCameraService.countPriceToday(merchantId);//日收
//            System.out.println(countDay);
//
//            List<DeviceCamera> devices = deviceCameraService.query().eq("user_id", collectDto.getSysUser().getUserId()).list();// 设备
//            int onlineDevice = 0;
//            for (DeviceCamera device : devices) {
//                String deviceId = device.getDeviceId();
//                if (WebSocketServer.checkOnline(deviceId)) {
//                    onlineDevice++;
//                }
//            }
//            collectDto.setDeviceCount(devices.size());
//            collectDto.setOnlineDevice(onlineDevice);
//            collectDto.setOutlineDevice(devices.size() - onlineDevice);
//            collectDto.setDayRevenue(countDay == null ? 0 : countDay);
//            collectDto.setMonRevenue(orderCollect.getCount());
//            collectDtos.add(collectDto);
//        }
//
//        return new TableDataInfo(collectDtos, count);
//    }
}
