package com.yunchuang.wxapp.model.domain;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Collection;

/**
 * 微信小程序 - 登录用户
 */
@Data
public class WxappLoginUser implements UserDetails {

    /**
     * 用户ID
     */
    private Long id;

    /**
     * 头像
     */
    @Excel(name = "头像")
    private String avatar;

    /**
     * 昵称
     */
    @Excel(name = "昵称")
    private String nickname;

    /**
     * openID
     */
    @Excel(name = "openID")
    private String openid;

    /**
     * token 到期时间
     */
    private Long jwtEndTime;

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return null;
    }

    @Override
    public String getPassword() {
        return "";
    }

    @Override
    public String getUsername() {
        return "";
    }

    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        return true;
    }

    // jwt 到期时间是否小于当前时间
    public boolean isTokenExpired() {
        return jwtEndTime < System.currentTimeMillis();
    }
}
