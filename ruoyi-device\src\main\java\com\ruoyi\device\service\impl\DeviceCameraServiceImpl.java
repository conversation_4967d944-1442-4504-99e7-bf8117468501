package com.ruoyi.device.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.QueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.device.domain.DeviceCamera;
import com.ruoyi.device.mapper.DeviceCameraMapper;
import com.ruoyi.device.service.IDeviceCameraService;
import com.ruoyi.system.domain.SysPost;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.system.service.ISysPostService;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

/**
 * 管理拍照机设备Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-07-14
 */
@Service
public class DeviceCameraServiceImpl extends ServiceImpl<DeviceCameraMapper, DeviceCamera> implements IDeviceCameraService {
    @Resource
    private ISysUserService userService;
    @Autowired
    private ISysPostService postService;

    @Autowired
    private ISysDeptService deptService;

    /**
     * 查询管理拍照机设备
     *
     * @param deviceId 管理拍照机设备主键
     * @return 管理拍照机设备
     */
    @Override
    public DeviceCamera selectDeviceCameraByDeviceId(String deviceId) {
        return getById(deviceId);

    }

    /**
     * 查询管理拍照机设备列表
     *
     * @param deviceCamera 管理拍照机设备
     * @return 管理拍照机设备
     */
    @Override
    public TableDataInfo selectDeviceCameraList(DeviceCamera deviceCamera, int pageNum, int pageSize) {
        LoginUser user = SecurityUtils.getLoginUser();
        if (user == null) return null;
        SysUser sysUser = user.getUser();
        if (sysUser == null) return null;

        QueryChainWrapper<DeviceCamera> query = query();
        query.eq("del_flag", 0);
        if (!sysUser.isAdmin()) {
            List<Long> userIds = new ArrayList<>();
            userIds.add(user.getUserId());
            Long loginUserDeptId = user.getDeptId();
            SysDept loginUserDept = deptService.selectDeptById(loginUserDeptId);
            if (loginUserDept.getLeader().equals(sysUser.getUserName())) {  //dept的负责人
                SysUser sysUserParams = new SysUser();
                sysUserParams.setDeptId(loginUserDeptId);
                List<SysUser> sysUsers = userService.selectUserList(sysUserParams);
                for (SysUser sysUser1 : sysUsers) {
                    userIds.add(sysUser1.getUserId());
                }
            }
            System.out.println(userIds);
            query.in("user_id", userIds).list();//用户所属设备
        }


        if (deviceCamera.getUserId() != null) {
            query.eq("user_id", deviceCamera.getUserId());
        }
        if (deviceCamera.getDeviceId() != null) {
            query.like("device_id", deviceCamera.getDeviceId());
        }
        if (deviceCamera.getDeviceName() != null) {
            query.like("device_name", deviceCamera.getDeviceName());
        }
        if (deviceCamera.getDetailAddress() != null) {
            query.like("detail_address", deviceCamera.getDetailAddress());
        }
        if (deviceCamera.getDeviceStatus() != null) {
            query.eq("device_status", deviceCamera.getDeviceStatus());
        }
        // 获取当前页数据
        Page<DeviceCamera> page = query.orderByDesc("create_time").page(new Page<>(pageNum, pageSize));
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 新增管理拍照机设备
     *
     * @param deviceCamera 管理拍照机设备
     * @return 结果
     */
    @Override
    public int insertDeviceCamera(DeviceCamera deviceCamera) {
        deviceCamera.setCreateTime(DateUtils.getNowDate());
        return save(deviceCamera) ? 1 : 0;
    }

    /**
     * 修改管理拍照机设备
     *
     * @param deviceCamera 管理拍照机设备
     * @return 结果
     */
    @Override
    public int updateDeviceCamera(DeviceCamera deviceCamera) {
        deviceCamera.setUpdateTime(DateUtils.getNowDate());
        String subAccount = deviceCamera.getSubAccount();
        if (subAccount != null && !subAccount.equals("")) {
            int record = 100;

            HashSet<String> merchantIds = new HashSet<>();
            //subAccount ==> xxxxxxx:30,xxxxxxx:40,xxxxxxx:30
            String[] split = subAccount.split(",");
            //split ==> xxxxxxx:30
            SysUser sysUser = new SysUser();
            int count = 0;
            for (String s : split) {
                String[] people = s.split(":");
                if (people.length != 2) return 0; //格式不对 返回0
                if (!merchantIds.add(people[0])) return 0; //存在相同商户号
                sysUser.setMerchantId(people[0]);
                List<SysUser> sysUsers = userService.selectUserList(sysUser);
                if (sysUsers.size() <= 0) return 0; //查无此人 返回0
                if (!people[1].matches("[0-9]+")) return 0; //格式不对 返回0

                int count1 = Integer.parseInt(people[1]);  //分成大小按顺序输入
                if (record < count1)
                    return 0;
                record = count1;

                count += count1;
            }
            if (count != 100) return 0; //分账比例不对 返回0

        }
        return updateById(deviceCamera) ? 1 : 0;
    }


    /**
     * 批量删除管理拍照机设备
     *
     * @param deviceIds 需要删除的管理拍照机设备主键
     * @return 结果
     */
    @Override
    public int deleteDeviceCameraByDeviceIds(String[] deviceIds) {
        List<String> list = new ArrayList<>(Arrays.asList(deviceIds));
        if (list.size() == 0) return 0;
        boolean update = update().set("del_flag", 2).in("device_id", list).update();
        return update ? 1 : 0;
    }

    /**
     * 删除管理拍照机设备信息
     *
     * @param deviceId 管理拍照机设备主键
     * @return 结果
     */
    @Override
    public int deleteDeviceCameraByDeviceId(String deviceId) {
        return removeById(deviceId) ? 1 : 0;
    }


    @Override
    public int selectUserIDByDeviceId(String deviceId) {
        DeviceCamera device = getById(deviceId);
        if (device == null) return -1;
        return Math.toIntExact(device.getUserId());
    }

    @Override
    public SysPost getSoftware(String deviceId) {
        DeviceCamera deviceCamera = query().eq("device_id", deviceId).one();
        if (deviceCamera == null) return null;
        Long softwareId = deviceCamera.getSoftwareId();
        SysPost sysPost = postService.selectPostById(softwareId);
        return sysPost;
    }

    @Override
    public boolean updateDeviceStatus(String deviceId, String printerStatus, int cameraStatus, int beautyStatus, int consumables, int paperConsumables) {
        return update().eq("device_id", deviceId)
                .set("update_time", DateUtils.getNowDate())
                .set("printer_status", printerStatus)
                .set("camera_status", cameraStatus)
                .set("beauty_status", beautyStatus)
                .set("consumables", consumables)
                .set("paper_consumables", paperConsumables)
                .update();
    }
}
