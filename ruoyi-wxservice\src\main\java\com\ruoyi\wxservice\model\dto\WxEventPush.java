package com.ruoyi.wxservice.model.dto;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

/**
 * 接收微信消息推送的实体类
 */
@Data
@XmlRootElement(name = "xml")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "xml", propOrder = {"ToUserName", "FromUserName", "MsgType", "Event", "CreateTime", "EventKey", "Ticket", "Content", "MsgId"})
public class WxEventPush {

    /**
     * 开发者微信号
     */
    private String ToUserName;
    /**
     * 发送方帐号（一个OpenID）
     */
    private String FromUserName;
    /**
     * 消息类型 event(关注/取消关注事件)
     */
    private String MsgType;
    /**
     * 事件类型，subscribe(订阅)、unsubscribe(取消订阅)
     */
    private String Event;
    /**
     * 消息创建时间 （整型）时间戳
     */
    private Long CreateTime;
    /**
     * 事件 KEY 值，qrscene_为前缀，后面为二维码的参数值
     */
    private String EventKey;
    /**
     * 二维码的ticket，可用来换取二维码图片
     */
    private String Ticket;
    /**
     * 接收到的消息内容
     */
    private String Content;
    /**
     * 消息id
     */
    private String MsgId;

    /**
     * 构建文本消息
     *
     * @param toUserName   接收方帐号（收到的OpenID）
     * @param fromUserName 开发者微信号
     * @param content      文本消息内容
     */
    public static WxEventPush buildTextMessage(String toUserName, String fromUserName, String content) {
        WxEventPush wxEventPush = new WxEventPush();
        wxEventPush.ToUserName = toUserName;
        wxEventPush.FromUserName = fromUserName;
        wxEventPush.MsgType = "text";
        wxEventPush.Content = content;
        wxEventPush.CreateTime = System.currentTimeMillis();
        return wxEventPush;
    }


    /**
     * 获取xml格式的字符串
     *
     * @return xml字符串
     */
    public String getXmlString() {
        String xml = "";
        xml = "<xml>";
        xml += "<ToUserName><![CDATA[";
        xml += this.getToUserName();
        xml += "]]></ToUserName>\n";
        xml += "<FromUserName><![CDATA[";
        xml += this.getFromUserName();
        xml += "]]></FromUserName>\n";
        xml += "<CreateTime>";
        xml += this.getCreateTime();
        xml += "</CreateTime>\n";
        xml += "<MsgType><![CDATA[";
        xml += this.getMsgType();
        xml += "]]></MsgType>\n";
        xml += "<Content><![CDATA[";
        xml += this.getContent();
        xml += "]]></Content>\n";
        xml += "</xml>";
        return xml;
    }
}

