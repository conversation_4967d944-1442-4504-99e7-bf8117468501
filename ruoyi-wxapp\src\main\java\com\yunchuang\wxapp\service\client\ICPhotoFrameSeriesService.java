package com.yunchuang.wxapp.service.client;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.device.domain.CameraSysImage;
import com.yunchuang.wxapp.model.domain.PhotoFrameSeries;
import com.yunchuang.wxapp.model.resp.CCommonGetFrameListResp;

import java.util.List;

/**
 * 相框系列 Service 接口
 */
public interface ICPhotoFrameSeriesService extends IService<PhotoFrameSeries> {

    /**
     * 获取相框列表
     *
     * @param photoFrameIdStr 相框ID字符串
     * @return 相框列表
     */
    List<CameraSysImage> getFrameList(String photoFrameIdStr);

    /**
     * 获取相框列表 - 系列分组
     *
     * @return 相框列表
     */
    List<CCommonGetFrameListResp> getFrameListGroup();

}
