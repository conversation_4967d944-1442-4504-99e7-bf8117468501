package com.yunchuang.wxapp.service.client;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yunchuang.wxapp.model.domain.HomeSet;
import com.yunchuang.wxapp.model.resp.CCommonHomeSetReq;

import java.util.List;

/**
 * 首页设置 Service 接口
 */
public interface ICHomeSetService extends IService<HomeSet> {

    /**
     * 获取轮播图列表
     *
     * @return 轮播图列表
     */
    List<HomeSet> getCarouselList();

    /**
     * 获取首页设置列表
     *
     * @return 首页设置列表
     */
    List<CCommonHomeSetReq> getHomeSetList();
}
