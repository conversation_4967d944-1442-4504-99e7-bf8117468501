package com.yunchuang.wxapp.model.enums.common;

import lombok.Getter;

/**
 * 通用启用状态
 */
@Getter
public enum CommonEnableStatus {

    /**
     * 禁用
     */
    DISABLE(0, "禁用"),
    /**
     * 启用
     */
    ENABLE(1, "启用");

    private final Integer value;
    private final String name;

    CommonEnableStatus(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public static CommonEnableStatus getCommonEnableStatus(Integer value) {
        for (CommonEnableStatus commonEnableStatus : CommonEnableStatus.values()) {
            if (commonEnableStatus.getValue().equals(value)) {
                return commonEnableStatus;
            }
        }
        return null;
    }
}
