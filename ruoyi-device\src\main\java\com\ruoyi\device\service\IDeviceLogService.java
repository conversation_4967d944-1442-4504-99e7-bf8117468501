package com.ruoyi.device.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.device.domain.DeviceLog;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public interface IDeviceLogService extends IService<DeviceLog> {


    /**
     * 新增日志
     * @param deviceLog
     * @return
     */
    public int addLog(DeviceLog deviceLog);

    /**
     * 查询日志列表
     * @param deviceLog
     * @return
     */
    TableDataInfo selectLog(DeviceLog deviceLog,int pageNum,int pageSize);

    public int deleteLog(String[] deviceIds);
}
