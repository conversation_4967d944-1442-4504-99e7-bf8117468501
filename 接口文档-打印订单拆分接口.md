# 打印订单接口文档

## 概述

将原有的 `uploadAndOrder` 接口拆分为两个独立的接口：
1. **创建订单接口** - 创建订单记录，返回订单ID
2. **上传文件接口** - 上传文件并计算价格，更新订单金额

## 系统架构说明

### 消息队列使用情况
- **无传统消息队列**：系统未使用 RabbitMQ、Kafka、ActiveMQ 等传统消息队列
- **Redis缓存**：用于缓存订单信息、短信验证码等
- **MQTT协议**：用于设备通信（端口1883）
- **短信服务**：集成阿里云、腾讯云、创蓝云智短信服务

---

## 接口一：创建订单

### 基本信息
- **接口路径**：`POST /order/printer/createOrder`
- **接口描述**：创建打印订单，返回订单ID
- **认证方式**：Bearer Token（微信小程序JWT）

### 请求参数

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| deviceId | String | 是 | 打印机设备ID | "PRINTER_001" |
| deviceName | String | 否 | 打印机设备名称 | "办公室打印机" |
| openid | String | 否 | 微信用户openid（优先从token获取） | "oABC123..." |
| phone | String | 否 | 用户手机号 | "13800138000" |

### 请求示例

```bash
curl -X POST "http://localhost:8081/order/printer/createOrder" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "Authorization: Bearer your_jwt_token" \
  -d "deviceId=PRINTER_001&deviceName=办公室打印机&phone=13800138000"
```

### 响应参数

| 参数名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| code | Integer | 响应状态码，200表示成功 | 200 |
| msg | String | 响应消息 | "创建订单成功" |
| data | Object | 响应数据 | - |
| ├─ orderId | String | 订单ID | "abc123def456" |
| ├─ deviceId | String | 设备ID | "PRINTER_001" |
| ├─ deviceName | String | 设备名称 | "办公室打印机" |
| └─ orderPrice | Long | 订单金额（分），初始为0 | 0 |

### 响应示例

```json
{
  "code": 200,
  "msg": "创建订单成功",
  "data": {
    "orderId": "abc123def456",
    "deviceId": "PRINTER_001", 
    "deviceName": "办公室打印机",
    "orderPrice": 0
  }
}
```

---

## 接口二：上传文件并计算价格

### 基本信息
- **接口路径**：`POST /order/printer/uploadFile`
- **接口描述**：上传文件，创建打印任务，计算价格并更新订单金额
- **认证方式**：Bearer Token（微信小程序JWT）
- **请求类型**：multipart/form-data

### 请求参数

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| orderId | String | 是 | 订单ID | "abc123def456" |
| file | File | 是 | 要打印的文件 | document.pdf |
| copies | Integer | 否 | 打印份数，默认1 | 2 |
| colorMode | Integer | 否 | 颜色模式：0-黑白，1-彩色，默认0 | 0 |
| duplexMode | Integer | 否 | 双面模式：0-单面，1-双面，默认0 | 1 |
| paperType | Integer | 否 | 纸张类型：1-A4，2-A5，3-照片纸，默认1 | 1 |
| pageRange | String | 否 | 页码范围，如"1-3,5,7-9"或"all" | "1-5" |

### 请求示例

```bash
curl -X POST "http://localhost:8081/order/printer/uploadFile" \
  -H "Authorization: Bearer your_jwt_token" \
  -F "orderId=abc123def456" \
  -F "file=@document.pdf" \
  -F "copies=2" \
  -F "colorMode=0" \
  -F "duplexMode=1" \
  -F "paperType=1" \
  -F "pageRange=1-5"
```

### 响应参数

| 参数名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| code | Integer | 响应状态码，200表示成功 | 200 |
| msg | String | 响应消息 | "上传文件成功" |
| data | Object | 响应数据 | - |
| ├─ taskId | String | 打印任务ID | "task123456" |
| ├─ taskPrice | Long | 本次任务价格（分） | 300 |
| ├─ pageCount | Integer | 计算出的页数 | 5 |
| ├─ totalOrderPrice | Long | 订单总金额（分） | 300 |
| ├─ fileName | String | 文件名 | "document.pdf" |
| └─ fileUrl | String | 文件存储路径 | "/profile/upload/2025/01/25/xxx.pdf" |

### 响应示例

```json
{
  "code": 200,
  "msg": "上传文件成功",
  "data": {
    "taskId": "task123456",
    "taskPrice": 300,
    "pageCount": 5,
    "totalOrderPrice": 300,
    "fileName": "document.pdf",
    "fileUrl": "/profile/upload/2025/01/25/xxx.pdf"
  }
}
```

---

## 价格计算规则

### 基础价格配置（单位：分）

| 纸张类型 | 黑白打印 | 彩色打印 |
|----------|----------|----------|
| A4 (paperType=1) | 20分/页 | 50分/页 |
| A5 (paperType=2) | 15分/页 | 30分/页 |
| 照片纸 (paperType=3) | 200分/页 | 200分/页 |

### 计算公式

```
任务价格 = 基础价格 × 页数 × 份数 × 双面系数

其中：
- 双面系数：单面=1.0，双面=1.5
- 页数：根据pageRange参数解析计算
- 份数：copies参数值
```

### 页码范围解析规则

| 输入格式 | 说明 | 计算页数 |
|----------|------|----------|
| "1-5" | 第1页到第5页 | 5页 |
| "1,3,5" | 第1、3、5页 | 3页 |
| "1-3,5,7-9" | 第1-3页、第5页、第7-9页 | 7页 |
| "all" 或 "*" | 全部页面 | 默认10页 |
| 空或null | 默认 | 1页 |

---

## 错误码说明

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 500 | 设备ID不能为空 | deviceId参数缺失 |
| 500 | 订单ID不能为空 | orderId参数缺失 |
| 500 | 请上传文件 | file参数缺失或文件为空 |
| 500 | 订单不存在 | 指定的orderId在数据库中不存在 |
| 500 | 创建订单失败: xxx | 创建订单时发生异常 |
| 500 | 上传文件失败: xxx | 上传文件时发生异常 |

---

## 使用流程

1. **第一步**：调用创建订单接口，获取订单ID
2. **第二步**：使用订单ID调用上传文件接口，可多次调用上传多个文件
3. **第三步**：每次上传文件都会计算价格并累加到订单总金额
4. **第四步**：完成所有文件上传后，进行支付流程

## 注意事项

1. **订单缓存**：订单信息会在Redis中缓存30分钟
2. **文件存储**：文件上传到服务器本地存储，路径前缀为 `/profile`
3. **用户认证**：优先从JWT token中获取用户openid，如果获取失败会生成访客ID
4. **价格精度**：所有价格以分为单位，避免浮点数精度问题
5. **并发安全**：支持同一订单多次上传文件，价格会正确累加

---

## 数据库表结构

### 订单表 (order_printer)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| order_id | VARCHAR(50) | 订单ID（主键） |
| device_id | VARCHAR(50) | 设备ID |
| device_name | VARCHAR(100) | 设备名称 |
| openid | VARCHAR(100) | 微信用户openid |
| phone | VARCHAR(20) | 手机号 |
| order_status | INT | 订单状态：0-未支付，1-已支付，2-已取消，3-已退款，4-已打印，5-打印中，6-打印失败 |
| create_time | DATETIME | 创建时间 |

### 打印任务表 (order_printer_tasks)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| task_id | VARCHAR(50) | 任务ID（主键） |
| order_id | VARCHAR(50) | 关联订单ID |
| device_id | VARCHAR(50) | 设备ID |
| file_url | VARCHAR(500) | 文件URL |
| file_name | VARCHAR(200) | 文件名 |
| file_type | VARCHAR(20) | 文件类型 |
| file_size | BIGINT | 文件大小（字节） |
| print_status | INT | 打印状态：0-待打印，1-打印中，2-打印完成，3-打印失败 |
| page_range | VARCHAR(100) | 页码范围 |
| copies | INT | 打印份数 |
| color_mode | INT | 颜色模式：0-黑白，1-彩色 |
| duplex_mode | INT | 双面模式：0-单面，1-双面 |
| paper_type | INT | 纸张类型：1-A4，2-A5，3-照片纸 |
| task_price | BIGINT | 任务价格（分） |
| create_time | DATETIME | 创建时间 |

---

## 技术实现细节

### 1. 文件上传处理
- 使用 `FileUploadUtils.upload()` 方法处理文件上传
- 文件存储路径：`${ruoyi.profile}/upload/年/月/日/随机文件名`
- 支持的文件类型：PDF、DOC、DOCX、XLS、XLSX、PPT、PPTX、TXT、图片等

### 2. 价格计算算法
```java
// 价格计算核心逻辑
public Long calculatePrintPrice(int pageCount, int copies, int colorMode, int duplexMode, int paperType, String deviceId) {
    // 根据纸张类型和颜色模式确定基础价格
    Long basePrice = getBasePrice(paperType, colorMode);

    // 双面打印系数
    if (duplexMode == 1) {
        basePrice = (long) (basePrice * 1.5);
    }

    // 总价格 = 基础价格 × 页数 × 份数
    return basePrice * pageCount * copies;
}
```

### 3. 页码范围解析算法
```java
// 页码范围解析示例
public int parsePageRange(String pageRange) {
    if (StringUtils.isEmpty(pageRange) || "all".equals(pageRange)) {
        return 10; // 默认页数
    }

    int totalPages = 0;
    String[] ranges = pageRange.split(",");

    for (String range : ranges) {
        if (range.contains("-")) {
            // 处理范围：1-5
            String[] parts = range.split("-");
            int start = Integer.parseInt(parts[0].trim());
            int end = Integer.parseInt(parts[1].trim());
            totalPages += (end - start + 1);
        } else {
            // 处理单页：5
            totalPages += 1;
        }
    }

    return totalPages;
}
```

### 4. Redis缓存策略
- **缓存Key格式**：`print_order:{orderId}`
- **缓存时间**：30分钟
- **缓存内容**：完整的订单对象
- **缓存用途**：提高查询性能，支持订单临时存储

### 5. 用户认证机制
- 使用微信小程序JWT Token认证
- Token通过 `UserContext.getCurrentUser()` 获取当前用户信息
- 如果认证失败，生成访客ID：`guest_{deviceId}_{timestamp}`

---

## 接口测试用例

### 测试用例1：正常流程
```bash
# 1. 创建订单
curl -X POST "http://localhost:8081/order/printer/createOrder" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiJ9..." \
  -d "deviceId=PRINTER_001&deviceName=测试打印机"

# 2. 上传文件
curl -X POST "http://localhost:8081/order/printer/uploadFile" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiJ9..." \
  -F "orderId=返回的订单ID" \
  -F "file=@test.pdf" \
  -F "copies=1" \
  -F "colorMode=0" \
  -F "pageRange=1-3"
```

### 测试用例2：多文件上传
```bash
# 第一个文件
curl -X POST "http://localhost:8081/order/printer/uploadFile" \
  -F "orderId=abc123" \
  -F "file=@file1.pdf" \
  -F "pageRange=1-5"

# 第二个文件
curl -X POST "http://localhost:8081/order/printer/uploadFile" \
  -F "orderId=abc123" \
  -F "file=@file2.pdf" \
  -F "pageRange=1-3"
```

### 测试用例3：错误处理
```bash
# 缺少必填参数
curl -X POST "http://localhost:8081/order/printer/createOrder" \
  -H "Content-Type: application/x-www-form-urlencoded"
  # 预期返回：设备ID不能为空

# 订单不存在
curl -X POST "http://localhost:8081/order/printer/uploadFile" \
  -F "orderId=invalid_order_id" \
  -F "file=@test.pdf"
  # 预期返回：订单不存在
```


