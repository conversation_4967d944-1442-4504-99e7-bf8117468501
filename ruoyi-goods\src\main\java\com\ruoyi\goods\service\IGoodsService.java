package com.ruoyi.goods.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.goods.domain.Goods;

/**
 * goodsService接口
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
public interface IGoodsService extends IService<Goods>
{
    /**
     * 查询goods
     * 
     * @param id goods主键
     * @return goods
     */
    public Goods selectGoodsById(Long id);

    /**
     * 查询goods列表
     * 
     * @param goods goods
     * @return goods集合
     */
    public TableDataInfo selectGoodsList(Goods goods, int pageNum, int pageSize);

    /**
     * 新增goods
     * 
     * @param goods goods
     * @return 结果
     */
    public int insertGoods(Goods goods);

    /**
     * 修改goods
     * 
     * @param goods goods
     * @return 结果
     */
    public int updateGoods(Goods goods);

    /**
     * 批量删除goods
     * 
     * @param ids 需要删除的goods主键集合
     * @return 结果
     */
    public int deleteGoodsByIds(Long[] ids);

    /**
     * 删除goods信息
     * 
     * @param id goods主键
     * @return 结果
     */
    public int deleteGoodsById(Long id);
}
