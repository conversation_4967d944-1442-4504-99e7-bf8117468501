package com.ruoyi.web.controller.wxapp.admin;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.yunchuang.wxapp.model.domain.PrivacyPolicy;
import com.yunchuang.wxapp.service.admin.IPrivacyPolicyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 隐私政策Controller
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@RestController
@RequestMapping("/wxapp/policy")
public class PrivacyPolicyController extends BaseController {
    @Autowired
    private IPrivacyPolicyService privacyPolicyService;

    /**
     * 查询隐私政策列表
     */
    @PreAuthorize("@ss.hasPermi('wxapp:policy:list')")
    @GetMapping("/list")
    public TableDataInfo list(PrivacyPolicy wxPrivacyPolicy) {
        startPage();
        List<PrivacyPolicy> list = privacyPolicyService.selectPrivacyPolicyList(wxPrivacyPolicy);
        return getDataTable(list);
    }

    /**
     * 导出隐私政策列表
     */
    @PreAuthorize("@ss.hasPermi('wxapp:policy:export')")
    @Log(title = "隐私政策", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PrivacyPolicy wxPrivacyPolicy) {
        List<PrivacyPolicy> list = privacyPolicyService.selectPrivacyPolicyList(wxPrivacyPolicy);
        ExcelUtil<PrivacyPolicy> util = new ExcelUtil<PrivacyPolicy>(PrivacyPolicy.class);
        util.exportExcel(response, list, "隐私政策数据");
    }

    /**
     * 获取隐私政策详细信息
     */
    @PreAuthorize("@ss.hasPermi('wxapp:policy:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(privacyPolicyService.selectPrivacyPolicyById(id));
    }

    /**
     * 新增隐私政策
     */
    @PreAuthorize("@ss.hasPermi('wxapp:policy:add')")
    @Log(title = "隐私政策", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PrivacyPolicy wxPrivacyPolicy) {
        return toAjax(privacyPolicyService.insertPrivacyPolicy(wxPrivacyPolicy));
    }

    /**
     * 修改隐私政策
     */
    @PreAuthorize("@ss.hasPermi('wxapp:policy:edit')")
    @Log(title = "隐私政策", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PrivacyPolicy wxPrivacyPolicy) {
        return toAjax(privacyPolicyService.updatePrivacyPolicy(wxPrivacyPolicy));
    }

    /**
     * 删除隐私政策
     */
    @PreAuthorize("@ss.hasPermi('wxapp:policy:remove')")
    @Log(title = "隐私政策", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(privacyPolicyService.deletePrivacyPolicyByIds(ids));
    }
}
