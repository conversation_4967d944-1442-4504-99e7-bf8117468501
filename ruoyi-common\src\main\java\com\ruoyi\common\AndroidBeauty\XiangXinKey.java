package com.ruoyi.common.AndroidBeauty;

import java.security.MessageDigest;

public class XiangXin<PERSON>ey {
    public static int sha1_32(byte[] buf){int ret=0;try{byte[] digest= MessageDigest.getInstance("SHA1").digest(buf);return ((int)(digest[0]&0xff)<<24)+((int)(digest[1]&0xff)<<16)+((int)(digest[2]&0xff)<<8)+((int)(digest[3]&0xff)<<0);}catch(Exception e){}return ret;}
    public static byte[] A(){
        byte[] buf=new byte[1261];
        int i=0;
        for(i=52;i<64;i++){ buf[0]=(byte)i; if(sha1_32(buf)==-1578328881){break;} }
        for(i=-107;i<-84;i++){ buf[1]=(byte)i; if(sha1_32(buf)==809170235){break;} }
        for(i=-126;i<-115;i++){ buf[2]=(byte)i; if(sha1_32(buf)==1502377942){break;} }
        for(i=-20;i<-18;i++){ buf[3]=(byte)i; if(sha1_32(buf)==339269511){break;} }
        for(i=-97;i<-87;i++){ buf[4]=(byte)i; if(sha1_32(buf)==1575159251){break;} }
        for(i=90;i<109;i++){ buf[5]=(byte)i; if(sha1_32(buf)==-573049472){break;} }
        for(i=70;i<75;i++){ buf[6]=(byte)i; if(sha1_32(buf)==-1018922147){break;} }
        for(i=56;i<83;i++){ buf[7]=(byte)i; if(sha1_32(buf)==-1788203066){break;} }
        for(i=-84;i<-77;i++){ buf[8]=(byte)i; if(sha1_32(buf)==431614279){break;} }
        for(i=16;i<41;i++){ buf[9]=(byte)i; if(sha1_32(buf)==482511099){break;} }
        for(i=-32;i<-15;i++){ buf[10]=(byte)i; if(sha1_32(buf)==-574595971){break;} }
        for(i=55;i<68;i++){ buf[11]=(byte)i; if(sha1_32(buf)==-1954930533){break;} }
        for(i=-76;i<-48;i++){ buf[12]=(byte)i; if(sha1_32(buf)==-84689491){break;} }
        for(i=21;i<37;i++){ buf[13]=(byte)i; if(sha1_32(buf)==-1100517797){break;} }
        for(i=-93;i<-84;i++){ buf[14]=(byte)i; if(sha1_32(buf)==-1101753094){break;} }
        for(i=-12;i<1;i++){ buf[15]=(byte)i; if(sha1_32(buf)==-2120557831){break;} }
        for(i=103;i<126;i++){ buf[16]=(byte)i; if(sha1_32(buf)==-952782954){break;} }
        for(i=65;i<77;i++){ buf[17]=(byte)i; if(sha1_32(buf)==-2014785129){break;} }
        for(i=37;i<57;i++){ buf[18]=(byte)i; if(sha1_32(buf)==-551085837){break;} }
        for(i=-16;i<-8;i++){ buf[19]=(byte)i; if(sha1_32(buf)==-660610045){break;} }
        for(i=73;i<77;i++){ buf[20]=(byte)i; if(sha1_32(buf)==-1451495808){break;} }
        for(i=-30;i<-17;i++){ buf[21]=(byte)i; if(sha1_32(buf)==-1618700764){break;} }
        for(i=-69;i<-51;i++){ buf[22]=(byte)i; if(sha1_32(buf)==1148528296){break;} }
        for(i=-55;i<-50;i++){ buf[23]=(byte)i; if(sha1_32(buf)==2049988473){break;} }
        for(i=-52;i<-28;i++){ buf[24]=(byte)i; if(sha1_32(buf)==-899392486){break;} }
        for(i=-38;i<-23;i++){ buf[25]=(byte)i; if(sha1_32(buf)==651777771){break;} }
        for(i=-98;i<-82;i++){ buf[26]=(byte)i; if(sha1_32(buf)==372535892){break;} }
        for(i=40;i<69;i++){ buf[27]=(byte)i; if(sha1_32(buf)==-1202616096){break;} }
        for(i=38;i<52;i++){ buf[28]=(byte)i; if(sha1_32(buf)==387992092){break;} }
        for(i=-114;i<-95;i++){ buf[29]=(byte)i; if(sha1_32(buf)==-581655259){break;} }
        for(i=-89;i<-72;i++){ buf[30]=(byte)i; if(sha1_32(buf)==-400073012){break;} }
        for(i=-28;i<-13;i++){ buf[31]=(byte)i; if(sha1_32(buf)==1921214557){break;} }
        for(i=99;i<107;i++){ buf[32]=(byte)i; if(sha1_32(buf)==1782006112){break;} }
        for(i=-126;i<-114;i++){ buf[33]=(byte)i; if(sha1_32(buf)==1823138535){break;} }
        for(i=-2;i<9;i++){ buf[34]=(byte)i; if(sha1_32(buf)==1759029347){break;} }
        for(i=45;i<61;i++){ buf[35]=(byte)i; if(sha1_32(buf)==-1517360836){break;} }
        for(i=-28;i<-17;i++){ buf[36]=(byte)i; if(sha1_32(buf)==-1353328036){break;} }
        for(i=-53;i<-38;i++){ buf[37]=(byte)i; if(sha1_32(buf)==-1725623138){break;} }
        for(i=103;i<118;i++){ buf[38]=(byte)i; if(sha1_32(buf)==-494440774){break;} }
        for(i=-88;i<-61;i++){ buf[39]=(byte)i; if(sha1_32(buf)==-1716670170){break;} }
        for(i=-45;i<-40;i++){ buf[40]=(byte)i; if(sha1_32(buf)==-754939642){break;} }
        for(i=64;i<74;i++){ buf[41]=(byte)i; if(sha1_32(buf)==-307595348){break;} }
        for(i=-52;i<-30;i++){ buf[42]=(byte)i; if(sha1_32(buf)==-479558722){break;} }
        for(i=-50;i<-43;i++){ buf[43]=(byte)i; if(sha1_32(buf)==1237285473){break;} }
        for(i=-8;i<10;i++){ buf[44]=(byte)i; if(sha1_32(buf)==1237285473){break;} }
        for(i=71;i<89;i++){ buf[45]=(byte)i; if(sha1_32(buf)==41945441){break;} }
        for(i=-19;i<-5;i++){ buf[46]=(byte)i; if(sha1_32(buf)==-2096846970){break;} }
        for(i=-35;i<-10;i++){ buf[47]=(byte)i; if(sha1_32(buf)==-331627834){break;} }
        for(i=106;i<118;i++){ buf[48]=(byte)i; if(sha1_32(buf)==191563590){break;} }
        for(i=2;i<18;i++){ buf[49]=(byte)i; if(sha1_32(buf)==1219169243){break;} }
        for(i=80;i<100;i++){ buf[50]=(byte)i; if(sha1_32(buf)==349718818){break;} }
        for(i=-29;i<-23;i++){ buf[51]=(byte)i; if(sha1_32(buf)==480914094){break;} }
        for(i=-99;i<-93;i++){ buf[52]=(byte)i; if(sha1_32(buf)==1818815864){break;} }
        for(i=92;i<104;i++){ buf[53]=(byte)i; if(sha1_32(buf)==-1924146314){break;} }
        for(i=60;i<78;i++){ buf[54]=(byte)i; if(sha1_32(buf)==357432813){break;} }
        for(i=81;i<90;i++){ buf[55]=(byte)i; if(sha1_32(buf)==1887052599){break;} }
        for(i=8;i<27;i++){ buf[56]=(byte)i; if(sha1_32(buf)==308480448){break;} }
        for(i=-26;i<3;i++){ buf[57]=(byte)i; if(sha1_32(buf)==-881897109){break;} }
        for(i=-7;i<12;i++){ buf[58]=(byte)i; if(sha1_32(buf)==1000293008){break;} }
        for(i=-128;i<-114;i++){ buf[59]=(byte)i; if(sha1_32(buf)==1225912594){break;} }
        for(i=-10;i<10;i++){ buf[60]=(byte)i; if(sha1_32(buf)==952375885){break;} }
        for(i=-63;i<-54;i++){ buf[61]=(byte)i; if(sha1_32(buf)==-1459173676){break;} }
        for(i=119;i<128;i++){ buf[62]=(byte)i; if(sha1_32(buf)==1274924159){break;} }
        for(i=-38;i<-10;i++){ buf[63]=(byte)i; if(sha1_32(buf)==1790838471){break;} }
        for(i=-70;i<-50;i++){ buf[64]=(byte)i; if(sha1_32(buf)==-250508029){break;} }
        for(i=-68;i<-58;i++){ buf[65]=(byte)i; if(sha1_32(buf)==1609549086){break;} }
        for(i=-44;i<-29;i++){ buf[66]=(byte)i; if(sha1_32(buf)==-1503193739){break;} }
        for(i=-68;i<-53;i++){ buf[67]=(byte)i; if(sha1_32(buf)==1289432356){break;} }
        for(i=67;i<82;i++){ buf[68]=(byte)i; if(sha1_32(buf)==1318315529){break;} }
        for(i=-112;i<-94;i++){ buf[69]=(byte)i; if(sha1_32(buf)==-1939750545){break;} }
        for(i=10;i<13;i++){ buf[70]=(byte)i; if(sha1_32(buf)==500337362){break;} }
        for(i=77;i<101;i++){ buf[71]=(byte)i; if(sha1_32(buf)==669476442){break;} }
        for(i=-15;i<6;i++){ buf[72]=(byte)i; if(sha1_32(buf)==578972752){break;} }
        for(i=32;i<53;i++){ buf[73]=(byte)i; if(sha1_32(buf)==-166493761){break;} }
        for(i=-3;i<19;i++){ buf[74]=(byte)i; if(sha1_32(buf)==-2069670010){break;} }
        for(i=-3;i<-1;i++){ buf[75]=(byte)i; if(sha1_32(buf)==902068338){break;} }
        for(i=-51;i<-43;i++){ buf[76]=(byte)i; if(sha1_32(buf)==1111051228){break;} }
        for(i=72;i<93;i++){ buf[77]=(byte)i; if(sha1_32(buf)==1479583439){break;} }
        for(i=84;i<104;i++){ buf[78]=(byte)i; if(sha1_32(buf)==940945470){break;} }
        for(i=88;i<102;i++){ buf[79]=(byte)i; if(sha1_32(buf)==2064843252){break;} }
        for(i=-49;i<-42;i++){ buf[80]=(byte)i; if(sha1_32(buf)==-210068285){break;} }
        for(i=120;i<128;i++){ buf[81]=(byte)i; if(sha1_32(buf)==1137326010){break;} }
        for(i=-72;i<-53;i++){ buf[82]=(byte)i; if(sha1_32(buf)==-501302302){break;} }
        for(i=-77;i<-68;i++){ buf[83]=(byte)i; if(sha1_32(buf)==1433323284){break;} }
        for(i=57;i<73;i++){ buf[84]=(byte)i; if(sha1_32(buf)==509955683){break;} }
        for(i=-36;i<-20;i++){ buf[85]=(byte)i; if(sha1_32(buf)==-460364916){break;} }
        for(i=-97;i<-72;i++){ buf[86]=(byte)i; if(sha1_32(buf)==845508119){break;} }
        for(i=-98;i<-79;i++){ buf[87]=(byte)i; if(sha1_32(buf)==-1430000987){break;} }
        for(i=-56;i<-44;i++){ buf[88]=(byte)i; if(sha1_32(buf)==1669242779){break;} }
        for(i=-32;i<-5;i++){ buf[89]=(byte)i; if(sha1_32(buf)==393649984){break;} }
        for(i=-109;i<-89;i++){ buf[90]=(byte)i; if(sha1_32(buf)==1766221080){break;} }
        for(i=-128;i<-120;i++){ buf[91]=(byte)i; if(sha1_32(buf)==-1454632600){break;} }
        for(i=-82;i<-61;i++){ buf[92]=(byte)i; if(sha1_32(buf)==93376324){break;} }
        for(i=92;i<104;i++){ buf[93]=(byte)i; if(sha1_32(buf)==-487542691){break;} }
        for(i=-63;i<-44;i++){ buf[94]=(byte)i; if(sha1_32(buf)==1543277307){break;} }
        for(i=-94;i<-85;i++){ buf[95]=(byte)i; if(sha1_32(buf)==380978511){break;} }
        for(i=89;i<106;i++){ buf[96]=(byte)i; if(sha1_32(buf)==1349964243){break;} }
        for(i=89;i<114;i++){ buf[97]=(byte)i; if(sha1_32(buf)==-1539493155){break;} }
        for(i=-32;i<-17;i++){ buf[98]=(byte)i; if(sha1_32(buf)==1298947573){break;} }
        for(i=-58;i<-37;i++){ buf[99]=(byte)i; if(sha1_32(buf)==-1828254216){break;} }
        for(i=-105;i<-78;i++){ buf[100]=(byte)i; if(sha1_32(buf)==359019549){break;} }
        for(i=-30;i<-6;i++){ buf[101]=(byte)i; if(sha1_32(buf)==-1990863504){break;} }
        for(i=13;i<20;i++){ buf[102]=(byte)i; if(sha1_32(buf)==-17480735){break;} }
        for(i=-39;i<-22;i++){ buf[103]=(byte)i; if(sha1_32(buf)==-1666947460){break;} }
        for(i=76;i<92;i++){ buf[104]=(byte)i; if(sha1_32(buf)==-1263601482){break;} }
        for(i=74;i<87;i++){ buf[105]=(byte)i; if(sha1_32(buf)==85318651){break;} }
        for(i=-60;i<-41;i++){ buf[106]=(byte)i; if(sha1_32(buf)==-1105991344){break;} }
        for(i=-77;i<-70;i++){ buf[107]=(byte)i; if(sha1_32(buf)==-2088111593){break;} }
        for(i=-78;i<-71;i++){ buf[108]=(byte)i; if(sha1_32(buf)==2029721339){break;} }
        for(i=51;i<57;i++){ buf[109]=(byte)i; if(sha1_32(buf)==142908655){break;} }
        for(i=95;i<106;i++){ buf[110]=(byte)i; if(sha1_32(buf)==489486990){break;} }
        for(i=-29;i<-9;i++){ buf[111]=(byte)i; if(sha1_32(buf)==1884111580){break;} }
        for(i=-86;i<-61;i++){ buf[112]=(byte)i; if(sha1_32(buf)==-6277131){break;} }
        for(i=101;i<121;i++){ buf[113]=(byte)i; if(sha1_32(buf)==-759926522){break;} }
        for(i=-65;i<-48;i++){ buf[114]=(byte)i; if(sha1_32(buf)==746724039){break;} }
        for(i=18;i<25;i++){ buf[115]=(byte)i; if(sha1_32(buf)==1490181049){break;} }
        for(i=29;i<41;i++){ buf[116]=(byte)i; if(sha1_32(buf)==-588352115){break;} }
        for(i=39;i<52;i++){ buf[117]=(byte)i; if(sha1_32(buf)==-967583119){break;} }
        for(i=-13;i<5;i++){ buf[118]=(byte)i; if(sha1_32(buf)==-2036715674){break;} }
        for(i=-108;i<-94;i++){ buf[119]=(byte)i; if(sha1_32(buf)==1180078417){break;} }
        for(i=5;i<23;i++){ buf[120]=(byte)i; if(sha1_32(buf)==-1078416045){break;} }
        for(i=5;i<11;i++){ buf[121]=(byte)i; if(sha1_32(buf)==-396097315){break;} }
        for(i=77;i<89;i++){ buf[122]=(byte)i; if(sha1_32(buf)==-1946219868){break;} }
        for(i=-128;i<-110;i++){ buf[123]=(byte)i; if(sha1_32(buf)==-1251234908){break;} }
        for(i=31;i<44;i++){ buf[124]=(byte)i; if(sha1_32(buf)==224909486){break;} }
        for(i=82;i<94;i++){ buf[125]=(byte)i; if(sha1_32(buf)==2017041692){break;} }
        for(i=-97;i<-94;i++){ buf[126]=(byte)i; if(sha1_32(buf)==74448833){break;} }
        for(i=31;i<39;i++){ buf[127]=(byte)i; if(sha1_32(buf)==-1506652517){break;} }
        for(i=-109;i<-83;i++){ buf[128]=(byte)i; if(sha1_32(buf)==967410012){break;} }
        for(i=99;i<106;i++){ buf[129]=(byte)i; if(sha1_32(buf)==-1188007754){break;} }
        for(i=18;i<46;i++){ buf[130]=(byte)i; if(sha1_32(buf)==-317351048){break;} }
        for(i=-58;i<-37;i++){ buf[131]=(byte)i; if(sha1_32(buf)==1687736490){break;} }
        for(i=79;i<86;i++){ buf[132]=(byte)i; if(sha1_32(buf)==1443543277){break;} }
        for(i=-96;i<-81;i++){ buf[133]=(byte)i; if(sha1_32(buf)==-1942677624){break;} }
        for(i=81;i<103;i++){ buf[134]=(byte)i; if(sha1_32(buf)==-1239998979){break;} }
        for(i=49;i<67;i++){ buf[135]=(byte)i; if(sha1_32(buf)==1910660847){break;} }
        for(i=-73;i<-50;i++){ buf[136]=(byte)i; if(sha1_32(buf)==1930386857){break;} }
        for(i=-24;i<-8;i++){ buf[137]=(byte)i; if(sha1_32(buf)==-9486554){break;} }
        for(i=-100;i<-85;i++){ buf[138]=(byte)i; if(sha1_32(buf)==-1035442989){break;} }
        for(i=93;i<111;i++){ buf[139]=(byte)i; if(sha1_32(buf)==1377676196){break;} }
        for(i=41;i<52;i++){ buf[140]=(byte)i; if(sha1_32(buf)==988424128){break;} }
        for(i=98;i<113;i++){ buf[141]=(byte)i; if(sha1_32(buf)==-1873083762){break;} }
        for(i=111;i<128;i++){ buf[142]=(byte)i; if(sha1_32(buf)==188580571){break;} }
        for(i=-80;i<-58;i++){ buf[143]=(byte)i; if(sha1_32(buf)==-986108232){break;} }
        for(i=39;i<53;i++){ buf[144]=(byte)i; if(sha1_32(buf)==-1180623122){break;} }
        for(i=69;i<91;i++){ buf[145]=(byte)i; if(sha1_32(buf)==1148820996){break;} }
        for(i=-108;i<-94;i++){ buf[146]=(byte)i; if(sha1_32(buf)==936337650){break;} }
        for(i=-34;i<-19;i++){ buf[147]=(byte)i; if(sha1_32(buf)==1997116690){break;} }
        for(i=-1;i<18;i++){ buf[148]=(byte)i; if(sha1_32(buf)==-653571670){break;} }
        for(i=-110;i<-86;i++){ buf[149]=(byte)i; if(sha1_32(buf)==-246051617){break;} }
        for(i=-65;i<-47;i++){ buf[150]=(byte)i; if(sha1_32(buf)==-1838800656){break;} }
        for(i=-62;i<-41;i++){ buf[151]=(byte)i; if(sha1_32(buf)==-284125923){break;} }
        for(i=-108;i<-95;i++){ buf[152]=(byte)i; if(sha1_32(buf)==-1430594523){break;} }
        for(i=-1;i<21;i++){ buf[153]=(byte)i; if(sha1_32(buf)==-2033106594){break;} }
        for(i=-77;i<-50;i++){ buf[154]=(byte)i; if(sha1_32(buf)==-1395175589){break;} }
        for(i=-19;i<-2;i++){ buf[155]=(byte)i; if(sha1_32(buf)==-1839279773){break;} }
        for(i=-64;i<-59;i++){ buf[156]=(byte)i; if(sha1_32(buf)==-775943583){break;} }
        for(i=107;i<124;i++){ buf[157]=(byte)i; if(sha1_32(buf)==1168080812){break;} }
        for(i=19;i<44;i++){ buf[158]=(byte)i; if(sha1_32(buf)==279292124){break;} }
        for(i=107;i<116;i++){ buf[159]=(byte)i; if(sha1_32(buf)==1918883897){break;} }
        for(i=-12;i<7;i++){ buf[160]=(byte)i; if(sha1_32(buf)==576581670){break;} }
        for(i=113;i<128;i++){ buf[161]=(byte)i; if(sha1_32(buf)==1351116565){break;} }
        for(i=20;i<34;i++){ buf[162]=(byte)i; if(sha1_32(buf)==-278419532){break;} }
        for(i=-99;i<-76;i++){ buf[163]=(byte)i; if(sha1_32(buf)==128889787){break;} }
        for(i=-30;i<-8;i++){ buf[164]=(byte)i; if(sha1_32(buf)==-443244827){break;} }
        for(i=-40;i<-36;i++){ buf[165]=(byte)i; if(sha1_32(buf)==-1887255761){break;} }
        for(i=87;i<101;i++){ buf[166]=(byte)i; if(sha1_32(buf)==-2136688637){break;} }
        for(i=124;i<128;i++){ buf[167]=(byte)i; if(sha1_32(buf)==1819430822){break;} }
        for(i=-52;i<-44;i++){ buf[168]=(byte)i; if(sha1_32(buf)==1216505629){break;} }
        for(i=79;i<94;i++){ buf[169]=(byte)i; if(sha1_32(buf)==-344042881){break;} }
        for(i=-120;i<-108;i++){ buf[170]=(byte)i; if(sha1_32(buf)==-2055946413){break;} }
        for(i=1;i<15;i++){ buf[171]=(byte)i; if(sha1_32(buf)==-1906269901){break;} }
        for(i=-26;i<-8;i++){ buf[172]=(byte)i; if(sha1_32(buf)==-350597040){break;} }
        for(i=26;i<38;i++){ buf[173]=(byte)i; if(sha1_32(buf)==-786515563){break;} }
        for(i=40;i<56;i++){ buf[174]=(byte)i; if(sha1_32(buf)==-1156548583){break;} }
        for(i=13;i<27;i++){ buf[175]=(byte)i; if(sha1_32(buf)==1108883158){break;} }
        for(i=-25;i<4;i++){ buf[176]=(byte)i; if(sha1_32(buf)==1726432667){break;} }
        for(i=26;i<32;i++){ buf[177]=(byte)i; if(sha1_32(buf)==-41912513){break;} }
        for(i=29;i<39;i++){ buf[178]=(byte)i; if(sha1_32(buf)==-81967353){break;} }
        for(i=112;i<120;i++){ buf[179]=(byte)i; if(sha1_32(buf)==1788981045){break;} }
        for(i=114;i<128;i++){ buf[180]=(byte)i; if(sha1_32(buf)==1320613446){break;} }
        for(i=-85;i<-70;i++){ buf[181]=(byte)i; if(sha1_32(buf)==-1925934865){break;} }
        for(i=28;i<30;i++){ buf[182]=(byte)i; if(sha1_32(buf)==-1353203393){break;} }
        for(i=109;i<121;i++){ buf[183]=(byte)i; if(sha1_32(buf)==712729150){break;} }
        for(i=86;i<104;i++){ buf[184]=(byte)i; if(sha1_32(buf)==385809824){break;} }
        for(i=-61;i<-54;i++){ buf[185]=(byte)i; if(sha1_32(buf)==-274332880){break;} }
        for(i=-122;i<-106;i++){ buf[186]=(byte)i; if(sha1_32(buf)==-168421435){break;} }
        for(i=12;i<30;i++){ buf[187]=(byte)i; if(sha1_32(buf)==-1410419102){break;} }
        for(i=113;i<128;i++){ buf[188]=(byte)i; if(sha1_32(buf)==-1434539314){break;} }
        for(i=-19;i<1;i++){ buf[189]=(byte)i; if(sha1_32(buf)==-479655861){break;} }
        for(i=-73;i<-65;i++){ buf[190]=(byte)i; if(sha1_32(buf)==1927320048){break;} }
        for(i=27;i<41;i++){ buf[191]=(byte)i; if(sha1_32(buf)==945744926){break;} }
        for(i=-116;i<-103;i++){ buf[192]=(byte)i; if(sha1_32(buf)==1341564294){break;} }
        for(i=-112;i<-100;i++){ buf[193]=(byte)i; if(sha1_32(buf)==-1644249692){break;} }
        for(i=76;i<83;i++){ buf[194]=(byte)i; if(sha1_32(buf)==-78786671){break;} }
        for(i=50;i<66;i++){ buf[195]=(byte)i; if(sha1_32(buf)==268621041){break;} }
        for(i=30;i<57;i++){ buf[196]=(byte)i; if(sha1_32(buf)==-1843750858){break;} }
        for(i=36;i<58;i++){ buf[197]=(byte)i; if(sha1_32(buf)==-714426138){break;} }
        for(i=-116;i<-95;i++){ buf[198]=(byte)i; if(sha1_32(buf)==-660532645){break;} }
        for(i=-105;i<-80;i++){ buf[199]=(byte)i; if(sha1_32(buf)==1114461325){break;} }
        for(i=40;i<57;i++){ buf[200]=(byte)i; if(sha1_32(buf)==-1110729046){break;} }
        for(i=-15;i<8;i++){ buf[201]=(byte)i; if(sha1_32(buf)==1523575566){break;} }
        for(i=-96;i<-88;i++){ buf[202]=(byte)i; if(sha1_32(buf)==1449730451){break;} }
        for(i=-74;i<-61;i++){ buf[203]=(byte)i; if(sha1_32(buf)==-1192778711){break;} }
        for(i=79;i<92;i++){ buf[204]=(byte)i; if(sha1_32(buf)==-392405091){break;} }
        for(i=-21;i<-20;i++){ buf[205]=(byte)i; if(sha1_32(buf)==1869322182){break;} }
        for(i=-2;i<22;i++){ buf[206]=(byte)i; if(sha1_32(buf)==649710371){break;} }
        for(i=-5;i<10;i++){ buf[207]=(byte)i; if(sha1_32(buf)==-1097868403){break;} }
        for(i=65;i<73;i++){ buf[208]=(byte)i; if(sha1_32(buf)==-885582505){break;} }
        for(i=83;i<104;i++){ buf[209]=(byte)i; if(sha1_32(buf)==-48492426){break;} }
        for(i=21;i<35;i++){ buf[210]=(byte)i; if(sha1_32(buf)==-1704187311){break;} }
        for(i=16;i<29;i++){ buf[211]=(byte)i; if(sha1_32(buf)==416137811){break;} }
        for(i=-107;i<-100;i++){ buf[212]=(byte)i; if(sha1_32(buf)==1294851969){break;} }
        for(i=100;i<120;i++){ buf[213]=(byte)i; if(sha1_32(buf)==-1017854564){break;} }
        for(i=89;i<120;i++){ buf[214]=(byte)i; if(sha1_32(buf)==1991178310){break;} }
        for(i=67;i<70;i++){ buf[215]=(byte)i; if(sha1_32(buf)==-543844254){break;} }
        for(i=27;i<38;i++){ buf[216]=(byte)i; if(sha1_32(buf)==689129068){break;} }
        for(i=62;i<74;i++){ buf[217]=(byte)i; if(sha1_32(buf)==663810229){break;} }
        for(i=6;i<33;i++){ buf[218]=(byte)i; if(sha1_32(buf)==15871081){break;} }
        for(i=-54;i<-36;i++){ buf[219]=(byte)i; if(sha1_32(buf)==-2081529007){break;} }
        for(i=106;i<128;i++){ buf[220]=(byte)i; if(sha1_32(buf)==1194463163){break;} }
        for(i=32;i<44;i++){ buf[221]=(byte)i; if(sha1_32(buf)==1259824061){break;} }
        for(i=66;i<79;i++){ buf[222]=(byte)i; if(sha1_32(buf)==2140524465){break;} }
        for(i=23;i<33;i++){ buf[223]=(byte)i; if(sha1_32(buf)==-1379534275){break;} }
        for(i=47;i<66;i++){ buf[224]=(byte)i; if(sha1_32(buf)==-781131105){break;} }
        for(i=-97;i<-86;i++){ buf[225]=(byte)i; if(sha1_32(buf)==1951158201){break;} }
        for(i=49;i<70;i++){ buf[226]=(byte)i; if(sha1_32(buf)==692428838){break;} }
        for(i=-75;i<-52;i++){ buf[227]=(byte)i; if(sha1_32(buf)==-1635218506){break;} }
        for(i=103;i<123;i++){ buf[228]=(byte)i; if(sha1_32(buf)==-1028071093){break;} }
        for(i=-116;i<-98;i++){ buf[229]=(byte)i; if(sha1_32(buf)==-638539502){break;} }
        for(i=-84;i<-61;i++){ buf[230]=(byte)i; if(sha1_32(buf)==1376613738){break;} }
        for(i=114;i<128;i++){ buf[231]=(byte)i; if(sha1_32(buf)==283469870){break;} }
        for(i=96;i<111;i++){ buf[232]=(byte)i; if(sha1_32(buf)==-1406838189){break;} }
        for(i=75;i<99;i++){ buf[233]=(byte)i; if(sha1_32(buf)==-1322406874){break;} }
        for(i=-114;i<-99;i++){ buf[234]=(byte)i; if(sha1_32(buf)==-1663168049){break;} }
        for(i=-13;i<1;i++){ buf[235]=(byte)i; if(sha1_32(buf)==-554226049){break;} }
        for(i=2;i<17;i++){ buf[236]=(byte)i; if(sha1_32(buf)==1222629602){break;} }
        for(i=86;i<103;i++){ buf[237]=(byte)i; if(sha1_32(buf)==-215685156){break;} }
        for(i=-10;i<-3;i++){ buf[238]=(byte)i; if(sha1_32(buf)==27650256){break;} }
        for(i=-123;i<-109;i++){ buf[239]=(byte)i; if(sha1_32(buf)==533985954){break;} }
        for(i=56;i<79;i++){ buf[240]=(byte)i; if(sha1_32(buf)==359294268){break;} }
        for(i=-87;i<-62;i++){ buf[241]=(byte)i; if(sha1_32(buf)==-1288626452){break;} }
        for(i=-61;i<-52;i++){ buf[242]=(byte)i; if(sha1_32(buf)==-996681211){break;} }
        for(i=-42;i<-28;i++){ buf[243]=(byte)i; if(sha1_32(buf)==-660605768){break;} }
        for(i=44;i<63;i++){ buf[244]=(byte)i; if(sha1_32(buf)==1369254028){break;} }
        for(i=-72;i<-66;i++){ buf[245]=(byte)i; if(sha1_32(buf)==893493004){break;} }
        for(i=98;i<108;i++){ buf[246]=(byte)i; if(sha1_32(buf)==-776814823){break;} }
        for(i=42;i<59;i++){ buf[247]=(byte)i; if(sha1_32(buf)==-1916590735){break;} }
        for(i=-88;i<-70;i++){ buf[248]=(byte)i; if(sha1_32(buf)==2012980304){break;} }
        for(i=-21;i<-18;i++){ buf[249]=(byte)i; if(sha1_32(buf)==1312881261){break;} }
        for(i=-103;i<-101;i++){ buf[250]=(byte)i; if(sha1_32(buf)==-1878553162){break;} }
        for(i=-108;i<-91;i++){ buf[251]=(byte)i; if(sha1_32(buf)==-1000423412){break;} }
        for(i=87;i<109;i++){ buf[252]=(byte)i; if(sha1_32(buf)==-141618608){break;} }
        for(i=66;i<70;i++){ buf[253]=(byte)i; if(sha1_32(buf)==-829986705){break;} }
        for(i=18;i<38;i++){ buf[254]=(byte)i; if(sha1_32(buf)==-1939809580){break;} }
        for(i=73;i<94;i++){ buf[255]=(byte)i; if(sha1_32(buf)==967323474){break;} }
        for(i=-2;i<16;i++){ buf[256]=(byte)i; if(sha1_32(buf)==1565919695){break;} }
        for(i=-93;i<-86;i++){ buf[257]=(byte)i; if(sha1_32(buf)==-352689004){break;} }
        for(i=-83;i<-75;i++){ buf[258]=(byte)i; if(sha1_32(buf)==-973795173){break;} }
        for(i=114;i<128;i++){ buf[259]=(byte)i; if(sha1_32(buf)==784151512){break;} }
        for(i=116;i<128;i++){ buf[260]=(byte)i; if(sha1_32(buf)==-1096791571){break;} }
        for(i=-105;i<-89;i++){ buf[261]=(byte)i; if(sha1_32(buf)==-1905461758){break;} }
        for(i=-112;i<-106;i++){ buf[262]=(byte)i; if(sha1_32(buf)==-2044823461){break;} }
        for(i=94;i<108;i++){ buf[263]=(byte)i; if(sha1_32(buf)==-2025922417){break;} }
        for(i=97;i<119;i++){ buf[264]=(byte)i; if(sha1_32(buf)==662576297){break;} }
        for(i=65;i<93;i++){ buf[265]=(byte)i; if(sha1_32(buf)==-1605842360){break;} }
        for(i=-61;i<-44;i++){ buf[266]=(byte)i; if(sha1_32(buf)==-112793704){break;} }
        for(i=-127;i<-109;i++){ buf[267]=(byte)i; if(sha1_32(buf)==1828612180){break;} }
        for(i=-128;i<-116;i++){ buf[268]=(byte)i; if(sha1_32(buf)==248071304){break;} }
        for(i=5;i<23;i++){ buf[269]=(byte)i; if(sha1_32(buf)==1898302758){break;} }
        for(i=-67;i<-46;i++){ buf[270]=(byte)i; if(sha1_32(buf)==996916667){break;} }
        for(i=116;i<121;i++){ buf[271]=(byte)i; if(sha1_32(buf)==41390745){break;} }
        for(i=111;i<128;i++){ buf[272]=(byte)i; if(sha1_32(buf)==-570282112){break;} }
        for(i=-90;i<-77;i++){ buf[273]=(byte)i; if(sha1_32(buf)==589117846){break;} }
        for(i=54;i<82;i++){ buf[274]=(byte)i; if(sha1_32(buf)==381595139){break;} }
        for(i=40;i<56;i++){ buf[275]=(byte)i; if(sha1_32(buf)==-1420096254){break;} }
        for(i=44;i<60;i++){ buf[276]=(byte)i; if(sha1_32(buf)==-1712300478){break;} }
        for(i=19;i<50;i++){ buf[277]=(byte)i; if(sha1_32(buf)==-1259156145){break;} }
        for(i=-77;i<-72;i++){ buf[278]=(byte)i; if(sha1_32(buf)==147345272){break;} }
        for(i=60;i<79;i++){ buf[279]=(byte)i; if(sha1_32(buf)==-144676948){break;} }
        for(i=-106;i<-100;i++){ buf[280]=(byte)i; if(sha1_32(buf)==648054411){break;} }
        for(i=-6;i<10;i++){ buf[281]=(byte)i; if(sha1_32(buf)==1252400761){break;} }
        for(i=63;i<80;i++){ buf[282]=(byte)i; if(sha1_32(buf)==247447617){break;} }
        for(i=16;i<31;i++){ buf[283]=(byte)i; if(sha1_32(buf)==1871994939){break;} }
        for(i=59;i<81;i++){ buf[284]=(byte)i; if(sha1_32(buf)==-1290860847){break;} }
        for(i=85;i<91;i++){ buf[285]=(byte)i; if(sha1_32(buf)==1161019302){break;} }
        for(i=44;i<65;i++){ buf[286]=(byte)i; if(sha1_32(buf)==-1353141650){break;} }
        for(i=87;i<97;i++){ buf[287]=(byte)i; if(sha1_32(buf)==293408221){break;} }
        for(i=-82;i<-55;i++){ buf[288]=(byte)i; if(sha1_32(buf)==312368692){break;} }
        for(i=10;i<29;i++){ buf[289]=(byte)i; if(sha1_32(buf)==1836631792){break;} }
        for(i=-128;i<-113;i++){ buf[290]=(byte)i; if(sha1_32(buf)==169216778){break;} }
        for(i=44;i<59;i++){ buf[291]=(byte)i; if(sha1_32(buf)==-1147596722){break;} }
        for(i=36;i<56;i++){ buf[292]=(byte)i; if(sha1_32(buf)==-1995669640){break;} }
        for(i=-97;i<-67;i++){ buf[293]=(byte)i; if(sha1_32(buf)==1553136349){break;} }
        for(i=62;i<70;i++){ buf[294]=(byte)i; if(sha1_32(buf)==344771452){break;} }
        for(i=107;i<123;i++){ buf[295]=(byte)i; if(sha1_32(buf)==-93247475){break;} }
        for(i=35;i<40;i++){ buf[296]=(byte)i; if(sha1_32(buf)==-1462321840){break;} }
        for(i=87;i<107;i++){ buf[297]=(byte)i; if(sha1_32(buf)==-97917512){break;} }
        for(i=-31;i<-10;i++){ buf[298]=(byte)i; if(sha1_32(buf)==782754891){break;} }
        for(i=52;i<69;i++){ buf[299]=(byte)i; if(sha1_32(buf)==-1898450851){break;} }
        for(i=-43;i<-26;i++){ buf[300]=(byte)i; if(sha1_32(buf)==1739613052){break;} }
        for(i=-99;i<-70;i++){ buf[301]=(byte)i; if(sha1_32(buf)==-1157254312){break;} }
        for(i=-90;i<-74;i++){ buf[302]=(byte)i; if(sha1_32(buf)==-1251002546){break;} }
        for(i=107;i<128;i++){ buf[303]=(byte)i; if(sha1_32(buf)==2141961441){break;} }
        for(i=-46;i<-33;i++){ buf[304]=(byte)i; if(sha1_32(buf)==-909309415){break;} }
        for(i=86;i<99;i++){ buf[305]=(byte)i; if(sha1_32(buf)==1980399885){break;} }
        for(i=-2;i<4;i++){ buf[306]=(byte)i; if(sha1_32(buf)==-757342287){break;} }
        for(i=-103;i<-78;i++){ buf[307]=(byte)i; if(sha1_32(buf)==382864891){break;} }
        for(i=69;i<74;i++){ buf[308]=(byte)i; if(sha1_32(buf)==1948541063){break;} }
        for(i=-92;i<-71;i++){ buf[309]=(byte)i; if(sha1_32(buf)==-2035900567){break;} }
        for(i=-43;i<-25;i++){ buf[310]=(byte)i; if(sha1_32(buf)==1535453350){break;} }
        for(i=-20;i<-4;i++){ buf[311]=(byte)i; if(sha1_32(buf)==-1236314996){break;} }
        for(i=83;i<102;i++){ buf[312]=(byte)i; if(sha1_32(buf)==-661833758){break;} }
        for(i=6;i<14;i++){ buf[313]=(byte)i; if(sha1_32(buf)==-1155141202){break;} }
        for(i=-46;i<-29;i++){ buf[314]=(byte)i; if(sha1_32(buf)==508442377){break;} }
        for(i=87;i<90;i++){ buf[315]=(byte)i; if(sha1_32(buf)==-1981905641){break;} }
        for(i=121;i<128;i++){ buf[316]=(byte)i; if(sha1_32(buf)==-1502642858){break;} }
        for(i=-13;i<9;i++){ buf[317]=(byte)i; if(sha1_32(buf)==-658834098){break;} }
        for(i=31;i<39;i++){ buf[318]=(byte)i; if(sha1_32(buf)==-603501496){break;} }
        for(i=-40;i<-26;i++){ buf[319]=(byte)i; if(sha1_32(buf)==218394005){break;} }
        for(i=67;i<76;i++){ buf[320]=(byte)i; if(sha1_32(buf)==1469590864){break;} }
        for(i=-70;i<-53;i++){ buf[321]=(byte)i; if(sha1_32(buf)==1857485488){break;} }
        for(i=-126;i<-113;i++){ buf[322]=(byte)i; if(sha1_32(buf)==854135065){break;} }
        for(i=-85;i<-79;i++){ buf[323]=(byte)i; if(sha1_32(buf)==1867991255){break;} }
        for(i=-36;i<-19;i++){ buf[324]=(byte)i; if(sha1_32(buf)==1857057698){break;} }
        for(i=-31;i<-24;i++){ buf[325]=(byte)i; if(sha1_32(buf)==-529244393){break;} }
        for(i=85;i<111;i++){ buf[326]=(byte)i; if(sha1_32(buf)==-674195304){break;} }
        for(i=104;i<110;i++){ buf[327]=(byte)i; if(sha1_32(buf)==-13625080){break;} }
        for(i=-106;i<-78;i++){ buf[328]=(byte)i; if(sha1_32(buf)==-238709376){break;} }
        for(i=-126;i<-124;i++){ buf[329]=(byte)i; if(sha1_32(buf)==-1697108238){break;} }
        for(i=-70;i<-61;i++){ buf[330]=(byte)i; if(sha1_32(buf)==857424943){break;} }
        for(i=62;i<64;i++){ buf[331]=(byte)i; if(sha1_32(buf)==165034026){break;} }
        for(i=50;i<57;i++){ buf[332]=(byte)i; if(sha1_32(buf)==1318368545){break;} }
        for(i=-15;i<-5;i++){ buf[333]=(byte)i; if(sha1_32(buf)==-1388193989){break;} }
        for(i=-49;i<-43;i++){ buf[334]=(byte)i; if(sha1_32(buf)==-1043960394){break;} }
        for(i=4;i<25;i++){ buf[335]=(byte)i; if(sha1_32(buf)==-1382292524){break;} }
        for(i=71;i<83;i++){ buf[336]=(byte)i; if(sha1_32(buf)==-32483865){break;} }
        for(i=1;i<28;i++){ buf[337]=(byte)i; if(sha1_32(buf)==-747098166){break;} }
        for(i=-127;i<-121;i++){ buf[338]=(byte)i; if(sha1_32(buf)==1582186474){break;} }
        for(i=42;i<51;i++){ buf[339]=(byte)i; if(sha1_32(buf)==525881626){break;} }
        for(i=98;i<116;i++){ buf[340]=(byte)i; if(sha1_32(buf)==-453123593){break;} }
        for(i=103;i<117;i++){ buf[341]=(byte)i; if(sha1_32(buf)==1136417271){break;} }
        for(i=-70;i<-59;i++){ buf[342]=(byte)i; if(sha1_32(buf)==1972256812){break;} }
        for(i=74;i<83;i++){ buf[343]=(byte)i; if(sha1_32(buf)==111012725){break;} }
        for(i=38;i<52;i++){ buf[344]=(byte)i; if(sha1_32(buf)==871289222){break;} }
        for(i=-15;i<-8;i++){ buf[345]=(byte)i; if(sha1_32(buf)==-498768667){break;} }
        for(i=10;i<20;i++){ buf[346]=(byte)i; if(sha1_32(buf)==-1412622571){break;} }
        for(i=59;i<66;i++){ buf[347]=(byte)i; if(sha1_32(buf)==1798933897){break;} }
        for(i=-103;i<-89;i++){ buf[348]=(byte)i; if(sha1_32(buf)==1251166478){break;} }
        for(i=62;i<74;i++){ buf[349]=(byte)i; if(sha1_32(buf)==1189072525){break;} }
        for(i=54;i<82;i++){ buf[350]=(byte)i; if(sha1_32(buf)==-389626517){break;} }
        for(i=-116;i<-100;i++){ buf[351]=(byte)i; if(sha1_32(buf)==748356092){break;} }
        for(i=111;i<123;i++){ buf[352]=(byte)i; if(sha1_32(buf)==-1599390477){break;} }
        for(i=28;i<39;i++){ buf[353]=(byte)i; if(sha1_32(buf)==-2107970334){break;} }
        for(i=-128;i<-112;i++){ buf[354]=(byte)i; if(sha1_32(buf)==-1486510854){break;} }
        for(i=-118;i<-101;i++){ buf[355]=(byte)i; if(sha1_32(buf)==-2107632201){break;} }
        for(i=103;i<119;i++){ buf[356]=(byte)i; if(sha1_32(buf)==151885951){break;} }
        for(i=16;i<27;i++){ buf[357]=(byte)i; if(sha1_32(buf)==637699745){break;} }
        for(i=88;i<107;i++){ buf[358]=(byte)i; if(sha1_32(buf)==540187965){break;} }
        for(i=27;i<33;i++){ buf[359]=(byte)i; if(sha1_32(buf)==1984670253){break;} }
        for(i=93;i<102;i++){ buf[360]=(byte)i; if(sha1_32(buf)==40312278){break;} }
        for(i=9;i<26;i++){ buf[361]=(byte)i; if(sha1_32(buf)==1916671538){break;} }
        for(i=-128;i<-112;i++){ buf[362]=(byte)i; if(sha1_32(buf)==572133115){break;} }
        for(i=59;i<64;i++){ buf[363]=(byte)i; if(sha1_32(buf)==2137664162){break;} }
        for(i=43;i<56;i++){ buf[364]=(byte)i; if(sha1_32(buf)==-93496845){break;} }
        for(i=35;i<40;i++){ buf[365]=(byte)i; if(sha1_32(buf)==302066698){break;} }
        for(i=-120;i<-98;i++){ buf[366]=(byte)i; if(sha1_32(buf)==-1160088804){break;} }
        for(i=79;i<89;i++){ buf[367]=(byte)i; if(sha1_32(buf)==73326280){break;} }
        for(i=-39;i<-18;i++){ buf[368]=(byte)i; if(sha1_32(buf)==1006885811){break;} }
        for(i=-48;i<-30;i++){ buf[369]=(byte)i; if(sha1_32(buf)==-1225760656){break;} }
        for(i=99;i<128;i++){ buf[370]=(byte)i; if(sha1_32(buf)==297602117){break;} }
        for(i=-59;i<-33;i++){ buf[371]=(byte)i; if(sha1_32(buf)==1956975649){break;} }
        for(i=69;i<84;i++){ buf[372]=(byte)i; if(sha1_32(buf)==266758598){break;} }
        for(i=43;i<53;i++){ buf[373]=(byte)i; if(sha1_32(buf)==-1644303471){break;} }
        for(i=-21;i<-20;i++){ buf[374]=(byte)i; if(sha1_32(buf)==718824920){break;} }
        for(i=-22;i<2;i++){ buf[375]=(byte)i; if(sha1_32(buf)==-333753228){break;} }
        for(i=-38;i<-16;i++){ buf[376]=(byte)i; if(sha1_32(buf)==-273641263){break;} }
        for(i=-13;i<18;i++){ buf[377]=(byte)i; if(sha1_32(buf)==-2068901553){break;} }
        for(i=-85;i<-69;i++){ buf[378]=(byte)i; if(sha1_32(buf)==210913221){break;} }
        for(i=-80;i<-63;i++){ buf[379]=(byte)i; if(sha1_32(buf)==788948343){break;} }
        for(i=1;i<5;i++){ buf[380]=(byte)i; if(sha1_32(buf)==1666473422){break;} }
        for(i=88;i<96;i++){ buf[381]=(byte)i; if(sha1_32(buf)==-1347995814){break;} }
        for(i=24;i<38;i++){ buf[382]=(byte)i; if(sha1_32(buf)==-666463494){break;} }
        for(i=21;i<28;i++){ buf[383]=(byte)i; if(sha1_32(buf)==9459524){break;} }
        for(i=67;i<83;i++){ buf[384]=(byte)i; if(sha1_32(buf)==2043665301){break;} }
        for(i=21;i<30;i++){ buf[385]=(byte)i; if(sha1_32(buf)==784874235){break;} }
        for(i=59;i<61;i++){ buf[386]=(byte)i; if(sha1_32(buf)==1293817724){break;} }
        for(i=-123;i<-101;i++){ buf[387]=(byte)i; if(sha1_32(buf)==2009210198){break;} }
        for(i=-55;i<-44;i++){ buf[388]=(byte)i; if(sha1_32(buf)==-1195544209){break;} }
        for(i=14;i<27;i++){ buf[389]=(byte)i; if(sha1_32(buf)==1341266377){break;} }
        for(i=85;i<113;i++){ buf[390]=(byte)i; if(sha1_32(buf)==-39844466){break;} }
        for(i=-92;i<-75;i++){ buf[391]=(byte)i; if(sha1_32(buf)==-2119996261){break;} }
        for(i=-49;i<-38;i++){ buf[392]=(byte)i; if(sha1_32(buf)==788411860){break;} }
        for(i=10;i<30;i++){ buf[393]=(byte)i; if(sha1_32(buf)==1276378362){break;} }
        for(i=64;i<85;i++){ buf[394]=(byte)i; if(sha1_32(buf)==757224614){break;} }
        for(i=-1;i<17;i++){ buf[395]=(byte)i; if(sha1_32(buf)==440835244){break;} }
        for(i=72;i<84;i++){ buf[396]=(byte)i; if(sha1_32(buf)==-1454608892){break;} }
        for(i=-55;i<-43;i++){ buf[397]=(byte)i; if(sha1_32(buf)==2007897324){break;} }
        for(i=-94;i<-84;i++){ buf[398]=(byte)i; if(sha1_32(buf)==1841619669){break;} }
        for(i=-26;i<-13;i++){ buf[399]=(byte)i; if(sha1_32(buf)==-1524507786){break;} }
        for(i=2;i<21;i++){ buf[400]=(byte)i; if(sha1_32(buf)==-2064995370){break;} }
        for(i=114;i<128;i++){ buf[401]=(byte)i; if(sha1_32(buf)==-200032846){break;} }
        for(i=107;i<118;i++){ buf[402]=(byte)i; if(sha1_32(buf)==-1224656141){break;} }
        for(i=-14;i<8;i++){ buf[403]=(byte)i; if(sha1_32(buf)==-1224656141){break;} }
        for(i=31;i<38;i++){ buf[404]=(byte)i; if(sha1_32(buf)==1987118217){break;} }
        for(i=-54;i<-37;i++){ buf[405]=(byte)i; if(sha1_32(buf)==-1222395106){break;} }
        for(i=-51;i<-35;i++){ buf[406]=(byte)i; if(sha1_32(buf)==-967946746){break;} }
        for(i=-75;i<-58;i++){ buf[407]=(byte)i; if(sha1_32(buf)==836074083){break;} }
        for(i=22;i<45;i++){ buf[408]=(byte)i; if(sha1_32(buf)==-1259803446){break;} }
        for(i=-61;i<-38;i++){ buf[409]=(byte)i; if(sha1_32(buf)==-1502716770){break;} }
        for(i=-72;i<-46;i++){ buf[410]=(byte)i; if(sha1_32(buf)==-1264484628){break;} }
        for(i=109;i<125;i++){ buf[411]=(byte)i; if(sha1_32(buf)==-2569648){break;} }
        for(i=-126;i<-108;i++){ buf[412]=(byte)i; if(sha1_32(buf)==-1798410624){break;} }
        for(i=-128;i<-122;i++){ buf[413]=(byte)i; if(sha1_32(buf)==-2033263713){break;} }
        for(i=112;i<126;i++){ buf[414]=(byte)i; if(sha1_32(buf)==1631742654){break;} }
        for(i=85;i<116;i++){ buf[415]=(byte)i; if(sha1_32(buf)==845857031){break;} }
        for(i=-118;i<-103;i++){ buf[416]=(byte)i; if(sha1_32(buf)==859047061){break;} }
        for(i=-1;i<15;i++){ buf[417]=(byte)i; if(sha1_32(buf)==224234045){break;} }
        for(i=-39;i<-35;i++){ buf[418]=(byte)i; if(sha1_32(buf)==-785730018){break;} }
        for(i=-115;i<-94;i++){ buf[419]=(byte)i; if(sha1_32(buf)==54000415){break;} }
        for(i=87;i<103;i++){ buf[420]=(byte)i; if(sha1_32(buf)==847557873){break;} }
        for(i=-128;i<-116;i++){ buf[421]=(byte)i; if(sha1_32(buf)==1127457851){break;} }
        for(i=-62;i<-53;i++){ buf[422]=(byte)i; if(sha1_32(buf)==-172837181){break;} }
        for(i=75;i<83;i++){ buf[423]=(byte)i; if(sha1_32(buf)==-299722852){break;} }
        for(i=26;i<56;i++){ buf[424]=(byte)i; if(sha1_32(buf)==1126317307){break;} }
        for(i=47;i<62;i++){ buf[425]=(byte)i; if(sha1_32(buf)==-1563216217){break;} }
        for(i=-87;i<-71;i++){ buf[426]=(byte)i; if(sha1_32(buf)==-656229224){break;} }
        for(i=-38;i<-33;i++){ buf[427]=(byte)i; if(sha1_32(buf)==476702684){break;} }
        for(i=37;i<57;i++){ buf[428]=(byte)i; if(sha1_32(buf)==-348861679){break;} }
        for(i=53;i<77;i++){ buf[429]=(byte)i; if(sha1_32(buf)==-1130845733){break;} }
        for(i=-48;i<-31;i++){ buf[430]=(byte)i; if(sha1_32(buf)==2031494866){break;} }
        for(i=-97;i<-78;i++){ buf[431]=(byte)i; if(sha1_32(buf)==179792868){break;} }
        for(i=66;i<75;i++){ buf[432]=(byte)i; if(sha1_32(buf)==2071372121){break;} }
        for(i=-66;i<-57;i++){ buf[433]=(byte)i; if(sha1_32(buf)==1399230584){break;} }
        for(i=-56;i<-52;i++){ buf[434]=(byte)i; if(sha1_32(buf)==-95854081){break;} }
        for(i=-3;i<10;i++){ buf[435]=(byte)i; if(sha1_32(buf)==1635027797){break;} }
        for(i=92;i<109;i++){ buf[436]=(byte)i; if(sha1_32(buf)==435745515){break;} }
        for(i=52;i<66;i++){ buf[437]=(byte)i; if(sha1_32(buf)==1803020768){break;} }
        for(i=-121;i<-92;i++){ buf[438]=(byte)i; if(sha1_32(buf)==121954619){break;} }
        for(i=4;i<13;i++){ buf[439]=(byte)i; if(sha1_32(buf)==-249765047){break;} }
        for(i=43;i<56;i++){ buf[440]=(byte)i; if(sha1_32(buf)==172542754){break;} }
        for(i=-14;i<4;i++){ buf[441]=(byte)i; if(sha1_32(buf)==87541684){break;} }
        for(i=-41;i<-32;i++){ buf[442]=(byte)i; if(sha1_32(buf)==-906730089){break;} }
        for(i=113;i<128;i++){ buf[443]=(byte)i; if(sha1_32(buf)==-1725771746){break;} }
        for(i=95;i<120;i++){ buf[444]=(byte)i; if(sha1_32(buf)==-1454531239){break;} }
        for(i=19;i<24;i++){ buf[445]=(byte)i; if(sha1_32(buf)==590899695){break;} }
        for(i=-19;i<-4;i++){ buf[446]=(byte)i; if(sha1_32(buf)==-2058097854){break;} }
        for(i=44;i<70;i++){ buf[447]=(byte)i; if(sha1_32(buf)==1516924116){break;} }
        for(i=-90;i<-76;i++){ buf[448]=(byte)i; if(sha1_32(buf)==1577480022){break;} }
        for(i=-73;i<-67;i++){ buf[449]=(byte)i; if(sha1_32(buf)==-808697391){break;} }
        for(i=56;i<67;i++){ buf[450]=(byte)i; if(sha1_32(buf)==215506114){break;} }
        for(i=8;i<18;i++){ buf[451]=(byte)i; if(sha1_32(buf)==46153381){break;} }
        for(i=74;i<88;i++){ buf[452]=(byte)i; if(sha1_32(buf)==-1274424917){break;} }
        for(i=-118;i<-113;i++){ buf[453]=(byte)i; if(sha1_32(buf)==-1945045520){break;} }
        for(i=-118;i<-107;i++){ buf[454]=(byte)i; if(sha1_32(buf)==1402669460){break;} }
        for(i=-58;i<-42;i++){ buf[455]=(byte)i; if(sha1_32(buf)==2096930317){break;} }
        for(i=-20;i<-2;i++){ buf[456]=(byte)i; if(sha1_32(buf)==803282262){break;} }
        for(i=-54;i<-38;i++){ buf[457]=(byte)i; if(sha1_32(buf)==1245894495){break;} }
        for(i=-87;i<-72;i++){ buf[458]=(byte)i; if(sha1_32(buf)==-2014362234){break;} }
        for(i=-61;i<-40;i++){ buf[459]=(byte)i; if(sha1_32(buf)==69193826){break;} }
        for(i=38;i<47;i++){ buf[460]=(byte)i; if(sha1_32(buf)==528590127){break;} }
        for(i=122;i<126;i++){ buf[461]=(byte)i; if(sha1_32(buf)==900100363){break;} }
        for(i=-47;i<-35;i++){ buf[462]=(byte)i; if(sha1_32(buf)==1196529667){break;} }
        for(i=14;i<33;i++){ buf[463]=(byte)i; if(sha1_32(buf)==-2025489730){break;} }
        for(i=79;i<83;i++){ buf[464]=(byte)i; if(sha1_32(buf)==-1496945178){break;} }
        for(i=80;i<90;i++){ buf[465]=(byte)i; if(sha1_32(buf)==-2083146956){break;} }
        for(i=23;i<42;i++){ buf[466]=(byte)i; if(sha1_32(buf)==952638675){break;} }
        for(i=2;i<20;i++){ buf[467]=(byte)i; if(sha1_32(buf)==-1723945146){break;} }
        for(i=-128;i<-121;i++){ buf[468]=(byte)i; if(sha1_32(buf)==-2111626600){break;} }
        for(i=36;i<52;i++){ buf[469]=(byte)i; if(sha1_32(buf)==79457578){break;} }
        for(i=26;i<47;i++){ buf[470]=(byte)i; if(sha1_32(buf)==-1614804211){break;} }
        for(i=64;i<79;i++){ buf[471]=(byte)i; if(sha1_32(buf)==963210343){break;} }
        for(i=109;i<121;i++){ buf[472]=(byte)i; if(sha1_32(buf)==-235260891){break;} }
        for(i=-59;i<-47;i++){ buf[473]=(byte)i; if(sha1_32(buf)==205930663){break;} }
        for(i=-27;i<-17;i++){ buf[474]=(byte)i; if(sha1_32(buf)==1694202492){break;} }
        for(i=109;i<128;i++){ buf[475]=(byte)i; if(sha1_32(buf)==1522456784){break;} }
        for(i=92;i<102;i++){ buf[476]=(byte)i; if(sha1_32(buf)==-2026815268){break;} }
        for(i=93;i<110;i++){ buf[477]=(byte)i; if(sha1_32(buf)==-1243775521){break;} }
        for(i=69;i<86;i++){ buf[478]=(byte)i; if(sha1_32(buf)==-287724215){break;} }
        for(i=-5;i<7;i++){ buf[479]=(byte)i; if(sha1_32(buf)==193906585){break;} }
        for(i=-45;i<-26;i++){ buf[480]=(byte)i; if(sha1_32(buf)==2088026411){break;} }
        for(i=5;i<17;i++){ buf[481]=(byte)i; if(sha1_32(buf)==62810851){break;} }
        for(i=30;i<55;i++){ buf[482]=(byte)i; if(sha1_32(buf)==-1597509951){break;} }
        for(i=-122;i<-110;i++){ buf[483]=(byte)i; if(sha1_32(buf)==-1588783451){break;} }
        for(i=-127;i<-111;i++){ buf[484]=(byte)i; if(sha1_32(buf)==-1559453695){break;} }
        for(i=63;i<85;i++){ buf[485]=(byte)i; if(sha1_32(buf)==1391727267){break;} }
        for(i=-38;i<-21;i++){ buf[486]=(byte)i; if(sha1_32(buf)==-1210163329){break;} }
        for(i=-128;i<-114;i++){ buf[487]=(byte)i; if(sha1_32(buf)==-2104751832){break;} }
        for(i=-118;i<-103;i++){ buf[488]=(byte)i; if(sha1_32(buf)==2034173652){break;} }
        for(i=22;i<36;i++){ buf[489]=(byte)i; if(sha1_32(buf)==-716624271){break;} }
        for(i=-128;i<-122;i++){ buf[490]=(byte)i; if(sha1_32(buf)==1018358890){break;} }
        for(i=-73;i<-61;i++){ buf[491]=(byte)i; if(sha1_32(buf)==1251615470){break;} }
        for(i=103;i<114;i++){ buf[492]=(byte)i; if(sha1_32(buf)==-5948339){break;} }
        for(i=50;i<65;i++){ buf[493]=(byte)i; if(sha1_32(buf)==1070834708){break;} }
        for(i=-67;i<-49;i++){ buf[494]=(byte)i; if(sha1_32(buf)==-1455219801){break;} }
        for(i=72;i<87;i++){ buf[495]=(byte)i; if(sha1_32(buf)==-648804168){break;} }
        for(i=-76;i<-57;i++){ buf[496]=(byte)i; if(sha1_32(buf)==-2114677849){break;} }
        for(i=-36;i<-24;i++){ buf[497]=(byte)i; if(sha1_32(buf)==-1236926831){break;} }
        for(i=-128;i<-118;i++){ buf[498]=(byte)i; if(sha1_32(buf)==1494690179){break;} }
        for(i=-75;i<-61;i++){ buf[499]=(byte)i; if(sha1_32(buf)==176565156){break;} }
        for(i=-52;i<-32;i++){ buf[500]=(byte)i; if(sha1_32(buf)==-405309265){break;} }
        for(i=-117;i<-91;i++){ buf[501]=(byte)i; if(sha1_32(buf)==867638842){break;} }
        for(i=-54;i<-41;i++){ buf[502]=(byte)i; if(sha1_32(buf)==-1876623197){break;} }
        for(i=-86;i<-69;i++){ buf[503]=(byte)i; if(sha1_32(buf)==-948574101){break;} }
        for(i=-4;i<8;i++){ buf[504]=(byte)i; if(sha1_32(buf)==-1638517293){break;} }
        for(i=64;i<74;i++){ buf[505]=(byte)i; if(sha1_32(buf)==944374782){break;} }
        for(i=-11;i<14;i++){ buf[506]=(byte)i; if(sha1_32(buf)==-27062817){break;} }
        for(i=101;i<109;i++){ buf[507]=(byte)i; if(sha1_32(buf)==1100873852){break;} }
        for(i=-123;i<-119;i++){ buf[508]=(byte)i; if(sha1_32(buf)==-1017941370){break;} }
        for(i=-18;i<0;i++){ buf[509]=(byte)i; if(sha1_32(buf)==942934302){break;} }
        for(i=74;i<91;i++){ buf[510]=(byte)i; if(sha1_32(buf)==1594176613){break;} }
        for(i=10;i<13;i++){ buf[511]=(byte)i; if(sha1_32(buf)==1640496136){break;} }
        for(i=-93;i<-74;i++){ buf[512]=(byte)i; if(sha1_32(buf)==126886761){break;} }
        for(i=52;i<66;i++){ buf[513]=(byte)i; if(sha1_32(buf)==-15076105){break;} }
        for(i=-95;i<-78;i++){ buf[514]=(byte)i; if(sha1_32(buf)==-73334306){break;} }
        for(i=-5;i<6;i++){ buf[515]=(byte)i; if(sha1_32(buf)==818930876){break;} }
        for(i=-3;i<2;i++){ buf[516]=(byte)i; if(sha1_32(buf)==-1434367605){break;} }
        for(i=5;i<17;i++){ buf[517]=(byte)i; if(sha1_32(buf)==-1062902362){break;} }
        for(i=-119;i<-94;i++){ buf[518]=(byte)i; if(sha1_32(buf)==-878180012){break;} }
        for(i=-45;i<-34;i++){ buf[519]=(byte)i; if(sha1_32(buf)==942546876){break;} }
        for(i=-102;i<-88;i++){ buf[520]=(byte)i; if(sha1_32(buf)==-1128131442){break;} }
        for(i=-1;i<6;i++){ buf[521]=(byte)i; if(sha1_32(buf)==-1094455959){break;} }
        for(i=84;i<89;i++){ buf[522]=(byte)i; if(sha1_32(buf)==952597448){break;} }
        for(i=31;i<39;i++){ buf[523]=(byte)i; if(sha1_32(buf)==-1622590906){break;} }
        for(i=-45;i<-27;i++){ buf[524]=(byte)i; if(sha1_32(buf)==1586905281){break;} }
        for(i=0;i<17;i++){ buf[525]=(byte)i; if(sha1_32(buf)==1826542150){break;} }
        for(i=36;i<57;i++){ buf[526]=(byte)i; if(sha1_32(buf)==-1722172241){break;} }
        for(i=0;i<16;i++){ buf[527]=(byte)i; if(sha1_32(buf)==1254720003){break;} }
        for(i=86;i<102;i++){ buf[528]=(byte)i; if(sha1_32(buf)==138482690){break;} }
        for(i=25;i<38;i++){ buf[529]=(byte)i; if(sha1_32(buf)==642531719){break;} }
        for(i=-71;i<-61;i++){ buf[530]=(byte)i; if(sha1_32(buf)==1318649929){break;} }
        for(i=76;i<83;i++){ buf[531]=(byte)i; if(sha1_32(buf)==-1702672858){break;} }
        for(i=3;i<11;i++){ buf[532]=(byte)i; if(sha1_32(buf)==-1892348830){break;} }
        for(i=-20;i<-3;i++){ buf[533]=(byte)i; if(sha1_32(buf)==868459522){break;} }
        for(i=34;i<43;i++){ buf[534]=(byte)i; if(sha1_32(buf)==-1907633978){break;} }
        for(i=-119;i<-96;i++){ buf[535]=(byte)i; if(sha1_32(buf)==-1890014177){break;} }
        for(i=-37;i<-17;i++){ buf[536]=(byte)i; if(sha1_32(buf)==1316648350){break;} }
        for(i=-11;i<7;i++){ buf[537]=(byte)i; if(sha1_32(buf)==1264369881){break;} }
        for(i=109;i<116;i++){ buf[538]=(byte)i; if(sha1_32(buf)==-1967338890){break;} }
        for(i=37;i<49;i++){ buf[539]=(byte)i; if(sha1_32(buf)==-1922225401){break;} }
        for(i=-8;i<12;i++){ buf[540]=(byte)i; if(sha1_32(buf)==-201151809){break;} }
        for(i=-21;i<7;i++){ buf[541]=(byte)i; if(sha1_32(buf)==-1492775319){break;} }
        for(i=-76;i<-59;i++){ buf[542]=(byte)i; if(sha1_32(buf)==41309159){break;} }
        for(i=110;i<124;i++){ buf[543]=(byte)i; if(sha1_32(buf)==767359815){break;} }
        for(i=-65;i<-57;i++){ buf[544]=(byte)i; if(sha1_32(buf)==893037413){break;} }
        for(i=-18;i<-10;i++){ buf[545]=(byte)i; if(sha1_32(buf)==-2116458725){break;} }
        for(i=-108;i<-97;i++){ buf[546]=(byte)i; if(sha1_32(buf)==874323420){break;} }
        for(i=-73;i<-45;i++){ buf[547]=(byte)i; if(sha1_32(buf)==965486519){break;} }
        for(i=-40;i<-11;i++){ buf[548]=(byte)i; if(sha1_32(buf)==-608869144){break;} }
        for(i=82;i<94;i++){ buf[549]=(byte)i; if(sha1_32(buf)==-1020496053){break;} }
        for(i=-100;i<-83;i++){ buf[550]=(byte)i; if(sha1_32(buf)==1230756512){break;} }
        for(i=18;i<41;i++){ buf[551]=(byte)i; if(sha1_32(buf)==-1740358631){break;} }
        for(i=52;i<65;i++){ buf[552]=(byte)i; if(sha1_32(buf)==-761403546){break;} }
        for(i=41;i<62;i++){ buf[553]=(byte)i; if(sha1_32(buf)==-2140880753){break;} }
        for(i=113;i<128;i++){ buf[554]=(byte)i; if(sha1_32(buf)==1651122898){break;} }
        for(i=60;i<76;i++){ buf[555]=(byte)i; if(sha1_32(buf)==-1688461967){break;} }
        for(i=19;i<33;i++){ buf[556]=(byte)i; if(sha1_32(buf)==979007027){break;} }
        for(i=-123;i<-107;i++){ buf[557]=(byte)i; if(sha1_32(buf)==494710264){break;} }
        for(i=-112;i<-93;i++){ buf[558]=(byte)i; if(sha1_32(buf)==-1805854772){break;} }
        for(i=47;i<56;i++){ buf[559]=(byte)i; if(sha1_32(buf)==1770452054){break;} }
        for(i=-16;i<1;i++){ buf[560]=(byte)i; if(sha1_32(buf)==-1783637915){break;} }
        for(i=68;i<75;i++){ buf[561]=(byte)i; if(sha1_32(buf)==1094584316){break;} }
        for(i=-71;i<-49;i++){ buf[562]=(byte)i; if(sha1_32(buf)==3426076){break;} }
        for(i=56;i<72;i++){ buf[563]=(byte)i; if(sha1_32(buf)==-510219737){break;} }
        for(i=66;i<85;i++){ buf[564]=(byte)i; if(sha1_32(buf)==-1610510675){break;} }
        for(i=35;i<54;i++){ buf[565]=(byte)i; if(sha1_32(buf)==1205277943){break;} }
        for(i=25;i<49;i++){ buf[566]=(byte)i; if(sha1_32(buf)==-356175155){break;} }
        for(i=6;i<27;i++){ buf[567]=(byte)i; if(sha1_32(buf)==-249066172){break;} }
        for(i=-90;i<-77;i++){ buf[568]=(byte)i; if(sha1_32(buf)==-929804686){break;} }
        for(i=116;i<128;i++){ buf[569]=(byte)i; if(sha1_32(buf)==-998184111){break;} }
        for(i=59;i<75;i++){ buf[570]=(byte)i; if(sha1_32(buf)==-1762497887){break;} }
        for(i=76;i<90;i++){ buf[571]=(byte)i; if(sha1_32(buf)==-739635197){break;} }
        for(i=121;i<128;i++){ buf[572]=(byte)i; if(sha1_32(buf)==1325367932){break;} }
        for(i=41;i<55;i++){ buf[573]=(byte)i; if(sha1_32(buf)==763206455){break;} }
        for(i=82;i<94;i++){ buf[574]=(byte)i; if(sha1_32(buf)==1706302697){break;} }
        for(i=-51;i<-35;i++){ buf[575]=(byte)i; if(sha1_32(buf)==-2042146585){break;} }
        for(i=56;i<72;i++){ buf[576]=(byte)i; if(sha1_32(buf)==1692046707){break;} }
        for(i=-10;i<10;i++){ buf[577]=(byte)i; if(sha1_32(buf)==-292388036){break;} }
        for(i=102;i<119;i++){ buf[578]=(byte)i; if(sha1_32(buf)==152023783){break;} }
        for(i=95;i<108;i++){ buf[579]=(byte)i; if(sha1_32(buf)==-785191802){break;} }
        for(i=75;i<93;i++){ buf[580]=(byte)i; if(sha1_32(buf)==1164287253){break;} }
        for(i=2;i<17;i++){ buf[581]=(byte)i; if(sha1_32(buf)==857907266){break;} }
        for(i=0;i<6;i++){ buf[582]=(byte)i; if(sha1_32(buf)==872842192){break;} }
        for(i=-128;i<-112;i++){ buf[583]=(byte)i; if(sha1_32(buf)==-1267895163){break;} }
        for(i=13;i<37;i++){ buf[584]=(byte)i; if(sha1_32(buf)==765378826){break;} }
        for(i=-100;i<-78;i++){ buf[585]=(byte)i; if(sha1_32(buf)==-1753932811){break;} }
        for(i=71;i<92;i++){ buf[586]=(byte)i; if(sha1_32(buf)==1127397919){break;} }
        for(i=51;i<72;i++){ buf[587]=(byte)i; if(sha1_32(buf)==45450599){break;} }
        for(i=-95;i<-75;i++){ buf[588]=(byte)i; if(sha1_32(buf)==-567429081){break;} }
        for(i=-30;i<-25;i++){ buf[589]=(byte)i; if(sha1_32(buf)==-1213567208){break;} }
        for(i=-119;i<-100;i++){ buf[590]=(byte)i; if(sha1_32(buf)==-1871420340){break;} }
        for(i=80;i<94;i++){ buf[591]=(byte)i; if(sha1_32(buf)==-1302287107){break;} }
        for(i=-95;i<-88;i++){ buf[592]=(byte)i; if(sha1_32(buf)==1656167336){break;} }
        for(i=10;i<34;i++){ buf[593]=(byte)i; if(sha1_32(buf)==-1746271277){break;} }
        for(i=-75;i<-55;i++){ buf[594]=(byte)i; if(sha1_32(buf)==-1026164977){break;} }
        for(i=45;i<64;i++){ buf[595]=(byte)i; if(sha1_32(buf)==-1819362233){break;} }
        for(i=6;i<22;i++){ buf[596]=(byte)i; if(sha1_32(buf)==1980553474){break;} }
        for(i=-100;i<-84;i++){ buf[597]=(byte)i; if(sha1_32(buf)==1430261795){break;} }
        for(i=-66;i<-59;i++){ buf[598]=(byte)i; if(sha1_32(buf)==-1708322828){break;} }
        for(i=16;i<45;i++){ buf[599]=(byte)i; if(sha1_32(buf)==-38055){break;} }
        for(i=-68;i<-58;i++){ buf[600]=(byte)i; if(sha1_32(buf)==1064958923){break;} }
        for(i=42;i<58;i++){ buf[601]=(byte)i; if(sha1_32(buf)==1437738391){break;} }
        for(i=60;i<76;i++){ buf[602]=(byte)i; if(sha1_32(buf)==-805042979){break;} }
        for(i=-109;i<-106;i++){ buf[603]=(byte)i; if(sha1_32(buf)==-1397410358){break;} }
        for(i=-37;i<-11;i++){ buf[604]=(byte)i; if(sha1_32(buf)==1575227110){break;} }
        for(i=59;i<82;i++){ buf[605]=(byte)i; if(sha1_32(buf)==-1994415389){break;} }
        for(i=-125;i<-107;i++){ buf[606]=(byte)i; if(sha1_32(buf)==308062549){break;} }
        for(i=101;i<128;i++){ buf[607]=(byte)i; if(sha1_32(buf)==915905127){break;} }
        for(i=-109;i<-101;i++){ buf[608]=(byte)i; if(sha1_32(buf)==-1031978673){break;} }
        for(i=-39;i<-13;i++){ buf[609]=(byte)i; if(sha1_32(buf)==-727160448){break;} }
        for(i=-46;i<-44;i++){ buf[610]=(byte)i; if(sha1_32(buf)==-1085003393){break;} }
        for(i=-35;i<-19;i++){ buf[611]=(byte)i; if(sha1_32(buf)==-445538686){break;} }
        for(i=10;i<32;i++){ buf[612]=(byte)i; if(sha1_32(buf)==31777477){break;} }
        for(i=103;i<121;i++){ buf[613]=(byte)i; if(sha1_32(buf)==166667929){break;} }
        for(i=-8;i<8;i++){ buf[614]=(byte)i; if(sha1_32(buf)==-320455083){break;} }
        for(i=-103;i<-88;i++){ buf[615]=(byte)i; if(sha1_32(buf)==-1162101345){break;} }
        for(i=-77;i<-68;i++){ buf[616]=(byte)i; if(sha1_32(buf)==-501006557){break;} }
        for(i=-43;i<-39;i++){ buf[617]=(byte)i; if(sha1_32(buf)==673514234){break;} }
        for(i=-124;i<-116;i++){ buf[618]=(byte)i; if(sha1_32(buf)==-952006063){break;} }
        for(i=-14;i<1;i++){ buf[619]=(byte)i; if(sha1_32(buf)==1029191896){break;} }
        for(i=74;i<77;i++){ buf[620]=(byte)i; if(sha1_32(buf)==1296698669){break;} }
        for(i=25;i<36;i++){ buf[621]=(byte)i; if(sha1_32(buf)==1294020020){break;} }
        for(i=-37;i<-17;i++){ buf[622]=(byte)i; if(sha1_32(buf)==-386660794){break;} }
        for(i=-49;i<-23;i++){ buf[623]=(byte)i; if(sha1_32(buf)==-866278362){break;} }
        for(i=-93;i<-80;i++){ buf[624]=(byte)i; if(sha1_32(buf)==957738921){break;} }
        for(i=60;i<84;i++){ buf[625]=(byte)i; if(sha1_32(buf)==-285714077){break;} }
        for(i=79;i<85;i++){ buf[626]=(byte)i; if(sha1_32(buf)==1458159272){break;} }
        for(i=101;i<120;i++){ buf[627]=(byte)i; if(sha1_32(buf)==-215359956){break;} }
        for(i=-111;i<-106;i++){ buf[628]=(byte)i; if(sha1_32(buf)==772011190){break;} }
        for(i=88;i<103;i++){ buf[629]=(byte)i; if(sha1_32(buf)==-1279450941){break;} }
        for(i=-30;i<-15;i++){ buf[630]=(byte)i; if(sha1_32(buf)==1112886918){break;} }
        for(i=67;i<83;i++){ buf[631]=(byte)i; if(sha1_32(buf)==-1865314971){break;} }
        for(i=104;i<119;i++){ buf[632]=(byte)i; if(sha1_32(buf)==1347731273){break;} }
        for(i=-79;i<-54;i++){ buf[633]=(byte)i; if(sha1_32(buf)==-589468583){break;} }
        for(i=74;i<100;i++){ buf[634]=(byte)i; if(sha1_32(buf)==-351164098){break;} }
        for(i=33;i<57;i++){ buf[635]=(byte)i; if(sha1_32(buf)==-2058102936){break;} }
        for(i=-34;i<-15;i++){ buf[636]=(byte)i; if(sha1_32(buf)==1179272569){break;} }
        for(i=59;i<71;i++){ buf[637]=(byte)i; if(sha1_32(buf)==-1746250309){break;} }
        for(i=-38;i<-25;i++){ buf[638]=(byte)i; if(sha1_32(buf)==1569181213){break;} }
        for(i=-128;i<-114;i++){ buf[639]=(byte)i; if(sha1_32(buf)==-1666703633){break;} }
        for(i=-52;i<-26;i++){ buf[640]=(byte)i; if(sha1_32(buf)==-1371565502){break;} }
        for(i=-53;i<-42;i++){ buf[641]=(byte)i; if(sha1_32(buf)==-344374930){break;} }
        for(i=-118;i<-101;i++){ buf[642]=(byte)i; if(sha1_32(buf)==1303792864){break;} }
        for(i=18;i<31;i++){ buf[643]=(byte)i; if(sha1_32(buf)==1176000310){break;} }
        for(i=-66;i<-48;i++){ buf[644]=(byte)i; if(sha1_32(buf)==2056599548){break;} }
        for(i=44;i<65;i++){ buf[645]=(byte)i; if(sha1_32(buf)==-817900044){break;} }
        for(i=-40;i<-31;i++){ buf[646]=(byte)i; if(sha1_32(buf)==1972773231){break;} }
        for(i=-40;i<-22;i++){ buf[647]=(byte)i; if(sha1_32(buf)==2117701165){break;} }
        for(i=51;i<72;i++){ buf[648]=(byte)i; if(sha1_32(buf)==-1120029942){break;} }
        for(i=-114;i<-99;i++){ buf[649]=(byte)i; if(sha1_32(buf)==-306929693){break;} }
        for(i=8;i<28;i++){ buf[650]=(byte)i; if(sha1_32(buf)==90286265){break;} }
        for(i=-63;i<-36;i++){ buf[651]=(byte)i; if(sha1_32(buf)==740646193){break;} }
        for(i=35;i<51;i++){ buf[652]=(byte)i; if(sha1_32(buf)==-2031449311){break;} }
        for(i=-33;i<-14;i++){ buf[653]=(byte)i; if(sha1_32(buf)==-742160296){break;} }
        for(i=-121;i<-106;i++){ buf[654]=(byte)i; if(sha1_32(buf)==1304048474){break;} }
        for(i=16;i<33;i++){ buf[655]=(byte)i; if(sha1_32(buf)==1867352727){break;} }
        for(i=68;i<87;i++){ buf[656]=(byte)i; if(sha1_32(buf)==548715056){break;} }
        for(i=-1;i<23;i++){ buf[657]=(byte)i; if(sha1_32(buf)==453378172){break;} }
        for(i=-51;i<-38;i++){ buf[658]=(byte)i; if(sha1_32(buf)==381878234){break;} }
        for(i=62;i<80;i++){ buf[659]=(byte)i; if(sha1_32(buf)==1695651172){break;} }
        for(i=-84;i<-77;i++){ buf[660]=(byte)i; if(sha1_32(buf)==983163386){break;} }
        for(i=-12;i<8;i++){ buf[661]=(byte)i; if(sha1_32(buf)==1158976837){break;} }
        for(i=112;i<118;i++){ buf[662]=(byte)i; if(sha1_32(buf)==645061504){break;} }
        for(i=16;i<35;i++){ buf[663]=(byte)i; if(sha1_32(buf)==1429716521){break;} }
        for(i=103;i<115;i++){ buf[664]=(byte)i; if(sha1_32(buf)==1556203644){break;} }
        for(i=57;i<71;i++){ buf[665]=(byte)i; if(sha1_32(buf)==1684607112){break;} }
        for(i=59;i<83;i++){ buf[666]=(byte)i; if(sha1_32(buf)==-617445444){break;} }
        for(i=51;i<69;i++){ buf[667]=(byte)i; if(sha1_32(buf)==-1126216641){break;} }
        for(i=-99;i<-76;i++){ buf[668]=(byte)i; if(sha1_32(buf)==-321491935){break;} }
        for(i=96;i<116;i++){ buf[669]=(byte)i; if(sha1_32(buf)==-267343979){break;} }
        for(i=117;i<128;i++){ buf[670]=(byte)i; if(sha1_32(buf)==-1814395861){break;} }
        for(i=-25;i<-18;i++){ buf[671]=(byte)i; if(sha1_32(buf)==-1099219080){break;} }
        for(i=47;i<66;i++){ buf[672]=(byte)i; if(sha1_32(buf)==-1816246056){break;} }
        for(i=43;i<71;i++){ buf[673]=(byte)i; if(sha1_32(buf)==1777972497){break;} }
        for(i=85;i<94;i++){ buf[674]=(byte)i; if(sha1_32(buf)==-1492472290){break;} }
        for(i=-16;i<4;i++){ buf[675]=(byte)i; if(sha1_32(buf)==-1685853086){break;} }
        for(i=32;i<52;i++){ buf[676]=(byte)i; if(sha1_32(buf)==4042418){break;} }
        for(i=75;i<87;i++){ buf[677]=(byte)i; if(sha1_32(buf)==-959289775){break;} }
        for(i=2;i<9;i++){ buf[678]=(byte)i; if(sha1_32(buf)==-1674962896){break;} }
        for(i=119;i<128;i++){ buf[679]=(byte)i; if(sha1_32(buf)==1870418121){break;} }
        for(i=-50;i<-38;i++){ buf[680]=(byte)i; if(sha1_32(buf)==-731700241){break;} }
        for(i=-37;i<-25;i++){ buf[681]=(byte)i; if(sha1_32(buf)==966072300){break;} }
        for(i=-55;i<-32;i++){ buf[682]=(byte)i; if(sha1_32(buf)==-1727294993){break;} }
        for(i=-44;i<-26;i++){ buf[683]=(byte)i; if(sha1_32(buf)==1799970856){break;} }
        for(i=-117;i<-108;i++){ buf[684]=(byte)i; if(sha1_32(buf)==226766746){break;} }
        for(i=61;i<63;i++){ buf[685]=(byte)i; if(sha1_32(buf)==-151066702){break;} }
        for(i=-7;i<4;i++){ buf[686]=(byte)i; if(sha1_32(buf)==-126520300){break;} }
        for(i=41;i<42;i++){ buf[687]=(byte)i; if(sha1_32(buf)==-839257152){break;} }
        for(i=67;i<81;i++){ buf[688]=(byte)i; if(sha1_32(buf)==465795962){break;} }
        for(i=-72;i<-50;i++){ buf[689]=(byte)i; if(sha1_32(buf)==-86373940){break;} }
        for(i=42;i<56;i++){ buf[690]=(byte)i; if(sha1_32(buf)==-2039380644){break;} }
        for(i=-116;i<-108;i++){ buf[691]=(byte)i; if(sha1_32(buf)==1598859292){break;} }
        for(i=-114;i<-104;i++){ buf[692]=(byte)i; if(sha1_32(buf)==1325514220){break;} }
        for(i=50;i<67;i++){ buf[693]=(byte)i; if(sha1_32(buf)==-1249297532){break;} }
        for(i=4;i<28;i++){ buf[694]=(byte)i; if(sha1_32(buf)==-2018230300){break;} }
        for(i=-29;i<-8;i++){ buf[695]=(byte)i; if(sha1_32(buf)==-308357755){break;} }
        for(i=89;i<109;i++){ buf[696]=(byte)i; if(sha1_32(buf)==-1916981450){break;} }
        for(i=43;i<64;i++){ buf[697]=(byte)i; if(sha1_32(buf)==-2040593957){break;} }
        for(i=-79;i<-60;i++){ buf[698]=(byte)i; if(sha1_32(buf)==875926863){break;} }
        for(i=12;i<35;i++){ buf[699]=(byte)i; if(sha1_32(buf)==1116101335){break;} }
        for(i=92;i<100;i++){ buf[700]=(byte)i; if(sha1_32(buf)==2135913139){break;} }
        for(i=-36;i<-17;i++){ buf[701]=(byte)i; if(sha1_32(buf)==1769270490){break;} }
        for(i=-51;i<-39;i++){ buf[702]=(byte)i; if(sha1_32(buf)==-1996774980){break;} }
        for(i=-98;i<-90;i++){ buf[703]=(byte)i; if(sha1_32(buf)==669895637){break;} }
        for(i=96;i<126;i++){ buf[704]=(byte)i; if(sha1_32(buf)==-758702215){break;} }
        for(i=-3;i<19;i++){ buf[705]=(byte)i; if(sha1_32(buf)==-419357892){break;} }
        for(i=-128;i<-112;i++){ buf[706]=(byte)i; if(sha1_32(buf)==-794892901){break;} }
        for(i=-112;i<-99;i++){ buf[707]=(byte)i; if(sha1_32(buf)==-1481578462){break;} }
        for(i=52;i<72;i++){ buf[708]=(byte)i; if(sha1_32(buf)==-118704766){break;} }
        for(i=-2;i<13;i++){ buf[709]=(byte)i; if(sha1_32(buf)==504653041){break;} }
        for(i=-16;i<4;i++){ buf[710]=(byte)i; if(sha1_32(buf)==-302051613){break;} }
        for(i=9;i<25;i++){ buf[711]=(byte)i; if(sha1_32(buf)==-2037033572){break;} }
        for(i=68;i<90;i++){ buf[712]=(byte)i; if(sha1_32(buf)==1649139441){break;} }
        for(i=-118;i<-112;i++){ buf[713]=(byte)i; if(sha1_32(buf)==1854801590){break;} }
        for(i=-75;i<-61;i++){ buf[714]=(byte)i; if(sha1_32(buf)==-1649580591){break;} }
        for(i=123;i<128;i++){ buf[715]=(byte)i; if(sha1_32(buf)==-230539250){break;} }
        for(i=-51;i<-35;i++){ buf[716]=(byte)i; if(sha1_32(buf)==-617181808){break;} }
        for(i=59;i<82;i++){ buf[717]=(byte)i; if(sha1_32(buf)==-1208928215){break;} }
        for(i=-9;i<6;i++){ buf[718]=(byte)i; if(sha1_32(buf)==1406052242){break;} }
        for(i=51;i<62;i++){ buf[719]=(byte)i; if(sha1_32(buf)==-848719663){break;} }
        for(i=-96;i<-81;i++){ buf[720]=(byte)i; if(sha1_32(buf)==1017314000){break;} }
        for(i=96;i<107;i++){ buf[721]=(byte)i; if(sha1_32(buf)==-900916631){break;} }
        for(i=79;i<101;i++){ buf[722]=(byte)i; if(sha1_32(buf)==1704024397){break;} }
        for(i=-39;i<-15;i++){ buf[723]=(byte)i; if(sha1_32(buf)==988664424){break;} }
        for(i=-37;i<-8;i++){ buf[724]=(byte)i; if(sha1_32(buf)==-766322357){break;} }
        for(i=71;i<102;i++){ buf[725]=(byte)i; if(sha1_32(buf)==83714050){break;} }
        for(i=10;i<20;i++){ buf[726]=(byte)i; if(sha1_32(buf)==74528496){break;} }
        for(i=-27;i<-14;i++){ buf[727]=(byte)i; if(sha1_32(buf)==-1369993162){break;} }
        for(i=-37;i<-29;i++){ buf[728]=(byte)i; if(sha1_32(buf)==-1325825948){break;} }
        for(i=-128;i<-114;i++){ buf[729]=(byte)i; if(sha1_32(buf)==-1229914803){break;} }
        for(i=-76;i<-60;i++){ buf[730]=(byte)i; if(sha1_32(buf)==-656647929){break;} }
        for(i=53;i<70;i++){ buf[731]=(byte)i; if(sha1_32(buf)==-108828130){break;} }
        for(i=28;i<52;i++){ buf[732]=(byte)i; if(sha1_32(buf)==1507533884){break;} }
        for(i=50;i<59;i++){ buf[733]=(byte)i; if(sha1_32(buf)==1551540322){break;} }
        for(i=-79;i<-64;i++){ buf[734]=(byte)i; if(sha1_32(buf)==642900319){break;} }
        for(i=-128;i<-115;i++){ buf[735]=(byte)i; if(sha1_32(buf)==-294592610){break;} }
        for(i=-47;i<-34;i++){ buf[736]=(byte)i; if(sha1_32(buf)==-804297893){break;} }
        for(i=70;i<73;i++){ buf[737]=(byte)i; if(sha1_32(buf)==1340227270){break;} }
        for(i=23;i<38;i++){ buf[738]=(byte)i; if(sha1_32(buf)==-100743501){break;} }
        for(i=-29;i<-12;i++){ buf[739]=(byte)i; if(sha1_32(buf)==784384201){break;} }
        for(i=-69;i<-53;i++){ buf[740]=(byte)i; if(sha1_32(buf)==-137992218){break;} }
        for(i=113;i<122;i++){ buf[741]=(byte)i; if(sha1_32(buf)==931367771){break;} }
        for(i=-8;i<2;i++){ buf[742]=(byte)i; if(sha1_32(buf)==442519472){break;} }
        for(i=-26;i<-16;i++){ buf[743]=(byte)i; if(sha1_32(buf)==-1017294053){break;} }
        for(i=-128;i<-124;i++){ buf[744]=(byte)i; if(sha1_32(buf)==-1921215072){break;} }
        for(i=-2;i<8;i++){ buf[745]=(byte)i; if(sha1_32(buf)==-1593301245){break;} }
        for(i=103;i<123;i++){ buf[746]=(byte)i; if(sha1_32(buf)==-1073771198){break;} }
        for(i=75;i<84;i++){ buf[747]=(byte)i; if(sha1_32(buf)==1454721467){break;} }
        for(i=-68;i<-54;i++){ buf[748]=(byte)i; if(sha1_32(buf)==-589188918){break;} }
        for(i=-19;i<1;i++){ buf[749]=(byte)i; if(sha1_32(buf)==337037940){break;} }
        for(i=97;i<117;i++){ buf[750]=(byte)i; if(sha1_32(buf)==-1581808693){break;} }
        for(i=-11;i<14;i++){ buf[751]=(byte)i; if(sha1_32(buf)==974796857){break;} }
        for(i=-106;i<-96;i++){ buf[752]=(byte)i; if(sha1_32(buf)==-1626429992){break;} }
        for(i=87;i<96;i++){ buf[753]=(byte)i; if(sha1_32(buf)==1724983569){break;} }
        for(i=-64;i<-54;i++){ buf[754]=(byte)i; if(sha1_32(buf)==-1044613630){break;} }
        for(i=-69;i<-50;i++){ buf[755]=(byte)i; if(sha1_32(buf)==54390450){break;} }
        for(i=-25;i<-16;i++){ buf[756]=(byte)i; if(sha1_32(buf)==1904699412){break;} }
        for(i=57;i<71;i++){ buf[757]=(byte)i; if(sha1_32(buf)==-230498827){break;} }
        for(i=63;i<82;i++){ buf[758]=(byte)i; if(sha1_32(buf)==-999230613){break;} }
        for(i=99;i<115;i++){ buf[759]=(byte)i; if(sha1_32(buf)==-1292424014){break;} }
        for(i=-12;i<4;i++){ buf[760]=(byte)i; if(sha1_32(buf)==-1450312682){break;} }
        for(i=-73;i<-49;i++){ buf[761]=(byte)i; if(sha1_32(buf)==1809305338){break;} }
        for(i=-28;i<-16;i++){ buf[762]=(byte)i; if(sha1_32(buf)==1006537632){break;} }
        for(i=66;i<77;i++){ buf[763]=(byte)i; if(sha1_32(buf)==845206561){break;} }
        for(i=30;i<48;i++){ buf[764]=(byte)i; if(sha1_32(buf)==-1492110118){break;} }
        for(i=2;i<18;i++){ buf[765]=(byte)i; if(sha1_32(buf)==1472338234){break;} }
        for(i=69;i<89;i++){ buf[766]=(byte)i; if(sha1_32(buf)==-1081451436){break;} }
        for(i=-31;i<-3;i++){ buf[767]=(byte)i; if(sha1_32(buf)==370061332){break;} }
        for(i=70;i<91;i++){ buf[768]=(byte)i; if(sha1_32(buf)==2051499104){break;} }
        for(i=72;i<83;i++){ buf[769]=(byte)i; if(sha1_32(buf)==998033664){break;} }
        for(i=-76;i<-68;i++){ buf[770]=(byte)i; if(sha1_32(buf)==-724059982){break;} }
        for(i=-18;i<-1;i++){ buf[771]=(byte)i; if(sha1_32(buf)==-191318050){break;} }
        for(i=64;i<76;i++){ buf[772]=(byte)i; if(sha1_32(buf)==-1504726297){break;} }
        for(i=66;i<70;i++){ buf[773]=(byte)i; if(sha1_32(buf)==240152783){break;} }
        for(i=49;i<66;i++){ buf[774]=(byte)i; if(sha1_32(buf)==-1728096022){break;} }
        for(i=-9;i<13;i++){ buf[775]=(byte)i; if(sha1_32(buf)==1095229512){break;} }
        for(i=-48;i<-27;i++){ buf[776]=(byte)i; if(sha1_32(buf)==464862165){break;} }
        for(i=13;i<30;i++){ buf[777]=(byte)i; if(sha1_32(buf)==1047536032){break;} }
        for(i=113;i<128;i++){ buf[778]=(byte)i; if(sha1_32(buf)==-738260953){break;} }
        for(i=106;i<117;i++){ buf[779]=(byte)i; if(sha1_32(buf)==1642213948){break;} }
        for(i=-5;i<8;i++){ buf[780]=(byte)i; if(sha1_32(buf)==-325383871){break;} }
        for(i=-69;i<-41;i++){ buf[781]=(byte)i; if(sha1_32(buf)==-280944174){break;} }
        for(i=-128;i<-124;i++){ buf[782]=(byte)i; if(sha1_32(buf)==-795446007){break;} }
        for(i=-75;i<-59;i++){ buf[783]=(byte)i; if(sha1_32(buf)==46375738){break;} }
        for(i=-73;i<-58;i++){ buf[784]=(byte)i; if(sha1_32(buf)==-474061192){break;} }
        for(i=30;i<45;i++){ buf[785]=(byte)i; if(sha1_32(buf)==1447538089){break;} }
        for(i=-128;i<-122;i++){ buf[786]=(byte)i; if(sha1_32(buf)==1343908580){break;} }
        for(i=47;i<62;i++){ buf[787]=(byte)i; if(sha1_32(buf)==722456233){break;} }
        for(i=-18;i<-11;i++){ buf[788]=(byte)i; if(sha1_32(buf)==2123970592){break;} }
        for(i=43;i<58;i++){ buf[789]=(byte)i; if(sha1_32(buf)==-793730196){break;} }
        for(i=24;i<33;i++){ buf[790]=(byte)i; if(sha1_32(buf)==162249149){break;} }
        for(i=-106;i<-91;i++){ buf[791]=(byte)i; if(sha1_32(buf)==36865374){break;} }
        for(i=-10;i<-2;i++){ buf[792]=(byte)i; if(sha1_32(buf)==-205338257){break;} }
        for(i=65;i<78;i++){ buf[793]=(byte)i; if(sha1_32(buf)==848608322){break;} }
        for(i=23;i<42;i++){ buf[794]=(byte)i; if(sha1_32(buf)==1778269896){break;} }
        for(i=67;i<85;i++){ buf[795]=(byte)i; if(sha1_32(buf)==-1204313860){break;} }
        for(i=-6;i<11;i++){ buf[796]=(byte)i; if(sha1_32(buf)==-280156814){break;} }
        for(i=-12;i<7;i++){ buf[797]=(byte)i; if(sha1_32(buf)==1985283397){break;} }
        for(i=-60;i<-38;i++){ buf[798]=(byte)i; if(sha1_32(buf)==2002647882){break;} }
        for(i=55;i<74;i++){ buf[799]=(byte)i; if(sha1_32(buf)==294588052){break;} }
        for(i=-86;i<-81;i++){ buf[800]=(byte)i; if(sha1_32(buf)==2025764824){break;} }
        for(i=81;i<103;i++){ buf[801]=(byte)i; if(sha1_32(buf)==-791296672){break;} }
        for(i=-20;i<2;i++){ buf[802]=(byte)i; if(sha1_32(buf)==1681559005){break;} }
        for(i=56;i<72;i++){ buf[803]=(byte)i; if(sha1_32(buf)==1581190884){break;} }
        for(i=67;i<83;i++){ buf[804]=(byte)i; if(sha1_32(buf)==1866931985){break;} }
        for(i=-128;i<-112;i++){ buf[805]=(byte)i; if(sha1_32(buf)==816930962){break;} }
        for(i=-61;i<-35;i++){ buf[806]=(byte)i; if(sha1_32(buf)==1702852179){break;} }
        for(i=84;i<97;i++){ buf[807]=(byte)i; if(sha1_32(buf)==1088377683){break;} }
        for(i=-3;i<16;i++){ buf[808]=(byte)i; if(sha1_32(buf)==-700908292){break;} }
        for(i=-60;i<-38;i++){ buf[809]=(byte)i; if(sha1_32(buf)==2056290200){break;} }
        for(i=-42;i<-26;i++){ buf[810]=(byte)i; if(sha1_32(buf)==-275385864){break;} }
        for(i=-41;i<-28;i++){ buf[811]=(byte)i; if(sha1_32(buf)==2108985440){break;} }
        for(i=-95;i<-76;i++){ buf[812]=(byte)i; if(sha1_32(buf)==-1690196749){break;} }
        for(i=61;i<80;i++){ buf[813]=(byte)i; if(sha1_32(buf)==2030253558){break;} }
        for(i=10;i<22;i++){ buf[814]=(byte)i; if(sha1_32(buf)==543764810){break;} }
        for(i=15;i<30;i++){ buf[815]=(byte)i; if(sha1_32(buf)==-1369013180){break;} }
        for(i=-78;i<-57;i++){ buf[816]=(byte)i; if(sha1_32(buf)==-1307125783){break;} }
        for(i=119;i<128;i++){ buf[817]=(byte)i; if(sha1_32(buf)==1900725564){break;} }
        for(i=-76;i<-70;i++){ buf[818]=(byte)i; if(sha1_32(buf)==-1389219487){break;} }
        for(i=-105;i<-89;i++){ buf[819]=(byte)i; if(sha1_32(buf)==-595617427){break;} }
        for(i=114;i<126;i++){ buf[820]=(byte)i; if(sha1_32(buf)==605114025){break;} }
        for(i=-58;i<-53;i++){ buf[821]=(byte)i; if(sha1_32(buf)==734217577){break;} }
        for(i=-107;i<-95;i++){ buf[822]=(byte)i; if(sha1_32(buf)==-1411065740){break;} }
        for(i=-7;i<5;i++){ buf[823]=(byte)i; if(sha1_32(buf)==-1850346162){break;} }
        for(i=7;i<14;i++){ buf[824]=(byte)i; if(sha1_32(buf)==27146895){break;} }
        for(i=-69;i<-62;i++){ buf[825]=(byte)i; if(sha1_32(buf)==118792688){break;} }
        for(i=87;i<99;i++){ buf[826]=(byte)i; if(sha1_32(buf)==1194179997){break;} }
        for(i=-95;i<-71;i++){ buf[827]=(byte)i; if(sha1_32(buf)==1246065562){break;} }
        for(i=3;i<18;i++){ buf[828]=(byte)i; if(sha1_32(buf)==378233050){break;} }
        for(i=61;i<77;i++){ buf[829]=(byte)i; if(sha1_32(buf)==1631714208){break;} }
        for(i=54;i<75;i++){ buf[830]=(byte)i; if(sha1_32(buf)==225888978){break;} }
        for(i=46;i<64;i++){ buf[831]=(byte)i; if(sha1_32(buf)==-177869817){break;} }
        for(i=52;i<70;i++){ buf[832]=(byte)i; if(sha1_32(buf)==632873542){break;} }
        for(i=-39;i<-8;i++){ buf[833]=(byte)i; if(sha1_32(buf)==876852487){break;} }
        for(i=-102;i<-80;i++){ buf[834]=(byte)i; if(sha1_32(buf)==630879566){break;} }
        for(i=94;i<109;i++){ buf[835]=(byte)i; if(sha1_32(buf)==-1220045642){break;} }
        for(i=48;i<54;i++){ buf[836]=(byte)i; if(sha1_32(buf)==-792964646){break;} }
        for(i=-128;i<-108;i++){ buf[837]=(byte)i; if(sha1_32(buf)==-367139280){break;} }
        for(i=84;i<109;i++){ buf[838]=(byte)i; if(sha1_32(buf)==573696108){break;} }
        for(i=14;i<25;i++){ buf[839]=(byte)i; if(sha1_32(buf)==-1778774040){break;} }
        for(i=114;i<120;i++){ buf[840]=(byte)i; if(sha1_32(buf)==687618308){break;} }
        for(i=-46;i<-24;i++){ buf[841]=(byte)i; if(sha1_32(buf)==-106321531){break;} }
        for(i=89;i<99;i++){ buf[842]=(byte)i; if(sha1_32(buf)==1225430729){break;} }
        for(i=-24;i<-21;i++){ buf[843]=(byte)i; if(sha1_32(buf)==-1369017522){break;} }
        for(i=108;i<117;i++){ buf[844]=(byte)i; if(sha1_32(buf)==-1920395695){break;} }
        for(i=-128;i<-105;i++){ buf[845]=(byte)i; if(sha1_32(buf)==1683223401){break;} }
        for(i=101;i<116;i++){ buf[846]=(byte)i; if(sha1_32(buf)==771043064){break;} }
        for(i=85;i<111;i++){ buf[847]=(byte)i; if(sha1_32(buf)==-939368093){break;} }
        for(i=26;i<48;i++){ buf[848]=(byte)i; if(sha1_32(buf)==1466887296){break;} }
        for(i=-4;i<12;i++){ buf[849]=(byte)i; if(sha1_32(buf)==135549875){break;} }
        for(i=-46;i<-25;i++){ buf[850]=(byte)i; if(sha1_32(buf)==-1639666926){break;} }
        for(i=-25;i<-3;i++){ buf[851]=(byte)i; if(sha1_32(buf)==1182540208){break;} }
        for(i=67;i<85;i++){ buf[852]=(byte)i; if(sha1_32(buf)==504081213){break;} }
        for(i=-64;i<-58;i++){ buf[853]=(byte)i; if(sha1_32(buf)==230045485){break;} }
        for(i=-18;i<6;i++){ buf[854]=(byte)i; if(sha1_32(buf)==-985960614){break;} }
        for(i=96;i<105;i++){ buf[855]=(byte)i; if(sha1_32(buf)==1014635709){break;} }
        for(i=95;i<108;i++){ buf[856]=(byte)i; if(sha1_32(buf)==-356840415){break;} }
        for(i=37;i<54;i++){ buf[857]=(byte)i; if(sha1_32(buf)==-1303137911){break;} }
        for(i=-128;i<-112;i++){ buf[858]=(byte)i; if(sha1_32(buf)==107040691){break;} }
        for(i=-68;i<-54;i++){ buf[859]=(byte)i; if(sha1_32(buf)==-1994984014){break;} }
        for(i=-12;i<-3;i++){ buf[860]=(byte)i; if(sha1_32(buf)==45779820){break;} }
        for(i=-121;i<-113;i++){ buf[861]=(byte)i; if(sha1_32(buf)==-1654931400){break;} }
        for(i=-40;i<-35;i++){ buf[862]=(byte)i; if(sha1_32(buf)==-525897767){break;} }
        for(i=-76;i<-65;i++){ buf[863]=(byte)i; if(sha1_32(buf)==1634215783){break;} }
        for(i=5;i<33;i++){ buf[864]=(byte)i; if(sha1_32(buf)==2111267842){break;} }
        for(i=-128;i<-119;i++){ buf[865]=(byte)i; if(sha1_32(buf)==-151631796){break;} }
        for(i=-18;i<3;i++){ buf[866]=(byte)i; if(sha1_32(buf)==920527309){break;} }
        for(i=-59;i<-54;i++){ buf[867]=(byte)i; if(sha1_32(buf)==-158431528){break;} }
        for(i=8;i<24;i++){ buf[868]=(byte)i; if(sha1_32(buf)==-1435181157){break;} }
        for(i=12;i<26;i++){ buf[869]=(byte)i; if(sha1_32(buf)==2121989812){break;} }
        for(i=-81;i<-66;i++){ buf[870]=(byte)i; if(sha1_32(buf)==1094056904){break;} }
        for(i=-128;i<-113;i++){ buf[871]=(byte)i; if(sha1_32(buf)==-1801726574){break;} }
        for(i=-101;i<-86;i++){ buf[872]=(byte)i; if(sha1_32(buf)==-139848539){break;} }
        for(i=-77;i<-52;i++){ buf[873]=(byte)i; if(sha1_32(buf)==1585193601){break;} }
        for(i=-115;i<-97;i++){ buf[874]=(byte)i; if(sha1_32(buf)==1360394095){break;} }
        for(i=113;i<123;i++){ buf[875]=(byte)i; if(sha1_32(buf)==-1036953142){break;} }
        for(i=-60;i<-52;i++){ buf[876]=(byte)i; if(sha1_32(buf)==-1805855909){break;} }
        for(i=-28;i<-16;i++){ buf[877]=(byte)i; if(sha1_32(buf)==-162345382){break;} }
        for(i=-67;i<-52;i++){ buf[878]=(byte)i; if(sha1_32(buf)==630795221){break;} }
        for(i=105;i<116;i++){ buf[879]=(byte)i; if(sha1_32(buf)==800034304){break;} }
        for(i=29;i<45;i++){ buf[880]=(byte)i; if(sha1_32(buf)==467662087){break;} }
        for(i=-72;i<-60;i++){ buf[881]=(byte)i; if(sha1_32(buf)==2035259909){break;} }
        for(i=-31;i<-14;i++){ buf[882]=(byte)i; if(sha1_32(buf)==-1872821815){break;} }
        for(i=-86;i<-79;i++){ buf[883]=(byte)i; if(sha1_32(buf)==-1596418873){break;} }
        for(i=-73;i<-55;i++){ buf[884]=(byte)i; if(sha1_32(buf)==470260676){break;} }
        for(i=-54;i<-25;i++){ buf[885]=(byte)i; if(sha1_32(buf)==-401083862){break;} }
        for(i=88;i<105;i++){ buf[886]=(byte)i; if(sha1_32(buf)==129287320){break;} }
        for(i=-22;i<-4;i++){ buf[887]=(byte)i; if(sha1_32(buf)==383348758){break;} }
        for(i=72;i<99;i++){ buf[888]=(byte)i; if(sha1_32(buf)==1093705960){break;} }
        for(i=-29;i<-22;i++){ buf[889]=(byte)i; if(sha1_32(buf)==-1317742662){break;} }
        for(i=-128;i<-118;i++){ buf[890]=(byte)i; if(sha1_32(buf)==2894752){break;} }
        for(i=-89;i<-72;i++){ buf[891]=(byte)i; if(sha1_32(buf)==-1625965988){break;} }
        for(i=93;i<111;i++){ buf[892]=(byte)i; if(sha1_32(buf)==485525779){break;} }
        for(i=29;i<38;i++){ buf[893]=(byte)i; if(sha1_32(buf)==1167481667){break;} }
        for(i=46;i<59;i++){ buf[894]=(byte)i; if(sha1_32(buf)==1893528381){break;} }
        for(i=-53;i<-34;i++){ buf[895]=(byte)i; if(sha1_32(buf)==-406230189){break;} }
        for(i=73;i<96;i++){ buf[896]=(byte)i; if(sha1_32(buf)==-1083927651){break;} }
        for(i=16;i<39;i++){ buf[897]=(byte)i; if(sha1_32(buf)==-1973275632){break;} }
        for(i=-106;i<-90;i++){ buf[898]=(byte)i; if(sha1_32(buf)==777857466){break;} }
        for(i=27;i<43;i++){ buf[899]=(byte)i; if(sha1_32(buf)==-395929341){break;} }
        for(i=-17;i<11;i++){ buf[900]=(byte)i; if(sha1_32(buf)==2074582134){break;} }
        for(i=-59;i<-54;i++){ buf[901]=(byte)i; if(sha1_32(buf)==568106871){break;} }
        for(i=4;i<11;i++){ buf[902]=(byte)i; if(sha1_32(buf)==-1322913030){break;} }
        for(i=-118;i<-104;i++){ buf[903]=(byte)i; if(sha1_32(buf)==-1548477208){break;} }
        for(i=61;i<72;i++){ buf[904]=(byte)i; if(sha1_32(buf)==1583978927){break;} }
        for(i=-118;i<-102;i++){ buf[905]=(byte)i; if(sha1_32(buf)==-1544176351){break;} }
        for(i=-112;i<-95;i++){ buf[906]=(byte)i; if(sha1_32(buf)==1394441939){break;} }
        for(i=-98;i<-76;i++){ buf[907]=(byte)i; if(sha1_32(buf)==-1671235275){break;} }
        for(i=59;i<67;i++){ buf[908]=(byte)i; if(sha1_32(buf)==2045545697){break;} }
        for(i=-56;i<-43;i++){ buf[909]=(byte)i; if(sha1_32(buf)==-1135444968){break;} }
        for(i=16;i<26;i++){ buf[910]=(byte)i; if(sha1_32(buf)==-2016208348){break;} }
        for(i=-53;i<-42;i++){ buf[911]=(byte)i; if(sha1_32(buf)==-1816132580){break;} }
        for(i=-106;i<-87;i++){ buf[912]=(byte)i; if(sha1_32(buf)==-406319614){break;} }
        for(i=41;i<52;i++){ buf[913]=(byte)i; if(sha1_32(buf)==96411171){break;} }
        for(i=68;i<85;i++){ buf[914]=(byte)i; if(sha1_32(buf)==1024690391){break;} }
        for(i=-58;i<-51;i++){ buf[915]=(byte)i; if(sha1_32(buf)==157513858){break;} }
        for(i=-110;i<-90;i++){ buf[916]=(byte)i; if(sha1_32(buf)==1741615314){break;} }
        for(i=-87;i<-78;i++){ buf[917]=(byte)i; if(sha1_32(buf)==-1451050895){break;} }
        for(i=81;i<90;i++){ buf[918]=(byte)i; if(sha1_32(buf)==1873223695){break;} }
        for(i=-63;i<-41;i++){ buf[919]=(byte)i; if(sha1_32(buf)==2078295207){break;} }
        for(i=-49;i<-27;i++){ buf[920]=(byte)i; if(sha1_32(buf)==-49084240){break;} }
        for(i=44;i<72;i++){ buf[921]=(byte)i; if(sha1_32(buf)==650243167){break;} }
        for(i=-55;i<-43;i++){ buf[922]=(byte)i; if(sha1_32(buf)==-970688920){break;} }
        for(i=91;i<96;i++){ buf[923]=(byte)i; if(sha1_32(buf)==-228155556){break;} }
        for(i=3;i<10;i++){ buf[924]=(byte)i; if(sha1_32(buf)==1086060746){break;} }
        for(i=-112;i<-100;i++){ buf[925]=(byte)i; if(sha1_32(buf)==1191728012){break;} }
        for(i=-89;i<-61;i++){ buf[926]=(byte)i; if(sha1_32(buf)==-347169887){break;} }
        for(i=-126;i<-113;i++){ buf[927]=(byte)i; if(sha1_32(buf)==2101688078){break;} }
        for(i=-16;i<8;i++){ buf[928]=(byte)i; if(sha1_32(buf)==-104226754){break;} }
        for(i=57;i<62;i++){ buf[929]=(byte)i; if(sha1_32(buf)==-966900174){break;} }
        for(i=84;i<87;i++){ buf[930]=(byte)i; if(sha1_32(buf)==769570509){break;} }
        for(i=-18;i<-8;i++){ buf[931]=(byte)i; if(sha1_32(buf)==2033170421){break;} }
        for(i=32;i<38;i++){ buf[932]=(byte)i; if(sha1_32(buf)==2050430772){break;} }
        for(i=-11;i<14;i++){ buf[933]=(byte)i; if(sha1_32(buf)==1288987426){break;} }
        for(i=44;i<58;i++){ buf[934]=(byte)i; if(sha1_32(buf)==-1720688747){break;} }
        for(i=-18;i<2;i++){ buf[935]=(byte)i; if(sha1_32(buf)==-2031444964){break;} }
        for(i=-89;i<-71;i++){ buf[936]=(byte)i; if(sha1_32(buf)==2008979166){break;} }
        for(i=-63;i<-50;i++){ buf[937]=(byte)i; if(sha1_32(buf)==1905460870){break;} }
        for(i=-128;i<-123;i++){ buf[938]=(byte)i; if(sha1_32(buf)==-374469775){break;} }
        for(i=-23;i<-9;i++){ buf[939]=(byte)i; if(sha1_32(buf)==281123609){break;} }
        for(i=-128;i<-124;i++){ buf[940]=(byte)i; if(sha1_32(buf)==1615767022){break;} }
        for(i=-107;i<-94;i++){ buf[941]=(byte)i; if(sha1_32(buf)==1377034700){break;} }
        for(i=-108;i<-84;i++){ buf[942]=(byte)i; if(sha1_32(buf)==874888747){break;} }
        for(i=41;i<43;i++){ buf[943]=(byte)i; if(sha1_32(buf)==612785574){break;} }
        for(i=16;i<26;i++){ buf[944]=(byte)i; if(sha1_32(buf)==626892710){break;} }
        for(i=-128;i<-121;i++){ buf[945]=(byte)i; if(sha1_32(buf)==2006612659){break;} }
        for(i=-83;i<-61;i++){ buf[946]=(byte)i; if(sha1_32(buf)==1431280085){break;} }
        for(i=111;i<128;i++){ buf[947]=(byte)i; if(sha1_32(buf)==4871952){break;} }
        for(i=46;i<63;i++){ buf[948]=(byte)i; if(sha1_32(buf)==1964456299){break;} }
        for(i=-106;i<-97;i++){ buf[949]=(byte)i; if(sha1_32(buf)==1820250770){break;} }
        for(i=84;i<104;i++){ buf[950]=(byte)i; if(sha1_32(buf)==-600542395){break;} }
        for(i=-122;i<-93;i++){ buf[951]=(byte)i; if(sha1_32(buf)==716504796){break;} }
        for(i=-7;i<-3;i++){ buf[952]=(byte)i; if(sha1_32(buf)==1677272657){break;} }
        for(i=81;i<98;i++){ buf[953]=(byte)i; if(sha1_32(buf)==-2078411982){break;} }
        for(i=5;i<23;i++){ buf[954]=(byte)i; if(sha1_32(buf)==618211648){break;} }
        for(i=106;i<128;i++){ buf[955]=(byte)i; if(sha1_32(buf)==-856332564){break;} }
        for(i=-31;i<-19;i++){ buf[956]=(byte)i; if(sha1_32(buf)==-1031877171){break;} }
        for(i=93;i<108;i++){ buf[957]=(byte)i; if(sha1_32(buf)==411074948){break;} }
        for(i=-112;i<-108;i++){ buf[958]=(byte)i; if(sha1_32(buf)==-1644808412){break;} }
        for(i=96;i<116;i++){ buf[959]=(byte)i; if(sha1_32(buf)==-1475803857){break;} }
        for(i=89;i<109;i++){ buf[960]=(byte)i; if(sha1_32(buf)==1673291876){break;} }
        for(i=111;i<124;i++){ buf[961]=(byte)i; if(sha1_32(buf)==-1362705359){break;} }
        for(i=24;i<38;i++){ buf[962]=(byte)i; if(sha1_32(buf)==-211531750){break;} }
        for(i=-114;i<-95;i++){ buf[963]=(byte)i; if(sha1_32(buf)==1237835967){break;} }
        for(i=-125;i<-112;i++){ buf[964]=(byte)i; if(sha1_32(buf)==1181209421){break;} }
        for(i=100;i<124;i++){ buf[965]=(byte)i; if(sha1_32(buf)==-1434525181){break;} }
        for(i=-61;i<-59;i++){ buf[966]=(byte)i; if(sha1_32(buf)==453904283){break;} }
        for(i=51;i<72;i++){ buf[967]=(byte)i; if(sha1_32(buf)==-1501997831){break;} }
        for(i=112;i<128;i++){ buf[968]=(byte)i; if(sha1_32(buf)==-324020369){break;} }
        for(i=-128;i<-111;i++){ buf[969]=(byte)i; if(sha1_32(buf)==-1303941549){break;} }
        for(i=-112;i<-95;i++){ buf[970]=(byte)i; if(sha1_32(buf)==-1329734627){break;} }
        for(i=49;i<68;i++){ buf[971]=(byte)i; if(sha1_32(buf)==1156523838){break;} }
        for(i=-68;i<-60;i++){ buf[972]=(byte)i; if(sha1_32(buf)==-1185066585){break;} }
        for(i=113;i<126;i++){ buf[973]=(byte)i; if(sha1_32(buf)==-111498941){break;} }
        for(i=56;i<62;i++){ buf[974]=(byte)i; if(sha1_32(buf)==760303943){break;} }
        for(i=40;i<68;i++){ buf[975]=(byte)i; if(sha1_32(buf)==1275020020){break;} }
        for(i=107;i<128;i++){ buf[976]=(byte)i; if(sha1_32(buf)==-1079017718){break;} }
        for(i=-91;i<-90;i++){ buf[977]=(byte)i; if(sha1_32(buf)==1884867054){break;} }
        for(i=74;i<98;i++){ buf[978]=(byte)i; if(sha1_32(buf)==-728331679){break;} }
        for(i=5;i<28;i++){ buf[979]=(byte)i; if(sha1_32(buf)==400255479){break;} }
        for(i=-70;i<-50;i++){ buf[980]=(byte)i; if(sha1_32(buf)==-158814461){break;} }
        for(i=-119;i<-110;i++){ buf[981]=(byte)i; if(sha1_32(buf)==-1633795448){break;} }
        for(i=109;i<128;i++){ buf[982]=(byte)i; if(sha1_32(buf)==-818013855){break;} }
        for(i=-85;i<-81;i++){ buf[983]=(byte)i; if(sha1_32(buf)==-1164113321){break;} }
        for(i=-128;i<-114;i++){ buf[984]=(byte)i; if(sha1_32(buf)==-312216666){break;} }
        for(i=109;i<121;i++){ buf[985]=(byte)i; if(sha1_32(buf)==-806708945){break;} }
        for(i=-45;i<-27;i++){ buf[986]=(byte)i; if(sha1_32(buf)==-1533769851){break;} }
        for(i=-14;i<4;i++){ buf[987]=(byte)i; if(sha1_32(buf)==-1714860427){break;} }
        for(i=19;i<44;i++){ buf[988]=(byte)i; if(sha1_32(buf)==100418758){break;} }
        for(i=-71;i<-47;i++){ buf[989]=(byte)i; if(sha1_32(buf)==1749433378){break;} }
        for(i=-56;i<-39;i++){ buf[990]=(byte)i; if(sha1_32(buf)==-1530160213){break;} }
        for(i=70;i<78;i++){ buf[991]=(byte)i; if(sha1_32(buf)==2040573665){break;} }
        for(i=37;i<62;i++){ buf[992]=(byte)i; if(sha1_32(buf)==157425215){break;} }
        for(i=-24;i<-9;i++){ buf[993]=(byte)i; if(sha1_32(buf)==-429075802){break;} }
        for(i=88;i<109;i++){ buf[994]=(byte)i; if(sha1_32(buf)==-323115794){break;} }
        for(i=20;i<43;i++){ buf[995]=(byte)i; if(sha1_32(buf)==-1230850328){break;} }
        for(i=4;i<15;i++){ buf[996]=(byte)i; if(sha1_32(buf)==264198025){break;} }
        for(i=93;i<115;i++){ buf[997]=(byte)i; if(sha1_32(buf)==-770463331){break;} }
        for(i=-59;i<-38;i++){ buf[998]=(byte)i; if(sha1_32(buf)==-688576685){break;} }
        for(i=-77;i<-59;i++){ buf[999]=(byte)i; if(sha1_32(buf)==751734126){break;} }
        for(i=-107;i<-98;i++){ buf[1000]=(byte)i; if(sha1_32(buf)==-1802293316){break;} }
        for(i=40;i<57;i++){ buf[1001]=(byte)i; if(sha1_32(buf)==-1930557439){break;} }
        for(i=13;i<25;i++){ buf[1002]=(byte)i; if(sha1_32(buf)==-729294079){break;} }
        for(i=-106;i<-105;i++){ buf[1003]=(byte)i; if(sha1_32(buf)==-1396859506){break;} }
        for(i=53;i<81;i++){ buf[1004]=(byte)i; if(sha1_32(buf)==-208742263){break;} }
        for(i=21;i<33;i++){ buf[1005]=(byte)i; if(sha1_32(buf)==1789079842){break;} }
        for(i=-61;i<-42;i++){ buf[1006]=(byte)i; if(sha1_32(buf)==-905024790){break;} }
        for(i=59;i<66;i++){ buf[1007]=(byte)i; if(sha1_32(buf)==-406425160){break;} }
        for(i=-105;i<-84;i++){ buf[1008]=(byte)i; if(sha1_32(buf)==-1516425145){break;} }
        for(i=72;i<85;i++){ buf[1009]=(byte)i; if(sha1_32(buf)==-303017111){break;} }
        for(i=-80;i<-70;i++){ buf[1010]=(byte)i; if(sha1_32(buf)==-475889095){break;} }
        for(i=-20;i<0;i++){ buf[1011]=(byte)i; if(sha1_32(buf)==-2140579645){break;} }
        for(i=72;i<79;i++){ buf[1012]=(byte)i; if(sha1_32(buf)==-1663243682){break;} }
        for(i=108;i<121;i++){ buf[1013]=(byte)i; if(sha1_32(buf)==1674052950){break;} }
        for(i=105;i<117;i++){ buf[1014]=(byte)i; if(sha1_32(buf)==-222897108){break;} }
        for(i=-49;i<-21;i++){ buf[1015]=(byte)i; if(sha1_32(buf)==1424774925){break;} }
        for(i=8;i<28;i++){ buf[1016]=(byte)i; if(sha1_32(buf)==-1520661348){break;} }
        for(i=-110;i<-94;i++){ buf[1017]=(byte)i; if(sha1_32(buf)==-296483274){break;} }
        for(i=-24;i<-1;i++){ buf[1018]=(byte)i; if(sha1_32(buf)==1440854690){break;} }
        for(i=114;i<128;i++){ buf[1019]=(byte)i; if(sha1_32(buf)==2099106629){break;} }
        for(i=-128;i<-119;i++){ buf[1020]=(byte)i; if(sha1_32(buf)==-1478833607){break;} }
        for(i=-69;i<-48;i++){ buf[1021]=(byte)i; if(sha1_32(buf)==-1320945352){break;} }
        for(i=-77;i<-51;i++){ buf[1022]=(byte)i; if(sha1_32(buf)==-14836130){break;} }
        for(i=8;i<35;i++){ buf[1023]=(byte)i; if(sha1_32(buf)==1925236731){break;} }
        for(i=-40;i<-31;i++){ buf[1024]=(byte)i; if(sha1_32(buf)==1795097260){break;} }
        for(i=-112;i<-107;i++){ buf[1025]=(byte)i; if(sha1_32(buf)==-168692959){break;} }
        for(i=-128;i<-108;i++){ buf[1026]=(byte)i; if(sha1_32(buf)==53961912){break;} }
        for(i=-51;i<-36;i++){ buf[1027]=(byte)i; if(sha1_32(buf)==-1953256351){break;} }
        for(i=-73;i<-63;i++){ buf[1028]=(byte)i; if(sha1_32(buf)==-851369149){break;} }
        for(i=48;i<66;i++){ buf[1029]=(byte)i; if(sha1_32(buf)==-1888362869){break;} }
        for(i=-84;i<-62;i++){ buf[1030]=(byte)i; if(sha1_32(buf)==953481505){break;} }
        for(i=91;i<101;i++){ buf[1031]=(byte)i; if(sha1_32(buf)==1535909113){break;} }
        for(i=12;i<29;i++){ buf[1032]=(byte)i; if(sha1_32(buf)==-2141878800){break;} }
        for(i=7;i<15;i++){ buf[1033]=(byte)i; if(sha1_32(buf)==1430632434){break;} }
        for(i=80;i<97;i++){ buf[1034]=(byte)i; if(sha1_32(buf)==-2016296831){break;} }
        for(i=-12;i<13;i++){ buf[1035]=(byte)i; if(sha1_32(buf)==1012043251){break;} }
        for(i=-53;i<-38;i++){ buf[1036]=(byte)i; if(sha1_32(buf)==-981729940){break;} }
        for(i=63;i<82;i++){ buf[1037]=(byte)i; if(sha1_32(buf)==628147259){break;} }
        for(i=82;i<105;i++){ buf[1038]=(byte)i; if(sha1_32(buf)==-1373618219){break;} }
        for(i=-62;i<-50;i++){ buf[1039]=(byte)i; if(sha1_32(buf)==-1528463929){break;} }
        for(i=40;i<50;i++){ buf[1040]=(byte)i; if(sha1_32(buf)==-1294430546){break;} }
        for(i=26;i<42;i++){ buf[1041]=(byte)i; if(sha1_32(buf)==-800302291){break;} }
        for(i=-36;i<-25;i++){ buf[1042]=(byte)i; if(sha1_32(buf)==719815122){break;} }
        for(i=5;i<21;i++){ buf[1043]=(byte)i; if(sha1_32(buf)==1076259240){break;} }
        for(i=-53;i<-26;i++){ buf[1044]=(byte)i; if(sha1_32(buf)==273267734){break;} }
        for(i=-11;i<15;i++){ buf[1045]=(byte)i; if(sha1_32(buf)==273267734){break;} }
        for(i=124;i<128;i++){ buf[1046]=(byte)i; if(sha1_32(buf)==-1722844955){break;} }
        for(i=117;i<128;i++){ buf[1047]=(byte)i; if(sha1_32(buf)==-1044017789){break;} }
        for(i=-48;i<-40;i++){ buf[1048]=(byte)i; if(sha1_32(buf)==100249781){break;} }
        for(i=-103;i<-90;i++){ buf[1049]=(byte)i; if(sha1_32(buf)==-1993648025){break;} }
        for(i=-51;i<-42;i++){ buf[1050]=(byte)i; if(sha1_32(buf)==-1326442962){break;} }
        for(i=108;i<112;i++){ buf[1051]=(byte)i; if(sha1_32(buf)==511205648){break;} }
        for(i=-21;i<-4;i++){ buf[1052]=(byte)i; if(sha1_32(buf)==-870959641){break;} }
        for(i=85;i<96;i++){ buf[1053]=(byte)i; if(sha1_32(buf)==1245667387){break;} }
        for(i=100;i<118;i++){ buf[1054]=(byte)i; if(sha1_32(buf)==-1361555150){break;} }
        for(i=74;i<94;i++){ buf[1055]=(byte)i; if(sha1_32(buf)==-1342590513){break;} }
        for(i=-3;i<24;i++){ buf[1056]=(byte)i; if(sha1_32(buf)==799912875){break;} }
        for(i=11;i<28;i++){ buf[1057]=(byte)i; if(sha1_32(buf)==-141930041){break;} }
        for(i=49;i<73;i++){ buf[1058]=(byte)i; if(sha1_32(buf)==-248246204){break;} }
        for(i=-127;i<-103;i++){ buf[1059]=(byte)i; if(sha1_32(buf)==1357815780){break;} }
        for(i=-73;i<-49;i++){ buf[1060]=(byte)i; if(sha1_32(buf)==1410842897){break;} }
        for(i=-71;i<-64;i++){ buf[1061]=(byte)i; if(sha1_32(buf)==-1526088601){break;} }
        for(i=-128;i<-119;i++){ buf[1062]=(byte)i; if(sha1_32(buf)==-721703935){break;} }
        for(i=-118;i<-105;i++){ buf[1063]=(byte)i; if(sha1_32(buf)==436794354){break;} }
        for(i=-74;i<-69;i++){ buf[1064]=(byte)i; if(sha1_32(buf)==1470446779){break;} }
        for(i=-53;i<-35;i++){ buf[1065]=(byte)i; if(sha1_32(buf)==-1150909930){break;} }
        for(i=-103;i<-87;i++){ buf[1066]=(byte)i; if(sha1_32(buf)==1732280909){break;} }
        for(i=82;i<105;i++){ buf[1067]=(byte)i; if(sha1_32(buf)==938601184){break;} }
        for(i=55;i<69;i++){ buf[1068]=(byte)i; if(sha1_32(buf)==-130448319){break;} }
        for(i=93;i<114;i++){ buf[1069]=(byte)i; if(sha1_32(buf)==665924293){break;} }
        for(i=-26;i<-3;i++){ buf[1070]=(byte)i; if(sha1_32(buf)==-2114447361){break;} }
        for(i=-56;i<-35;i++){ buf[1071]=(byte)i; if(sha1_32(buf)==-357974390){break;} }
        for(i=75;i<95;i++){ buf[1072]=(byte)i; if(sha1_32(buf)==-645900197){break;} }
        for(i=84;i<101;i++){ buf[1073]=(byte)i; if(sha1_32(buf)==-1839322177){break;} }
        for(i=98;i<114;i++){ buf[1074]=(byte)i; if(sha1_32(buf)==1400011491){break;} }
        for(i=83;i<104;i++){ buf[1075]=(byte)i; if(sha1_32(buf)==-1011864317){break;} }
        for(i=-100;i<-77;i++){ buf[1076]=(byte)i; if(sha1_32(buf)==1903359989){break;} }
        for(i=68;i<82;i++){ buf[1077]=(byte)i; if(sha1_32(buf)==-1371992992){break;} }
        for(i=105;i<112;i++){ buf[1078]=(byte)i; if(sha1_32(buf)==-756977693){break;} }
        for(i=-18;i<0;i++){ buf[1079]=(byte)i; if(sha1_32(buf)==1845814046){break;} }
        for(i=78;i<104;i++){ buf[1080]=(byte)i; if(sha1_32(buf)==1881699764){break;} }
        for(i=-121;i<-106;i++){ buf[1081]=(byte)i; if(sha1_32(buf)==-430026268){break;} }
        for(i=-124;i<-114;i++){ buf[1082]=(byte)i; if(sha1_32(buf)==-1855494279){break;} }
        for(i=-128;i<-117;i++){ buf[1083]=(byte)i; if(sha1_32(buf)==1965191990){break;} }
        for(i=40;i<49;i++){ buf[1084]=(byte)i; if(sha1_32(buf)==-2012086717){break;} }
        for(i=-8;i<4;i++){ buf[1085]=(byte)i; if(sha1_32(buf)==-2012086717){break;} }
        for(i=-128;i<-115;i++){ buf[1086]=(byte)i; if(sha1_32(buf)==954513397){break;} }
        for(i=-66;i<-47;i++){ buf[1087]=(byte)i; if(sha1_32(buf)==1280543549){break;} }
        for(i=103;i<104;i++){ buf[1088]=(byte)i; if(sha1_32(buf)==-1941691917){break;} }
        for(i=96;i<112;i++){ buf[1089]=(byte)i; if(sha1_32(buf)==-245829466){break;} }
        for(i=-17;i<0;i++){ buf[1090]=(byte)i; if(sha1_32(buf)==1443959213){break;} }
        for(i=35;i<55;i++){ buf[1091]=(byte)i; if(sha1_32(buf)==1155919269){break;} }
        for(i=-18;i<3;i++){ buf[1092]=(byte)i; if(sha1_32(buf)==1554944757){break;} }
        for(i=116;i<123;i++){ buf[1093]=(byte)i; if(sha1_32(buf)==1660689604){break;} }
        for(i=-9;i<9;i++){ buf[1094]=(byte)i; if(sha1_32(buf)==-788650828){break;} }
        for(i=-63;i<-51;i++){ buf[1095]=(byte)i; if(sha1_32(buf)==-1076492865){break;} }
        for(i=66;i<90;i++){ buf[1096]=(byte)i; if(sha1_32(buf)==-1995220967){break;} }
        for(i=95;i<98;i++){ buf[1097]=(byte)i; if(sha1_32(buf)==-235441860){break;} }
        for(i=-37;i<-13;i++){ buf[1098]=(byte)i; if(sha1_32(buf)==1331233095){break;} }
        for(i=-85;i<-73;i++){ buf[1099]=(byte)i; if(sha1_32(buf)==16719264){break;} }
        for(i=52;i<69;i++){ buf[1100]=(byte)i; if(sha1_32(buf)==-1040476940){break;} }
        for(i=3;i<18;i++){ buf[1101]=(byte)i; if(sha1_32(buf)==2070209064){break;} }
        for(i=-127;i<-120;i++){ buf[1102]=(byte)i; if(sha1_32(buf)==2131749557){break;} }
        for(i=26;i<43;i++){ buf[1103]=(byte)i; if(sha1_32(buf)==198482921){break;} }
        for(i=-76;i<-50;i++){ buf[1104]=(byte)i; if(sha1_32(buf)==27730104){break;} }
        for(i=-88;i<-70;i++){ buf[1105]=(byte)i; if(sha1_32(buf)==-1656362711){break;} }
        for(i=-97;i<-79;i++){ buf[1106]=(byte)i; if(sha1_32(buf)==-898956768){break;} }
        for(i=-67;i<-54;i++){ buf[1107]=(byte)i; if(sha1_32(buf)==2098037042){break;} }
        for(i=-2;i<18;i++){ buf[1108]=(byte)i; if(sha1_32(buf)==-380153442){break;} }
        for(i=112;i<125;i++){ buf[1109]=(byte)i; if(sha1_32(buf)==-1398722002){break;} }
        for(i=-121;i<-99;i++){ buf[1110]=(byte)i; if(sha1_32(buf)==230216637){break;} }
        for(i=-106;i<-97;i++){ buf[1111]=(byte)i; if(sha1_32(buf)==570151232){break;} }
        for(i=106;i<112;i++){ buf[1112]=(byte)i; if(sha1_32(buf)==-499202618){break;} }
        for(i=-13;i<5;i++){ buf[1113]=(byte)i; if(sha1_32(buf)==1403726612){break;} }
        for(i=84;i<106;i++){ buf[1114]=(byte)i; if(sha1_32(buf)==1649547257){break;} }
        for(i=2;i<16;i++){ buf[1115]=(byte)i; if(sha1_32(buf)==840460748){break;} }
        for(i=-41;i<-28;i++){ buf[1116]=(byte)i; if(sha1_32(buf)==1208686945){break;} }
        for(i=100;i<112;i++){ buf[1117]=(byte)i; if(sha1_32(buf)==-1204234505){break;} }
        for(i=94;i<110;i++){ buf[1118]=(byte)i; if(sha1_32(buf)==310256793){break;} }
        for(i=-124;i<-115;i++){ buf[1119]=(byte)i; if(sha1_32(buf)==-157648707){break;} }
        for(i=-128;i<-118;i++){ buf[1120]=(byte)i; if(sha1_32(buf)==-579156966){break;} }
        for(i=-63;i<-42;i++){ buf[1121]=(byte)i; if(sha1_32(buf)==1946546821){break;} }
        for(i=77;i<99;i++){ buf[1122]=(byte)i; if(sha1_32(buf)==-2106301633){break;} }
        for(i=70;i<95;i++){ buf[1123]=(byte)i; if(sha1_32(buf)==915667797){break;} }
        for(i=36;i<48;i++){ buf[1124]=(byte)i; if(sha1_32(buf)==1898614610){break;} }
        for(i=111;i<128;i++){ buf[1125]=(byte)i; if(sha1_32(buf)==-281928557){break;} }
        for(i=90;i<103;i++){ buf[1126]=(byte)i; if(sha1_32(buf)==341717558){break;} }
        for(i=105;i<121;i++){ buf[1127]=(byte)i; if(sha1_32(buf)==-482619462){break;} }
        for(i=72;i<89;i++){ buf[1128]=(byte)i; if(sha1_32(buf)==1092062024){break;} }
        for(i=-58;i<-44;i++){ buf[1129]=(byte)i; if(sha1_32(buf)==-2091230791){break;} }
        for(i=113;i<128;i++){ buf[1130]=(byte)i; if(sha1_32(buf)==-1185883507){break;} }
        for(i=3;i<29;i++){ buf[1131]=(byte)i; if(sha1_32(buf)==355967159){break;} }
        for(i=-44;i<-35;i++){ buf[1132]=(byte)i; if(sha1_32(buf)==1639281901){break;} }
        for(i=-126;i<-103;i++){ buf[1133]=(byte)i; if(sha1_32(buf)==937198186){break;} }
        for(i=-83;i<-74;i++){ buf[1134]=(byte)i; if(sha1_32(buf)==-404023113){break;} }
        for(i=-106;i<-89;i++){ buf[1135]=(byte)i; if(sha1_32(buf)==-1438936424){break;} }
        for(i=105;i<115;i++){ buf[1136]=(byte)i; if(sha1_32(buf)==187220771){break;} }
        for(i=-115;i<-93;i++){ buf[1137]=(byte)i; if(sha1_32(buf)==1233726736){break;} }
        for(i=122;i<128;i++){ buf[1138]=(byte)i; if(sha1_32(buf)==-1535784193){break;} }
        for(i=81;i<104;i++){ buf[1139]=(byte)i; if(sha1_32(buf)==891983336){break;} }
        for(i=-71;i<-69;i++){ buf[1140]=(byte)i; if(sha1_32(buf)==-353043150){break;} }
        for(i=-48;i<-28;i++){ buf[1141]=(byte)i; if(sha1_32(buf)==896592545){break;} }
        for(i=-127;i<-122;i++){ buf[1142]=(byte)i; if(sha1_32(buf)==-1385592379){break;} }
        for(i=102;i<105;i++){ buf[1143]=(byte)i; if(sha1_32(buf)==-992743457){break;} }
        for(i=77;i<93;i++){ buf[1144]=(byte)i; if(sha1_32(buf)==202597165){break;} }
        for(i=23;i<31;i++){ buf[1145]=(byte)i; if(sha1_32(buf)==1356337771){break;} }
        for(i=111;i<128;i++){ buf[1146]=(byte)i; if(sha1_32(buf)==-1172884832){break;} }
        for(i=-107;i<-89;i++){ buf[1147]=(byte)i; if(sha1_32(buf)==-709333805){break;} }
        for(i=100;i<115;i++){ buf[1148]=(byte)i; if(sha1_32(buf)==-13668053){break;} }
        for(i=119;i<128;i++){ buf[1149]=(byte)i; if(sha1_32(buf)==-193021851){break;} }
        for(i=-67;i<-50;i++){ buf[1150]=(byte)i; if(sha1_32(buf)==1177710509){break;} }
        for(i=-62;i<-47;i++){ buf[1151]=(byte)i; if(sha1_32(buf)==2070456918){break;} }
        for(i=-84;i<-73;i++){ buf[1152]=(byte)i; if(sha1_32(buf)==-1341872214){break;} }
        for(i=34;i<48;i++){ buf[1153]=(byte)i; if(sha1_32(buf)==1511606961){break;} }
        for(i=78;i<103;i++){ buf[1154]=(byte)i; if(sha1_32(buf)==1379878386){break;} }
        for(i=84;i<95;i++){ buf[1155]=(byte)i; if(sha1_32(buf)==383501328){break;} }
        for(i=36;i<61;i++){ buf[1156]=(byte)i; if(sha1_32(buf)==983923147){break;} }
        for(i=115;i<128;i++){ buf[1157]=(byte)i; if(sha1_32(buf)==1615018904){break;} }
        for(i=-25;i<-16;i++){ buf[1158]=(byte)i; if(sha1_32(buf)==-1615440620){break;} }
        for(i=74;i<89;i++){ buf[1159]=(byte)i; if(sha1_32(buf)==-1420257198){break;} }
        for(i=-128;i<-111;i++){ buf[1160]=(byte)i; if(sha1_32(buf)==-1999878963){break;} }
        for(i=-78;i<-62;i++){ buf[1161]=(byte)i; if(sha1_32(buf)==1850567241){break;} }
        for(i=-14;i<13;i++){ buf[1162]=(byte)i; if(sha1_32(buf)==-277323993){break;} }
        for(i=88;i<93;i++){ buf[1163]=(byte)i; if(sha1_32(buf)==1186035868){break;} }
        for(i=-35;i<-18;i++){ buf[1164]=(byte)i; if(sha1_32(buf)==-645972558){break;} }
        for(i=24;i<49;i++){ buf[1165]=(byte)i; if(sha1_32(buf)==-895074415){break;} }
        for(i=28;i<55;i++){ buf[1166]=(byte)i; if(sha1_32(buf)==-228966968){break;} }
        for(i=-89;i<-70;i++){ buf[1167]=(byte)i; if(sha1_32(buf)==-845175835){break;} }
        for(i=-127;i<-109;i++){ buf[1168]=(byte)i; if(sha1_32(buf)==548991341){break;} }
        for(i=-54;i<-35;i++){ buf[1169]=(byte)i; if(sha1_32(buf)==399936115){break;} }
        for(i=-79;i<-69;i++){ buf[1170]=(byte)i; if(sha1_32(buf)==-1627100321){break;} }
        for(i=108;i<128;i++){ buf[1171]=(byte)i; if(sha1_32(buf)==-1253082437){break;} }
        for(i=-110;i<-88;i++){ buf[1172]=(byte)i; if(sha1_32(buf)==1345089712){break;} }
        for(i=99;i<127;i++){ buf[1173]=(byte)i; if(sha1_32(buf)==2040479468){break;} }
        for(i=-50;i<-39;i++){ buf[1174]=(byte)i; if(sha1_32(buf)==-559426396){break;} }
        for(i=-61;i<-49;i++){ buf[1175]=(byte)i; if(sha1_32(buf)==1320955299){break;} }
        for(i=54;i<62;i++){ buf[1176]=(byte)i; if(sha1_32(buf)==-1418166800){break;} }
        for(i=74;i<86;i++){ buf[1177]=(byte)i; if(sha1_32(buf)==88469926){break;} }
        for(i=-82;i<-64;i++){ buf[1178]=(byte)i; if(sha1_32(buf)==644231986){break;} }
        for(i=82;i<92;i++){ buf[1179]=(byte)i; if(sha1_32(buf)==-312985430){break;} }
        for(i=98;i<122;i++){ buf[1180]=(byte)i; if(sha1_32(buf)==-224891807){break;} }
        for(i=-92;i<-69;i++){ buf[1181]=(byte)i; if(sha1_32(buf)==1220217353){break;} }
        for(i=124;i<125;i++){ buf[1182]=(byte)i; if(sha1_32(buf)==-1326960305){break;} }
        for(i=110;i<122;i++){ buf[1183]=(byte)i; if(sha1_32(buf)==-748411663){break;} }
        for(i=-89;i<-69;i++){ buf[1184]=(byte)i; if(sha1_32(buf)==835049434){break;} }
        for(i=-20;i<5;i++){ buf[1185]=(byte)i; if(sha1_32(buf)==-39817497){break;} }
        for(i=-94;i<-79;i++){ buf[1186]=(byte)i; if(sha1_32(buf)==94336531){break;} }
        for(i=63;i<78;i++){ buf[1187]=(byte)i; if(sha1_32(buf)==771604695){break;} }
        for(i=-104;i<-84;i++){ buf[1188]=(byte)i; if(sha1_32(buf)==1969735930){break;} }
        for(i=-51;i<-23;i++){ buf[1189]=(byte)i; if(sha1_32(buf)==1670191456){break;} }
        for(i=-26;i<-9;i++){ buf[1190]=(byte)i; if(sha1_32(buf)==407999752){break;} }
        for(i=-49;i<-38;i++){ buf[1191]=(byte)i; if(sha1_32(buf)==1867468766){break;} }
        for(i=-95;i<-73;i++){ buf[1192]=(byte)i; if(sha1_32(buf)==173076027){break;} }
        for(i=-125;i<-111;i++){ buf[1193]=(byte)i; if(sha1_32(buf)==-1693024754){break;} }
        for(i=-111;i<-84;i++){ buf[1194]=(byte)i; if(sha1_32(buf)==-840579274){break;} }
        for(i=-104;i<-93;i++){ buf[1195]=(byte)i; if(sha1_32(buf)==808685657){break;} }
        for(i=86;i<98;i++){ buf[1196]=(byte)i; if(sha1_32(buf)==2144979465){break;} }
        for(i=-128;i<-109;i++){ buf[1197]=(byte)i; if(sha1_32(buf)==-692307387){break;} }
        for(i=21;i<24;i++){ buf[1198]=(byte)i; if(sha1_32(buf)==-1880814791){break;} }
        for(i=-99;i<-86;i++){ buf[1199]=(byte)i; if(sha1_32(buf)==1315158296){break;} }
        for(i=42;i<47;i++){ buf[1200]=(byte)i; if(sha1_32(buf)==-1727352814){break;} }
        for(i=45;i<61;i++){ buf[1201]=(byte)i; if(sha1_32(buf)==-1892838810){break;} }
        for(i=-85;i<-65;i++){ buf[1202]=(byte)i; if(sha1_32(buf)==437386401){break;} }
        for(i=33;i<52;i++){ buf[1203]=(byte)i; if(sha1_32(buf)==97199744){break;} }
        for(i=-85;i<-58;i++){ buf[1204]=(byte)i; if(sha1_32(buf)==1227904885){break;} }
        for(i=-44;i<-31;i++){ buf[1205]=(byte)i; if(sha1_32(buf)==1182783892){break;} }
        for(i=69;i<92;i++){ buf[1206]=(byte)i; if(sha1_32(buf)==127265954){break;} }
        for(i=94;i<97;i++){ buf[1207]=(byte)i; if(sha1_32(buf)==981869599){break;} }
        for(i=-41;i<-27;i++){ buf[1208]=(byte)i; if(sha1_32(buf)==393480867){break;} }
        for(i=-40;i<-19;i++){ buf[1209]=(byte)i; if(sha1_32(buf)==-689952933){break;} }
        for(i=10;i<16;i++){ buf[1210]=(byte)i; if(sha1_32(buf)==-1641246723){break;} }
        for(i=80;i<87;i++){ buf[1211]=(byte)i; if(sha1_32(buf)==593438032){break;} }
        for(i=80;i<87;i++){ buf[1212]=(byte)i; if(sha1_32(buf)==-1684041051){break;} }
        for(i=-27;i<-11;i++){ buf[1213]=(byte)i; if(sha1_32(buf)==-174004047){break;} }
        for(i=-102;i<-75;i++){ buf[1214]=(byte)i; if(sha1_32(buf)==-1424795597){break;} }
        for(i=-111;i<-102;i++){ buf[1215]=(byte)i; if(sha1_32(buf)==-415539741){break;} }
        for(i=80;i<97;i++){ buf[1216]=(byte)i; if(sha1_32(buf)==-1966100584){break;} }
        for(i=-2;i<8;i++){ buf[1217]=(byte)i; if(sha1_32(buf)==1995860391){break;} }
        for(i=-85;i<-77;i++){ buf[1218]=(byte)i; if(sha1_32(buf)==225907922){break;} }
        for(i=104;i<122;i++){ buf[1219]=(byte)i; if(sha1_32(buf)==1587058695){break;} }
        for(i=77;i<91;i++){ buf[1220]=(byte)i; if(sha1_32(buf)==2041179650){break;} }
        for(i=18;i<29;i++){ buf[1221]=(byte)i; if(sha1_32(buf)==1421011825){break;} }
        for(i=-55;i<-40;i++){ buf[1222]=(byte)i; if(sha1_32(buf)==-417041048){break;} }
        for(i=-84;i<-62;i++){ buf[1223]=(byte)i; if(sha1_32(buf)==-87923831){break;} }
        for(i=-31;i<-24;i++){ buf[1224]=(byte)i; if(sha1_32(buf)==1338223817){break;} }
        for(i=-27;i<-15;i++){ buf[1225]=(byte)i; if(sha1_32(buf)==-2784511){break;} }
        for(i=-23;i<-9;i++){ buf[1226]=(byte)i; if(sha1_32(buf)==-1627527174){break;} }
        for(i=49;i<72;i++){ buf[1227]=(byte)i; if(sha1_32(buf)==-2100593702){break;} }
        for(i=-55;i<-29;i++){ buf[1228]=(byte)i; if(sha1_32(buf)==1516119261){break;} }
        for(i=-73;i<-51;i++){ buf[1229]=(byte)i; if(sha1_32(buf)==800007642){break;} }
        for(i=-106;i<-91;i++){ buf[1230]=(byte)i; if(sha1_32(buf)==951901797){break;} }
        for(i=21;i<28;i++){ buf[1231]=(byte)i; if(sha1_32(buf)==524205512){break;} }
        for(i=118;i<128;i++){ buf[1232]=(byte)i; if(sha1_32(buf)==-512965095){break;} }
        for(i=-47;i<-25;i++){ buf[1233]=(byte)i; if(sha1_32(buf)==23973221){break;} }
        for(i=-113;i<-110;i++){ buf[1234]=(byte)i; if(sha1_32(buf)==1480516689){break;} }
        for(i=95;i<112;i++){ buf[1235]=(byte)i; if(sha1_32(buf)==1969344882){break;} }
        for(i=-116;i<-99;i++){ buf[1236]=(byte)i; if(sha1_32(buf)==-1127592872){break;} }
        for(i=-40;i<-27;i++){ buf[1237]=(byte)i; if(sha1_32(buf)==-464077352){break;} }
        for(i=55;i<79;i++){ buf[1238]=(byte)i; if(sha1_32(buf)==-947041946){break;} }
        for(i=-116;i<-112;i++){ buf[1239]=(byte)i; if(sha1_32(buf)==1972930323){break;} }
        for(i=94;i<113;i++){ buf[1240]=(byte)i; if(sha1_32(buf)==1614980132){break;} }
        for(i=-77;i<-65;i++){ buf[1241]=(byte)i; if(sha1_32(buf)==-1029566321){break;} }
        for(i=-109;i<-99;i++){ buf[1242]=(byte)i; if(sha1_32(buf)==-1213246147){break;} }
        for(i=120;i<128;i++){ buf[1243]=(byte)i; if(sha1_32(buf)==-151628406){break;} }
        for(i=-114;i<-103;i++){ buf[1244]=(byte)i; if(sha1_32(buf)==-1782848630){break;} }
        for(i=80;i<90;i++){ buf[1245]=(byte)i; if(sha1_32(buf)==1776476327){break;} }
        for(i=66;i<84;i++){ buf[1246]=(byte)i; if(sha1_32(buf)==-1635129780){break;} }
        for(i=6;i<20;i++){ buf[1247]=(byte)i; if(sha1_32(buf)==1892623836){break;} }
        for(i=-1;i<6;i++){ buf[1248]=(byte)i; if(sha1_32(buf)==1465694420){break;} }
        for(i=-8;i<18;i++){ buf[1249]=(byte)i; if(sha1_32(buf)==1559435478){break;} }
        for(i=101;i<115;i++){ buf[1250]=(byte)i; if(sha1_32(buf)==1324136850){break;} }
        for(i=-17;i<-4;i++){ buf[1251]=(byte)i; if(sha1_32(buf)==1070164153){break;} }
        for(i=15;i<26;i++){ buf[1252]=(byte)i; if(sha1_32(buf)==-522891487){break;} }
        for(i=-23;i<3;i++){ buf[1253]=(byte)i; if(sha1_32(buf)==265247360){break;} }
        for(i=-48;i<-23;i++){ buf[1254]=(byte)i; if(sha1_32(buf)==-739017657){break;} }
        for(i=-29;i<-24;i++){ buf[1255]=(byte)i; if(sha1_32(buf)==1929376724){break;} }
        for(i=86;i<95;i++){ buf[1256]=(byte)i; if(sha1_32(buf)==-838014090){break;} }
        for(i=-51;i<-30;i++){ buf[1257]=(byte)i; if(sha1_32(buf)==-377480007){break;} }
        for(i=15;i<39;i++){ buf[1258]=(byte)i; if(sha1_32(buf)==-848167303){break;} }
        for(i=-84;i<-69;i++){ buf[1259]=(byte)i; if(sha1_32(buf)==-1565755144){break;} }
        for(i=47;i<65;i++){ buf[1260]=(byte)i; if(sha1_32(buf)==-764469554){break;} }
        return buf;
    }
}
