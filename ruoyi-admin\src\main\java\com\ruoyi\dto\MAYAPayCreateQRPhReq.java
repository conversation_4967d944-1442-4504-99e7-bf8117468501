package com.ruoyi.dto;

import lombok.Data;

/**
 * MAYA支付 - 创建支付码请求
 */
@Data
public class MAYAPayCreateQRPhReq {

    /**
     * 金额
     */
    private TotalAmount totalAmount;

    /**
     * 请求参考编号
     * <p>
     *     商家的交易参考号，用于唯一标识一笔交易
     * </p>
     */
    private String requestReferenceNumber;

    /**
     * 附加数据
     */
    private MetadataObj metadata;

    @Data
    public static class TotalAmount{

        /**
         * 金额
         */
        private double value;

        /**
         * 币种
         */
        private String currency;

        // 构造函数
        public TotalAmount(double value, String currency) {
            this.value = value;
            this.currency = currency;
        }
    }

    @Data
    public static class MetadataObj
    {
        /**
         * 商户的详细信息
         */
        private Pf pf;

        // 构造函数
        public MetadataObj(Pf pf) {
            this.pf = pf;
        }
    }

    @Data
    public static class Pf
    {
        /**
         * 商户id
         */
        private String smi;

        /**
         * 商户名称
         */
        private String smn;

        /**
         * 商户地址
         */
        private String mci;

        /**
         * 货币代码 - 3位ISO 4217代码
         */
        private String mpc;

        /**
         * 商户国家 - 3位ISO 3166-3代码
         */
        private String mco;

        // 构造函数
        public Pf(String smi, String smn, String mci, String mpc, String mco) {
            this.smi = smi;
            this.smn = smn;
            this.mci = mci;
            this.mpc = mpc;
            this.mco = mco;
        }
    }

}
