<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.photo.mapper.UserPhotoMapper">

    <resultMap type="UserPhoto" id="UserPhotoResult">
        <result property="id"    column="id"    />
        <result property="openId"    column="open_id"    />
        <result property="deviceId"    column="device_id"    />
        <result property="orderId"    column="order_id"    />
        <result property="objectName"    column="object_name"    />
        <result property="url"    column="url"    />
        <result property="type"    column="type"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectUserPhotoVo">
        select id, open_id, object_name, url, type, create_by, create_time, device_id, order_id
               from user_photo
    </sql>

    <select id="selectUserPhotoList" parameterType="UserPhoto" resultMap="UserPhotoResult">
        <include refid="selectUserPhotoVo"/>
        <where>
            <if test="openId != null  and openId != ''"> and open_id = #{openId}</if>
            <if test="type != null "> and type = #{type}</if>
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="selectUserPhotoById" parameterType="Long" resultMap="UserPhotoResult">
        <include refid="selectUserPhotoVo"/>
        where id = #{id}
    </select>
    
    <insert id="insertUserPhoto" parameterType="UserPhoto" useGeneratedKeys="true" keyProperty="id">
        insert into user_photo
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="openId != null">open_id,</if>
            <if test="objectName != null">object_name,</if>
            <if test="url != null">url,</if>
            <if test="type != null">type,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="deviceId != null">device_id</if>
            <if test="orderId != null">order_id</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="openId != null">#{openId},</if>
            <if test="objectName != null">#{objectName},</if>
            <if test="url != null">#{url},</if>
            <if test="type != null">#{type},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="deviceId != null">#{deviceId}</if>
            <if test="orderId != null">#{orderId}</if>
         </trim>
    </insert>

    <update id="updateUserPhoto" parameterType="UserPhoto">
        update user_photo
        <trim prefix="SET" suffixOverrides=",">
            <if test="openId != null">open_id = #{openId},</if>
            <if test="objectName != null">object_name = #{objectName},</if>
            <if test="url != null">url = #{url},</if>
            <if test="type != null">type = #{type},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUserPhotoById" parameterType="Long">
        delete from user_photo where id = #{id}
    </delete>

    <delete id="deleteUserPhotoByIds" parameterType="String">
        delete from user_photo where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
