package com.ruoyi.web.controller.photo;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.device.domain.DeviceCamera;
import com.ruoyi.device.service.ICameraSysImageService;
import com.ruoyi.device.service.IDeviceCameraService;
import com.ruoyi.dto.Result;
import com.ruoyi.dto.UploadPhotoDto;
import com.ruoyi.order.domain.OrderCamera;
import com.ruoyi.order.service.IOrderCameraService;
import com.ruoyi.photo.domain.ImageBase64;
import com.ruoyi.photo.domain.UserPhoto;
import com.ruoyi.photo.service.IUserPhotoService;
import com.ruoyi.photo.service.ImageBase64Service;
import com.ruoyi.socket.WebSocketServer;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.nio.file.*;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;

import static com.ruoyi.utils.FileUtils.*;

/**
 * photoController
 *
 * <AUTHOR>
 * @date 2023-09-05
 */
@RestController
@RequestMapping("/photo/photo")
public class UserPhotoController extends BaseController {
    @Autowired
    private IUserPhotoService userPhotoService;

    @Autowired
    private ICameraSysImageService iCameraSysImageService;
    @Autowired
    private IOrderCameraService orderCameraService;

    @Autowired
    private IDeviceCameraService deviceCameraService;
    @Autowired
    private ImageBase64Service imageBase64Service;


    /**
     * 查询photo列表
     */
    @PreAuthorize("@ss.hasPermi('photo:photo:list')")
    @GetMapping("/list")
    public TableDataInfo list(UserPhoto userPhoto) {
        startPage();
        List<UserPhoto> list = userPhotoService.selectUserPhotoList(userPhoto);
        TableDataInfo dataTable = getDataTable(list);
        DeviceCamera deviceCamera = deviceCameraService.selectDeviceCameraByDeviceId(userPhoto.getDeviceId());
        if (deviceCamera == null) return dataTable;

        List<UserPhoto> rows = (List<UserPhoto>) dataTable.getRows();
        for (UserPhoto photo : rows) {
            photo.setDeviceName(deviceCamera.getDeviceName());
        }
        return dataTable;
    }

    /**
     * 导出photo列表
     */
    @PreAuthorize("@ss.hasPermi('photo:photo:export')")
    @Log(title = "photo", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, UserPhoto userPhoto) {
        List<UserPhoto> list = userPhotoService.selectUserPhotoList(userPhoto);
        ExcelUtil<UserPhoto> util = new ExcelUtil<UserPhoto>(UserPhoto.class);
        util.exportExcel(response, list, "photo数据");
    }

    /**
     * 获取photo详细信息
     */
    @PreAuthorize("@ss.hasPermi('photo:photo:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(userPhotoService.selectUserPhotoById(id));
    }

    /**
     * 新增photo
     */
    @PreAuthorize("@ss.hasPermi('photo:photo:add')")
    @Log(title = "photo", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody UserPhoto userPhoto) {
        return toAjax(userPhotoService.insertUserPhoto(userPhoto));
    }

    /**
     * 修改photo
     */
    @PreAuthorize("@ss.hasPermi('photo:photo:edit')")
    @Log(title = "photo", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody UserPhoto userPhoto) {
        return toAjax(userPhotoService.updateUserPhoto(userPhoto));
    }

    /**
     * 删除photo
     */
    @PreAuthorize("@ss.hasPermi('photo:photo:remove')")
    @Log(title = "photo", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(userPhotoService.deleteUserPhotoByIds(ids));
    }


    /**
     * 保存照片
     */
    @PostMapping("/save")
    public Result save(@RequestParam(value = "file", required = false) MultipartFile[] file,
                       String orderId,
                       @RequestParam(value = "deviceId", required = false) String deviceId) {
        return savePhoto(file, orderId, deviceId);
    }

    private Result savePhoto(MultipartFile[] file,String orderId,String deviceId){
        List<String> urls = new ArrayList<>();
        OrderCamera orderCamera = null;
        if (orderId != null)
            orderCamera = orderCameraService.selectOrderCameraByOrderId(orderId);
        if (orderCamera == null) {
            orderCamera = new OrderCamera();
            orderCamera.setDeviceId(deviceId == null ? "test01" : deviceId);
            orderCamera.setOrderId(orderId);
            orderCamera.setRemark("订单不存在，为了上传照片而生成");
            orderCamera.setOrderStatus(1L);
            orderCamera.setPhotoType(9999);
        }
        if (orderCamera.getDeviceId() == null) orderCamera.setDeviceId(deviceId == null ? "test01" : deviceId);

        if (file != null && file.length > 0) {
            for (int i = 0; i < file.length; i++) {
                String url = userPhotoService.savePhoto(file[i], orderCamera, i);
                urls.add(url);
            } 
            return Result.ok(200, "照片上传成功", String.valueOf(System.currentTimeMillis()), urls);
        } else {
            return Result.fail(500, "照片上传失败，file为空", "");
        }
    }
    /**
     * 保存照片（无公众号）
     */
    @PostMapping("/saveNoWX")
    public Result saveNoWX(@RequestParam(value = "file", required = false) MultipartFile[] file,
                           @RequestParam(value = "orderId", required = false) String orderId,
                           @RequestParam(value = "deviceId", required = false) String deviceId) {
        return savePhoto(file, orderId, deviceId);
    }

    /**
     * 保存照片 无订单
     */
    @PostMapping("/saveNoOrder")
    public Result saveNoOrder(@RequestParam(value = "file", required = false) MultipartFile[] file, String deviceId) {
        List<String> urls = new ArrayList<>();

        if (file != null && file.length > 0) {
            for (MultipartFile multipartFile : file) {
                String url = userPhotoService.savePhotoNoOrder(multipartFile, deviceId);
                urls.add(url);
            }
            return Result.ok(200, "照片上传成功", String.valueOf(System.currentTimeMillis()), urls);
        } else {
            return Result.fail(500, "照片上传失败，file为空", "");
        }
    }

    /**
     * 公众号下载图片
     */
    @GetMapping("/myPhoto")
    public Result myPhoto(@RequestParam String unionId) {
        System.out.println("unionId :" + unionId);
        if (unionId == null || unionId.equals("")) return Result.fail(401, "无法获取用户信息", "");
        UserPhoto userPhoto = new UserPhoto();
        userPhoto.setOpenId(unionId);
        List<UserPhoto> userPhotos = userPhotoService.selectUserPhotoList(userPhoto);

        return Result.ok(200, "获取成功", String.valueOf(System.currentTimeMillis()), userPhotos);
    }


    /**
     * 公众号下载图片（免支付，免提前扫码）
     */
    @GetMapping("/myPhoto_free")
    public Result myPhoto_(@RequestParam String unionId, @RequestParam String photoId) {
        if (unionId == null || unionId.equals("")) return Result.fail(401, "无法获取用户信息", "");
        System.out.println("unionId :" + unionId);
        UserPhoto freePhoto = userPhotoService.selectUserPhotoById(Long.valueOf(photoId));
        if (freePhoto != null) {
            freePhoto.setOpenId(unionId);
            userPhotoService.updateUserPhoto(freePhoto);
        } else {
            freePhoto = new UserPhoto();
            freePhoto.setId(Long.valueOf(photoId));
            freePhoto.setOpenId(unionId);
            userPhotoService.insertUserPhoto(freePhoto);
        }
        UserPhoto userPhoto = new UserPhoto();
        userPhoto.setOpenId(unionId);
        List<UserPhoto> userPhotos = userPhotoService.selectUserPhotoList(userPhoto);
        return Result.ok(200, "获取成功", String.valueOf(System.currentTimeMillis()), userPhotos);
    }


    /**
     * 微信图片检测，先上传图片后，发给服务器检测
     */
    @PostMapping("/WxImageDetection")
    public Result WxImageDetection(@RequestParam(value = "file", required = false) MultipartFile file) throws WxErrorException, WxErrorException {
        System.out.println(file);
        if (file != null) {
            String path = userPhotoService.WxImageDetection(file);
            System.out.println("WxImageDetection::" + path);
            return Result.ok(200, "上传成功", String.valueOf(System.currentTimeMillis()), null);
        }
        return null;
    }

    /**
     * 上传photo打印
     */
    @PostMapping("/upload")
    public Result upload(@RequestParam(value = "file", required = false) MultipartFile file,
                         @RequestParam String deviceId,
                         @RequestParam(required = false) String openId) {
        if (file != null) {
            String path = userPhotoService.uploadPhoto(file, deviceId, openId);

            WebSocketServer.sendInfo(path, deviceId);

            return Result.ok(200, "上传成功", String.valueOf(System.currentTimeMillis()), null);
        }
        return null;
    }


    /**
     * 保存照片（无公众号）
     */
    @PostMapping("/saveNoWX_free")
    public Result saveNoWX_free(@RequestParam(value = "file", required = false) MultipartFile file, String photoId) {
        if (file != null) {
            String url = userPhotoService.savePhotoNoWX_free(file, photoId);
            return Result.ok(200, "照片上传成功", String.valueOf(System.currentTimeMillis()), url);
        } else {
            return Result.fail(500, "照片上传失败", "");
        }
    }

    /**
     * 上传单张照片入口
     */
    @GetMapping("/upload_")
    public ModelAndView upload_(String deviceId) {
        ModelAndView modelAndView = new ModelAndView("upload");
        modelAndView.addObject("deviceId", deviceId);
        return modelAndView;
    }

    /**
     * 上传多张照片入口  标版在用
     */
    @GetMapping("/uploads_")
    public ModelAndView uploads_(String deviceId) {
        ModelAndView modelAndView = new ModelAndView("uploadPhotos");
        modelAndView.addObject("deviceId", deviceId);
        return modelAndView;
    }

    /**
     * 上传photo打印
     */
    @PostMapping("/upload_base64")
    public Result upload_base64(@RequestParam(required = false) String file_base64,
                                @RequestParam String deviceId) {
        System.out.println("上传photo打印:" + deviceId);
        if (deviceId == null) return null;
        DeviceCamera deviceCameraServiceById = deviceCameraService.getById(deviceId);
        if (deviceCameraServiceById == null) return null;
        String folderPath = RuoYiConfig.getProfile() + "/photo/";
        String fileName = deviceId + ".txt";
        try {
            // 检查文件夹和文件是否存在，不存在则创建
            createFolderAndFile(folderPath, fileName);
            // 获取完整文件路径
            String filePath = Paths.get(folderPath, fileName).toString();
            // 将base64字符串写入文件
            writeBase64ToFile(file_base64, filePath);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Result.ok(200, "上传成功", "", null);
    }

    /**
     * 删除上传打印的photo
     */
    @GetMapping("/delete_upload")
    public Result delete_upload(@RequestParam String deviceId) {
        // 指定文件路径
        String filePath = RuoYiConfig.getProfile() + "/photo/" + deviceId + ".txt";
        // 创建File对象
        File file = new File(filePath);
        // 检查文件是否存在
        if (file.exists()) {
            // 如果存在，尝试删除文件
            if (file.delete()) {
                System.out.println("上传的文件删除成功");
            } else {
                System.out.println("上传的文件删除失败");
            }
        } else {
            System.out.println("上传的文件不存在");
        }
        return Result.ok(200, "删除成功", "", null);
    }

    /**
     * 查询上传打印的photo
     */
    @GetMapping("/check_upload")
    public Result check_upload(@RequestParam String deviceId) {
        // 指定文件路径
        String filePath = RuoYiConfig.getProfile() + "/photo/" + deviceId + ".txt";
        // 创建File对象
        File file = new File(filePath);
        // 检查文件是否存在
        if (file.exists()) {
            StringBuilder stringBuilder = new StringBuilder();
            try {
                // 使用FileReader和BufferedReader读取文件内容
                FileReader fileReader = new FileReader(file);
                BufferedReader bufferedReader = new BufferedReader(fileReader);
                String line;
                // 逐行读取文件内容
                while ((line = bufferedReader.readLine()) != null) {
                    stringBuilder.append(line).append("\n");
                }
                // 关闭读取流
                bufferedReader.close();
                // 输出读取的文件内容
            } catch (IOException e) {
                e.printStackTrace();
                return Result.fail(400, e.getMessage(), "");
            }
            String fileContent = stringBuilder.toString();
            return Result.ok(200, "照片获取成功", "", fileContent);
        } else {
            return Result.fail(500, "照片不存在，请上传", "");
        }
    }


    /**
     * 上传photo打印  标版在用  旧
     */
    @PostMapping("/upload_base64s")
    public Result upload_base64s(@RequestParam(required = false) String file_base64,
                                 @RequestParam String deviceId) {
        System.out.println("上传photo打印:" + deviceId);
        if (deviceId == null) return Result.fail(201, "设备id不能为空", "");
        DeviceCamera deviceCameraServiceById = deviceCameraService.getById(deviceId);
        if (deviceCameraServiceById == null) return Result.fail(201, "设备id不存在", "");
        String folderPath = RuoYiConfig.getProfile() + "/photo/" + deviceId + "/";

        int countFiles = countFiles(folderPath);
        System.out.println(countFiles);
        if (countFiles >= 10) return Result.fail(201, "最多上传10张", "");

        String fileName = System.currentTimeMillis() + ".txt";
        try {
            // 检查文件夹和文件是否存在，不存在则创建
            createFolderAndFile(folderPath, fileName);
            // 获取完整文件路径
            String filePath = Paths.get(folderPath, fileName).toString();
            // 将base64字符串写入文件
            writeBase64ToFile(file_base64, filePath);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Result.ok(200, "成功上传：" + (countFiles + 1) + "张", "", null);
    }

    /**
     * 删除上传打印的photo
     */
    @GetMapping("/delete_uploads")
    public Result delete_uploads(@RequestParam String deviceId) {
        if (deviceId == null || deviceId.equals("")) return Result.fail(201, "设备id不能为空", "");
        // 指定文件路径
        String filePath = RuoYiConfig.getProfile() + "/photo/" + deviceId + "/";
        try {
            Path directory = Paths.get(filePath);

            // 检查文件夹是否存在
            if (Files.exists(directory) && Files.isDirectory(directory)) {
                // 删除文件夹及其内容
                Files.walk(directory)
                        .sorted(Comparator.reverseOrder())
                        .map(Path::toFile)
                        .forEach(File::delete);

                System.out.println("文件夹删除成功");
            } else {
                System.out.println("文件夹不存在或不是一个有效的文件夹路径");
                return Result.fail(201, "地址不存在", "");
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Result.ok(200, "清除成功", "", null);
    }

    /**
     * 查询上传打印的photo
     */
    @GetMapping("/check_uploads")
    public Result check_uploads(@RequestParam String deviceId) {
        String folderPath = RuoYiConfig.getProfile() + "/photo/" + deviceId + "/";  // 替换成你的文件夹路径
        List<String> fileContents = readTxtFiles(folderPath);
        return Result.ok(200, "照片获取成功", "", fileContents);
    }

    @GetMapping("/byOrderId")
    public Result queryByOrderId(@RequestParam String orderId) {
        List<UserPhoto> userPhotos = userPhotoService.query().eq("order_id", orderId).list();
        return Result.ok(200, "查询成功", "", userPhotos);
    }

    /**
     * 上传photo打印  标版在用  新
     */

    @PostMapping("/uploadPhoto")
    public Result userUploadPhoto_base64(@RequestBody UploadPhotoDto uploadPhotoDto) {
        String deviceId = uploadPhotoDto.getDeviceId();
        String[] base64s = uploadPhotoDto.getBase64s();
        Integer limit = uploadPhotoDto.getLimit();
        if (limit == null || limit == 0) limit = 1;
        if (deviceId == null) return Result.fail(601, "设备id不能为空", "");
        DeviceCamera deviceCameraServiceById = deviceCameraService.getById(deviceId);
        if (deviceCameraServiceById == null) return Result.fail(602, "设备id不存在", "");
        String folderPath = RuoYiConfig.getProfile() + "/photo/" + deviceId + "/";

        int countFiles = countFiles(folderPath);
        System.out.println(countFiles);
        if (countFiles + base64s.length > limit) return Result.fail(603, "最多上传" + limit + "张", "");
        int success = 0;
        int fail = 0;
        HashSet<String> fileNameSet = new HashSet();
        for (int i = 0; i < base64s.length; i++) {
            if (i >= limit) break;
            String fileName = System.currentTimeMillis() + ".txt";
            while (!fileNameSet.add(fileName))
                fileName = System.currentTimeMillis() + ".txt";

            try {
                // 检查文件夹和文件是否存在，不存在则创建
                createFolderAndFile(folderPath, fileName);
                // 获取完整文件路径
                String filePath = Paths.get(folderPath, fileName).toString();
                // 将base64字符串写入文件
                writeBase64ToFile(base64s[i], filePath);
                success++;
            } catch (Exception e) {
                e.printStackTrace();
                fail++;
            }
        }
        WebSocketServer.sendInfo("sccg", deviceId); // 主推支付成功
        return Result.ok(200, "已经上传：" + (countFiles + success) + "张,失败" + fail + "张", "", countFiles + base64s.length);
    }

    /**
     * 上传photo打印  以色列用的
     */

    @PostMapping("/uploadPhoto_Israel")
    public Result UploadPhoto_Israel(@RequestBody UploadPhotoDto uploadPhotoDto) {
        String deviceId = uploadPhotoDto.getDeviceId();
        String[] base64s = uploadPhotoDto.getBase64s();
        Integer limit = uploadPhotoDto.getLimit();
        if (limit == null || limit == 0) limit = 1;
        if (deviceId == null) return Result.fail(601, "设备id不能为空", "");
        DeviceCamera deviceCameraServiceById = deviceCameraService.getById(deviceId);
        if (deviceCameraServiceById == null) return Result.fail(602, "设备id不存在", "");

        Long countFiles = imageBase64Service.query().eq("device_id", deviceId).count();

        if (countFiles + base64s.length > limit) return Result.fail(603, "最多上传" + limit + "张", "");
        int success = 0;
        int fail = 0;
        for (int i = 0; i < base64s.length; i++) {
            if (i >= limit) break;
            ImageBase64 imageBase64 = new ImageBase64();
            imageBase64.setBase64(base64s[i]);
            imageBase64.setDeviceId(deviceId);
            boolean save = imageBase64Service.save(imageBase64);
            if (save) success++;
            else fail++;
        }
        return Result.ok(200, "已经上传：" + (countFiles + success) + "张,失败" + fail + "张", "", countFiles + base64s.length);
    }

    /**
     * 删除上传打印的photo 以色列用的
     */
    @GetMapping("/delete_uploads_Israel")
    public Result delete_uploads_Israel(@RequestParam String deviceId) {
        if (deviceId == null || deviceId.equals("")) return Result.fail(201, "设备id不能为空", "");
        boolean remove = imageBase64Service.remove(new QueryWrapper<ImageBase64>().eq("device_id", deviceId));
        return remove ? Result.ok(200, "清除成功", "", null) : Result.fail(400, "清除失败", "");
    }

    /**
     * 查询上传打印的photo  以色列
     */
    @GetMapping("/check_uploads_Israel")
    public Result check_uploads_Israel(@RequestParam String deviceId) {
        List<ImageBase64> imageBase64List = imageBase64Service.query().eq("device_id", deviceId).list();
        List<String> fileContents = new ArrayList<>();
        for (ImageBase64 imageBase64 : imageBase64List) {
            fileContents.add(imageBase64.getBase64());
        }
        return Result.ok(200, "照片获取成功", "", fileContents);
    }


    /**
     * 根据订单号查图片资源
     */
    @GetMapping("/getPhotoByOrderId")
    public Result getPhotoByOrderId(@RequestParam String orderId) {
        if (orderId.contains("_")) {
            orderId = orderId.substring(2);
        }
        List<UserPhoto> userPhotos = userPhotoService.query().eq("order_id", orderId).list();

        List<UserPhoto> myUserPhotos = new ArrayList<>();
        for (UserPhoto userPhoto : userPhotos) {
            if (!userPhoto.getUrl().contains("mp4") && !userPhoto.getUrl().contains("gif"))
                myUserPhotos.add(userPhoto);
        }
        for (UserPhoto userPhoto : userPhotos) {
            if (userPhoto.getUrl().contains("mp4") || userPhoto.getUrl().contains("gif"))
                myUserPhotos.add(userPhoto);
        }

        if (myUserPhotos.size() > 0)
            return Result.ok(200, "照片获取成功", "", myUserPhotos);
        else
            return Result.ok(201, "订单无照片", "", null);
    }


}
