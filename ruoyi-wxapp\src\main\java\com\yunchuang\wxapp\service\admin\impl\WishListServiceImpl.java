package com.yunchuang.wxapp.service.admin.impl;

import com.yunchuang.wxapp.mapper.WishListMapper;
import com.yunchuang.wxapp.model.domain.WishList;
import com.yunchuang.wxapp.service.admin.IWishListService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 心愿单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-06
 */
@Service
public class WishListServiceImpl implements IWishListService {
    @Autowired
    private WishListMapper wishListMapper;

    /**
     * 查询心愿单
     *
     * @param id 心愿单主键
     * @return 心愿单
     */
    @Override
    public WishList selectWishListById(Long id) {
        return wishListMapper.selectWishListById(id);
    }

    /**
     * 查询心愿单列表
     *
     * @param wishList 心愿单
     * @return 心愿单
     */
    @Override
    public List<WishList> selectWishListList(WishList wishList) {
        return wishListMapper.selectWishListList(wishList);
    }

    /**
     * 新增心愿单
     *
     * @param wishList 心愿单
     * @return 结果
     */
    @Override
    public int insertWishList(WishList wishList) {
        return wishListMapper.insertWishList(wishList);
    }

    /**
     * 修改心愿单
     *
     * @param wishList 心愿单
     * @return 结果
     */
    @Override
    public int updateWishList(WishList wishList) {
        return wishListMapper.updateWishList(wishList);
    }

    /**
     * 批量删除心愿单
     *
     * @param ids 需要删除的心愿单主键
     * @return 结果
     */
    @Override
    public int deleteWishListByIds(Long[] ids) {
        return wishListMapper.deleteWishListByIds(ids);
    }

    /**
     * 删除心愿单信息
     *
     * @param id 心愿单主键
     * @return 结果
     */
    @Override
    public int deleteWishListById(Long id) {
        return wishListMapper.deleteWishListById(id);
    }
}
