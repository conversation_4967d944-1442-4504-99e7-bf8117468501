package com.ruoyi.voucher.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 优惠券订单对象 voucher_order
 * 
 * <AUTHOR>
 * @date 2024-04-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("voucher_order")
public class VoucherOrder extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 券订单id */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /** 下单用户id */
    @Excel(name = "下单用户id")
    private String customerId;

    /** 优惠券码 */
    @Excel(name = "优惠券码")
    private String voucherCode;

    /** 优惠券码 */
    @Excel(name = "优惠券码",cellType = Excel.ColumnType.IMAGE_BYTE)
    @TableField(exist = false)
    private byte[] qrCode;

    /** 优惠券id */
    @Excel(name = "优惠券id")
    private String voucherId;

    /** 使用次数 */
    @Excel(name = "使用次数")
    private Integer useCount;

    /** 状态 0未支付 1已支付 2已取消 3已退款 4已核销 5批量生成 */
    @Excel(name = "状态")
    private Integer status;

    /** 支付时间 */
    private Date payTime;

    //批量生成时的数量
    @TableField(exist = false)
    private int count;

    @TableField(exist = false)
    private Date beginCreateTime;

    @TableField(exist = false)
    private Date endCreateTime;

}
