package com.ruoyi.beauty.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 美颜强度对象 beauty_value
 * 
 * <AUTHOR>
 * @date 2023-10-15
 */
public class BeautyValue extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 磨皮最小值 */
    @Excel(name = "磨皮最小值")
    private int mopiMin;

    /** 磨皮最大值 */
    @Excel(name = "磨皮最大值")
    private int mopiMax;

    /** 美白最小值 */
    @Excel(name = "美白最小值")
    private int meibaiMin;

    /** 美白最大值 */
    @Excel(name = "美白最大值")
    private int meibaiMax;

    /** 五官立体最小值 */
    @Excel(name = "五官立体最小值")
    private int wuguanlitiMin;

    /** 五官立体最大值 */
    @Excel(name = "五官立体最大值")
    private int wuguanlitiMax;

    /** 亮眼最小值 */
    @Excel(name = "亮眼最小值")
    private int liangyanMin;

    /** 亮眼最大值 */
    @Excel(name = "亮眼最大值")
    private int liangyanMax;

    /** 红润最小值 */
    @Excel(name = "红润最小值")
    private int hongrunMin;

    /** 红润最大值 */
    @Excel(name = "红润最大值")
    private int hongrunMax;

    /** 瘦脸最小值 */
    @Excel(name = "瘦脸最小值")
    private int shoulianMin;

    /** 瘦脸最大值 */
    @Excel(name = "瘦脸最大值")
    private int shoulianMax;

    /** 大眼最小值 */
    @Excel(name = "大眼最小值")
    private int dayanMin;

    /** 大眼最大值 */
    @Excel(name = "大眼最大值")
    private int dayanMax;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setMopiMin(int mopiMin)
    {
        this.mopiMin = mopiMin;
    }

    public int getMopiMin()
    {
        return mopiMin;
    }
    public void setMopiMax(int mopiMax)
    {
        this.mopiMax = mopiMax;
    }

    public int getMopiMax()
    {
        return mopiMax;
    }
    public void setMeibaiMin(int meibaiMin)
    {
        this.meibaiMin = meibaiMin;
    }

    public int getMeibaiMin()
    {
        return meibaiMin;
    }
    public void setMeibaiMax(int meibaiMax)
    {
        this.meibaiMax = meibaiMax;
    }

    public int getMeibaiMax()
    {
        return meibaiMax;
    }
    public void setWuguanlitiMin(int wuguanlitiMin)
    {
        this.wuguanlitiMin = wuguanlitiMin;
    }

    public int getWuguanlitiMin()
    {
        return wuguanlitiMin;
    }
    public void setWuguanlitiMax(int wuguanlitiMax)
    {
        this.wuguanlitiMax = wuguanlitiMax;
    }

    public int getWuguanlitiMax()
    {
        return wuguanlitiMax;
    }
    public void setLiangyanMin(int liangyanMin)
    {
        this.liangyanMin = liangyanMin;
    }

    public int getLiangyanMin()
    {
        return liangyanMin;
    }
    public void setLiangyanMax(int liangyanMax)
    {
        this.liangyanMax = liangyanMax;
    }

    public int getLiangyanMax()
    {
        return liangyanMax;
    }
    public void setHongrunMin(int hongrunMin)
    {
        this.hongrunMin = hongrunMin;
    }

    public int getHongrunMin()
    {
        return hongrunMin;
    }
    public void setHongrunMax(int hongrunMax)
    {
        this.hongrunMax = hongrunMax;
    }

    public int getHongrunMax()
    {
        return hongrunMax;
    }
    public void setShoulianMin(int shoulianMin)
    {
        this.shoulianMin = shoulianMin;
    }

    public int getShoulianMin()
    {
        return shoulianMin;
    }
    public void setShoulianMax(int shoulianMax)
    {
        this.shoulianMax = shoulianMax;
    }

    public int getShoulianMax()
    {
        return shoulianMax;
    }
    public void setDayanMin(int dayanMin)
    {
        this.dayanMin = dayanMin;
    }

    public int getDayanMin()
    {
        return dayanMin;
    }
    public void setDayanMax(int dayanMax)
    {
        this.dayanMax = dayanMax;
    }

    public int getDayanMax()
    {
        return dayanMax;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("mopiMin", getMopiMin())
            .append("mopiMax", getMopiMax())
            .append("meibaiMin", getMeibaiMin())
            .append("meibaiMax", getMeibaiMax())
            .append("wuguanlitiMin", getWuguanlitiMin())
            .append("wuguanlitiMax", getWuguanlitiMax())
            .append("liangyanMin", getLiangyanMin())
            .append("liangyanMax", getLiangyanMax())
            .append("hongrunMin", getHongrunMin())
            .append("hongrunMax", getHongrunMax())
            .append("shoulianMin", getShoulianMin())
            .append("shoulianMax", getShoulianMax())
            .append("dayanMin", getDayanMin())
            .append("dayanMax", getDayanMax())
            .toString();
    }
}
