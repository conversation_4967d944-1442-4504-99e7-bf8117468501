package com.ruoyi.device.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.Date;
import java.util.List;

/**
 * 打印机设备对象 device_printer
 * 
 * <AUTHOR>
 * @date 2024-07-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("device_printer")
public class DevicePrinter extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 设备ID */
    @Excel(name = "设备ID")
    @TableId(value = "device_id")
    private String deviceId;

    /** 自定义设备名称 */
    @Excel(name = "设备名称")
    private String deviceName;

    /** 设备编码/序列号 */
    @Excel(name = "设备编码")
    private String deviceCode;

    /** 设备型号 */
    @Excel(name = "设备型号")
    private String deviceModel;

    /** 品牌ID */
    @Excel(name = "品牌ID")
    private Integer brandId;

    /** 软件ID */
    @Excel(name = "软件ID")
    private Integer softwareId;

    /** 省 */
    @Excel(name = "省")
    private String province;

    /** 市 */
    @Excel(name = "市")
    private String city;

    /** 区/县 */
    @Excel(name = "区/县")
    private String district;

    /** 除省市区外的详细地址 */
    @Excel(name = "详细地址")
    private String detailAddress;

    /** 设备状态 0-停用 1-启用 2-维护中 */
    @Excel(name = "设备状态", readConverterExp = "0=停用,1=启用,2=维护中")
    private Integer deviceStatus;

    /** 在线状态 0-离线 1-在线 */
    @Excel(name = "在线状态", readConverterExp = "0=离线,1=在线")
    private Integer onlineStatus;

    /** 经度 */
    @Excel(name = "经度")
    private Double lng;

    /** 纬度 */
    @Excel(name = "纬度")
    private Double lat;

    /** 代理商ID */
    @Excel(name = "代理商ID")
    private Long agentId;

    /** 代理商名称 */
    @Excel(name = "代理商名称")
    private String agentName;

    /** 管理员ID */
    @Excel(name = "管理员ID")
    private Long managerId;

    /** 管理员姓名 */
    @Excel(name = "管理员姓名")
    private String managerName;

    /** 打印机类型 1-热敏 2-喷墨 3-激光 4-照片 */
    @Excel(name = "打印机类型", readConverterExp = "1=热敏,2=喷墨,3=激光,4=照片")
    private Integer printerType;

    /** 支持纸张类型 1-A4 2-A5 3-照片纸 4-收据纸 */
    @Excel(name = "纸张类型", readConverterExp = "1=A4,2=A5,3=照片纸,4=收据纸")
    private Integer paperType;

    /** 是否支持彩色 0-不支持 1-支持 */
    @Excel(name = "彩色支持", readConverterExp = "0=不支持,1=支持")
    private Integer colorSupport;

    /** 是否支持双面 0-不支持 1-支持 */
    @Excel(name = "双面支持", readConverterExp = "0=不支持,1=支持")
    private Integer duplexSupport;

    /** 墨粉/色带剩余量(%) */
    @Excel(name = "墨粉剩余量(%)")
    private Integer tonerLevel;

    /** 纸张剩余量(张) */
    @Excel(name = "纸张剩余量(张)")
    private Integer paperRemain;

    /** 最后心跳时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最后心跳时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date lastHeartbeat;

    /** 总打印次数 */
    @Excel(name = "总打印次数")
    private Integer totalPrints;

    /** 本月打印次数 */
    @Excel(name = "本月打印次数")
    private Integer monthPrints;

    /** 错误代码 */
    @Excel(name = "错误代码")
    private String errorCode;

    /** 错误信息 */
    @Excel(name = "错误信息")
    private String errorMessage;

    /** 单页价格 单位：元 */
    @Excel(name = "单页价格(元)")
    private Integer pricePerPage;

    /** 总销售额 单位：元 */
    @Excel(name = "总销售额(元)")
    private Long countPrice;

    /** 分账信息(商户号:百分比,...) */
    @Excel(name = "分账信息")
    private String subAccount;

    /** 是否预警 0-否 1-是 */
    @Excel(name = "是否预警", readConverterExp = "0=否,1=是")
    private Boolean isWarning;

    /** 预警阈值(剩余纸张/墨粉%) */
    @Excel(name = "预警阈值(%)")
    private Integer warningThreshold;

    /** 预警接收人 */
    @Excel(name = "预警接收人")
    private String warningRecipients;

    /** 删除标志 0-存在 1-删除 */
    private Integer delFlag;

    /** 设备标签数组 */
    @Excel(name = "设备标签")
    private List<String> tags;

    /** 设备图片数组(存储图片URL) */
    @Excel(name = "设备图片")
    private List<String> images;

    /** 距离（米）- 非数据库字段，用于计算与用户位置的距离 */
    @TableField(exist = false)
    private Double distance;
}