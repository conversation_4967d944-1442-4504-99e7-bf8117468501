<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.voucher.mapper.VoucherMapper">
    
    <resultMap type="Voucher" id="VoucherResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="deviceId"    column="device_id"    />
        <result property="cellPrice"    column="cell_price"    />
        <result property="actualPrice"    column="actual_price"    />
        <result property="title"    column="title"    />
        <result property="voucherDescribe"    column="voucher_describe"    />
        <result property="type"    column="type"    />
        <result property="status"    column="status"    />
        <result property="expirationTime"    column="expiration_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectVoucherVo">
        select id, user_id, device_id, cell_price, actual_price, title, voucher_describe, type, status, expiration_time, create_by, create_time, update_by, update_time from voucher
    </sql>

    <select id="selectVoucherList" parameterType="Voucher" resultMap="VoucherResult">
        <include refid="selectVoucherVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="deviceId != null  and deviceId != ''"> and device_id = #{deviceId}</if>
            <if test="cellPrice != null "> and cell_price = #{cellPrice}</if>
            <if test="actualPrice != null "> and actual_price = #{actualPrice}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectVoucherById" parameterType="Integer" resultMap="VoucherResult">
        <include refid="selectVoucherVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertVoucher" parameterType="Voucher" useGeneratedKeys="true" keyProperty="id">
        insert into voucher
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="deviceId != null">device_id,</if>
            <if test="cellPrice != null">cell_price,</if>
            <if test="actualPrice != null">actual_price,</if>
            <if test="title != null">title,</if>
            <if test="voucherDescribe != null">voucher_describe,</if>
            <if test="type != null">type,</if>
            <if test="status != null">status,</if>
            <if test="expirationTime != null">expiration_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="deviceId != null">#{deviceId},</if>
            <if test="cellPrice != null">#{cellPrice},</if>
            <if test="actualPrice != null">#{actualPrice},</if>
            <if test="title != null">#{title},</if>
            <if test="voucherDescribe != null">#{voucherDescribe},</if>
            <if test="type != null">#{type},</if>
            <if test="status != null">#{status},</if>
            <if test="expirationTime != null">#{expirationTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateVoucher" parameterType="Voucher">
        update voucher
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="cellPrice != null">cell_price = #{cellPrice},</if>
            <if test="actualPrice != null">actual_price = #{actualPrice},</if>
            <if test="title != null">title = #{title},</if>
            <if test="voucherDescribe != null">voucher_describe = #{voucherDescribe},</if>
            <if test="type != null">type = #{type},</if>
            <if test="status != null">status = #{status},</if>
            <if test="expirationTime != null">expiration_time = #{expirationTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteVoucherById" parameterType="Integer">
        delete from voucher where id = #{id}
    </delete>

    <delete id="deleteVoucherByIds" parameterType="String">
        delete from voucher where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>