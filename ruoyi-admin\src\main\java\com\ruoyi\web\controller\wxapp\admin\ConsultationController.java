package com.ruoyi.web.controller.wxapp.admin;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.yunchuang.wxapp.model.domain.Consultation;
import com.yunchuang.wxapp.service.admin.IConsultationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 咨询Controller
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
@RestController
@RequestMapping("/wxapp/consultation")
public class ConsultationController extends BaseController {
    @Autowired
    private IConsultationService consultationService;

    /**
     * 查询咨询列表
     */
    @PreAuthorize("@ss.hasPermi('wxapp:consultation:list')")
    @GetMapping("/list")
    public TableDataInfo list(Consultation consultation) {
        startPage();
        List<Consultation> list = consultationService.selectConsultationList(consultation);
        return getDataTable(list);
    }

    /**
     * 导出咨询列表
     */
    @PreAuthorize("@ss.hasPermi('wxapp:consultation:export')")
    @Log(title = "咨询", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Consultation consultation) {
        List<Consultation> list = consultationService.selectConsultationList(consultation);
        ExcelUtil<Consultation> util = new ExcelUtil<Consultation>(Consultation.class);
        util.exportExcel(response, list, "咨询数据");
    }

    /**
     * 获取咨询详细信息
     */
    @PreAuthorize("@ss.hasPermi('wxapp:consultation:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(consultationService.selectConsultationById(id));
    }

    /**
     * 新增咨询
     */
    @PreAuthorize("@ss.hasPermi('wxapp:consultation:add')")
    @Log(title = "咨询", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Consultation consultation) {
        return toAjax(consultationService.insertConsultation(consultation));
    }

    /**
     * 修改咨询
     */
    @PreAuthorize("@ss.hasPermi('wxapp:consultation:edit')")
    @Log(title = "咨询", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Consultation consultation) {
        return toAjax(consultationService.updateConsultation(consultation));
    }

    /**
     * 删除咨询
     */
    @PreAuthorize("@ss.hasPermi('wxapp:consultation:remove')")
    @Log(title = "咨询", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(consultationService.deleteConsultationByIds(ids));
    }
}
