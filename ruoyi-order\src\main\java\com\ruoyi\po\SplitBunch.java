package com.ruoyi.po;

import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;


import java.util.List;

/**
 * @Author: yjzhang
 * @CreateDate: 2019/8/7 10:13
 * @Description: SplitBunch
 */
@Data
public class SplitBunch {

    private String divCusCount;  //分账商户数

    private String feeMemberId; // 费率承担方

    private List<SplitInfo> acctInfos;

    public SplitBunch(String divCusCount, String feeMemberId, List<SplitInfo> acctInfos) {
        this.divCusCount = divCusCount;
        this.feeMemberId = feeMemberId;
        this.acctInfos = acctInfos;
    }

    public String genJsonBase64() throws JSONException {
        JSONObject obj = new JSONObject();
        obj.put("divCusCount", divCusCount);
        obj.put("feeMemberId", feeMemberId);
        obj.put("acctInfos", acctInfos);

        System.out.println(obj.toString());

//        return new BASE64Encoder().encode(obj.toString().getBytes());
        return null;
    }
}
