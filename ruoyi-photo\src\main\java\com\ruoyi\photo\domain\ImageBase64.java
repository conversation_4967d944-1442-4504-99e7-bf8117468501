package com.ruoyi.photo.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 管理拍照机设备对象 device_camera
 *
 * <AUTHOR>
 * @date 2023-07-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("image_base64")
public class ImageBase64 {
    private static final long serialVersionUID = 1L;

    /**
     * 设备id
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;


    private String base64;

    private String deviceId;


}
