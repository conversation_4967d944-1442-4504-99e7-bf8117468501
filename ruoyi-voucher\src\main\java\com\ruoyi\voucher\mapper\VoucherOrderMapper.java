package com.ruoyi.voucher.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.voucher.domain.Voucher;
import com.ruoyi.voucher.domain.VoucherOrder;

/**
 * 优惠券订单Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-04-16
 */
public interface VoucherOrderMapper extends BaseMapper<VoucherOrder>
{
    /**
     * 查询优惠券订单
     * 
     * @param id 优惠券订单主键
     * @return 优惠券订单
     */
    public VoucherOrder selectVoucherOrderById(String id);

    /**
     * 查询优惠券订单列表
     * 
     * @param voucherOrder 优惠券订单
     * @return 优惠券订单集合
     */
    public List<VoucherOrder> selectVoucherOrderList(VoucherOrder voucherOrder);

    /**
     * 新增优惠券订单
     * 
     * @param voucherOrder 优惠券订单
     * @return 结果
     */
    public int insertVoucherOrder(VoucherOrder voucherOrder);

    /**
     * 修改优惠券订单
     * 
     * @param voucherOrder 优惠券订单
     * @return 结果
     */
    public int updateVoucherOrder(VoucherOrder voucherOrder);

    /**
     * 删除优惠券订单
     * 
     * @param id 优惠券订单主键
     * @return 结果
     */
    public int deleteVoucherOrderById(String id);

    /**
     * 批量删除优惠券订单
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteVoucherOrderByIds(String[] ids);
}
