package com.ruoyi.web.controller.pay;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.dto.Result;
import com.ruoyi.dto.WSSPayOpenNativePayReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.util.DigestUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

/**
 * MQTT支付 - 控制器
 */
@Slf4j
@RestController
@RequestMapping("/MQTTPay")
public class MQTTPay {

    @Resource
    private RestTemplate restTemplate;

    /**
     * 获取支付状态
     */
    @GetMapping("/getPayStatus")
    public Result getOrderStatus(String orderNo) {
        log.info("获取支付状态");
        if (orderNo == null || orderNo.isEmpty()) {
            return Result.fail(500, "商户订单号不能为空", String.valueOf(System.currentTimeMillis()));
        }

        // 创建请求参数
        WSSPayOpenNativePayReq openNativePayReq = new WSSPayOpenNativePayReq();
        openNativePayReq.setIsv_no("9000381");
        openNativePayReq.setBank_code("900001");
        openNativePayReq.setBank_mer_no("********");
        openNativePayReq.setBank_shop_no("S2410220773588");
        openNativePayReq.setMer_order_no(orderNo);
        openNativePayReq.setNonce_str(String.valueOf(System.currentTimeMillis()));
        openNativePayReq.setSign_type("MD5");

        // 签名
        String sign = "bank_code=" + openNativePayReq.getBank_code() + "&bank_mer_no=" +
                openNativePayReq.getBank_mer_no() + "&bank_shop_no=" + openNativePayReq.getBank_shop_no() + "&isv_no=" +
                openNativePayReq.getIsv_no() + "&mer_order_no=" + openNativePayReq.getMer_order_no() + "&nonce_str=" +
                openNativePayReq.getNonce_str() + "&sign_type=" + openNativePayReq.getSign_type() + "&key=i9XoojXMW14ZZOEkQrz0KJMQbL0Q5EnLKQOhcDvD8BNfn/YH72g6NQ==";
        String md5Sign = DigestUtils.md5DigestAsHex(sign.getBytes()).toUpperCase();
        openNativePayReq.setSign(md5Sign);
        String openNativePayReqStr = JSON.toJSONString(openNativePayReq);

        // 设置请求头
        HttpHeaders requestHeaders = new HttpHeaders();
        requestHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> r = new HttpEntity<String>(openNativePayReqStr, requestHeaders);

        // 请求地址
        String url = "https://up.chinanums.com/openpayapi/v2.0/openQuery";
        String result = restTemplate.postForObject(url, r, String.class);

        // 将json字符串转为json对象
        JSONObject jsonObject = JSON.parseObject(result);
        // 如果返回的json对象中 return_code 为 "00000" 表示请求成功
        if (jsonObject != null && "00000".equals(jsonObject.getString("return_code"))) {
            // 获取返回的数据
            String data = jsonObject.getString("data");
            // 将data转为json对象
            JSONObject dataObject = JSON.parseObject(data);
            // 获取订单状态
            String orderStatus = dataObject.getString("order_state");
            log.info("orderStatus:{}", orderStatus);
            return Result.ok(200, "获取支付状态成功", String.valueOf(System.currentTimeMillis()), orderStatus);
        } else {
            System.out.println(jsonObject);
            return Result.fail(500, "获取支付状态失败", String.valueOf(System.currentTimeMillis()));
        }
    }
}
