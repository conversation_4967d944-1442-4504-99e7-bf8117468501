package com.ruoyi.message.service;

import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.message.util.ChuangLanSmsUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 创蓝云智 消息服务
 */
@Slf4j
@Service
public class ChuangLanSmsService {

    @Resource
    private RedisCache redisCache;

    @Resource
    private ChuangLanSmsUtils chuangLanSmsUtils;

    // 短信手机号保存到 Redis 中的前缀
    private final String SMS_PER = "sms:phone:";
    // 短信验证码过期时间 单位:秒 5分钟
    private final int SMS_EXPIRE_TIME = 5 * 60;
    // 短信验证码重复限制时间 单位:秒 1分钟
    private final int SMS_LIMIT_TIME = 60;

    /**
     * 发送短信验证码
     *
     * @param phoneNumber 手机号码
     * @param code        验证码
     * @return 是否发送成功
     */
    public boolean sendSmsVerifyCode(String phoneNumber, String code) {
        // 限流，限制1分钟内不能重复发送短信
        Map<String, Object> smsMap = redisCache.getCacheMap(SMS_PER + phoneNumber);
        if (smsMap != null && !smsMap.isEmpty()) {
            long currentTime = (long) smsMap.get("currentTime");
            long nowTime = System.currentTimeMillis();
            // 如果当前时间减去上次发送时间小于1分钟，则不允许发送
            if (nowTime - currentTime < SMS_LIMIT_TIME * 1000) {
                log.info("请勿频繁发送短信验证码");
                return false;
            }
        }
        String smsContent = "【云创智能】尊敬的用户，您正在申请手机号绑定，验证码为：" + code + "，5分钟内有效！";
        // 发送短信
        boolean success = chuangLanSmsUtils.sendSms(smsContent, "18871097959");

        if (success) {
            // 将发送时间存入 Redis
            Map<String, Object> newMap = new HashMap<>();
            newMap.put("code", code);
            newMap.put("currentTime", System.currentTimeMillis());
            // 发送短信成功，写入redis，设置生存时间为5分钟
            redisCache.setCacheObject(SMS_PER + phoneNumber, newMap, SMS_EXPIRE_TIME, TimeUnit.SECONDS);

            return true;
        } else {
            return false;
        }

    }

    /**
     * 校验短信验证码
     *
     * @param phoneNumber 手机号码
     * @param code        验证码
     * @return 是否校验成功
     */
    public boolean checkSmsVerifyCode(String phoneNumber, String code) {
        // 从 Redis 中获取验证码
        Map<String, Object> smsMap = redisCache.getCacheMap(SMS_PER + phoneNumber);
        if (smsMap == null || smsMap.isEmpty()) {
            log.info("验证码已过期或不存在");
            return false;
        }
        String redisCode = (String) smsMap.get("code");
        // 校验验证码
        if (!code.equals(redisCode)) {
            log.info("验证码错误");
            return false;
        }
        // 验证成功，删除 Redis 中的验证码
        redisCache.deleteObject(SMS_PER + phoneNumber);
        return true;
    }

    /**
     * 发送耗材余量预警消息 - 短信
     *
     * @param phoneList           手机号码列表
     * @param deviceName          设备名称
     * @param deviceId            设备ID
     * @param deviceAddress       设备地址
     * @param consumablesQuantity 耗材余量
     * @return 是否发送成功
     */
    public boolean sendConsumablesWarningSms(List<String> phoneList, String deviceName, String deviceId, String deviceAddress, Long consumablesQuantity) {
        String msg = "【云创智能】尊敬的用户，您的设备 " + deviceName + "（ID：" + deviceId + "，地址：" + deviceAddress + "）剩余库存仅剩" + consumablesQuantity + "，请及时补充。";
        String phone = String.join(",", phoneList); // 将手机号列表转换为逗号分隔的字符串
        // 发送短信
        boolean success = chuangLanSmsUtils.sendSms(msg, phone);
        if (success) {
            log.info("耗材余量预警短信发送成功，手机号：{}", phoneList);
            return true;
        } else {
            log.error("耗材余量预警短信发送失败，手机号：{}", phoneList);
            return false;
        }
    }
}
