package com.ruoyi.device.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.yulichang.base.MPJBaseMapper;
import com.ruoyi.device.domain.CameraSysImage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * camera_sys_imageMapper接口
 * 
 * <AUTHOR>
 * @date 2023-07-19
 */
@Mapper
public interface CameraSysImageMapper extends MPJBaseMapper<CameraSysImage>
{
    /**
     * 查询camera_sys_image
     * 
     * @param id camera_sys_image主键
     * @return camera_sys_image
     */
    public CameraSysImage selectCameraSysImageById(Long id);

    /**
     * 查询camera_sys_image列表
     * 
     * @param cameraSysImage camera_sys_image
     * @return camera_sys_image集合
     */
    public List<CameraSysImage> selectCameraSysImageList(CameraSysImage cameraSysImage);

    /**
     * 查询私人的camera_sys_image列表
     *
     * @param cameraSysImage camera_sys_image
     * @return camera_sys_image集合
     */
    public List<CameraSysImage> selectMyCameraSysImageList(CameraSysImage cameraSysImage);

    /**
     * 查询camera_sys_background列表
     *
     * @return camera_sys_image集合
     */
    public List<CameraSysImage> selectCameraSysBackgroundList();

    /**
     * 新增camera_sys_image
     * 
     * @param cameraSysImage camera_sys_image
     * @return 结果
     */
    public int insertCameraSysImage(CameraSysImage cameraSysImage);

    /**
     * 修改camera_sys_image
     * 
     * @param cameraSysImage camera_sys_image
     * @return 结果
     */
    public int updateCameraSysImage(CameraSysImage cameraSysImage);

    /**
     * 删除camera_sys_image
     * 
     * @param id camera_sys_image主键
     * @return 结果
     */
    public int deleteCameraSysImageById(Long id);

    /**
     * 批量删除camera_sys_image
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCameraSysImageByIds(Long[] ids);

    /**
     * 根据用户查询该用户的模版
     * @param createBy 用户
     * @return
     */
    List<CameraSysImage> selectCameraSysImageByUser(@Param("createBy") String createBy, @Param("type") String type, @Param("name") String name);


    List<CameraSysImage> selectCameraSysImageBydeviceName(@Param("userName") String userName);

    List<CameraSysImage> selectCameraSysImageListAll(@Param("cameraSysImage") CameraSysImage cameraSysImage, @Param("type") String type, @Param("name") String name);
}
