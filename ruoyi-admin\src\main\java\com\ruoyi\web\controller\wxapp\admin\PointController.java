package com.ruoyi.web.controller.wxapp.admin;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.yunchuang.wxapp.model.domain.Point;
import com.yunchuang.wxapp.service.admin.IPointService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 点位Controller
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
@RestController
@RequestMapping("/wxapp/point")
public class PointController extends BaseController {

    @Autowired
    private IPointService pointService;

    /**
     * 查询点位列表
     */
    @PreAuthorize("@ss.hasPermi('wxapp:point:list')")
    @GetMapping("/list")
    public TableDataInfo list(Point point) {
        startPage();
        List<Point> list = pointService.selectPointList(point);
        return getDataTable(list);
    }

    /**
     * 导出点位列表
     */
    @PreAuthorize("@ss.hasPermi('wxapp:point:export')")
    @Log(title = "点位", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Point point) {
        List<Point> list = pointService.selectPointList(point);
        ExcelUtil<Point> util = new ExcelUtil<Point>(Point.class);
        util.exportExcel(response, list, "点位数据");
    }

    /**
     * 获取点位详细信息
     */
    @PreAuthorize("@ss.hasPermi('wxapp:point:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(pointService.selectPointById(id));
    }

    /**
     * 新增点位
     */
    @PreAuthorize("@ss.hasPermi('wxapp:point:add')")
    @Log(title = "点位", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Point point) {
        return toAjax(pointService.insertPoint(point));
    }

    /**
     * 修改点位
     */
    @PreAuthorize("@ss.hasPermi('wxapp:point:edit')")
    @Log(title = "点位", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Point point) {
        return toAjax(pointService.updatePoint(point));
    }

    /**
     * 删除点位
     */
    @PreAuthorize("@ss.hasPermi('wxapp:point:remove')")
    @Log(title = "点位", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(pointService.deletePointByIds(ids));
    }
}
