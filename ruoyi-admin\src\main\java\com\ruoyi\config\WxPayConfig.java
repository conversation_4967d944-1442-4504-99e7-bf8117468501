package com.ruoyi.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 微信支付配置类
 * 
 * <AUTHOR>
 * @date 2025-01-30
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "wxpay")
public class WxPayConfig {

    /**
     * 微信支付AppID
     */
    private String appId;

    /**
     * 微信支付商户号
     */
    private String mchId = "1631162326";

    /**
     * 微信支付API密钥（API v2密钥）
     */
    private String apiKey;

    /**
     * 微信支付证书序列号
     */
    private String certSerialNo = "671F99B4529CDF45E97F5CB341E02838E1DEE092";

    /**
     * 微信支付证书文件路径（p12格式）
     */
    private String certPath;

    /**
     * 微信支付证书密码（通常是商户号）
     */
    private String certPassword;

    /**
     * 微信支付回调地址
     */
    private String notifyUrl;

    /**
     * 微信支付API基础URL（API v2）
     */
    private String apiBaseUrl = "https://api.mch.weixin.qq.com";

    /**
     * 是否为沙箱环境
     */
    private boolean sandbox = false;

    /**
     * 连接超时时间（毫秒）
     */
    private int connectTimeout = 8000;

    /**
     * 读取超时时间（毫秒）
     */
    private int readTimeout = 10000;

    /**
     * 获取统一下单URL（API v2）
     */
    public String getUnifiedOrderUrl() {
        return apiBaseUrl + "/pay/unifiedorder";
    }

    /**
     * 获取订单查询URL（API v2）
     */
    public String getOrderQueryUrl() {
        return apiBaseUrl + "/pay/orderquery";
    }

    /**
     * 获取退款URL（API v2）
     */
    public String getRefundUrl() {
        return apiBaseUrl + "/secapi/pay/refund";
    }

    /**
     * 获取退款查询URL（API v2）
     */
    public String getRefundQueryUrl() {
        return apiBaseUrl + "/pay/refundquery";
    }
}
