package com.ruoyi.beauty.service;

import java.util.List;
import com.ruoyi.beauty.domain.Beauty;
import com.ruoyi.common.core.domain.entity.SysUser;

/**
 * 美颜Service接口
 * 
 * <AUTHOR>
 * @date 2023-10-15
 */
public interface IBeautyService 
{
    /**
     * 查询美颜
     * 
     * @param useeId 美颜主键
     * @return 美颜
     */
    public Beauty selectBeautyByUseeId(Long useeId);

    /**
     * 查询美颜列表
     * 
     * @param beauty 美颜
     * @return 美颜集合
     */
    public List<Beauty> selectBeautyList(Beauty beauty);

    /**
     * 新增美颜
     * 
     * @param beauty 美颜
     * @return 结果
     */
    public int insertBeauty(Beauty beauty);

    /**
     * 修改美颜
     * 
     * @param beauty 美颜
     * @return 结果
     */
    public int updateBeauty(Beauty beauty);

    /**
     * 批量删除美颜
     * 
     * @param useeIds 需要删除的美颜主键集合
     * @return 结果
     */
    public int deleteBeautyByUseeIds(Long[] useeIds);

    /**
     * 删除美颜信息
     * 
     * @param useeId 美颜主键
     * @return 结果
     */
    public int deleteBeautyByUseeId(Long useeId);


}
