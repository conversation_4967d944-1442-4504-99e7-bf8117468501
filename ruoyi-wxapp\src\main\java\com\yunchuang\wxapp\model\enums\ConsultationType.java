package com.yunchuang.wxapp.model.enums;

import lombok.Getter;

/**
 * 咨询类型
 */
@Getter
public enum ConsultationType {

    // 拍照咨询
    PHOTOGRAPHY(0, "拍照咨询"),

    // 优惠福利
    PREFERENTIAL_BENEFITS(1, "优惠福利"),

    // 合作咨询
    COOPERATION(2, "合作咨询"),

    // 售后问题
    AFTER_SALES(3, "售后问题");

    private final Integer value;
    private final String name;

    ConsultationType(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public static ConsultationType getConsultationType(Integer value) {
        for (ConsultationType consultationType : ConsultationType.values()) {
            if (consultationType.getValue().equals(value)) {
                return consultationType;
            }
        }
        return null;
    }
}
