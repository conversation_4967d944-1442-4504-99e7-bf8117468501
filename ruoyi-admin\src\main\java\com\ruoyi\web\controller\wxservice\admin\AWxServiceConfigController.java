package com.ruoyi.web.controller.wxservice.admin;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.utils.MyResultUtil;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.wxservice.model.domain.WxServiceConfig;
import com.ruoyi.wxservice.service.IWxServiceConfigService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 微信服务号配置 Controller
 */
@RestController
@RequestMapping("/wxservice/config")
public class AWxServiceConfigController {

    @Resource
    private IWxServiceConfigService wxServiceConfigService;

    @Resource
    private ISysDeptService sysDeptService;

    /**
     * 查询微信服务号配置
     */
    @GetMapping("/{configId}")
    public Map<String, Object> getOne(@PathVariable Long configId) {
        WxServiceConfig wxServiceConfig = wxServiceConfigService.getById(configId);
        if (wxServiceConfig == null) {
            return MyResultUtil.error();
        }
        return MyResultUtil.success(wxServiceConfig);
    }

    /**
     * 更新或保存微信服务号配置
     */
    @PostMapping("/saveOrUpdate")
    public Map<String, Object> saveOrUpdate(@RequestParam Long deptId, @RequestBody WxServiceConfig wxServiceConfig) {
        if (wxServiceConfig.getConfigId() == null) { // 新建
            wxServiceConfigService.save(wxServiceConfig);
            SysDept sysDept = new SysDept();
            sysDept.setDeptId(deptId);
            sysDept.setWxserviceConfigId(wxServiceConfig.getConfigId());
            sysDeptService.updateById(sysDept);
        } else { // 更新
            wxServiceConfigService.updateById(wxServiceConfig);
            SysDept sysDept = new SysDept();
            sysDept.setDeptId(deptId);
            sysDept.setWxserviceConfigId(wxServiceConfig.getConfigId());
            sysDeptService.updateById(sysDept);
        }
        return MyResultUtil.success();
    }

    /**
     * 删除微信服务号配置
     */
    @DeleteMapping("/delete")
    public Map<String, Object> delete(@RequestParam Long configId, @RequestParam Long deptId) {
        boolean isSuccess = wxServiceConfigService.removeById(configId);
        return isSuccess ? MyResultUtil.success() : MyResultUtil.error();
    }
}
