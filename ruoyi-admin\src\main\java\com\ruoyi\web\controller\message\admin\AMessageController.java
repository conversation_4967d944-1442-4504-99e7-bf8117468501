package com.ruoyi.web.controller.message.admin;

import com.ruoyi.common.utils.MyResultUtil;
import com.ruoyi.common.utils.RandomUtil;
import com.ruoyi.dto.message.CheckVerifyCodeReqDTO;
import com.ruoyi.dto.message.SendVerifyCodeReqDTO;
import com.ruoyi.message.service.AliSmsService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 消息Controller
 */
@RestController
@RequestMapping("/message")
public class AMessageController {

    @Resource
    private AliSmsService aliSmsService;

    /**
     * 发送短信验证码
     */
    @PostMapping("/sendSmsVerifyCode")
    public Map<String, Object> sendSmsVerifyCode(@RequestBody SendVerifyCodeReqDTO sendReq) {
        String code = RandomUtil.getFourBitRandom(); // 生成验证码的随机值
        boolean isSuccess = aliSmsService.sendSmsVerifyCode(sendReq.getPhoneNumber(), code);
        return isSuccess ? MyResultUtil.success() : MyResultUtil.error();
    }

    /**
     * 校验短信验证码
     */
    @PostMapping("/checkSmsVerifyCode")
    public Map<String, Object> checkSmsVerifyCode(@RequestBody CheckVerifyCodeReqDTO sendReq) {
        boolean isSuccess = aliSmsService.checkSmsVerifyCode(sendReq.getPhoneNumber(), sendReq.getCode());
        return isSuccess ? MyResultUtil.success() : MyResultUtil.error();
    }

}
