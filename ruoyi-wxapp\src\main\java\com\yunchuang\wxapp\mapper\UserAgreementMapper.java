package com.yunchuang.wxapp.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yunchuang.wxapp.model.domain.UserAgreement;

import java.util.List;

/**
 * 用户协议Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
public interface UserAgreementMapper extends BaseMapper<UserAgreement> {
    /**
     * 查询用户协议
     *
     * @param id 用户协议主键
     * @return 用户协议
     */
    public UserAgreement selectUserAgreementById(Long id);

    /**
     * 查询用户协议列表
     *
     * @param userAgreement 用户协议
     * @return 用户协议集合
     */
    public List<UserAgreement> selectUserAgreementList(UserAgreement userAgreement);

    /**
     * 新增用户协议
     *
     * @param userAgreement 用户协议
     * @return 结果
     */
    public int insertUserAgreement(UserAgreement userAgreement);

    /**
     * 修改用户协议
     *
     * @param userAgreement 用户协议
     * @return 结果
     */
    public int updateUserAgreement(UserAgreement userAgreement);

    /**
     * 删除用户协议
     *
     * @param id 用户协议主键
     * @return 结果
     */
    public int deleteUserAgreementById(Long id);

    /**
     * 批量删除用户协议
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteUserAgreementByIds(Long[] ids);
}
