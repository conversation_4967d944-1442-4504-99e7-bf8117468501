package com.ruoyi.fee.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.fee.mapper.FeeMapper;
import com.ruoyi.fee.domain.Fee;
import com.ruoyi.fee.service.IFeeService;

/**
 * 费用Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */
@Service
public class FeeServiceImpl extends ServiceImpl<FeeMapper,Fee> implements IFeeService
{
    @Autowired
    private FeeMapper feeMapper;

    /**
     * 查询费用
     * 
     * @param id 费用主键
     * @return 费用
     */
    @Override
    public Fee selectFeeById(Long id)
    {
        return feeMapper.selectFeeById(id);
    }

    /**
     * 查询费用列表
     * 
     * @param fee 费用
     * @return 费用
     */
    @Override
    public List<Fee> selectFeeList(Fee fee)
    {
        return feeMapper.selectFeeList(fee);
    }

    /**
     * 新增费用
     * 
     * @param fee 费用
     * @return 结果
     */
    @Override
    public int insertFee(Fee fee)
    {
        fee.setCreateTime(DateUtils.getNowDate());
        return feeMapper.insertFee(fee);
    }

    /**
     * 修改费用
     * 
     * @param fee 费用
     * @return 结果
     */
    @Override
    public int updateFee(Fee fee)
    {
        fee.setUpdateTime(DateUtils.getNowDate());
        return feeMapper.updateFee(fee);
    }

    /**
     * 批量删除费用
     * 
     * @param ids 需要删除的费用主键
     * @return 结果
     */
    @Override
    public int deleteFeeByIds(Long[] ids)
    {
        return feeMapper.deleteFeeByIds(ids);
    }

    /**
     * 删除费用信息
     * 
     * @param id 费用主键
     * @return 结果
     */
    @Override
    public int deleteFeeById(Long id)
    {
        return feeMapper.deleteFeeById(id);
    }
}
