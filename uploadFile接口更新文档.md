# /user/uploadFile 接口更新文档

## 更新内容

在 `/user/uploadFile` 接口中新增了两个参数：
1. `isPhoto` - 是否为照片
2. `sizeSpec` - 尺寸大小

## 接口地址
```
POST /order/printer/user/uploadFile
```

## 请求参数

### 表单参数 (multipart/form-data)

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| file | File | 是 | 上传的文件 | document.pdf |
| orderId | String | 是 | 订单ID | ORDER_123456 |
| deviceId | String | 是 | 设备ID | PRINTER_001 |
| deviceName | String | 否 | 设备名称 | 图书馆打印机 |
| openid | String | 否 | 微信用户openid | wx_abc123 |
| phone | String | 否 | 用户手机号 | 13800138000 |
| copies | Integer | 否 | 打印份数 | 1 |
| colorMode | Integer | 否 | 颜色模式：0-黑白，1-彩色 | 0 |
| duplexMode | Integer | 否 | 双面模式：0-单面，1-双面 | 0 |
| paperType | Integer | 否 | 纸张类型：1-A4，2-A5，3-照片纸 | 1 |
| pageRange | String | 否 | 页码范围 | 1-5 |
| isLastUpload | Boolean | 是 | 是否最后一次上传 | true |
| **isPhoto** | **Integer** | **否** | **是否为照片：0-否，1-是** | **0** |
| **sizeSpec** | **String** | **否** | **尺寸大小** | **A4** |

### 新增参数详细说明

#### isPhoto（是否为照片）
- **类型**: Integer
- **必填**: 否
- **默认值**: 0
- **取值范围**: 
  - `0`: 不是照片（文档类）
  - `1`: 是照片
- **用途**: 用于区分上传的文件类型，可能影响打印价格计算和处理方式

#### sizeSpec（尺寸大小）
- **类型**: String
- **必填**: 否
- **默认值**: null
- **取值示例**: 
  - 文档类: `A4`, `A5`, `A3`
  - 照片类: `4寸`, `6寸`, `7寸`, `8寸`, `10寸`
- **用途**: 指定打印的尺寸规格，用于价格计算和打印设置

## 请求示例

### 文档打印示例
```bash
curl -X POST "http://localhost:8081/order/printer/user/uploadFile" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@document.pdf" \
  -F "orderId=ORDER_123456" \
  -F "deviceId=PRINTER_001" \
  -F "copies=2" \
  -F "colorMode=0" \
  -F "duplexMode=1" \
  -F "paperType=1" \
  -F "pageRange=1-10" \
  -F "isLastUpload=true" \
  -F "isPhoto=0" \
  -F "sizeSpec=A4"
```

### 照片打印示例
```bash
curl -X POST "http://localhost:8081/order/printer/user/uploadFile" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@photo.jpg" \
  -F "orderId=ORDER_789012" \
  -F "deviceId=PRINTER_002" \
  -F "copies=1" \
  -F "colorMode=1" \
  -F "paperType=3" \
  -F "isLastUpload=true" \
  -F "isPhoto=1" \
  -F "sizeSpec=6寸"
```

## 响应格式

响应格式保持不变，但返回的数据中会包含新的字段信息：

```json
{
  "code": 200,
  "msg": "上传文件成功",
  "data": {
    "taskId": "TASK_123456",
    "taskPrice": 1.5,
    "pageCount": 5,
    "fileName": "document.pdf",
    "fileUrl": "https://oss.example.com/files/xxx.pdf",
    "totalAmount": 1.5,
    "isOrderComplete": true,
    "isPhoto": 0,
    "sizeSpec": "A4"
  }
}
```

## 数据库变更

### 新增字段
在 `order_printer_tasks` 表中新增了两个字段：

```sql
-- 是否为照片字段
ALTER TABLE order_printer_tasks 
ADD COLUMN is_photo INT(1) DEFAULT 0 COMMENT '是否为照片 0-否 1-是';

-- 尺寸大小字段
ALTER TABLE order_printer_tasks 
ADD COLUMN size_spec VARCHAR(50) DEFAULT NULL COMMENT '尺寸大小（如：A4、4寸、6寸等）';
```

## 业务逻辑影响

### 1. 价格计算
- 新增的 `isPhoto` 和 `sizeSpec` 参数可能会影响打印价格的计算
- 照片打印和文档打印可能有不同的定价策略

### 2. 打印处理
- 根据 `isPhoto` 参数，打印机可能会采用不同的打印模式
- `sizeSpec` 参数会影响纸张选择和打印设置

### 3. 统计分析
- 可以根据这两个字段进行业务数据分析
- 区分照片打印和文档打印的订单量和收入

## 兼容性说明

### 向后兼容
- 新增的两个参数都是可选的，不会影响现有的调用方式
- 如果不传递这两个参数，系统会使用默认值：
  - `isPhoto`: 0（不是照片）
  - `sizeSpec`: null（无特定尺寸）

### 前端适配建议
1. **文件类型检测**: 前端可以根据文件扩展名自动设置 `isPhoto` 参数
2. **尺寸选择**: 为用户提供尺寸选择界面，特别是照片打印时
3. **价格预览**: 根据选择的参数实时计算和显示价格

## 测试建议

### 测试用例
1. **文档上传**: 测试 `isPhoto=0` 的各种文档类型
2. **照片上传**: 测试 `isPhoto=1` 的各种照片格式
3. **尺寸测试**: 测试不同 `sizeSpec` 值的处理
4. **兼容性测试**: 测试不传递新参数的情况
5. **边界测试**: 测试无效的参数值

### 验证点
- 参数正确传递到数据库
- 价格计算是否考虑新参数
- 日志记录是否包含新参数
- 返回数据是否包含新字段

## 注意事项

1. **数据库迁移**: 部署前需要执行数据库迁移脚本
2. **缓存清理**: 如果有相关缓存，需要清理
3. **监控**: 关注新参数对系统性能的影响
4. **文档更新**: 更新相关的API文档和用户手册
