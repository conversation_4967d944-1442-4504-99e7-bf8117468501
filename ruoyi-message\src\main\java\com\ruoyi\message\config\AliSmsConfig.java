package com.ruoyi.message.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 短信配置类 - 阿里云
 * <p>
 * 该类用于读取阿里云短信相关的配置属性
 * </p>
 * <p>
 * 注意：请确保在 application.yml 或 application.properties 中配置了以下属性：
 * <ul>
 *     <li>aliyun.sms.access-key-id</li>
 *     <li>aliyun.sms.access-key-secret</li>
 * </ul>
 * </p>
 */
@Data
@Component
@ConfigurationProperties(prefix = "aliyun.sms")
public class AliSmsConfig {

    /**
     * Access Key ID
     */
    private String accessKeyId;

    /**
     * Access Key Secret
     */
    private String accessKeySecret;

//    /**
//     * 短信签名
//     */
//    private String signName;
//
//    /**
//     * 短信模板code
//     */
//    private String templateCode;
}
