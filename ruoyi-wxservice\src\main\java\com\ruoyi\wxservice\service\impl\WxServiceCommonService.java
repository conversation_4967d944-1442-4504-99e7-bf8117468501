package com.ruoyi.wxservice.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.JaxbUtil;
import com.ruoyi.device.domain.DeviceCamera;
import com.ruoyi.device.domain.WarningRecipients;
import com.ruoyi.device.mapper.DeviceCameraMapper;
import com.ruoyi.order.domain.OrderCamera;
import com.ruoyi.order.mapper.OrderCameraMapper;
import com.ruoyi.system.mapper.SysDeptMapper;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.wxservice.exception.WxserviceBusinessException;
import com.ruoyi.wxservice.mapper.WxServiceConfigMapper;
import com.ruoyi.wxservice.model.domain.WxServiceConfig;
import com.ruoyi.wxservice.model.dto.Button;
import com.ruoyi.wxservice.model.dto.Menu;
import com.ruoyi.wxservice.model.dto.WxEventPush;
import com.ruoyi.wxservice.model.dto.template.DeviceReplTplDTO;
import com.ruoyi.wxservice.model.enums.TemplateMessageEnum;
import com.ruoyi.wxservice.model.enums.exception.WxserviceBusinessExceptionCode;
import com.ruoyi.wxservice.util.CheckWXTokenUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 微信服务号业务处理
 */
@Slf4j
@Service
public class WxServiceCommonService {

    @Resource
    private WxServiceOpenApiService wxServiceOpenApiService;

    @Resource
    private DeviceCameraMapper deviceCameraMapper;

    @Resource
    private OrderCameraMapper orderCameraMapper;

    @Resource
    private SysDeptMapper sysDeptMapper;

    @Resource
    private SysUserMapper sysUserMapper;

    @Resource
    private WxServiceConfigMapper wxServiceConfigMapper;

    /**
     * 根据用户ID查询服务号配置信息
     */
    public WxServiceConfig selectWxServiceConfigIdByUserId(Long userId) {
        // 查询用户信息
        SysUser currentUser = sysUserMapper.selectUserById(userId);
        if (currentUser == null) // 用户不存在
            return null;
        // 查询服务号配置ID
        Long configId = sysDeptMapper.selectWxserviceConfigIdByDeptId(currentUser.getDeptId());
        if (configId == null) // 服务号配置不存在
            return null;
        // 查询服务号配置信息
        return wxServiceConfigMapper.selectById(configId);

    }

    /**
     * 验证微信服务器签名
     *
     * @param signature 微信加密签名
     * @param timestamp 时间戳
     * @param nonce     随机数
     * @param echostr   随机字符串
     * @return String
     */
    public String checkWxServerSign(String signature, String timestamp, String nonce, String echostr) {
        log.info("开始校验此次消息是否来自微信服务器，param->signature:{},\ntimestamp:{},\nnonce:{},\nechostr:{}",
                signature, timestamp, nonce, echostr);
        if (CheckWXTokenUtil.checkSignature(signature, timestamp, nonce)) {
            return echostr;
        }
        return "";
    }

    /**
     * 发送耗材余量预警消息
     *
     * @param deviceId            设备ID
     * @param consumablesQuantity 耗材余量
     * @return 是否发送成功
     */
    public boolean sendWarningMessage(String deviceId, Long consumablesQuantity) {
        // 查询当前设备信息
        LambdaQueryWrapper<DeviceCamera> cameraQW = new LambdaQueryWrapper<>();
        cameraQW.select(DeviceCamera::getDeviceName, DeviceCamera::getUserId, DeviceCamera::getIsWarning, DeviceCamera::getWarningRecipients, DeviceCamera::getWarningThreshold);
        cameraQW.eq(DeviceCamera::getDeviceId, deviceId);
        DeviceCamera deviceCamera = deviceCameraMapper.selectOne(cameraQW);
        if (deviceCamera == null) // 设备不存在
            return false;
        if (!deviceCamera.getIsWarning() || consumablesQuantity > deviceCamera.getWarningThreshold()) // 不需要发送预警消息
            return true;
        WarningRecipients warningRecipients = deviceCamera.getWarningRecipients();
        if (warningRecipients == null) // 未设置预警接收人
            return true;

        // 查询服务号配置信息
        long userId = deviceCamera.getUserId();
        WxServiceConfig wxServiceConfig = selectWxServiceConfigIdByUserId(userId);
        if (wxServiceConfig == null) // 服务号配置不存在
            return false;

        // 构建消息模板数据
        String templateId = TemplateMessageEnum.DEVICE_REPLENISHMENT.getId();
        DeviceReplTplDTO deviceReplTplDTO = DeviceReplTplDTO.build(deviceCamera.getDeviceName(), deviceId, deviceCamera.getDetailAddress() == null ? "请完善地址信息" : deviceCamera.getDetailAddress(), consumablesQuantity);
        // 对所有接收人发送消息
        if (warningRecipients.getWxOpenidList() != null && !warningRecipients.getWxOpenidList().isEmpty()) {
            warningRecipients.getWxOpenidList().forEach(wxOpenid -> {
                // 发送消息
                wxServiceOpenApiService.sendTemplateMessage(wxServiceConfig, wxOpenid, templateId, "", deviceReplTplDTO);
            });
        }
        return true;
    }

    /**
     * 生成预警绑定二维码
     *
     * @param deviceId 设备ID
     * @return 二维码图片地址 (base64)
     */
    public String generateWarningBindQrCode(String deviceId) {
        // 查询当前设备信息
        DeviceCamera deviceCamera = deviceCameraMapper.selectById(deviceId);
        if (deviceCamera == null) // 设备不存在
            throw new WxserviceBusinessException(WxserviceBusinessExceptionCode.EC_60202);
        // 查询服务号配置信息
        long userId = deviceCamera.getUserId();
        WxServiceConfig wxServiceConfig = selectWxServiceConfigIdByUserId(userId);
        if (wxServiceConfig == null) // 服务号配置不存在
            throw new WxserviceBusinessException(WxserviceBusinessExceptionCode.EC_60203);

        // 生成绑定url 过期时间为30分钟
        //        // 转为base64
//        return QrCodeUtil.generateQrCodeToBase64(qrCodeUrl, 250, 250, "png");
        return wxServiceOpenApiService.createTempQrCode(wxServiceConfig, "bind:" + deviceId, 30 * 60);
    }

    /**
     * 设备解绑预警接收人 - 微信
     *
     * @param wxOpenid 微信openid
     * @param deviceId 设备id
     * @return 是否解绑成功
     */
    public boolean unbindWarningRecipient(String wxOpenid, String deviceId) {
        return handleUnbindEvent(deviceId, wxOpenid);
    }

    /**
     * 创建自定义菜单
     * ●取照片：发送订单号获取高清电子照和底片●联系客服：联系在线客服，解决拍摄小问题●动态福利：获取福利、活动、合作咨询
     */
    public boolean createCustomMenu(Long wxServiceConfigId) {
        // 查询服务号配置信息
        WxServiceConfig wxServiceConfig = wxServiceConfigMapper.selectById(wxServiceConfigId);
        if (wxServiceConfig == null) {
            throw new RuntimeException("服务号配置不存在");
        }

        Menu menu = new Menu();
        List<Button> buttonList = new ArrayList<>();
        // 取照片
        Button takePhotoBtn = new Button();
        takePhotoBtn.setType("click");
        takePhotoBtn.setName("取照片");
        takePhotoBtn.setKey("takePhoto:true");
        buttonList.add(takePhotoBtn);
        // 联系客服
        Button contactServiceBtn = new Button();
        contactServiceBtn.setType("click");
        contactServiceBtn.setName("联系客服");
        contactServiceBtn.setKey("contactService:true");
        buttonList.add(contactServiceBtn);
        // 动态福利
        Button dynamicWelfareBtn = new Button();
        dynamicWelfareBtn.setType("click");
        dynamicWelfareBtn.setName("动态福利");
        dynamicWelfareBtn.setKey("dynamicWelfare:true");
        buttonList.add(dynamicWelfareBtn);
        menu.setButton(buttonList.toArray(new Button[0]));
        return wxServiceOpenApiService.createMenu(wxServiceConfig, menu);
    }

    /**
     * 处理微信推送消息
     *
     * @param param 微信消息参数
     * @return 返回消息
     */
    public String handleWxPushMessage(String param) {
        log.info("接收到微信推送消息：{}", param);
        // 解析消息
        WxEventPush wxEventPush = JaxbUtil.converyToJavaBean(param, WxEventPush.class);
        if (wxEventPush == null) {
            log.error("解析微信推送消息失败");
            return "";
        }

        // 判断消息类型
        String msgType = wxEventPush.getMsgType();
        switch (msgType) {
            case "event": // 事件消息
                return handleEventMessage(wxEventPush);
            case "text": // 文本消息
                return handleTextMessage(wxEventPush);
            default:
                return "";
        }
    }

    /**
     * 处理微信推送消息 - 事件
     *
     * @param wxEventPush 微信事件推送
     */
    public String handleEventMessage(WxEventPush wxEventPush) {
        String senderId = wxEventPush.getFromUserName(); // 发送者openid
        String serviceId = wxEventPush.getToUserName(); // 服务号ID
        String eventType = wxEventPush.getEvent();
        String eventKey = wxEventPush.getEventKey();
        if (eventKey == null || eventKey.isEmpty()) { // 事件key为空
            return handleNoParamEvent(senderId, serviceId, eventType, wxEventPush);
        } else { // 事件key不为空
            return handleCustomEvent(senderId, serviceId, eventType, eventKey, wxEventPush);
        }
    }

    /**
     * 处理微信事件 - 无参事件
     *
     * @param senderId    发送者openid
     * @param serviceId   服务号ID
     * @param eventType   事件类型
     * @param wxEventPush 微信事件推送
     */
    public String handleNoParamEvent(String senderId, String serviceId, String eventType, WxEventPush wxEventPush) {
        switch (eventType) {
            case "subscribe": // 关注事件
                String content = "欢迎关注公众号！";
                // 根据服务号ID返回不同的欢迎语
                if (serviceId.equals("gh_b9aa0b1d3455")) { // 一拍集盒专属
                    content = "Hi~欢迎关注ONE.SHOT一拍集盒\n" +
                            "留下独一无二的记忆\n" +
                            "\n" +
                            "领取【周周拍】福利，最高可享5折优惠更多精彩\n" +
                            "\n" +
                            "点击下方菜单栏：\n" +
                            "●取照片：发送订单号获取高清电子照和底片\n" +
                            "●联系客服：联系在线客服，解决拍摄小问题\n" +
                            "●动态福利：获取福利、活动、合作咨询\n" +
                            "加入我们，铸造未来娱乐生活新视界\n" +
                            "\n" +
                            "ONE SHOT, One Memory!";
                }
                return WxEventPush.buildTextMessage(senderId, serviceId, content).getXmlString();
            case "unsubscribe": // 取消关注事件
                return "";
            case "SCAN": // 已关注扫码事件
                break;
            default:
                return "";
        }
        return "";
    }

    /**
     * 处理微信事件 - 自定义事件
     *
     * @param senderId    发送者openid
     * @param serviceId   服务号ID
     * @param eventType   事件类型
     * @param eventKey    事件key
     * @param wxEventPush 微信事件推送
     */
    public String handleCustomEvent(String senderId, String serviceId, String eventType, String eventKey, WxEventPush wxEventPush) {
        String fromUserName = wxEventPush.getFromUserName(); // 发送者openid
        // 处理eventKey 如果前面带qrscene_ 则去掉
        if (eventKey.startsWith("qrscene_")) {
            eventKey = eventKey.substring(8);
        }
        String customEventType = eventKey.split(":")[0];  // 事件类型
        String eventContent = eventKey.split(":")[1];  // 事件内容

        switch (eventType) {
            case "subscribe": // 关注事件
                switch (customEventType) {
                    case "bind": // 绑定事件
                        return handleBindEvent(senderId, serviceId, eventContent);
                    default:
                        return WxEventPush.buildTextMessage(senderId, serviceId, "欢迎关注").getXmlString();
                }
            case "unsubscribe": // 取消关注事件
                return "";
            case "SCAN": // 已关注扫码事件
                switch (customEventType) {
                    case "bind": // 绑定事件
                        return handleBindEvent(senderId, serviceId, eventContent);
                    case "unbind": // 解绑事件
                        return "";
                    default:
                        return "";
                }
            case "CLICK": // 点击菜单事件
                switch (customEventType) {
                    case "takePhoto": // 取照片 oid-123456789 获取高清电子照和底片
                        return WxEventPush.buildTextMessage(senderId, serviceId, "发送【oid-订单号】获取高清电子照和底片。\n注：订单号为微信/支付宝订单详情内的交易单号\n例如：oid-1234567890").getXmlString();
                    case "contactService": // 联系客服
                        return WxEventPush.buildTextMessage(senderId, serviceId, "联系在线客服，解决拍摄小问题").getXmlString();
                    case "dynamicWelfare": // 动态福利
                        return WxEventPush.buildTextMessage(senderId, serviceId, "获取福利、活动、合作咨询").getXmlString();
                    default:
                        return "";
                }
            default:
                return "";
        }
    }

    /**
     * 处理绑定事件
     *
     * @param senderId  发送者openid
     * @param serviceId 服务号ID
     * @param deviceId  设备ID
     */
    private String handleBindEvent(String senderId, String serviceId, String deviceId) {
        // 查询设备信息
        DeviceCamera deviceCamera = deviceCameraMapper.selectById(deviceId);
        if (deviceCamera == null) {
            log.error("设备不存在，deviceId:{}", deviceId);
            return WxEventPush.buildTextMessage(senderId, serviceId, "设备不存在\ndeviceId: " + deviceId).getXmlString();
        }
        // 更新设备绑定状态
        WarningRecipients warningRecipients = deviceCamera.getWarningRecipients();
        if (warningRecipients == null || warningRecipients.getWxOpenidList() == null) { // 未设置预警接收人
            warningRecipients = new WarningRecipients();
            warningRecipients.setWxOpenidList(Collections.singletonList(senderId));
            deviceCamera.setWarningRecipients(warningRecipients);
        } else if (warningRecipients.getWxOpenidList().contains(senderId)) { // 已绑定
            return WxEventPush.buildTextMessage(senderId, serviceId, "您已绑定过该设备\ndeviceId: " + deviceId).getXmlString();
        } else { // 未绑定
            warningRecipients.getWxOpenidList().add(senderId);
        }
        if (deviceCameraMapper.updateById(deviceCamera) > 0) {
            return WxEventPush.buildTextMessage(senderId, serviceId, "绑定成功\ndeviceId: " + deviceId).getXmlString();
        } else {
            return WxEventPush.buildTextMessage(senderId, serviceId, "绑定失败\ndeviceId: " + deviceId).getXmlString();
        }
    }

    /**
     * 处理解绑事件
     *
     * @param deviceId     设备ID
     * @param fromUserName 发送者openid
     */
    private boolean handleUnbindEvent(String deviceId, String fromUserName) {
        // 查询设备信息
        DeviceCamera deviceCamera = deviceCameraMapper.selectById(deviceId);
        if (deviceCamera == null) {
            log.error("设备不存在，deviceId:{}", deviceId);
            return false;
        }
        // 更新设备绑定状态
        return handleUnbindEvent(deviceCamera, fromUserName);
    }

    /**
     * 处理解绑事件
     *
     * @param deviceCamera 设备信息
     * @param fromUserName 发送者openid
     */
    private boolean handleUnbindEvent(DeviceCamera deviceCamera, String fromUserName) {
        // 更新设备绑定状态
        WarningRecipients warningRecipients = deviceCamera.getWarningRecipients();
        if (warningRecipients == null || warningRecipients.getWxOpenidList() == null) { // 未设置预警接收人
            return true;
        } else if (warningRecipients.getWxOpenidList().contains(fromUserName)) { // 已绑定
            warningRecipients.getWxOpenidList().remove(fromUserName);
        } else { // 未绑定
            return true;
        }
        return deviceCameraMapper.updateById(deviceCamera) > 0;
    }

    /**
     * 处理微信推送消息 - 文本
     *
     * @param wxEventPush 微信事件推送
     */
    public String handleTextMessage(WxEventPush wxEventPush) {
        String TextContent = wxEventPush.getContent();
        String senderId = wxEventPush.getFromUserName(); // 发送者openid
        String serviceId = wxEventPush.getToUserName(); // 服务号ID

        // 如果是oid-开头的消息
        if (TextContent.startsWith("oid-")) {
            String orderId = TextContent.substring(4);
            // 查询订单信息
            LambdaQueryWrapper<OrderCamera> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.select(OrderCamera::getOrderId);
            queryWrapper.eq(OrderCamera::getTransactionId, orderId);
            OrderCamera orderCamera = orderCameraMapper.selectOne(queryWrapper);
            if (orderCamera == null) {
                return WxEventPush.buildTextMessage(senderId, serviceId, "订单不存在\norderId: " + orderId).getXmlString();
            }
            return WxEventPush.buildTextMessage(senderId, serviceId, "电子照详情页：http://photo.oneshotbox.cn/photo/image?orderId=" + orderCamera.getOrderId()).getXmlString();
        }
        String content = "";
        return "";
    }
}
