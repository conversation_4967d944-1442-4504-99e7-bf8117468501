package com.ruoyi.dto;

import javax.validation.constraints.Min;

/**
 * VIETQR创建支付码响应
 */
public class VIETQRCreatePayCodeResp {

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 支付码
     */
    private String payCode;


    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getPayCode() {
        return payCode;
    }

    public void setPayCode(String payCode) {
        this.payCode = payCode;
    }



    @Override
    public String toString() {
        return "WSSPayCreatePayCodeReq{" +
                "orderId='" + orderId + '\'' +
                ", payCode='" + payCode + '\'' +
                '}';
    }
}
