package com.yunchuang.wxapp.handler;

import com.ruoyi.common.utils.MyResultUtil;
import com.yunchuang.wxapp.exception.WxappAuthenticationException;
import com.yunchuang.wxapp.exception.WxappBusinessException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.Map;

/**
 * 微信小程序 - 异常处理器
 */
@RestControllerAdvice
@Order(1)
public class WxappExceptionHandler {

    private static final Logger log = LoggerFactory.getLogger(WxappExceptionHandler.class);

    /**
     * 处理 小程序 认证异常
     *
     * @param e 异常
     * @return JSONResult
     */
    @ResponseBody
    @ExceptionHandler(WxappAuthenticationException.class)
    public ResponseEntity<Map<String, Object>> authenticationExceptionHandler(WxappAuthenticationException e) {
        log.error("微信小程序 - 认证异常【 code：{} msg：{} 】", e.getCode(), e.getMessage());
        return new ResponseEntity<>(MyResultUtil.error(e.getCode(), e.getMessage()), HttpStatus.UNAUTHORIZED);
    }

    /**
     * 处理 小程序 业务异常
     *
     * @param e 异常
     * @return JSONResult
     */
    @ResponseBody
    @ExceptionHandler(WxappBusinessException.class)
    public ResponseEntity<Map<String, Object>> businessExceptionHandler(WxappBusinessException e) {
        log.error("微信小程序 - 业务异常【 code：{} msg：{} 】", e.getCode(), e.getMessage());
        return new ResponseEntity<>(MyResultUtil.error(e.getCode(), e.getMessage()), HttpStatus.BAD_REQUEST);
    }

}
