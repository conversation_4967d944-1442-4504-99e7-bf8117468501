package com.yunchuang.wxapp.service.client.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yunchuang.wxapp.mapper.PrivacyPolicyMapper;
import com.yunchuang.wxapp.model.domain.PrivacyPolicy;
import com.yunchuang.wxapp.service.client.ICPrivacyPolicyService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

@Service
@Transactional
public class CPrivacyPolicyServiceImpl extends ServiceImpl<PrivacyPolicyMapper, PrivacyPolicy> implements ICPrivacyPolicyService {

    @Resource
    private PrivacyPolicyMapper privacyPolicyMapper;

    /**
     * 查询启用的隐私政策
     *
     * @return 隐私政策
     */
    @Override
    public PrivacyPolicy getEnabledPrivacyPolicy() {
        LambdaQueryWrapper<PrivacyPolicy> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(PrivacyPolicy::getReleaseDate, PrivacyPolicy::getEffectiveDate, PrivacyPolicy::getPolicyContent);
        queryWrapper.eq(PrivacyPolicy::getBeEnabled, 1);
        return privacyPolicyMapper.selectOne(queryWrapper);
    }
}
