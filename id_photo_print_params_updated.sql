-- 证件照打印参数表数据插入SQL（更新版本）
-- 类型：0-通用，1-证件，2-教育，3-从业资格，4-签证
-- 状态：0-启用，1-禁用

-- 先清空表数据
TRUNCATE TABLE `id_photo_print_params`;

-- 通用规格 (type = 0)
INSERT INTO `id_photo_print_params` (`type`, `name`, `width_mm`, `height_mm`, `status`, `sort_order`, `create_by`, `remark`) VALUES
(0, '一寸', 25, 35, 0, 1, 'system', '通用一寸证件照'),
(0, '小一寸', 22, 32, 0, 2, 'system', '通用小一寸证件照'),
(0, '大一寸', 33, 48, 0, 3, 'system', '通用大一寸证件照'),
(0, '二寸', 35, 49, 0, 4, 'system', '通用二寸证件照'),
(0, '大二寸', 35, 53, 0, 5, 'system', '通用大二寸证件照'),
(0, '小二寸', 35, 45, 0, 6, 'system', '通用小二寸证件照'),
(0, '三寸', 55, 84, 0, 7, 'system', '通用三寸证件照'),
(0, '五寸', 89, 127, 0, 8, 'system', '通用五寸证件照');

-- 证件类 (type = 1)
INSERT INTO `id_photo_print_params` (`type`, `name`, `width_mm`, `height_mm`, `status`, `sort_order`, `create_by`, `remark`) VALUES
(1, '身份证', 26, 32, 0, 10, 'system', '身份证照片'),
(1, '居住证', 26, 32, 0, 11, 'system', '居住证照片'),
(1, '结婚照', 53, 35, 0, 12, 'system', '结婚证照片'),
(1, '驾驶证', 22, 32, 0, 13, 'system', '驾驶证照片'),
(1, '社保(医保)', 26, 32, 0, 14, 'system', '社保医保证照片'),
(1, '入台证', 35, 45, 0, 15, 'system', '入台证照片'),
(1, '行驶证', 88, 60, 0, 16, 'system', '行驶证照片'),
(1, '会计信息采集', 25, 35, 0, 17, 'system', '会计信息采集照片'),
(1, '离婚证', 35, 49, 0, 18, 'system', '离婚证照片');

-- 教育类 (type = 2)
INSERT INTO `id_photo_print_params` (`type`, `name`, `width_mm`, `height_mm`, `status`, `sort_order`, `create_by`, `remark`) VALUES
(2, '入学报名', 25, 35, 0, 20, 'system', '入学报名照片'),
(2, '高考报名', 25, 35, 0, 21, 'system', '高考报名照片'),
(2, '大学入学报名', 35, 45, 0, 22, 'system', '大学入学报名照片');

-- 从业资格类 (type = 3)
INSERT INTO `id_photo_print_params` (`type`, `name`, `width_mm`, `height_mm`, `status`, `sort_order`, `create_by`, `remark`) VALUES
(3, '网约车照片', 35, 45, 0, 30, 'system', '网约车从业资格证照片'),
(3, '电子导游证', 35, 49, 0, 31, 'system', '电子导游证照片'),
(3, '保安员证', 26, 32, 0, 32, 'system', '保安员证照片');

-- 签证类 (type = 4)
INSERT INTO `id_photo_print_params` (`type`, `name`, `width_mm`, `height_mm`, `status`, `sort_order`, `create_by`, `remark`) VALUES
(4, '泰国签证', 35, 45, 0, 40, 'system', '泰国签证照片'),
(4, '韩国签证', 35, 45, 0, 41, 'system', '韩国签证照片'),
(4, '日本签证', 35, 45, 0, 42, 'system', '日本签证照片'),
(4, '美国签证', 51, 51, 0, 43, 'system', '美国签证照片'),
(4, '越南签证', 35, 45, 0, 44, 'system', '越南签证照片'),
(4, '法国签证', 35, 45, 0, 45, 'system', '法国签证照片'),
(4, '澳大利亚签证', 35, 45, 0, 46, 'system', '澳大利亚签证照片'),
(4, '柬埔寨签证', 35, 45, 0, 47, 'system', '柬埔寨签证照片'),
(4, '德国签证', 35, 45, 0, 48, 'system', '德国签证照片'),
(4, '加拿大签证', 35, 45, 0, 49, 'system', '加拿大签证照片'),
(4, '希腊签证', 35, 40, 0, 50, 'system', '希腊签证照片'),
(4, '英国签证', 35, 45, 0, 51, 'system', '英国签证照片'),
(4, '西班牙签证', 35, 45, 0, 52, 'system', '西班牙签证照片'),
(4, '瑞士签证', 35, 45, 0, 53, 'system', '瑞士签证照片'),
(4, '新西兰签证', 35, 45, 0, 54, 'system', '新西兰签证照片'),
(4, '南非签证', 35, 45, 0, 55, 'system', '南非签证照片'),
(4, '尼泊尔签证', 35, 45, 0, 56, 'system', '尼泊尔签证照片'),
(4, '老挝签证', 35, 45, 0, 57, 'system', '老挝签证照片'),
(4, '缅甸签证', 35, 45, 0, 58, 'system', '缅甸签证照片'),
(4, '意大利签证', 35, 40, 0, 59, 'system', '意大利签证照片'),
(4, '俄罗斯签证', 35, 45, 0, 60, 'system', '俄罗斯签证照片'),
(4, '荷兰签证', 35, 45, 0, 61, 'system', '荷兰签证照片'),
(4, '阿联酋签证', 35, 45, 0, 62, 'system', '阿联酋签证照片'),
(4, '印度签证', 51, 51, 0, 63, 'system', '印度签证照片'),
(4, '奥地利签证', 35, 45, 0, 64, 'system', '奥地利签证照片'),
(4, '比利时签证', 35, 45, 0, 65, 'system', '比利时签证照片');
