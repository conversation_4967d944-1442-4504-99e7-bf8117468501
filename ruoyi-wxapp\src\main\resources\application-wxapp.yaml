# wxapp 配置
wxapp:
  # 鉴权拦截器
  auth-interceptor:
    # 拦截路径
    path-patterns: /client/wxapp/**,/order/printer/user/**
    # 排除路径
    #    exclude-path-patterns: /client/wxapp/common/**,/client/wxapp/user/login_or_register
    exclude-path-patterns: /client/wxapp/common/**,/client/wxapp/user/login_or_register,/client/wxapp/test/**
  # 微信开发平台
  wechat:
    # 小程序配置
    applets:
      # 小程序appId
      app-id: wxb4c2d62fc737311f
      # 小程序密钥
      app-secret: f37690c219544053db752bbe423d4080
  # 鉴权
  auth:
    # token 过期时间 单位分钟 暂时为1个月
    token-expire-time: 43200
    # token 过期前刷新时间 单位分钟 暂时为3天
    token-refresh-time: 4320
  # 静态资源
  static:
    # 默认
    default:
      # 头像
      avatar: /profile/default/avatar.jpg

# api
api:
  # 日志
  log:
    # 拦截路径
    path-patterns: /client/wxapp/**
    # 排除路径
    exclude-path-patterns: /client/wxapp/test/**

# 微信支付配置（API v2）
wxpay:
  # 微信支付AppID（需要您提供）
  app-id: wx945478823d7032b8
  # 微信支付商户号（已配置）
  mch-id: 1631162326
  # 微信支付API密钥（API v2密钥，需要您提供）
  api-key: 123456789123456789123456789abcde
  # 微信支付证书序列号（已配置）
  cert-serial-no: 671F99B4529CDF45E97F5CB341E02838E1DEE092
  # 微信支付证书文件路径（p12格式，需要您提供）
  cert-path: classpath:cert/apiclient_cert.p12
  # 微信支付证书密码（通常是商户号）
  cert-password: 1631162326
  # 微信支付回调地址（临时设置，主要使用主动查询）
  notify-url: https://temp.example.com/client/wxapp/wxpay/notify
  # 是否为沙箱环境
  sandbox: false
  # 连接超时时间（毫秒）
  connect-timeout: 8000
  # 读取超时时间（毫秒）
  read-timeout: 10000

