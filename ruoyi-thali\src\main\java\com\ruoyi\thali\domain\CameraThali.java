package com.ruoyi.thali.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 套餐对象 camera_thali
 * 
 * <AUTHOR>
 * @date 2023-07-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("camera_thali")
public class CameraThali extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 套餐id */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;


    /** 设备id*/
    @Excel(name = "设备id")
    private String deviceId;

    //10个价格
    private int payOne;
    private int payTwo;
    private int payThree;
    private int payFour;
    private int payFive;
    private int paySix;
    private int paySeven;
    private int payEight;
    private int payNine;
    private int payTen;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public int getPayOne() {
        return payOne;
    }

    public void setPayOne(int payOne) {
        this.payOne = payOne;
    }

    public int getPayTwo() {
        return payTwo;
    }

    public void setPayTwo(int payTwo) {
        this.payTwo = payTwo;
    }

    public int getPayThree() {
        return payThree;
    }

    public void setPayThree(int payThree) {
        this.payThree = payThree;
    }

    public int getPayFour() {
        return payFour;
    }

    public void setPayFour(int payFour) {
        this.payFour = payFour;
    }

    public int getPayFive() {
        return payFive;
    }

    public void setPayFive(int payFive) {
        this.payFive = payFive;
    }

    public int getPaySix() {
        return paySix;
    }

    public void setPaySix(int paySix) {
        this.paySix = paySix;
    }

    public int getPaySeven() {
        return paySeven;
    }

    public void setPaySeven(int paySeven) {
        this.paySeven = paySeven;
    }

    public int getPayEight() {
        return payEight;
    }

    public void setPayEight(int payEight) {
        this.payEight = payEight;
    }

    public int getPayNine() {
        return payNine;
    }

    public void setPayNine(int payNine) {
        this.payNine = payNine;
    }

    public int getPayTen() {
        return payTen;
    }

    public void setPayTen(int payTen) {
        this.payTen = payTen;
    }

    @Override
    public String toString() {
        return "CameraThali{" +
                "id=" + id +
                ", deviceId='" + deviceId + '\'' +
                ", payOne=" + payOne +
                ", payTwo=" + payTwo +
                ", payThree=" + payThree +
                ", payFour=" + payFour +
                ", payFive=" + payFive +
                ", paySix=" + paySix +
                ", paySeven=" + paySeven +
                ", payEight=" + payEight +
                ", payNine=" + payNine +
                ", payTen=" + payTen +
                '}';
    }
}
