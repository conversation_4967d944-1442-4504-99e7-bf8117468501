package com.ruoyi.beauty.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 美颜对象 beauty
 * 
 * <AUTHOR>
 * @date 2023-10-15
 */
public class Beauty extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 用户id */
    private Long useeId;

    /** 该用户是否有磨皮 */
    @Excel(name = "该用户是否有磨皮")
    private Boolean mopi;

    /** 该用户是否有美白 */
    @Excel(name = "该用户是否有美白")
    private Boolean meibai;

    /** 该用户是否有五官立体 */
    @Excel(name = "该用户是否有五官立体")
    private Boolean wuguanliti;

    /** 该用户是否有亮眼 */
    @Excel(name = "该用户是否有亮眼")
    private Boolean liangyan;

    /** 该用户是否有红润 */
    @Excel(name = "该用户是否有红润")
    private Boolean hongrun;

    /** 该用户是否有瘦脸 */
    @Excel(name = "该用户是否有瘦脸")
    private Boolean shoulian;

    /** 该用户是否有大眼 */
    @Excel(name = "该用户是否有大眼")
    private Boolean dayan;

    public void setUseeId(Long useeId) 
    {
        this.useeId = useeId;
    }

    public Long getUseeId() 
    {
        return useeId;
    }
    public void setMopi(Boolean mopi) 
    {
        this.mopi = mopi;
    }

    public Boolean getMopi() 
    {
        return mopi;
    }
    public void setMeibai(Boolean meibai) 
    {
        this.meibai = meibai;
    }

    public Boolean getMeibai() 
    {
        return meibai;
    }
    public void setWuguanliti(Boolean wuguanliti) 
    {
        this.wuguanliti = wuguanliti;
    }

    public Boolean getWuguanliti() 
    {
        return wuguanliti;
    }
    public void setLiangyan(Boolean liangyan) 
    {
        this.liangyan = liangyan;
    }

    public Boolean getLiangyan() 
    {
        return liangyan;
    }
    public void setHongrun(Boolean hongrun) 
    {
        this.hongrun = hongrun;
    }

    public Boolean getHongrun() 
    {
        return hongrun;
    }
    public void setShoulian(Boolean shoulian) 
    {
        this.shoulian = shoulian;
    }

    public Boolean getShoulian() 
    {
        return shoulian;
    }
    public void setDayan(Boolean dayan) 
    {
        this.dayan = dayan;
    }

    public Boolean getDayan() 
    {
        return dayan;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("useeId", getUseeId())
            .append("mopi", getMopi())
            .append("meibai", getMeibai())
            .append("wuguanliti", getWuguanliti())
            .append("liangyan", getLiangyan())
            .append("hongrun", getHongrun())
            .append("shoulian", getShoulian())
            .append("dayan", getDayan())
            .toString();
    }
}
