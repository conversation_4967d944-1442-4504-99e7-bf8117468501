-- 修改打印价格单位从分改为元
-- 执行前请先备份数据库

-- 1. 备份原有数据
CREATE TABLE order_printer_tasks_backup AS SELECT * FROM order_printer_tasks;

-- 2. 修改task_price字段类型为DECIMAL，支持小数
ALTER TABLE order_printer_tasks MODIFY COLUMN task_price DECIMAL(10,2) COMMENT '任务价格（元）';

-- 3. 将现有数据从分转换为元（除以100）
UPDATE order_printer_tasks SET task_price = task_price / 100 WHERE task_price IS NOT NULL;

-- 4. 验证修改结果
SELECT task_id, task_price FROM order_printer_tasks LIMIT 10;

-- 5. 查看表结构
DESCRIBE order_printer_tasks;
