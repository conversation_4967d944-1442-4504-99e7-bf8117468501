package com.ruoyi.task;

import com.ruoyi.order.domain.OrderPrinter;
import com.ruoyi.order.service.IOrderPrinterService;
import com.ruoyi.utils.WxPayUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 微信支付状态查询定时任务
 * 每分钟查询一次未支付订单的状态
 * 
 * <AUTHOR>
 * @date 2025-01-30
 */
@Slf4j
@Component
public class WxPayQueryTask implements CommandLineRunner {

    @Autowired(required = false)
    private WxPayUtil wxPayUtil;

    @Autowired(required = false)
    private IOrderPrinterService orderPrinterService;

    @Override
    public void run(String... args) throws Exception {
        log.info("=== 微信支付状态查询定时任务已启动 ===");
        log.info("定时任务将每分钟执行一次，自动查询未支付订单状态");
    }

    /**
     * 定时查询支付状态
     * 每分钟执行一次
     */
    @Scheduled(fixedRate = 60000) // 60秒 = 60000毫秒
    public void queryPaymentStatus() {
        log.info("=== 开始执行微信支付状态查询定时任务 ===");

        try {
            // 检查依赖是否正确注入
            if (wxPayUtil == null) {
                log.warn("WxPayUtil未正确注入，跳过本次查询任务");
                return;
            }

            if (orderPrinterService == null) {
                log.warn("OrderPrinterService未正确注入，跳过本次查询任务");
                return;
            }
            // TODO: 根据实际业务需求实现以下逻辑
            // 1. 从数据库查询最近创建的未支付订单（建议查询最近30分钟内的订单）
            // 2. 批量查询这些订单的微信支付状态
            // 3. 更新已支付订单的状态
            // 4. 发送支付成功通知
            
            // 示例实现（需要根据实际业务调整）
            List<String> unpaidOrders = getUnpaidOrders();

            if (unpaidOrders == null || unpaidOrders.isEmpty()) {
                log.debug("没有需要查询的未支付订单");
                return;
            }
            
            log.info("找到{}个未支付订单需要查询状态", unpaidOrders.size());
            
            int querySuccessCount = 0;  // 查询成功次数
            int paymentSuccessCount = 0; // 支付成功次数
            int paymentClosedCount = 0;  // 订单关闭次数
            int queryErrorCount = 0;     // 查询失败次数
            int waitingCount = 0;        // 等待支付次数

            for (String outTradeNo : unpaidOrders) {
                try {
                    // 查询微信支付状态
                    Map<String, Object> queryResult = wxPayUtil.queryOrder(outTradeNo);

                    String returnCode = (String) queryResult.get("return_code");
                    String resultCode = (String) queryResult.get("result_code");
                    String tradeState = (String) queryResult.get("trade_state");

                    if ("SUCCESS".equals(returnCode) && "SUCCESS".equals(resultCode)) {
                        querySuccessCount++;
                        if ("SUCCESS".equals(tradeState)) {
                            // 支付成功，更新订单状态
                            handlePaymentSuccess(outTradeNo, queryResult);
                            paymentSuccessCount++;
                            log.info("订单{}支付成功，已更新状态", outTradeNo);
                        } else if ("CLOSED".equals(tradeState) || "REVOKED".equals(tradeState)) {
                            // 订单已关闭或撤销，更新订单状态
                            handlePaymentClosed(outTradeNo, tradeState);
                            paymentClosedCount++;
                            log.info("订单{}状态为{}，已更新", outTradeNo, tradeState);
                        } else {
                            // NOTPAY, USERPAYING 等状态，继续等待
                            waitingCount++;
                            log.debug("订单{}状态为{}，继续等待", outTradeNo, tradeState);
                        }
                    } else {
                        queryErrorCount++;
                        log.warn("查询订单{}失败: {}", outTradeNo, queryResult.get("err_code_des"));
                    }

                    // 避免请求过于频繁，每次查询间隔100ms
                    Thread.sleep(100);

                } catch (Exception e) {
                    queryErrorCount++;
                    log.error("查询订单{}状态时发生异常", outTradeNo, e);
                }
            }

            log.info("=== 微信支付状态查询定时任务完成 === 查询成功: {}, 支付成功: {}, 订单关闭: {}, 等待支付: {}, 查询失败: {}",
                    querySuccessCount, paymentSuccessCount, paymentClosedCount, waitingCount, queryErrorCount);
            
        } catch (Exception e) {
            log.error("执行微信支付状态查询定时任务失败", e);
        }
    }

    /**
     * 获取未支付订单列表
     * 从数据库查询最近30分钟内创建的微信未支付订单
     */
    private List<String> getUnpaidOrders() {
        try {
            // 构建查询条件
            OrderPrinter queryOrder = new OrderPrinter();
            queryOrder.setOrderStatus(0); // 0-未支付
            queryOrder.setPayWay(1);      // 1-微信支付

            // 设置时间范围：最近30分钟
            LocalDateTime thirtyMinutesAgo = LocalDateTime.now().minusMinutes(30);
            queryOrder.setStartTime(java.sql.Timestamp.valueOf(thirtyMinutesAgo));

            // 查询订单列表
            List<OrderPrinter> unpaidOrders = orderPrinterService.selectOrderPrinterList(queryOrder);

            if (unpaidOrders == null || unpaidOrders.isEmpty()) {
                return Collections.emptyList();
            }

            // 提取订单号列表
            List<String> orderIds = unpaidOrders.stream()
                    .map(OrderPrinter::getOrderId)
                    .filter(orderId -> orderId != null && !orderId.trim().isEmpty())
                    .collect(Collectors.toList());

            log.debug("查询到{}个未支付的微信订单", orderIds.size());
            return orderIds;

        } catch (Exception e) {
            log.error("查询未支付订单列表失败", e);
            return Collections.emptyList();
        }
    }

    /**
     * 处理支付成功
     * 更新订单状态为已支付，并记录支付信息
     */
    private void handlePaymentSuccess(String outTradeNo, Map<String, Object> queryResult) {
        try {
            String transactionId = (String) queryResult.get("transaction_id");
            String totalFee = (String) queryResult.get("total_fee");
            String timeEnd = (String) queryResult.get("time_end");

            log.info("处理支付成功 - 订单号: {}, 微信订单号: {}, 金额: {}分, 完成时间: {}",
                    outTradeNo, transactionId, totalFee, timeEnd);

            // 查询订单信息
            OrderPrinter order = orderPrinterService.selectOrderPrinterByOrderId(outTradeNo);
            if (order == null) {
                log.warn("订单不存在: {}", outTradeNo);
                return;
            }

            // 检查订单状态，避免重复处理
            if (order.getOrderStatus() != 0) {
                log.info("订单{}状态已更新，当前状态: {}，跳过处理", outTradeNo, order.getOrderStatus());
                return;
            }

            // 更新订单状态为已支付
            OrderPrinter updateOrder = new OrderPrinter();
            updateOrder.setOrderId(outTradeNo);
            updateOrder.setOrderStatus(1); // 1-已支付
            updateOrder.setTransactionId(transactionId);

            // 解析支付时间
            if (timeEnd != null && !timeEnd.isEmpty()) {
                try {
                    // 微信支付时间格式：yyyyMMddHHmmss
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
                    LocalDateTime payDateTime = LocalDateTime.parse(timeEnd, formatter);
                    updateOrder.setPayTime(java.sql.Timestamp.valueOf(payDateTime));
                } catch (Exception e) {
                    log.warn("解析支付时间失败: {}", timeEnd, e);
                    updateOrder.setPayTime(new java.util.Date());
                }
            } else {
                updateOrder.setPayTime(new java.util.Date());
            }

            // 更新订单
            int updateResult = orderPrinterService.updateOrderPrinter(updateOrder);
            if (updateResult > 0) {
                log.info("订单{}支付状态更新成功", outTradeNo);

                // TODO: 可以在这里添加其他业务逻辑
                // 1. 发送支付成功通知
                // 2. 触发打印流程
                // 3. 记录支付日志

            } else {
                log.error("订单{}支付状态更新失败", outTradeNo);
            }

        } catch (Exception e) {
            log.error("处理支付成功业务逻辑失败 - 订单号: {}", outTradeNo, e);
        }
    }

    /**
     * 处理订单关闭
     * 更新订单状态为已取消
     */
    private void handlePaymentClosed(String outTradeNo, String tradeState) {
        try {
            log.info("处理订单关闭 - 订单号: {}, 状态: {}", outTradeNo, tradeState);

            // 查询订单信息
            OrderPrinter order = orderPrinterService.selectOrderPrinterByOrderId(outTradeNo);
            if (order == null) {
                log.warn("订单不存在: {}", outTradeNo);
                return;
            }

            // 检查订单状态，只有未支付的订单才能关闭
            if (order.getOrderStatus() != 0) {
                log.info("订单{}状态为{}，不需要关闭", outTradeNo, order.getOrderStatus());
                return;
            }

            // 更新订单状态为已取消
            OrderPrinter updateOrder = new OrderPrinter();
            updateOrder.setOrderId(outTradeNo);
            updateOrder.setOrderStatus(2); // 2-已取消

            // 更新订单
            int updateResult = orderPrinterService.updateOrderPrinter(updateOrder);
            if (updateResult > 0) {
                log.info("订单{}状态更新为已取消", outTradeNo);

                // TODO: 可以在这里添加其他业务逻辑
                // 1. 释放库存（如果有预占库存）
                // 2. 发送订单取消通知
                // 3. 记录取消日志

            } else {
                log.error("订单{}状态更新失败", outTradeNo);
            }

        } catch (Exception e) {
            log.error("处理订单关闭业务逻辑失败 - 订单号: {}", outTradeNo, e);
        }
    }

    /**
     * 手动触发查询任务（用于测试）
     */
    public void manualQuery() {
        log.info("手动触发微信支付状态查询任务");
        queryPaymentStatus();
    }
}
