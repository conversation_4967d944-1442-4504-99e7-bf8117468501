package com.ruoyi.device.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.QueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.device.domain.DeviceLog;
import com.ruoyi.device.mapper.DeviceLogMapper;
import com.ruoyi.device.service.IDeviceLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class IDeviceLogServiceImpl extends ServiceImpl<DeviceLogMapper, DeviceLog> implements IDeviceLogService {

    @Autowired
    private DeviceLogMapper deviceLogMapper;

    @Override
    public int addLog(DeviceLog deviceLog) {
        Date date = new Date();
        deviceLog.setTime(date);
        return deviceLogMapper.addLog(deviceLog);

    }

    @Override
    public TableDataInfo selectLog(DeviceLog deviceLog,int pageNum,int pageSize) {
        QueryChainWrapper<DeviceLog> query = query();


        if (StringUtils.isNotEmpty(deviceLog.getDeviceId())) {
            query.eq("device_id", deviceLog.getDeviceId());
        }
        if (deviceLog.getStartTime() != null || deviceLog.getEndTime() != null){
            query.between("time",deviceLog.getStartTime(),deviceLog.getEndTime());
        }

        Page<DeviceLog> page = query.orderByDesc("time").page(new Page<>(pageNum, pageSize));
        List<DeviceLog> records = page.getRecords();
        return new TableDataInfo(records,page.getTotal());
    }

    @Override
    public int deleteLog(String[] deviceIds) {
        return deviceLogMapper.deleteLog(deviceIds);
    }
}
