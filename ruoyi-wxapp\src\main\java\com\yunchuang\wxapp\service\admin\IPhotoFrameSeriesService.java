package com.yunchuang.wxapp.service.admin;

import com.yunchuang.wxapp.model.domain.PhotoFrameSeries;

import java.util.List;

/**
 * 相框系列Service接口
 *
 * <AUTHOR>
 * @date 2025-02-06
 */
public interface IPhotoFrameSeriesService {
    /**
     * 查询相框系列
     *
     * @param id 相框系列主键
     * @return 相框系列
     */
    public PhotoFrameSeries selectPhotoFrameSeriesById(Long id);

    /**
     * 查询相框系列列表
     *
     * @param photoFrameSeries 相框系列
     * @return 相框系列集合
     */
    public List<PhotoFrameSeries> selectPhotoFrameSeriesList(PhotoFrameSeries photoFrameSeries);

    /**
     * 新增相框系列
     *
     * @param photoFrameSeries 相框系列
     * @return 结果
     */
    public int insertPhotoFrameSeries(PhotoFrameSeries photoFrameSeries);

    /**
     * 修改相框系列
     *
     * @param photoFrameSeries 相框系列
     * @return 结果
     */
    public int updatePhotoFrameSeries(PhotoFrameSeries photoFrameSeries);

    /**
     * 批量删除相框系列
     *
     * @param ids 需要删除的相框系列主键集合
     * @return 结果
     */
    public int deletePhotoFrameSeriesByIds(Long[] ids);

    /**
     * 删除相框系列信息
     *
     * @param id 相框系列主键
     * @return 结果
     */
    public int deletePhotoFrameSeriesById(Long id);
}
