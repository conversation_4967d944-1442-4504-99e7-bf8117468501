package com.ruoyi.web.controller.device;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.device.domain.DeviceLog;
import com.ruoyi.device.service.IDeviceLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/device_log")
public class DeviceLogController extends BaseController {

    @Autowired
    private IDeviceLogService deviceLogService;

    @PostMapping("/addLog")
    public AjaxResult addLog(DeviceLog deviceLog){
        System.out.println(deviceLog);
        return AjaxResult.success(deviceLogService.addLog(deviceLog));
    }

    @GetMapping("/list")
    public TableDataInfo list(DeviceLog deviceLog,int pageNum,int pageSize){
        return deviceLogService.selectLog(deviceLog,pageNum,pageSize);
    }

    @DeleteMapping("/{deviceIds}")
    public int deleteLog(@PathVariable String[] deviceIds){
        return deviceLogService.deleteLog(deviceIds);
    }


}
