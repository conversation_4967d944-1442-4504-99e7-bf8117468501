<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.device.mapper.DeviceCameraMapper">

    <resultMap type="DeviceCamera" id="DeviceCameraResult">
        <result property="deviceId" column="device_id"/>
        <result property="deviceName" column="device_name"/>
        <result property="softwareId" column="software_id"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="district" column="district"/>
        <result property="detailAddress" column="detail_address"/>
        <result property="deviceStatus" column="device_status"/>
        <result property="lng" column="lng"/>
        <result property="lat" column="lat"/>
        <result property="agentUser" column="agent_user"/>
        <result property="userDevice" column="user_device"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="userId" column="user_id"/>
        <result property="nickName" column="nick_name"/>
        <result property="countPrice" column="count_price"/>
        <result property="subAccount" column="sub_account"/>
        <result property="isWarning" column="is_warning"/>
        <result property="warningThreshold" column="warning_threshold"/>
        <result property="warningRecipients" column="warning_recipients" jdbcType="VARCHAR"
                typeHandler="com.ruoyi.device.handler.WarningRecipientsTypeHandler"/>
    </resultMap>

    <sql id="selectDeviceCameraVo">
        select device_id,
               device_name,
               software_id,
               province,
               city,
               district,
               detail_address,
               count_price,
               sub_account,
               device_status,
               lng,
               lat,
               agent_user,
               user_device,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_warning,
               warning_threshold,
               warning_recipients
        from device_camera
    </sql>

    <select id="selectDeviceCameraList" parameterType="DeviceCamera" resultMap="DeviceCameraResult">
        <include refid="selectDeviceCameraVo"/>
        <where>
            <if test="deviceId != null  and deviceId != ''">and device_id like concat('%', #{deviceId}, '%')</if>
            <if test="deviceName != null  and deviceName != ''">and device_name like concat('%', #{deviceName}, '%')
            </if>
            <if test="userDevice != null  and userDevice != ''">and user_device like concat('%', #{userDevice}, '%')
            </if>
            <if test="province != null  and province != ''">and province = #{province}</if>
            <if test="city != null  and city != ''">and city = #{city}</if>
            <if test="district != null  and district != ''">and district = #{district}</if>
            <if test="detailAddress != null  and detailAddress != ''">and detail_address like concat('%',
                #{detailAddress}, '%')
            </if>
            <if test="isWarning != null ">and is_warning = #{isWarning}</if>
            <if test="warningThreshold != null ">and warning_threshold = #{warningThreshold}</if>
            <if test="warningRecipients != null  and warningRecipients != ''">and warning_recipients =
                #{warningRecipients}
            </if>
            <if test="deviceStatus != null ">and device_status = #{deviceStatus}</if>
        </where>
    </select>

    <select id="selectDeviceCameraByDeviceId" parameterType="String" resultMap="DeviceCameraResult">
        <include refid="selectDeviceCameraVo"/>
        where device_id = #{deviceId}
    </select>
    <select id="selectDeviceCameraByUserId" resultType="java.lang.String">
        select device_name
        from device_camera
        where user_id = #{userId}
    </select>
    <select id="selectDeviceNameByDeviceId" resultType="java.lang.String">
        select device_name
        from device_camera
        where device_id = #{deviceId}
    </select>
    <select id="selectDeviceCameraListByUserName" resultType="com.ruoyi.device.domain.DeviceCamera">
        select *
        from device_camera
        where user_device = #{userName}
    </select>
    <select id="selectUserIDByDeviceId" resultType="java.lang.Integer">
        select user_id
        from device_camera
        where device_id = #{deviceId}
    </select>
    <select id="selectDeviceCameraListByDeviceName" resultType="com.ruoyi.device.domain.DeviceCamera">
        select *
        from device_camera
        where device_name = #{deviceName}
    </select>
    <select id="selectDeviceIdByUserId" resultType="java.lang.String">
        select device_id
        from device_camera
        where user_id = #{userId}
    </select>


    <insert id="insertDeviceCamera" parameterType="DeviceCamera">
        insert into device_camera
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deviceId != null and deviceId != ''">device_id,</if>
            <if test="deviceName != null">device_name,</if>
            <if test="softwareId != null">software_id,</if>
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="district != null">district,</if>
            <if test="detailAddress != null">detail_address,</if>
            <if test="deviceStatus != null">device_status,</if>
            <if test="lng != null">lng,</if>
            <if test="lat != null">lat,</if>
            <if test="agentUser != null and agentUser != 0">agent_user,</if>
            <if test="userDevice != null and userDevice != ''">user_device,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="isWarning != null">is_warning,</if>
            <if test="warningThreshold != null">warning_threshold,</if>
            <if test="warningRecipients != null">warning_recipients,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deviceId != null and deviceId != ''">#{deviceId},</if>
            <if test="deviceName != null">#{deviceName},</if>
            <if test="softwareId != null">#{softwareId},</if>
            <if test="userDevice != null adn userDevice != ''">#{userDevice},</if>
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="district != null">#{district},</if>
            <if test="detailAddress != null">#{detailAddress},</if>
            <if test="deviceStatus != null">#{deviceStatus},</if>
            <if test="lng != null">#{lng},</if>
            <if test="lat != null">#{lat},</if>
            <if test="agentUser != null and agentUser != 0">#{agentUser},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="isWarning != null">#{isWarning},</if>
            <if test="warningThreshold != null">#{warningThreshold},</if>
            <if test="warningRecipients != null">#{warningRecipients},</if>
        </trim>
    </insert>


    <update id="updateDeviceCamera" parameterType="DeviceCamera">
        update device_camera
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceName != null">device_name = #{deviceName},</if>
            <if test="softwareId != null">software_id = #{softwareId},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="district != null">district = #{district},</if>
            <if test="detailAddress != null">detail_address = #{detailAddress},</if>
            <if test="deviceStatus != null">device_status = #{deviceStatus},</if>
            <if test="lng != null">lng = #{lng},</if>
            <if test="lat != null">lat = #{lat},</if>
            <if test="agentUser != null and agentUser != 0">agent_user = #{agentUser},</if>
            <if test="userDevice != null and userDevice != ''">user_device = #{userDevice},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="isWarning != null">is_warning = #{isWarning},</if>
            <if test="warningThreshold != null">warning_threshold = #{warningThreshold},</if>
            <if test="warningRecipients != null">warning_recipients = #{warningRecipients},</if>
        </trim>
        where device_id = #{deviceId}
    </update>

    <delete id="deleteDeviceCameraByDeviceId" parameterType="String">
        delete
        from device_camera
        where device_id = #{deviceId}
    </delete>

    <delete id="deleteDeviceCameraByDeviceIds" parameterType="String">
        delete from device_camera where device_id in
        <foreach item="deviceId" collection="array" open="(" separator="," close=")">
            #{deviceId}
        </foreach>
    </delete>
</mapper>