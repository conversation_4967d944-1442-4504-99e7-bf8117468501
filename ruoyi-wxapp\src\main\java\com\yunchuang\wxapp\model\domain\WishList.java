package com.yunchuang.wxapp.model.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 心愿单对象 wxapp_wish _list
 *
 * <AUTHOR>
 * @date 2025-02-06
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("wxapp_wish_list")
public class WishList extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 心愿单ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @Excel(name = "用户ID")
    private Long userId;

    /**
     * 相框ID
     */
    @Excel(name = "相框ID")
    private Long photoFrameId;

    /**
     * 打卡时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "打卡时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date clockinTime;
}
