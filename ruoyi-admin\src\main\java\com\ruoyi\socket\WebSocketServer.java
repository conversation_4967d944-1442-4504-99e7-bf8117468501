package com.ruoyi.socket;

import cn.hutool.json.JSONUtil;
import com.ruoyi.common.constant.Constant;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.device.domain.DeviceCamera;
import com.ruoyi.device.service.IDeviceCameraService;
import com.ruoyi.system.service.ISysUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

// @ServerEndpoint 声明并创建了webSocket端点, 并且指明了请求路径
// id 为客户端请求时携带的参数, 用于服务端区分客户端使用
@ServerEndpoint("/WebSocketServer/{sid}")
@Component
public class WebSocketServer {

    private static IDeviceCameraService deviceCameraService;

    @Autowired
    public void setDeviceCameraService(IDeviceCameraService service) {
        WebSocketServer.deviceCameraService = service;
    }


    // 日志对象
    private static final Logger log = LoggerFactory.getLogger(WebSocketServer.class);


    // concurrent包的线程安全Set，用来存放每个客户端对应的MyWebSocket对象。
    private static final ConcurrentHashMap<String, Session> cameraWebsocketList = new ConcurrentHashMap<>();

    /*
     * 客户端创建连接时触发
     * */
    @OnOpen
    public void onOpen(Session session, @PathParam(Constant.SID) String sid) {
        DeviceCamera deviceCamera = deviceCameraService.selectDeviceCameraByDeviceId(sid);
        session.getUserProperties().put(Constant.SID, sid);
        if (deviceCamera == null) {
            closeSession(session);
            log.warn("sid {} 不存在", sid);
            return;
        }

        session.setMaxIdleTimeout(2 * 60 * 1000);

//        if (cameraWebsocketList.containsKey(sid)) {
//            log.info("关闭已有的冲突连接：" + sid);
//            closeSession(cameraWebsocketList.get(sid));
//        }
        cameraWebsocketList.put(sid, session);

        log.info(sid + "成功接入，当前在线设备数量：" + cameraWebsocketList.size());
    }

    /**
     * 客户端连接关闭时触发
     **/
    @OnClose
    public void onClose(Session session) {
        String sid = (String) session.getUserProperties().get(Constant.SID);
        if (StringUtils.isEmpty(sid)) {
            log.error("无法从session中获取sid, session {}", JSONUtil.toJsonStr(session));
            return;
        }
        closeSession(session);
        if (sid != null)
            cameraWebsocketList.remove(sid);
    }

    private void closeSession(Session session) {
        try {
            log.info("关闭连接：" + session.getUserProperties().get(Constant.SID) + "当前在线数量：" + cameraWebsocketList.size());
            if (session.isOpen())
                session.close();
        } catch (IOException e) {
            log.error("closeSessionError ", e);
        }
    }


    /**
     * 接收到客户端消息时触发
     */
    @OnMessage
    public void onMessage(String message, Session session) {
        String sid = (String) session.getUserProperties().get(Constant.SID);
        if (!message.equals("heartbeat")) {
            System.out.println("来自：" + sid + " 的信息:" + message);
        } else {
//            log.info("来自：sid :" + sid + "; sessionId:" + session.getId() + "; 的心跳~~");
            try {
                if (session.isOpen())
                    session.getBasicRemote().sendText(message);
            } catch (IOException e) {
                log.info("推送信息异常" + sid + "，指令内容:" + message + "错误信息：" + e.getMessage());
            }
            cameraWebsocketList.putIfAbsent(sid, session); // 加入set中
        }
    }

    /**
     * 连接发生异常时候触发
     */
    @OnError
    public void onError(Session session, Throwable error) {
        String sid = (String) session.getUserProperties().get(Constant.SID);
        log.error("长连接" + sid + "发生错误 :" + error.getMessage());
        try {
            if (session.isOpen())
                session.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (sid != null)
            cameraWebsocketList.remove(sid); // 确保异常时也从集合中移除
    }

    /**
     * 发送消息到所有客户端
     * 指定sid则向指定客户端发消息
     * 不指定sid则向所有客户端发送消息
     */
    public static int sendInfo(String message, String sid) {
        log.info("推送操作指令到窗口" + sid + "，指令内容:" + message);
        Session session = cameraWebsocketList.get(sid);
        if (session != null && session.isOpen()) {
            log.info("服务器消息推送：" + message);
            try {
                session.getBasicRemote().sendText(message);
            } catch (IOException e) {
                log.info("推送信息异常" + sid + "，指令内容:" + message + "错误信息：" + e.getMessage());
            }
            return 1;
        }
        return 0;
    }

    public static boolean checkOnline(String sid) {
        Session session = cameraWebsocketList.get(sid);
        return session != null && session.isOpen();
    }

}

//package com.ruoyi.socket;
//
//        import cn.hutool.json.JSONUtil;
//        import com.ruoyi.common.utils.StringUtils;
//        import com.ruoyi.device.domain.DeviceCamera;
//        import com.ruoyi.device.service.IDeviceCameraService;
//        import com.ruoyi.system.service.ISysUserService;
//        import org.slf4j.Logger;
//        import org.slf4j.LoggerFactory;
//        import org.springframework.beans.factory.annotation.Autowired;
//        import org.springframework.scheduling.annotation.Scheduled;
//        import org.springframework.stereotype.Component;
//
//        import javax.websocket.*;
//        import javax.websocket.server.PathParam;
//        import javax.websocket.server.ServerEndpoint;
//        import java.io.IOException;
//        import java.util.Objects;
//        import java.util.concurrent.ConcurrentHashMap;
//
//// @ServerEndpoint 声明并创建了webSocket端点, 并且指明了请求路径
//// id 为客户端请求时携带的参数, 用于服务端区分客户端使用
//@ServerEndpoint("/WebSocketServer/{sid}")
//@Component
//public class WebSocketServer {
//
//    @Autowired
//    private static IDeviceCameraService deviceCameraService;
//
//    private long connectedTime;
//
//    @Autowired
//    private void setService(IDeviceCameraService deviceCameraService, ISysUserService userService) {
//        WebSocketServer.deviceCameraService = deviceCameraService;
//    }
//
//    // 日志对象
//    private static final Logger log = LoggerFactory.getLogger(WebSocketServer.class);
//
//
//    // concurrent包的线程安全Set，用来存放每个客户端对应的MyWebSocket对象。
////    private static final CopyOnWriteArraySet<WebSocketServer> webSocketSet = new CopyOnWriteArraySet<>();
//    private static final ConcurrentHashMap<String, WebSocketServer> cameraWebsocketList = new ConcurrentHashMap<>();
//
//    // 与某个客户端的连接会话，需要通过它来给客户端发送数据
//    private Session session;
//
//    // 接收sid
//    private String sid = "";
//
//    /*
//     * 客户端创建连接时触发
//     * */
//    @OnOpen
//    public void onOpen(Session session, @PathParam("sid") String sid) {
//        log("OnOpen");
//
//        session.setMaxIdleTimeout(60 * 1000);
//        System.out.println("来自：" + sid + " 的连接");
//        DeviceCamera deviceCamera = deviceCameraService.selectDeviceCameraByDeviceId(sid);
//        if (deviceCamera == null) {
//            System.out.println("sid" + sid + "不存在!!!");
//            return;
//        }
//
//        // 检查当前sid是否已连接
//        WebSocketServer webSocketServer = cameraWebsocketList.get(sid);
//        if (webSocketServer != null) {
//            if (!Objects.equals(webSocketServer.session.getId(), session.getId())) {
//                try {
//                    if (webSocketServer.session.isOpen()) {
//                        webSocketServer.session.close();
//                        log.info("同一sid的新连接接入，关闭旧连接:(" + webSocketServer.sid + ")");
//                    } else {
//                        log.info("同一sid的新连接接入，旧连接:(" + webSocketServer.sid + ")本身已死亡 无需关闭");
//                    }// 关闭旧连接
//                } catch (IOException e) {
//                    log.error("关闭旧连接失败" + e.getMessage());
//                } finally {
//                    cameraWebsocketList.remove(webSocketServer.sid); // 移除旧连接
//                }
//            }
//        }
//
//        this.connectedTime = System.currentTimeMillis(); // 设置连接创建时间
//        this.session = session;
//        this.sid = sid;
//        cameraWebsocketList.put(sid, this); // 加入set中
//        log.info("有新窗口开始监听:" + sid + ", 当前在线设备数为" + cameraWebsocketList.size());
//    }
//
//    /**
//     * 客户端连接关闭时触发
//     **/
//    @OnClose
//    public void onClose() {
//        try {
//            log("OnClose");
//            WebSocketServer webSocketServer = cameraWebsocketList.get(this.sid);
//            if (Objects.nonNull(webSocketServer) && webSocketServer.session.isOpen()) {
//                webSocketServer.session.close();
//            }
//        } catch (IOException e) {
//            log.error("onClose 在关闭 WebSocket 会话时发生错误: " + e.getMessage());
//        } finally {
//            cameraWebsocketList.remove(this.sid); // 从集合中删除
//            log.info("onClose 有一连接:(" + this.sid + ")关闭！当前在线设备数为" + cameraWebsocketList.size());
//        }
//    }
//
//
//    /**
//     * 接收到客户端消息时触发
//     */
//    @OnMessage
//    public void onMessage(String message, Session session) {
//        if (!message.equals("heartbeat"))
//            System.out.println("来自：" + session.getId() + " 的信息:" + message);
//        else {
////            log.info("来自：sid :" + this.sid + "; sessionId:" + session.getId() + "; 的心跳~~");
//            this.connectedTime = System.currentTimeMillis();  // 更新连接时间
//            try {
//                this.session.getBasicRemote().sendText(message);
//            } catch (IOException e) {
//                log.info("推送信息异常" + sid + "，指令内容:" + message + "错误信息：" + e.getMessage());
//            }
//            cameraWebsocketList.putIfAbsent(this.sid, this); // 加入set中
//        }
//    }
//
//    /**
//     * 连接发生异常时候触发
//     */
//    @OnError
//    public void onError(Session session, Throwable error) {
//        log("OnError");
//        log.error("长连接" + this.sid + "发生错误 :" + error.getMessage());
//        try {
//            if (session.isOpen())
//                session.close();
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//        cameraWebsocketList.remove(this.sid); // 确保异常时也从集合中移除
//    }
//
//    /**
//     * 实现服务器主动推送(向浏览器发消息)
//     */
//    public void sendMessage(String message) {
//        log.info("服务器消息推送：" + message);
//        try {
//            this.session.getBasicRemote().sendText(message);
//        } catch (IOException e) {
//            log.info("推送信息异常" + sid + "，指令内容:" + message + "错误信息：" + e.getMessage());
//        }
//    }
//
//    /**
//     * 发送消息到所有客户端
//     * 指定sid则向指定客户端发消息
//     * 不指定sid则向所有客户端发送消息
//     */
//    public static int sendInfo(String message, String sid) {
//        log.info("推送操作指令到窗口" + sid + "，指令内容:" + message);
//        WebSocketServer item = cameraWebsocketList.get(sid);
//        if (item != null) {
//            item.sendMessage(message);
//            return 1;
//        }
//        return 0;
//    }
//
//    public static boolean checkOnline(String sid) {
//        WebSocketServer webSocketServer = cameraWebsocketList.get(sid);
//        return webSocketServer != null && webSocketServer.session != null && webSocketServer.session.isOpen();
//    }
//
//    @Scheduled(fixedRate = 60 * 1000) // 每60秒执行一次
//    public void cleanInvalidConnections() {
//        long currentTime = System.currentTimeMillis();
//        cameraWebsocketList.entrySet().removeIf(entry -> {
//            WebSocketServer server = entry.getValue();
//            if (server == null) return true;
//            return (server.session == null || !server.session.isOpen()) && currentTime - server.connectedTime > 60 * 1000;
//        });
//        log.info("已清理无效连接，当前在线设备数为：" + cameraWebsocketList.size());
//    }
//
//    private void log(String event) {
//        log.info("{}事件 当前sid:{}, 当前session:{},当前ws列表:{}", event, sid, JSONUtil.toJsonStr(session), JSONUtil.toJsonStr(cameraWebsocketList));
//    }
//
//}









