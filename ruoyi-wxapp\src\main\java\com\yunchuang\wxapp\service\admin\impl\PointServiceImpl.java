package com.yunchuang.wxapp.service.admin.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.DateUtils;
import com.yunchuang.wxapp.mapper.PointMapper;
import com.yunchuang.wxapp.model.domain.Point;
import com.yunchuang.wxapp.service.admin.IPointService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 点位Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
@Service
public class PointServiceImpl extends ServiceImpl<PointMapper, Point> implements IPointService {
    @Autowired
    private PointMapper pointMapper;

    /**
     * 查询点位
     *
     * @param id 点位主键
     * @return 点位
     */
    @Override
    public Point selectPointById(Long id) {
        return pointMapper.selectPointById(id);
    }

    /**
     * 查询点位列表
     *
     * @param point 点位
     * @return 点位
     */
    @Override
    public List<Point> selectPointList(Point point) {
        return pointMapper.selectPointList(point);
    }

    /**
     * 新增点位
     *
     * @param point 点位
     * @return 结果
     */
    @Override
    public int insertPoint(Point point) {
        point.setCreateTime(DateUtils.getNowDate());
        return pointMapper.insertPoint(point);
    }

    /**
     * 修改点位
     *
     * @param point 点位
     * @return 结果
     */
    @Override
    public int updatePoint(Point point) {
        point.setUpdateTime(DateUtils.getNowDate());
        return pointMapper.updatePoint(point);
    }

    /**
     * 批量删除点位
     *
     * @param ids 需要删除的点位主键
     * @return 结果
     */
    @Override
    public int deletePointByIds(Long[] ids) {
        return pointMapper.deletePointByIds(ids);
    }

    /**
     * 删除点位信息
     *
     * @param id 点位主键
     * @return 结果
     */
    @Override
    public int deletePointById(Long id) {
        return pointMapper.deletePointById(id);
    }
}
