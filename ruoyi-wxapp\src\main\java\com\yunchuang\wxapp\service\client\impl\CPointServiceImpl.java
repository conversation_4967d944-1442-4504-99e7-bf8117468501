package com.yunchuang.wxapp.service.client.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.MyResultUtil;
import com.yunchuang.wxapp.mapper.PointMapper;
import com.yunchuang.wxapp.model.domain.Point;
import com.yunchuang.wxapp.service.client.ICPointService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 点位 Service 业务层处理
 */
@Service
@Transactional
public class CPointServiceImpl extends ServiceImpl<PointMapper, Point> implements ICPointService {

    @Resource
    private PointMapper pointMapper;

    /**
     * 查询点位列表
     *
     * @param pageNum  页码
     * @param pageSize 每页数量
     * @return 点位列表
     */
    @Override
    public Map<String, Object> getPointList(Integer pageNum, Integer pageSize) {
        LambdaQueryWrapper<Point> queryWrapper = new LambdaQueryWrapper<>();
        // 其他查询条件
        Page<Point> page = new Page<>(pageNum, pageSize);
        pointMapper.selectPage(page, queryWrapper);

        // 返回结果
        return MyResultUtil.pageResult(page, new String[]{});
    }

    /**
     * 查询附近的点位列表
     *
     * @param longitude 经度
     * @param latitude  纬度
     * @param number    数量
     * @return 点位列表
     */
    @Override
    public List<Point> getNearbyPointList(Double longitude, Double latitude, Integer number) {
        LambdaQueryWrapper<Point> queryWrapper = new LambdaQueryWrapper<>();
        // 其他查询条件

        // 返回结果
        return pointMapper.selectNearestPointsWithWrapper(longitude, latitude, number, queryWrapper);
    }
}
