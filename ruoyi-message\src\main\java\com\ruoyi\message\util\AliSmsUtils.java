package com.ruoyi.message.util;

import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.aliyun.teaopenapi.models.Config;
import com.ruoyi.message.config.AliSmsConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 阿里云短信服务工具类
 */
@Slf4j
@Component
@EnableConfigurationProperties(AliSmsConfig.class)
public class AliSmsUtils {

    @Resource
    private AliSmsConfig aliSmsConfig;

    //短信API产品名称（短信产品名固定，无需修改）
    private final String product = "Dysmsapi";
    //短信API产品域名（接口地址固定，无需修改）
    private final String domain = "dysmsapi.aliyuncs.com";
    //发送短信最小间隔（分钟）
    private final Long SMS_MIN_INTERVAL_MINUTE = 60000L;

    /**
     * 创建短信客户端
     *
     * @return Client 短信客户端
     * @throws Exception 异常
     */
    public Client createClient() throws Exception {
        Config config = new Config()
                // 配置 AccessKey ID，请确保代码运行环境配置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID。
                .setAccessKeyId(aliSmsConfig.getAccessKeyId())
                // 配置 AccessKey Secret，请确保代码运行环境配置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_SECRET。
                .setAccessKeySecret(aliSmsConfig.getAccessKeySecret());

        // 配置 Endpoint
        config.endpoint = domain;

        return new Client(config);
    }

    /**
     * 发送短信
     *
     * @param phoneNumber   手机号码
     * @param signName      短信签名
     * @param templateCode  短信模板code
     * @param templateParam 短信模板参数
     * @return SendSmsResponse 响应体
     */
    public SendSmsResponse sendSms(String phoneNumber, String signName, String templateCode, String templateParam) {
        try {
            // 初始化请求客户端
            Client client = createClient();
            //组装请求对象
            SendSmsRequest request = new SendSmsRequest();
            //使用post提交
//            request.setMethod(MethodType.POST);
            //必填:待发送手机号。支持以逗号分隔的形式进行批量调用，批量上限为1000个手机号码,批量调用相对于单条调用及时性稍有延迟,验证码类型的短信推荐使用单条调用的方式；发送国际/港澳台消息时，接收号码格式为国际区号+号码，如“85200000000”
            request.setPhoneNumbers(phoneNumber);
            //必填:短信签名-可在短信控制台中找到
            request.setSignName(signName);
            //必填:短信模板-可在短信控制台中找到，发送国际/港澳台消息时，请使用国际/港澳台短信模版
            request.setTemplateCode(templateCode);
            //可选:模板中的变量替换JSON串,如模板内容为"亲爱的${name},您的验证码为${code}"时,此处的值为
            //友情提示:如果JSON中需要带换行符,请参照标准的JSON协议对换行符的要求,比如短信内容中包含\r\n的情况在JSON中需要表示成\\r\\n,否则会导致JSON在服务端解析失败
            request.setTemplateParam(templateParam);
            //请求失败这里会抛ClientException异常
            SendSmsResponse response = client.sendSms(request);
            if (!response.getBody().getCode().equals("OK")) {
                log.info("[短信服务] 发送短息失败，手机号码：{} ，原因：{}", phoneNumber, response.getBody().getMessage());
            }
            return response;
        } catch (Exception e) {
            log.error("[短信服务] 发送短息异常，手机号码：{}", phoneNumber, e);
            return null;
        }
    }
}