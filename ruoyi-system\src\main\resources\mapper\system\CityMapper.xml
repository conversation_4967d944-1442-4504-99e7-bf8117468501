<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.CityMapper">
    
    <resultMap type="City" id="CityResult">
        <result property="id"    column="id"    />
        <result property="parent"    column="parent"    />
        <result property="value"    column="value"    />
        <result property="text"    column="text"    />
    </resultMap>

    <sql id="selectCityVo">
        select id, parent, value, text from city
    </sql>

    <select id="selectCityList" parameterType="City" resultMap="CityResult">
        <include refid="selectCityVo"/>
        <where>  
            <if test="parent != null  and parent != ''"> and parent = #{parent}</if>
            <if test="value != null  and value != ''"> and value = #{value}</if>
            <if test="text != null  and text != ''"> and text = #{text}</if>
        </where>
    </select>
    
    <select id="selectCityById" parameterType="Long" resultMap="CityResult">
        <include refid="selectCityVo"/>
        where id = #{id}
    </select>

    <select id="queryProvinceByValue" resultType="com.ruoyi.system.domain.City">
        SELECT * FROM city where parent = 86 and value = #{province}
    </select>
    <select id="queryCityByProvince" resultType="com.ruoyi.system.domain.City">
        select * from city
        WHERE parent = #{province} and value = #{city}
    </select>
    <select id="queryDistrictByCity" resultType="com.ruoyi.system.domain.City">
        select * from city
        WHERE parent = #{city} and value = #{district}
    </select>

    <insert id="insertCity" parameterType="City" useGeneratedKeys="true" keyProperty="id">
        insert into city
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="parent != null">parent,</if>
            <if test="value != null">value,</if>
            <if test="text != null">text,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="parent != null">#{parent},</if>
            <if test="value != null">#{value},</if>
            <if test="text != null">#{text},</if>
         </trim>
    </insert>

    <update id="updateCity" parameterType="City">
        update city
        <trim prefix="SET" suffixOverrides=",">
            <if test="parent != null">parent = #{parent},</if>
            <if test="value != null">value = #{value},</if>
            <if test="text != null">text = #{text},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCityById" parameterType="Long">
        delete from city where id = #{id}
    </delete>

    <delete id="deleteCityByIds" parameterType="String">
        delete from city where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>