<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.thali.mapper.CameraThaliMapper">
    
    <resultMap type="CameraThali" id="CameraThaliResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <!--<result property="num"    column="num"    />-->
        <result property="pay"    column="pay"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectCameraThaliVo">
        select id, name, /*num,*/ pay, create_by, create_time, update_by, update_time, remark from camera_thali
    </sql>

    <select id="selectCameraThaliList" parameterType="CameraThali" resultMap="CameraThaliResult">
        <include refid="selectCameraThaliVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <!--<if test="num != null "> and num = #{num}</if>-->
            <if test="pay != null "> and pay = #{pay}</if>
            <if test="createTime != null "> and create_time = #{createTime}</if>
        </where>
    </select>
    
    <select id="selectCameraThaliById" parameterType="Long" resultMap="CameraThaliResult">
        <include refid="selectCameraThaliVo"/>
        where id = #{id}
    </select>
    <select id="selectPackageByDeciceId" resultType="com.ruoyi.thali.domain.CameraThali">
        select * from camera_thali where device_id = #{deviceId}
    </select>


    <insert id="insertCameraThali" parameterType="CameraThali" useGeneratedKeys="true" keyProperty="id">
        insert into camera_thali
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <!--<if test="num != null">num,</if>-->
            <if test="pay != null">pay,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <!--<if test="num != null">#{num},</if>-->
            <if test="pay != null">#{pay},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateCameraThali" parameterType="CameraThali">
        update camera_thali
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <!--<if test="num != null">num = #{num},</if>-->
            <if test="pay != null">pay = #{pay},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updatePrice" parameterType="CameraThali">
    update camera_thali
    set twod_photo = #{twodPhoto},comic_king = #{comicKing},four_sticker = #{fourSticker},five_sticker = #{fiveSticker},six_sticker = #{sixSticker},
        eight_sticker = #{eightSticker},one_album = #{oneAlbum},two_album = #{twoAlbum},three_album = #{threeAlbum},
        four_album = #{fourAlbum},identification = #{identification},uploading = #{uploading},photo_overprint = #{photoOverprint},
        stencil_overprint = #{stencilOverprint}
    where device_id = #{deviceId}
    </update>

    <insert id="updateCameraThaliById" parameterType="CameraThali">
        INSERT INTO camera_thali (device_id, twod_photo, comic_king, four_sticker, five_sticker, six_sticker, eight_sticker, one_album, two_album, three_album, four_album, identification, uploading, photo_overprint, stencil_overprint,create_time,remark)
        VALUES (#{deviceId}, #{twodPhoto}, #{comicKing}, #{fourSticker}, #{fiveSticker}, #{sixSticker}, #{eightSticker}, #{oneAlbum}, #{twoAlbum}, #{threeAlbum}, #{fourAlbum}, #{identification}, #{uploading}, #{photoOverprint}, #{stencilOverprint},#{createTime},#{remark})
    </insert>

    <delete id="deleteCameraThaliById" parameterType="Long">
        delete from camera_thali where id = #{id}
    </delete>

    <delete id="deleteCameraThaliByIds" parameterType="String">
        delete from camera_thali where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>