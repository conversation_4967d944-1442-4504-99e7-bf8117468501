package com.ruoyi.po;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/4/20 13:47
 * 四要素实时代付接口
 */
@Data
public class DaiFuOrder extends RequestModel {
    private Integer id;
    private String outTradeNo;
    private String orderId;
    private String bankResNo;
    private Integer dfMchId;
    private Integer userId;
    private String payCompanyCode;
    private String name;
    private String cardNo;
    private String purpose;
    private BigDecimal amount;
    private String idCardNo;
    private String summary;
    private String phoneNo;
    private String businessType;
    private BigDecimal daiFuBalance;
    private BigDecimal feeValue;
    private Integer status;
    private BigDecimal profit;
    private String resultMsg;
    private String notifyUrl;
    private Integer notifyStatus;
    private String notifyResponse;
    private Date createTime;
    private Date updateTime;

    /** 非实体类参数*/
    private String adminKey;
    private String dfMerchantId;
    //订单状态 00：成功  10：处理中  20：失败
    private Object daiFuStatus;

    //代付支付公司code
    private String daiFuPayCompanyCode;

    private String bankName;    //银行名称
    private String contactLine; // 联行号，跨行必填
    private String bankAddress;// 银行地址，填城市即可，跨行必填
}
