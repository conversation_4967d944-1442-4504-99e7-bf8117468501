package com.ruoyi.order.service.impl;

import java.util.*;
import java.util.concurrent.TimeUnit;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.constant.Constant;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.oss.ALY_OSS;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.order.domain.OrderPrinterTask;
import com.ruoyi.order.mapper.OrderPrinterTaskMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.order.mapper.OrderPrinterMapper;
import com.ruoyi.order.domain.OrderPrinter;
import com.ruoyi.order.service.IOrderPrinterService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 订单打印机服务实现
 * 
 * <AUTHOR>
 * @date 2024-06-15
 */
@Service
public class OrderPrinterServiceImpl extends ServiceImpl<OrderPrinterMapper, OrderPrinter> implements IOrderPrinterService
{
    private static final Logger log = LoggerFactory.getLogger(OrderPrinterServiceImpl.class);

    @Autowired
    private OrderPrinterMapper orderPrinterMapper;
    
    @Autowired
    private OrderPrinterTaskMapper orderPrinterTaskMapper;
    
    @Autowired
    private RedisCache redisCache;
    


    /**
     * 查询订单打印机
     * 
     * @param orderId 订单打印机主键
     * @return 订单打印机
     */
    @Override
    public OrderPrinter selectOrderPrinterByOrderId(String orderId)
    {
        return orderPrinterMapper.selectOrderPrinterByOrderId(orderId);
    }

    /**
     * 查询订单打印机列表
     * 
     * @param orderPrinter 订单打印机
     * @return 订单打印机
     */
    @Override
    public List<OrderPrinter> selectOrderPrinterList(OrderPrinter orderPrinter)
    {
        return orderPrinterMapper.selectOrderPrinterList(orderPrinter);
    }

    /**
     * 新增订单打印机
     * 
     * @param orderPrinter 订单打印机
     * @return 结果
     */
    @Override
    public int insertOrderPrinter(OrderPrinter orderPrinter)
    {
        if (StringUtils.isNull(orderPrinter.getCreateTime())) {
            orderPrinter.setCreateTime(DateUtils.getNowDate());
        }
        if (orderPrinter.getStatus() == null) {
            orderPrinter.setStatus(0); // 默认状态为正常
        }
        return orderPrinterMapper.insertOrderPrinter(orderPrinter);
    }

    /**
     * 修改订单打印机
     * 
     * @param orderPrinter 订单打印机
     * @return 结果
     */
    @Override
    public int updateOrderPrinter(OrderPrinter orderPrinter)
    {
        orderPrinter.setUpdateTime(DateUtils.getNowDate());
        return orderPrinterMapper.updateOrderPrinter(orderPrinter);
    }

    /**
     * 批量删除订单打印机
     * 
     * @param orderIds 需要删除的订单打印机主键
     * @return 结果
     */
    @Override
    public int deleteOrderPrinterByOrderIds(String[] orderIds)
    {
        return orderPrinterMapper.deleteOrderPrinterByOrderIds(orderIds);
    }

    /**
     * 删除订单打印机信息
     * 
     * @param orderId 订单打印机主键
     * @return 结果
     */
    @Override
    public int deleteOrderPrinterByOrderId(String orderId)
    {
        return orderPrinterMapper.deleteOrderPrinterByOrderId(orderId);
    }
    
    /**
     * 创建打印预订单
     *
     * @param params 预订单参数
     * @return 预订单信息
     */
    @Override
    @Transactional
    public Map<String, Object> createPreOrder(Map<String, Object> params) {
        // 生成订单ID
        String orderId = IdUtils.fastSimpleUUID();
        
        // 获取参数
        String deviceId = (String) params.get("deviceId");
        String deviceName = (String) params.get("deviceName");
        String openid = (String) params.get("openid");
        String phone = (String) params.get("phone");
        Long userId = params.get("userId") != null ? Long.valueOf(params.get("userId").toString()) : null;
        Long roleId = params.get("roleId") != null ? Long.valueOf(params.get("roleId").toString()) : null;
        Integer payWay = params.get("payWay") != null ? Integer.valueOf(params.get("payWay").toString()) : null;
        
        // 创建订单
        OrderPrinter orderPrinter = new OrderPrinter();
        orderPrinter.setOrderId(orderId);
        orderPrinter.setDeviceId(deviceId);
        orderPrinter.setDeviceName(deviceName);
        orderPrinter.setOpenid(openid);
        orderPrinter.setPhone(phone);
        orderPrinter.setUserId(userId);
        orderPrinter.setRoleId(roleId);
        orderPrinter.setOrderStatus(0); // 未支付
        orderPrinter.setHide(0);

        // 设置支付方式（如果提供了的话）
        if (payWay != null) {
            orderPrinter.setPayWay(payWay);
        }
        
        // 保存订单
        insertOrderPrinter(orderPrinter);
        
        // 如果有文件信息，创建任务
        if (params.containsKey("fileUrl") && params.get("fileUrl") != null) {
            String fileUrl = (String) params.get("fileUrl");
            String fileName = (String) params.get("fileName");
            String originalFileName = (String) params.get("originalFileName");
            // 如果没有传递originalFileName，使用fileName作为默认值
            if (StringUtils.isEmpty(originalFileName)) {
                originalFileName = fileName;
            }
            String fileType = (String) params.get("fileType");
            Long fileSize = params.get("fileSize") != null ? Long.valueOf(params.get("fileSize").toString()) : null;
            Integer copies = params.get("copies") != null ? Integer.valueOf(params.get("copies").toString()) : 1;
            Integer colorMode = params.get("colorMode") != null ? Integer.valueOf(params.get("colorMode").toString()) : 0;
            Integer duplexMode = params.get("duplexMode") != null ? Integer.valueOf(params.get("duplexMode").toString()) : 0;
            Integer paperType = params.get("paperType") != null ? Integer.valueOf(params.get("paperType").toString()) : 1;
            String pageRange = (String) params.get("pageRange");
            
            // 创建打印任务
            OrderPrinterTask task = new OrderPrinterTask();
            task.setTaskId(IdUtils.fastSimpleUUID());
            task.setOrderId(orderId);
            task.setDeviceId(deviceId);
            task.setFileUrl(fileUrl);
            task.setFileName(fileName);
            task.setOriginalFileName(originalFileName);
            task.setFileType(fileType);
            task.setFileSize(fileSize);
            task.setPrintStatus(0); // 待打印
            task.setCopies(copies);
            task.setColorMode(colorMode);
            task.setDuplexMode(duplexMode);
            task.setPaperType(paperType);
            task.setPageRange(pageRange);
            task.setRetryCount(0);
            
            // 保存任务
            orderPrinterTaskMapper.insertOrderPrinterTask(task);
        }
        
        // 缓存订单信息，设置30分钟过期
        redisCache.setCacheObject("print_order:" + orderId, orderPrinter, 30, TimeUnit.MINUTES);
        
        // 返回预订单信息
        Map<String, Object> result = new HashMap<>();
        result.put("orderId", orderId);
        result.put("deviceId", deviceId);
        result.put("deviceName", deviceName);
        
        return result;
    }
    
    /**
     * 支付成功后处理
     *
     * @param orderId 订单ID
     * @param transactionId 交易ID
     * @return 结果
     */
    @Override
    @Transactional
    public boolean paySuccess(String orderId, String transactionId) {
        // 查询订单
        OrderPrinter orderPrinter = selectOrderPrinterByOrderId(orderId);
        if (orderPrinter == null) {
            return false;
        }
        
        // 更新订单状态
        orderPrinter.setOrderStatus(1); // 已支付
        orderPrinter.setTransactionId(transactionId);
        orderPrinter.setPayTime(DateUtils.getNowDate());
        updateOrderPrinter(orderPrinter);
        
        // 通过WebSocket通知设备支付成功
        try {
            Map<String, Object> message = new HashMap<>();
            message.put("type", "zfcg"); // 支付成功
            message.put("orderId", orderId);
            message.put("deviceId", orderPrinter.getDeviceId());
            
//            webSocketServer.sendMessageToDevice(orderPrinter.getDeviceId(), message.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        
        return true;
    }
    
    /**
     * 添加打印任务
     *
     * @param task 打印任务
     * @return 结果
     */
    @Override
    public int addPrintTask(OrderPrinterTask task) {
        if (StringUtils.isEmpty(task.getTaskId())) {
            task.setTaskId(IdUtils.fastSimpleUUID());
        }
        if (task.getPrintStatus() == null) {
            task.setPrintStatus(0); // 默认待打印
        }
        if (task.getRetryCount() == null) {
            task.setRetryCount(0);
        }
        if (task.getStatus() == null) {
            task.setStatus(0); // 默认状态为正常
        }
        return orderPrinterTaskMapper.insertOrderPrinterTask(task);
    }
    
    /**
     * 查询订单的打印任务
     *
     * @param orderId 订单ID
     * @return 打印任务列表
     */
    @Override
    public List<OrderPrinterTask> getOrderTasks(String orderId) {
        return orderPrinterTaskMapper.selectOrderPrinterTasksByOrderId(orderId);
    }
    
    /**
     * 更新打印任务状态
     *
     * @param taskId 任务ID
     * @param status 状态
     * @param errorMsg 错误信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updateTaskStatus(String taskId, Integer status, String errorMsg) {
        // 查询任务
        OrderPrinterTask task = orderPrinterTaskMapper.selectOrderPrinterTaskByTaskId(taskId);
        if (task == null) {
            return 0;
        }
        
        // 更新任务状态
        task.setPrintStatus(status);
        if (StringUtils.isNotEmpty(errorMsg)) {
            task.setErrorMsg(errorMsg);
        }
        
        // 如果是开始打印
        if (status == 1) {
            task.setStartTime(DateUtils.getNowDate());
        }
        // 如果是打印完成或失败
        else if (status == 2 || status == 3) {
            task.setEndTime(DateUtils.getNowDate());
            
            // 更新订单状态
            OrderPrinter orderPrinter = selectOrderPrinterByOrderId(task.getOrderId());
            if (orderPrinter != null) {
                // 如果是打印完成，更新订单状态为已打印
                if (status == 2) {
                    orderPrinter.setOrderStatus(4); // 已打印
                    orderPrinter.setPrintTime(DateUtils.getNowDate());
                }
                // 如果是打印失败，更新订单状态为打印失败
                else if (status == 3) {
                    orderPrinter.setOrderStatus(6); // 打印失败
                }
                updateOrderPrinter(orderPrinter);
            }
        }
        
        return orderPrinterTaskMapper.updateOrderPrinterTask(task);
    }

    /**
     * 创建订单（不包含文件）
     *
     * @param params 订单参数
     * @return 订单信息
     */
    @Override
    @Transactional
    public Map<String, Object> createOrder(Map<String, Object> params) {
        // 生成订单ID
        String orderId = IdUtils.fastSimpleUUID();

        // 获取参数
        String deviceId = (String) params.get("deviceId");
        String deviceName = (String) params.get("deviceName");
        String openid = (String) params.get("openid");
        String phone = (String) params.get("phone");
        Long userId = params.get("userId") != null ? Long.valueOf(params.get("userId").toString()) : null;
        Long roleId = params.get("roleId") != null ? Long.valueOf(params.get("roleId").toString()) : null;
        Integer payWay = params.get("payWay") != null ? Integer.valueOf(params.get("payWay").toString()) : null;

        // 创建订单
        OrderPrinter orderPrinter = new OrderPrinter();
        orderPrinter.setOrderId(orderId);
        orderPrinter.setDeviceId(deviceId);
        orderPrinter.setDeviceName(deviceName);
        orderPrinter.setOpenid(openid);
        orderPrinter.setPhone(phone);
        orderPrinter.setUserId(userId);
        orderPrinter.setRoleId(roleId);
        orderPrinter.setOrderStatus(0); // 未支付
        orderPrinter.setHide(0);

        // 设置支付方式（如果提供了的话）
        if (payWay != null) {
            orderPrinter.setPayWay(payWay);
        }

      

        // 保存订单
        insertOrderPrinter(orderPrinter);

        // 缓存订单信息，设置30分钟过期
        redisCache.setCacheObject("print_order:" + orderId, orderPrinter, 30, TimeUnit.MINUTES);

        // 返回订单信息
        Map<String, Object> result = new HashMap<>();
        result.put("orderId", orderId);
        result.put("deviceId", deviceId);
        result.put("deviceName", deviceName);

        return result;
    }

    /**
     * 上传文件并计算价格
     *
     * @param orderId 订单ID
     * @param fileParams 文件和打印参数
     * @return 任务信息和价格
     */
    @Override
    @Transactional
    public Map<String, Object> uploadFileAndCalculatePrice(String orderId, Map<String, Object> fileParams) {
        // 验证订单是否存在
        OrderPrinter orderPrinter = selectOrderPrinterByOrderId(orderId);
        if (orderPrinter == null) {
            throw new RuntimeException("订单不存在");
        }

        // 获取文件参数
        String fileUrl = (String) fileParams.get("fileUrl");
        String fileName = (String) fileParams.get("fileName"); // OSS中的文件路径
        String originalFileName = (String) fileParams.get("originalFileName"); // 用户上传的原始文件名
        // 如果没有传递originalFileName，使用fileName作为默认值（向后兼容）
        if (StringUtils.isEmpty(originalFileName)) {
            originalFileName = fileName;
        }
        String fileType = (String) fileParams.get("fileType");
        Long fileSize = fileParams.get("fileSize") != null ? Long.valueOf(fileParams.get("fileSize").toString()) : null;
        Integer copies = fileParams.get("copies") != null ? Integer.valueOf(fileParams.get("copies").toString()) : 1;
        Integer colorMode = fileParams.get("colorMode") != null ? Integer.valueOf(fileParams.get("colorMode").toString()) : 0;
        Integer duplexMode = fileParams.get("duplexMode") != null ? Integer.valueOf(fileParams.get("duplexMode").toString()) : 0;
        Integer paperType = fileParams.get("paperType") != null ? Integer.valueOf(fileParams.get("paperType").toString()) : 1;
        String pageRange = (String) fileParams.get("pageRange");
        Integer isPhoto = fileParams.get("isPhoto") != null ? Integer.valueOf(fileParams.get("isPhoto").toString()) : 0;
        String sizeSpec = (String) fileParams.get("sizeSpec");

        // 解析页码范围，计算页数
        int pageCount = parsePageRange(pageRange);

        // 计算打印价格
        Double taskPrice = calculatePrintPrice(pageCount, copies, colorMode, duplexMode, paperType, orderPrinter.getDeviceId());

        // 创建打印任务
        OrderPrinterTask task = new OrderPrinterTask();
        task.setTaskId(IdUtils.fastSimpleUUID());
        task.setOrderId(orderId);
        task.setDeviceId(orderPrinter.getDeviceId());
        task.setFileUrl(fileUrl);
        task.setFileName(fileName);
        task.setOriginalFileName(originalFileName);
        task.setFileType(fileType);
        task.setFileSize(fileSize);
        task.setPrintStatus(0); // 待打印
        task.setStatus(0); // 默认状态为正常
        task.setCopies(copies);
        task.setColorMode(colorMode);
        task.setDuplexMode(duplexMode);
        task.setPaperType(paperType);
        task.setPageRange(pageRange);
        task.setTaskPrice(taskPrice);
        task.setRetryCount(0);
        task.setIsPhoto(isPhoto);
        task.setSizeSpec(sizeSpec);

        // 保存任务
        orderPrinterTaskMapper.insertOrderPrinterTask(task);

        // 返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("taskId", task.getTaskId());
        result.put("taskPrice", taskPrice);
        result.put("pageCount", pageCount);
        result.put("fileName", fileName);
        result.put("fileUrl", fileUrl);

        return result;
    }

    /**
     * 计算打印价格
     *
     * @param pageCount 页数
     * @param copies 份数
     * @param colorMode 颜色模式 0-黑白 1-彩色
     * @param duplexMode 双面模式 0-单面 1-双面
     * @param paperType 纸张类型 1-A4 2-A5 3-照片纸
     * @param deviceId 设备ID
     * @return 价格（元）
     */
    @Override
    public Double calculatePrintPrice(int pageCount, int copies, int colorMode, int duplexMode, int paperType, String deviceId) {
        // 基础价格配置（单位：元）
        Double basePrice = 0.0;

        // 根据纸张类型设置基础价格
        switch (paperType) {
            case 1: // A4
                basePrice = colorMode == 1 ? 0.5 : 0.2; // 彩色0.5元/页，黑白0.2元/页
                break;
            case 2: // A5
                basePrice = colorMode == 1 ? 0.3 : 0.15; // 彩色0.3元/页，黑白0.15元/页
                break;
            case 3: // 照片纸
                basePrice = 2.0; // 照片纸2元/页
                break;
            default:
                basePrice = colorMode == 1 ? 0.5 : 0.2;
                break;
        }

        // 双面打印折扣（双面打印每页按1.5倍计算，相比单面打印节省成本）
        if (duplexMode == 1) {
            basePrice = basePrice * 1.5;
        }

        // 计算总价格：基础价格 * 页数 * 份数
        Double totalPrice = basePrice * pageCount * copies;

        // 可以根据设备ID获取特殊价格配置（预留扩展）
        // DevicePrinter device = devicePrinterService.selectDevicePrinterByDeviceId(deviceId);
        // if (device != null && device.getPricePerPage() != null) {
        //     totalPrice = device.getPricePerPage() * pageCount * copies;
        // }

        // 保留两位小数
        return Math.round(totalPrice * 100.0) / 100.0;
    }

    /**
     * 解析页码范围，计算总页数
     *
     * @param pageRange 页码范围，如"1-3,5,7-9"
     * @return 总页数
     */
    @Override
    public int parsePageRange(String pageRange) {
        if (StringUtils.isEmpty(pageRange)) {
            return 1; // 默认1页
        }

        // 如果是"all"或"*"，默认按10页计算（实际应该根据文件解析）
        if ("all".equalsIgnoreCase(pageRange) || "*".equals(pageRange)) {
            return 10;
        }

        int totalPages = 0;
        String[] ranges = pageRange.split(",");

        for (String range : ranges) {
            range = range.trim();
            if (range.contains("-")) {
                // 处理范围，如"1-3"
                String[] parts = range.split("-");
                if (parts.length == 2) {
                    try {
                        int start = Integer.parseInt(parts[0].trim());
                        int end = Integer.parseInt(parts[1].trim());
                        if (start <= end) {
                            totalPages += (end - start + 1);
                        }
                    } catch (NumberFormatException e) {
                        // 忽略无效格式
                    }
                }
            } else {
                // 处理单页，如"5"
                try {
                    Integer.parseInt(range);
                    totalPages += 1;
                } catch (NumberFormatException e) {
                    // 忽略无效格式
                }
            }
        }

        return totalPages > 0 ? totalPages : 1;
    }

    /**
     * 计算订单总金额
     *
     * @param orderId 订单ID
     * @return 总金额（元）
     */
    @Override
    public Double calculateOrderTotalAmount(String orderId) {
        if (StringUtils.isEmpty(orderId)) {
            return 0.0;
        }

        // 获取订单的所有任务
        List<OrderPrinterTask> tasks = getOrderTasks(orderId);
        if (tasks == null || tasks.isEmpty()) {
            return 0.0;
        }

        // 计算总金额
        Double totalAmount = tasks.stream()
                .filter(task -> task.getTaskPrice() != null)
                .mapToDouble(OrderPrinterTask::getTaskPrice)
                .sum();

        // 保留两位小数
        return Math.round(totalAmount * 100.0) / 100.0;
    }

    /**
     * 查询用户订单列表（包含总金额）- 性能优化版本
     *
     * @param openid 用户openid
     * @return 订单列表（包含总金额）
     */
    @Override
    public List<OrderPrinter> selectOrderPrinterListWithTotalAmount(String openid) {
        return orderPrinterMapper.selectOrderPrinterListWithTotalAmount(openid);
    }

    /**
     * 查询用户订单列表（支持状态筛选）
     *
     * @param openid 用户openid
     * @param payStatus 支付状态筛选：1-已支付，3-已退款，null-不筛选
     * @param printStatus 打印状态筛选：pending-待打印，completed-已完成，null-不筛选
     * @return 订单列表（包含总金额和打印状态）
     */
    @Override
    public List<OrderPrinter> selectOrderPrinterListWithStatusFilter(String openid, Integer payStatus, String printStatus) {
        return orderPrinterMapper.selectOrderPrinterListWithStatusFilter(openid, payStatus, printStatus);
    }

    /**
     * 删除用户的所有订单数据（逻辑删除）
     * @param openid 用户openid
     * @return 删除结果
     */
    @Override
    public boolean deleteUserOrdersByOpenid(String openid) {
        log.info("开始删除用户订单数据 - openid: {}", openid);

        if (StringUtils.isEmpty(openid)) {
            log.error("删除用户订单失败: openid不能为空");
            return false;
        }

        try {
            // 1. 查询用户的所有订单（Mapper.xml已自动过滤hide=0）
            OrderPrinter queryOrder = new OrderPrinter();
            queryOrder.setOpenid(openid);
            List<OrderPrinter> userOrders = orderPrinterMapper.selectOrderPrinterList(queryOrder);
            if (userOrders == null || userOrders.isEmpty()) {
                log.info("用户没有订单数据需要删除 - openid: {}", openid);
                return true; // 没有数据也算成功
            }

            log.info("找到{}个订单需要删除 - openid: {}", userOrders.size(), openid);

            // 2. 逻辑删除订单（设置status=1）
            int orderUpdateCount = 0;
            for (OrderPrinter order : userOrders) {
                order.setStatus(1); // 设置为删除状态（逻辑删除）
                int updateResult = orderPrinterMapper.updateOrderPrinter(order);
                if (updateResult > 0) {
                    orderUpdateCount++;
                }
            }

            // 3. 逻辑删除订单关联的任务（设置status=1）
            int taskUpdateCount = 0;
            for (OrderPrinter order : userOrders) {
                // 查询订单的所有任务（Mapper.xml已自动过滤status=0）
                List<OrderPrinterTask> tasks = orderPrinterTaskMapper.selectOrderPrinterTasksByOrderId(order.getOrderId());
                if (tasks != null && !tasks.isEmpty()) {
                    for (OrderPrinterTask task : tasks) {
                        task.setStatus(1); // 设置为删除状态
                        int updateResult = orderPrinterTaskMapper.updateOrderPrinterTask(task);
                        if (updateResult > 0) {
                            taskUpdateCount++;
                        }
                    }
                }
            }

            log.info("用户订单数据删除完成 - openid: {}, 订单数: {}/{}, 任务数: {}",
                    openid, orderUpdateCount, userOrders.size(), taskUpdateCount);

            // 只要订单逻辑删除成功就算成功
            return orderUpdateCount == userOrders.size();

        } catch (Exception e) {
            log.error("删除用户订单数据异常 - openid: {}, error: {}", openid, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 更新打印任务信息
     * @param task 打印任务对象
     * @return 更新结果
     */
    @Override
    public int updateTask(OrderPrinterTask task) {
        if (task == null || StringUtils.isEmpty(task.getTaskId())) {
            log.error("更新任务失败: 任务对象或任务ID为空");
            return 0;
        }

        // 设置更新时间
        task.setUpdateTime(DateUtils.getNowDate());

        // 更新任务
        int result = orderPrinterTaskMapper.updateOrderPrinterTask(task);

        if (result > 0) {
            log.info("任务更新成功: taskId={}, price={}", task.getTaskId(), task.getTaskPrice());
        } else {
            log.error("任务更新失败: taskId={}", task.getTaskId());
        }

        return result;
    }
}