<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信支付测试页面</title>
    <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #07c160;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover {
            background-color: #06ad56;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            display: none;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .qr-code {
            text-align: center;
            margin-top: 20px;
        }
        #qrcode {
            margin: 20px auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>微信支付测试页面</h1>
        
        <div class="form-group">
            <label for="totalAmount">支付金额（分）:</label>
            <input type="number" id="totalAmount" value="100" min="1">
        </div>
        
        <div class="form-group">
            <label for="openId">用户OpenID:</label>
            <input type="text" id="openId" placeholder="请输入微信用户OpenID">
        </div>
        
        <div class="form-group">
            <label for="outTradeNo">商户订单号:</label>
            <input type="text" id="outTradeNo" placeholder="自动生成" readonly>
        </div>
        
        <div class="form-group">
            <label for="body">商品描述:</label>
            <input type="text" id="body" value="测试商品支付">
        </div>
        
        <div style="text-align: center;">
            <button onclick="generateOrderNo()">生成订单号</button>
            <button onclick="createJsapiPay()">JSAPI支付</button>
            <button onclick="createNativePay()">Native支付</button>
            <button onclick="queryOrder()">查询订单</button>
            <button onclick="batchQueryOrders()">批量查询</button>
            <button onclick="startAutoQuery()">开始自动查询</button>
            <button onclick="stopAutoQuery()">停止自动查询</button>
        </div>

        <div class="form-group">
            <label for="batchOrderNos">批量查询订单号（逗号分隔）:</label>
            <textarea id="batchOrderNos" rows="3" placeholder="请输入多个订单号，用逗号分隔"></textarea>
        </div>

        <div class="form-group">
            <label>
                <input type="checkbox" id="autoQuery"> 自动查询支付状态（每5秒查询一次）
            </label>
        </div>
        
        <div id="result" class="result"></div>
        
        <div id="qrCodeContainer" class="qr-code" style="display: none;">
            <h3>扫码支付</h3>
            <div id="qrcode"></div>
            <p>请使用微信扫描上方二维码完成支付</p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <script>
        // 生成订单号
        function generateOrderNo() {
            const timestamp = Date.now();
            const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
            const orderNo = 'WX' + timestamp + random;
            document.getElementById('outTradeNo').value = orderNo;
        }

        // 显示结果
        function showResult(message, isSuccess = true) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = 'result ' + (isSuccess ? 'success' : 'error');
            resultDiv.innerHTML = message;
            resultDiv.style.display = 'block';
        }

        // 隐藏二维码
        function hideQrCode() {
            document.getElementById('qrCodeContainer').style.display = 'none';
        }

        // 显示二维码
        function showQrCode(codeUrl) {
            const qrCodeContainer = document.getElementById('qrCodeContainer');
            const qrCodeDiv = document.getElementById('qrcode');
            
            // 清空之前的二维码
            qrCodeDiv.innerHTML = '';
            
            // 生成新的二维码
            QRCode.toCanvas(qrCodeDiv, codeUrl, {
                width: 256,
                height: 256,
                margin: 2
            }, function (error) {
                if (error) {
                    console.error('生成二维码失败:', error);
                    showResult('生成二维码失败: ' + error.message, false);
                } else {
                    qrCodeContainer.style.display = 'block';
                }
            });
        }

        // 创建JSAPI支付
        function createJsapiPay() {
            const totalAmount = document.getElementById('totalAmount').value;
            const openId = document.getElementById('openId').value;
            const outTradeNo = document.getElementById('outTradeNo').value;
            const body = document.getElementById('body').value;

            if (!totalAmount || !openId || !outTradeNo) {
                showResult('请填写完整的支付信息', false);
                return;
            }

            hideQrCode();

            fetch('/client/wxapp/wxpay/jsapi', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    totalAmount: totalAmount,
                    openId: openId,
                    outTradeNo: outTradeNo,
                    body: body
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    showResult('JSAPI支付订单创建成功！<br>支付参数: ' + JSON.stringify(data.data, null, 2));
                    
                    // 在微信环境中可以调用微信支付
                    if (typeof WeixinJSBridge !== 'undefined') {
                        callWxPay(data.data);
                    } else {
                        showResult('请在微信环境中使用JSAPI支付', false);
                    }
                } else {
                    showResult('创建支付订单失败: ' + data.msg, false);
                }
            })
            .catch(error => {
                console.error('请求失败:', error);
                showResult('请求失败: ' + error.message, false);
            });
        }

        // 创建Native支付
        function createNativePay() {
            const totalAmount = document.getElementById('totalAmount').value;
            const outTradeNo = document.getElementById('outTradeNo').value;
            const body = document.getElementById('body').value;

            if (!totalAmount || !outTradeNo) {
                showResult('请填写完整的支付信息', false);
                return;
            }

            fetch('/client/wxapp/wxpay/native', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    totalAmount: totalAmount,
                    outTradeNo: outTradeNo,
                    body: body
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    showResult('Native支付二维码生成成功！');
                    showQrCode(data.data.codeUrl);
                } else {
                    showResult('生成支付二维码失败: ' + data.msg, false);
                    hideQrCode();
                }
            })
            .catch(error => {
                console.error('请求失败:', error);
                showResult('请求失败: ' + error.message, false);
                hideQrCode();
            });
        }

        // 查询订单
        function queryOrder() {
            const outTradeNo = document.getElementById('outTradeNo').value;

            if (!outTradeNo) {
                showResult('请输入订单号', false);
                return;
            }

            hideQrCode();

            fetch('/client/wxapp/wxpay/query/' + outTradeNo)
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    showResult('订单查询成功！<br>订单信息: ' + JSON.stringify(data.data, null, 2));
                } else {
                    showResult('查询订单失败: ' + data.msg, false);
                }
            })
            .catch(error => {
                console.error('请求失败:', error);
                showResult('请求失败: ' + error.message, false);
            });
        }

        // 调用微信支付
        function callWxPay(payParams) {
            WeixinJSBridge.invoke('getBrandWCPayRequest', payParams, function(res) {
                if (res.err_msg === "get_brand_wcpay_request:ok") {
                    showResult('支付成功！');
                } else if (res.err_msg === "get_brand_wcpay_request:cancel") {
                    showResult('用户取消支付', false);
                } else {
                    showResult('支付失败: ' + res.err_msg, false);
                }
            });
        }

        // 页面加载时生成订单号
        window.onload = function() {
            generateOrderNo();
        };
    </script>
</body>
</html>
