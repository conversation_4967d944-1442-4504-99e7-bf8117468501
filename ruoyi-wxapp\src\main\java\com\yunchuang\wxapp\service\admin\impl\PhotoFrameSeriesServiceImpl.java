package com.yunchuang.wxapp.service.admin.impl;

import com.ruoyi.common.utils.DateUtils;
import com.yunchuang.wxapp.mapper.PhotoFrameSeriesMapper;
import com.yunchuang.wxapp.model.domain.PhotoFrameSeries;
import com.yunchuang.wxapp.service.admin.IPhotoFrameSeriesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 相框系列Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-06
 */
@Service
public class PhotoFrameSeriesServiceImpl implements IPhotoFrameSeriesService {
    @Autowired
    private PhotoFrameSeriesMapper photoFrameSeriesMapper;

    /**
     * 查询相框系列
     *
     * @param id 相框系列主键
     * @return 相框系列
     */
    @Override
    public PhotoFrameSeries selectPhotoFrameSeriesById(Long id) {
        return photoFrameSeriesMapper.selectPhotoFrameSeriesById(id);
    }

    /**
     * 查询相框系列列表
     *
     * @param photoFrameSeries 相框系列
     * @return 相框系列
     */
    @Override
    public List<PhotoFrameSeries> selectPhotoFrameSeriesList(PhotoFrameSeries photoFrameSeries) {
        return photoFrameSeriesMapper.selectPhotoFrameSeriesList(photoFrameSeries);
    }

    /**
     * 新增相框系列
     *
     * @param photoFrameSeries 相框系列
     * @return 结果
     */
    @Override
    public int insertPhotoFrameSeries(PhotoFrameSeries photoFrameSeries) {
        photoFrameSeries.setCreateTime(DateUtils.getNowDate());
        return photoFrameSeriesMapper.insertPhotoFrameSeries(photoFrameSeries);
    }

    /**
     * 修改相框系列
     *
     * @param photoFrameSeries 相框系列
     * @return 结果
     */
    @Override
    public int updatePhotoFrameSeries(PhotoFrameSeries photoFrameSeries) {
        photoFrameSeries.setUpdateTime(DateUtils.getNowDate());
        return photoFrameSeriesMapper.updatePhotoFrameSeries(photoFrameSeries);
    }

    /**
     * 批量删除相框系列
     *
     * @param ids 需要删除的相框系列主键
     * @return 结果
     */
    @Override
    public int deletePhotoFrameSeriesByIds(Long[] ids) {
        return photoFrameSeriesMapper.deletePhotoFrameSeriesByIds(ids);
    }

    /**
     * 删除相框系列信息
     *
     * @param id 相框系列主键
     * @return 结果
     */
    @Override
    public int deletePhotoFrameSeriesById(Long id) {
        return photoFrameSeriesMapper.deletePhotoFrameSeriesById(id);
    }
}
