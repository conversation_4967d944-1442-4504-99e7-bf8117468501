<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yunchuang.wxapp.mapper.UserMapper">

    <resultMap type="User" id="UserResult">
        <result property="id" column="id"/>
        <result property="avatar" column="avatar"/>
        <result property="nickname" column="nickname"/>
        <result property="shotNum" column="shot_num"/>
        <result property="lastTimeShot" column="last_time_shot"/>
        <result property="openid" column="openid"/>
        <result property="mobile" column="mobile"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectUserVo">
        select id,
               avatar,
               nickname,
               shot_num,
               last_time_shot,
               openid,
               mobile,
               status,
               create_by,
               create_time,
               update_by,
               update_time,
               remark
        from wxapp_user
    </sql>

    <select id="selectUserList" parameterType="User" resultMap="UserResult">
        <include refid="selectUserVo"/>
        <where>
            and status = 0
            <if test="nickname != null  and nickname != ''">and nickname like concat('%', #{nickname}, '%')</if>
            <if test="openid != null  and openid != ''">and openid = #{openid}</if>
        </where>
    </select>

    <select id="selectUserById" parameterType="Long" resultMap="UserResult">
        <include refid="selectUserVo"/>
        where id = #{id} and status = 0
    </select>

    <insert id="insertUser" parameterType="User" useGeneratedKeys="true" keyProperty="id">
        insert into wxapp_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="avatar != null and avatar != ''">avatar,</if>
            <if test="nickname != null and nickname != ''">nickname,</if>
            <if test="shotNum != null">shot_num,</if>
            <if test="lastTimeShot != null">last_time_shot,</if>
            <if test="openid != null and openid != ''">openid,</if>
            <if test="mobile != null and mobile != ''">mobile,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="avatar != null and avatar != ''">#{avatar},</if>
            <if test="nickname != null and nickname != ''">#{nickname},</if>
            <if test="shotNum != null">#{shotNum},</if>
            <if test="lastTimeShot != null">#{lastTimeShot},</if>
            <if test="openid != null and openid != ''">#{openid},</if>
            <if test="mobile != null and mobile != ''">#{mobile},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateUser" parameterType="User">
        update wxapp_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="avatar != null and avatar != ''">avatar = #{avatar},</if>
            <if test="nickname != null and nickname != ''">nickname = #{nickname},</if>
            <if test="shotNum != null">shot_num = #{shotNum},</if>
            <if test="lastTimeShot != null">last_time_shot = #{lastTimeShot},</if>
            <if test="openid != null and openid != ''">openid = #{openid},</if>
            <if test="mobile != null and mobile != ''">mobile = #{mobile},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id} and (status = 0 or #{status} is not null)
    </update>

    <delete id="deleteUserById" parameterType="Long">
        delete
        from wxapp_user
        where id = #{id}
    </delete>

    <delete id="deleteUserByIds" parameterType="String">
        delete from wxapp_user where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>