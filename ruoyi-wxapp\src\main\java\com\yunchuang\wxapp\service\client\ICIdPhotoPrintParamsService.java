package com.yunchuang.wxapp.service.client;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yunchuang.wxapp.model.domain.IdPhotoPrintParams;

import java.util.List;
import java.util.Map;

/**
 * 证件照打印参数 Service 接口
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
public interface ICIdPhotoPrintParamsService extends IService<IdPhotoPrintParams> {

    /**
     * 获取启用的证件照打印参数列表
     *
     * @return 证件照打印参数列表
     */
    List<IdPhotoPrintParams> getEnabledIdPhotoPrintParams();

    /**
     * 根据类型获取证件照打印参数
     *
     * @param type 类型
     * @return 证件照打印参数
     */
    IdPhotoPrintParams getIdPhotoPrintParamsByType(Integer type);

    /**
     * 获取启用状态的证件照打印参数并按类型分组
     *
     * @return 按类型分组的证件照打印参数
     */
    Map<String, List<IdPhotoPrintParams>> getEnabledParamsGroupByType();

}
