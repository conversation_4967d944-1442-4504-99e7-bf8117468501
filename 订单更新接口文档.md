# 订单更新接口文档

## 接口概述

本接口用于更新用户的打印订单信息，包括订单基本信息和订单明细（打印任务）的更新。支持两种调用方式：
1. 使用DTO对象（推荐）
2. 使用Map参数（兼容旧版本）

## 1. 使用DTO对象的接口（推荐）

### 接口地址
```
POST /order/printer/updateOrderWithDto
```

### 请求头
```
Content-Type: application/json
Authorization: Bearer {JWT_TOKEN}  // 可选，用于用户身份验证
```

### 请求参数（JSON格式）

```json
{
  "orderId": "订单ID",
  "deviceName": "新的设备名称",
  "phone": "新的手机号",
  "voucherCode": "优惠券码",
  "remark": "备注信息",
  "taskUpdates": [
    {
      "taskId": "任务ID",
      "copies": 2,
      "colorMode": 1,
      "duplexMode": 0,
      "paperType": 1,
      "pageRange": "1-5"
    }
  ]
}
```

### 参数说明

#### 订单基本信息字段
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | String | 是 | 订单ID |
| deviceName | String | 否 | 设备名称 |
| phone | String | 否 | 用户手机号 |
| voucherCode | String | 否 | 优惠券码 |
| remark | String | 否 | 备注信息 |

#### 任务更新信息字段
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| taskId | String | 是 | 任务ID |
| copies | Integer | 否 | 打印份数（必须大于0） |
| colorMode | Integer | 否 | 颜色模式：0-黑白，1-彩色 |
| duplexMode | Integer | 否 | 双面模式：0-单面，1-双面 |
| paperType | Integer | 否 | 纸张类型：1-A4，2-A5，3-照片纸 |
| pageRange | String | 否 | 页码范围，如"1-3,5,7-9" |

### 响应格式

#### 成功响应
```json
{
  "code": 200,
  "msg": "订单更新成功",
  "data": {
    "orderId": "订单ID",
    "orderUpdate": {
      "deviceName": "已更新",
      "phone": "已更新",
      "updateCount": 1
    },
    "taskUpdate": {
      "successCount": 1,
      "failCount": 0,
      "errors": []
    },
    "totalAmount": 1.5
  }
}
```

#### 失败响应
```json
{
  "code": 500,
  "msg": "订单更新失败: 具体错误信息"
}
```

## 2. 使用Map参数的接口（兼容旧版本）

### 接口地址
```
POST /order/printer/updateOrder
```

### 请求参数（JSON格式）
```json
{
  "orderId": "订单ID",
  "deviceName": "新的设备名称",
  "phone": "新的手机号",
  "voucherCode": "优惠券码",
  "remark": "备注信息",
  "taskUpdates": [
    {
      "taskId": "任务ID",
      "copies": 2,
      "colorMode": 1,
      "duplexMode": 0,
      "paperType": 1,
      "pageRange": "1-5"
    }
  ]
}
```

## 业务规则

### 权限验证
1. 如果请求中包含JWT Token，会验证用户身份
2. 只有订单所属用户才能更新订单
3. 未登录用户无法更新订单

### 订单状态限制
1. 已取消的订单（orderStatus=2）不允许更新
2. 已退款的订单（orderStatus=3）不允许更新
3. 其他状态的订单可以更新

### 任务状态限制
1. 已完成的任务（printStatus=2）不允许更新
2. 待打印、打印中、打印失败的任务可以更新

### 价格重新计算
1. 当任务的打印参数发生变化时，会自动重新计算任务价格
2. 订单总金额会根据所有任务价格重新计算

## 错误码说明

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 400 | 订单ID不能为空 | 请求参数中缺少orderId |
| 404 | 订单不存在 | 指定的订单ID不存在 |
| 403 | 无权限更新该订单 | 当前用户不是订单所有者 |
| 400 | 已取消或已退款的订单不允许更新 | 订单状态不允许更新 |
| 400 | 任务ID不能为空 | 任务更新中缺少taskId |
| 404 | 任务不存在或不属于该订单 | 指定的任务ID不存在或不属于该订单 |
| 400 | 已完成的任务不允许更新 | 任务状态不允许更新 |

## 使用示例

### 示例1：只更新订单基本信息
```bash
curl -X POST "http://localhost:8081/order/printer/updateOrderWithDto" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiJ9..." \
  -d '{
    "orderId": "ORDER123456",
    "deviceName": "新打印机名称",
    "phone": "13800138000",
    "remark": "更新后的备注"
  }'
```

### 示例2：只更新任务信息
```bash
curl -X POST "http://localhost:8081/order/printer/updateOrderWithDto" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiJ9..." \
  -d '{
    "orderId": "ORDER123456",
    "taskUpdates": [
      {
        "taskId": "TASK123456",
        "copies": 3,
        "colorMode": 1,
        "pageRange": "1-10"
      }
    ]
  }'
```

### 示例3：同时更新订单和任务信息
```bash
curl -X POST "http://localhost:8081/order/printer/updateOrderWithDto" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiJ9..." \
  -d '{
    "orderId": "ORDER123456",
    "deviceName": "新打印机名称",
    "phone": "13800138000",
    "taskUpdates": [
      {
        "taskId": "TASK123456",
        "copies": 2,
        "colorMode": 0,
        "duplexMode": 1,
        "paperType": 1,
        "pageRange": "1-5,8-10"
      },
      {
        "taskId": "TASK789012",
        "copies": 1,
        "colorMode": 1,
        "paperType": 3
      }
    ]
  }'
```

## 注意事项

1. **数据验证**：所有参数都会进行严格的数据验证
2. **事务处理**：订单更新和任务更新在同一个事务中执行
3. **价格计算**：任务参数变更会触发价格重新计算
4. **日志记录**：所有更新操作都会记录详细日志
5. **权限控制**：确保只有订单所有者才能更新订单
6. **状态检查**：更新前会检查订单和任务的状态是否允许更新

## 开发建议

1. 推荐使用DTO接口（`/updateOrderWithDto`）以获得更好的类型安全性
2. 在前端进行参数验证以提高用户体验
3. 处理可能的并发更新情况
4. 合理使用批量更新以提高性能
