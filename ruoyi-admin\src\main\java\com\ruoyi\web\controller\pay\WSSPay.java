package com.ruoyi.web.controller.pay;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.dto.Result;
import com.ruoyi.dto.WSSPayCreatePayCodeReq;
import com.ruoyi.dto.WSSPayCreatePayCodeResp;
import com.ruoyi.dto.WSSPayOpenNativePayReq;
import com.ruoyi.order.domain.OrderCamera;
import com.ruoyi.order.service.IOrderCameraService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.util.DigestUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;

/**
 * 万岁山支付 - 控制器
 */
@Slf4j
@RestController
@RequestMapping("/WSSPay")
public class WSSPay {

    @Resource
    private RestTemplate restTemplate;
    @Autowired
    private IOrderCameraService orderCameraService;

    /**
     * 创建支付码
     */
    @GetMapping("/createPayCode")
    public Result createPayCode(Long amount, Integer paySource, String orderNo) {
        log.info("创建万岁山付款码");
        // 创建请求参数
        WSSPayOpenNativePayReq openNativePayReq=new WSSPayOpenNativePayReq ();
        openNativePayReq.setIsv_no("9000381");
        openNativePayReq.setBank_code("900001");
        openNativePayReq.setBank_mer_no("********");
        openNativePayReq.setBank_shop_no("S2410220773588");
        openNativePayReq.setMer_order_no(orderNo);
        openNativePayReq.setAmount(String.valueOf(amount));
        openNativePayReq.setPay_source(String.valueOf(paySource));
        openNativePayReq.setNotify_url("http://38080i660i.zicp.vip:34762/notify");
        openNativePayReq.setBank_org_no("000679");
        openNativePayReq.setDevice_no("2222311");
        // 时间戳
        openNativePayReq.setNonce_str(String.valueOf(System.currentTimeMillis()));
        openNativePayReq.setSign_type("MD5");
        // 签名
        // 非空参数值的参数按照参数名ASCII码从小到大排序（字典序），使用URL键值对的格式（即key1=value1&key2=value2…）拼接成字符串
        String sign = "amount=" + openNativePayReq.getAmount() + "&bank_code=" + openNativePayReq.getBank_code() + "&bank_mer_no=" +
                openNativePayReq.getBank_mer_no() + "&bank_org_no=" + openNativePayReq.getBank_org_no() + "&bank_shop_no=" +
                openNativePayReq.getBank_shop_no() + "&device_no=" + openNativePayReq.getDevice_no() + "&isv_no=" +
                openNativePayReq.getIsv_no() + "&mer_order_no=" + openNativePayReq.getMer_order_no() + "&nonce_str=" +
                openNativePayReq.getNonce_str() + "&notify_url=" + openNativePayReq.getNotify_url() + "&pay_source=" +
                openNativePayReq.getPay_source() + "&sign_type=" + openNativePayReq.getSign_type()+"&key=i9XoojXMW14ZZOEkQrz0KJMQbL0Q5EnLKQOhcDvD8BNfn/YH72g6NQ==";
        // 对stringSignTemp进行MD5运算，再将得到的字符串所有字符转换为大写，得到sign值signValue。 注意：密钥的长度为32个字节。
        String md5Sign = DigestUtils.md5DigestAsHex(sign.getBytes()).toUpperCase();
        System.out.println(md5Sign);
        openNativePayReq.setSign(md5Sign);
        String openNativePayReqStr = JSON.toJSONString(openNativePayReq);
        // 设置请求头
        HttpHeaders requestHeaders = new HttpHeaders();
        requestHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> r = new HttpEntity<String>(openNativePayReqStr, requestHeaders);
        // 请求地址
        String url = "https://up.chinanums.com/openpayapi/v2.0/openNativePay";
        String result = restTemplate.postForObject(url, r, String.class);
        // 将json字符串转为json对象
        JSONObject jsonObject = JSON.parseObject(result);
        // 如果返回的json对象中 return_code 为 "00000" 表示请求成功
        if (jsonObject != null && "00000".equals(jsonObject.getString("return_code"))) {
            // 获取返回的数据
            String data = jsonObject.getString("data");
            // 将data转为json对象
            JSONObject dataObject = JSON.parseObject(data);
            // 获取支付url
            String payCode = dataObject.getString("code_url");
            log.info("payCode:{}", payCode);
            return Result.ok(200, "创建支付码成功", String.valueOf(System.currentTimeMillis()), payCode);
        }else{
            if (jsonObject != null) {
                log.info("创建支付码失败：{}", jsonObject.getString("return_msg"));
            }
            return Result.fail(500, "创建支付码失败", String.valueOf(System.currentTimeMillis()));
        }
    }

    /**
     * 获取支付状态
     */
    @GetMapping("/getPayStatus")
    public Result getOrderStatus(String orderNo) {
        log.info("获取支付状态");
        if (orderNo == null || orderNo.isEmpty()) {
            return Result.fail(500, "商户订单号不能为空", String.valueOf(System.currentTimeMillis()));
        }
        // 创建请求参数
        WSSPayOpenNativePayReq openNativePayReq=new WSSPayOpenNativePayReq ();
        openNativePayReq.setIsv_no("9000381");
        openNativePayReq.setBank_code("900001");
        openNativePayReq.setBank_mer_no("********");
        openNativePayReq.setBank_shop_no("S2410220773588");
        openNativePayReq.setMer_order_no(orderNo);
        openNativePayReq.setNonce_str(String.valueOf(System.currentTimeMillis()));
        openNativePayReq.setSign_type("MD5");
        // 签名
        String sign = "bank_code=" + openNativePayReq.getBank_code() + "&bank_mer_no=" +
                openNativePayReq.getBank_mer_no() + "&bank_shop_no=" + openNativePayReq.getBank_shop_no() + "&isv_no=" +
                openNativePayReq.getIsv_no() + "&mer_order_no=" + openNativePayReq.getMer_order_no() + "&nonce_str=" +
                openNativePayReq.getNonce_str() + "&sign_type=" + openNativePayReq.getSign_type()+"&key=i9XoojXMW14ZZOEkQrz0KJMQbL0Q5EnLKQOhcDvD8BNfn/YH72g6NQ==";
        String md5Sign = DigestUtils.md5DigestAsHex(sign.getBytes()).toUpperCase();
        openNativePayReq.setSign(md5Sign);
        String openNativePayReqStr = JSON.toJSONString(openNativePayReq);
        // 设置请求头
        HttpHeaders requestHeaders = new HttpHeaders();
        requestHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> r = new HttpEntity<String>(openNativePayReqStr, requestHeaders);
        // 请求地址
        String url = "https://up.chinanums.com/openpayapi/v2.0/openQuery";
        String result = restTemplate.postForObject(url, r, String.class);
        // 将json字符串转为json对象
        JSONObject jsonObject = JSON.parseObject(result);
        // 如果返回的json对象中 return_code 为 "00000" 表示请求成功
        if (jsonObject != null && "00000".equals(jsonObject.getString("return_code"))) {
            // 获取返回的数据
            String data = jsonObject.getString("data");
            // 将data转为json对象
            JSONObject dataObject = JSON.parseObject(data);
            // 获取订单状态
            String orderStatus = dataObject.getString("order_state");
            log.info("orderStatus:{}", orderStatus);

            if (orderStatus.equals("SUCCESS")){
                OrderCamera orderCamera = new OrderCamera();
                orderCamera.setOrderId(orderNo);
                orderCamera.setOrderStatus(1L);
                orderCamera.setPayWay(1L);
                orderCamera.setPhotoType(1);
                orderCamera.setProductDescription("万岁山支付大头贴");
                orderCamera.setCreateTime(DateUtils.getNowDate());
                orderCamera.setPayTime(DateUtils.getNowDate());
                orderCameraService.save(orderCamera);
            }

            return Result.ok(200, "获取支付状态成功", String.valueOf(System.currentTimeMillis()), orderStatus);
        }else{
            System.out.println(jsonObject);
            return Result.fail(500, "获取支付状态失败", String.valueOf(System.currentTimeMillis()));
        }
    }

}
