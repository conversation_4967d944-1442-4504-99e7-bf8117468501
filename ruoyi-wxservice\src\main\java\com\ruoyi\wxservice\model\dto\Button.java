package com.ruoyi.wxservice.model.dto;

import lombok.Data;

/**
 * 菜单
 */
@Data
public class Button {

    /**
     * 菜单的响应动作类型
     */
    private String type;

    /**
     * 菜单标题，不超过16个字节，子菜单不超过60个字节
     */
    private String name;

    /**
     * click等点击类型必须
     */
    private String key;

    /**
     * view类型必须
     */
    private String url;

    /**
     * media_id类型和view_limited类型必须
     */
    private Button[] sub_button;
}
