package com.yunchuang.wxapp.service.client;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yunchuang.wxapp.model.domain.Consultation;
import com.yunchuang.wxapp.model.req.CConsultationLikeOrUnlikeReq;

import java.util.List;

/**
 * 咨询 Service 接口
 */
public interface ICConsultationService extends IService<Consultation> {

    /**
     * 获取咨询问题列表
     *
     * @param type 类型
     * @return 咨询列表
     */
    List<Consultation> getConsultList(Integer type);

    /**
     * 咨询问题点赞或点踩
     *
     * @param req 请求参数
     * @return 是否成功
     */
    boolean likeOrUnlike(CConsultationLikeOrUnlikeReq req);
}
