package com.yunchuang.wxapp.service.admin.impl;

import com.ruoyi.common.utils.DateUtils;
import com.yunchuang.wxapp.mapper.HomeSetMapper;
import com.yunchuang.wxapp.model.domain.HomeSet;
import com.yunchuang.wxapp.service.admin.IHomeSetService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 首页设置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-07
 */
@Service
public class HomeSetServiceImpl implements IHomeSetService {
    @Autowired
    private HomeSetMapper homeSetMapper;

    /**
     * 查询首页设置
     *
     * @param id 首页设置主键
     * @return 首页设置
     */
    @Override
    public HomeSet selectHomeSetById(Long id) {
        return homeSetMapper.selectHomeSetById(id);
    }

    /**
     * 查询首页设置列表
     *
     * @param homeSet 首页设置
     * @return 首页设置
     */
    @Override
    public List<HomeSet> selectHomeSetList(HomeSet homeSet) {
        return homeSetMapper.selectHomeSetList(homeSet);
    }

    /**
     * 新增首页设置
     *
     * @param homeSet 首页设置
     * @return 结果
     */
    @Override
    public int insertHomeSet(HomeSet homeSet) {
        homeSet.setCreateTime(DateUtils.getNowDate());
        return homeSetMapper.insertHomeSet(homeSet);
    }

    /**
     * 修改首页设置
     *
     * @param homeSet 首页设置
     * @return 结果
     */
    @Override
    public int updateHomeSet(HomeSet homeSet) {
        homeSet.setUpdateTime(DateUtils.getNowDate());
        return homeSetMapper.updateHomeSet(homeSet);
    }

    /**
     * 批量删除首页设置
     *
     * @param ids 需要删除的首页设置主键
     * @return 结果
     */
    @Override
    public int deleteHomeSetByIds(Long[] ids) {
        return homeSetMapper.deleteHomeSetByIds(ids);
    }

    /**
     * 删除首页设置信息
     *
     * @param id 首页设置主键
     * @return 结果
     */
    @Override
    public int deleteHomeSetById(Long id) {
        return homeSetMapper.deleteHomeSetById(id);
    }
}
