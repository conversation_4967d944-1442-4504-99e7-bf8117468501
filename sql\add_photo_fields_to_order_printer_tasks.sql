-- 为 order_printer_tasks 表添加照片相关字段
-- 执行时间：2025-01-30

-- 添加是否为照片字段
ALTER TABLE order_printer_tasks 
ADD COLUMN is_photo INT(1) DEFAULT 0 COMMENT '是否为照片 0-否 1-是';

-- 添加尺寸大小字段
ALTER TABLE order_printer_tasks 
ADD COLUMN size_spec VARCHAR(50) DEFAULT NULL COMMENT '尺寸大小（如：A4、4寸、6寸等）';

-- 添加字段注释
ALTER TABLE order_printer_tasks 
MODIFY COLUMN is_photo INT(1) DEFAULT 0 COMMENT '是否为照片 0-否 1-是';

ALTER TABLE order_printer_tasks 
MODIFY COLUMN size_spec VARCHAR(50) DEFAULT NULL COMMENT '尺寸大小（如：A4、4寸、6寸等）';

-- 验证字段是否添加成功
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'order_printer_tasks' 
    AND COLUMN_NAME IN ('is_photo', 'size_spec');
