# 订单总金额功能实现说明

## 功能概述

为订单详情和用户订单列表接口添加总金额字段，总金额通过累加该订单下所有打印任务的价格计算得出。

## 涉及接口

### 1. 用户订单列表接口
- **路径**: `GET /order/printer/user`
- **功能**: 查询当前用户的所有订单，每个订单包含总金额

### 2. 订单详情接口
- **路径**: `GET /order/printer/detail/{orderId}`
- **功能**: 获取指定订单的详细信息，包含总金额和任务列表

## 实现方案

### 1. 数据模型扩展

#### OrderPrinter实体类新增字段
```java
/** 订单总金额（所有任务价格之和）- 非数据库字段 */
@TableField(exist = false)
private Double totalAmount;
```

**说明**：
- 使用`@TableField(exist = false)`标注，表示该字段不对应数据库字段
- 类型为`Double`，单位为元，支持小数

### 2. 业务逻辑实现

#### Service接口新增方法
```java
/**
 * 计算订单总金额
 *
 * @param orderId 订单ID
 * @return 总金额（元）
 */
public Double calculateOrderTotalAmount(String orderId);
```

#### Service实现类
```java
@Override
public Double calculateOrderTotalAmount(String orderId) {
    if (StringUtils.isEmpty(orderId)) {
        return 0.0;
    }
    
    // 获取订单的所有任务
    List<OrderPrinterTask> tasks = getOrderTasks(orderId);
    if (tasks == null || tasks.isEmpty()) {
        return 0.0;
    }
    
    // 计算总金额
    Double totalAmount = tasks.stream()
            .filter(task -> task.getTaskPrice() != null)
            .mapToDouble(OrderPrinterTask::getTaskPrice)
            .sum();
    
    // 保留两位小数
    return Math.round(totalAmount * 100.0) / 100.0;
}
```

**计算逻辑**：
1. 根据订单ID获取所有关联的打印任务
2. 使用Java 8 Stream API对任务价格进行求和
3. 过滤掉价格为null的任务
4. 保留两位小数精度

### 3. 接口响应修改

#### 用户订单列表接口响应
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "orderId": "order123",
      "deviceId": "device001",
      "deviceName": "打印机A",
      "orderStatus": 1,
      "totalAmount": 1.5,
      "createTime": "2025-01-25 10:00:00",
      // ... 其他订单字段
    }
  ]
}
```

#### 订单详情接口响应
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "order": {
      "orderId": "order123",
      "deviceId": "device001",
      "deviceName": "打印机A",
      "orderStatus": 1,
      "totalAmount": 1.5,
      "createTime": "2025-01-25 10:00:00",
      // ... 其他订单字段
    },
    "tasks": [
      {
        "taskId": "task001",
        "orderId": "order123",
        "fileName": "document.pdf",
        "taskPrice": 0.8,
        "printStatus": 2,
        // ... 其他任务字段
      },
      {
        "taskId": "task002",
        "orderId": "order123",
        "fileName": "image.jpg",
        "taskPrice": 0.7,
        "printStatus": 2,
        // ... 其他任务字段
      }
    ],
    "totalAmount": 1.5
  }
}
```

## 数据关系

### 订单与任务关系
```
一个用户(openid) -> 多个订单(OrderPrinter)
一个订单(orderId) -> 多个打印任务(OrderPrinterTask)
订单总金额 = 该订单下所有任务的taskPrice之和
```

### 计算示例
假设订单`order123`有以下任务：
- 任务1：taskPrice = 0.8元
- 任务2：taskPrice = 0.7元
- 任务3：taskPrice = null（忽略）

则订单总金额 = 0.8 + 0.7 = 1.5元

## 性能考虑

### 优化建议
1. **缓存机制**：可以考虑将订单总金额缓存到Redis中
2. **数据库优化**：可以在数据库层面使用SUM聚合查询
3. **批量计算**：对于订单列表，可以批量计算总金额

### 数据库查询优化（可选）
```sql
-- 可以在OrderPrinterMapper.xml中添加直接计算总金额的SQL
SELECT 
    o.*,
    COALESCE(SUM(t.task_price), 0) as total_amount
FROM order_printer o
LEFT JOIN order_printer_tasks t ON o.order_id = t.order_id
WHERE o.openid = #{openid}
GROUP BY o.order_id
ORDER BY o.create_time DESC
```

## 测试用例

### 1. 正常情况
- 订单有多个任务，每个任务都有价格
- 验证总金额计算正确

### 2. 边界情况
- 订单没有任务：总金额应为0
- 订单有任务但价格为null：忽略该任务
- 订单不存在：返回0

### 3. 精度测试
- 验证小数计算精度
- 验证四舍五入逻辑

## 部署说明

1. **代码部署**：部署修改后的代码
2. **无需数据库变更**：totalAmount是非数据库字段
3. **向后兼容**：不影响现有功能
4. **测试验证**：验证接口返回数据正确

## 注意事项

1. **性能影响**：每次查询订单都会计算总金额，如果订单量大可能影响性能
2. **数据一致性**：总金额是实时计算的，确保与任务价格保持一致
3. **错误处理**：计算过程中的异常处理
4. **日志记录**：建议添加计算总金额的日志便于调试
