package com.ruoyi.fee.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.fee.domain.Fee;

/**
 * 费用Service接口
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */
public interface IFeeService extends IService<Fee>
{
    /**
     * 查询费用
     * 
     * @param id 费用主键
     * @return 费用
     */
    public Fee selectFeeById(Long id);

    /**
     * 查询费用列表
     * 
     * @param fee 费用
     * @return 费用集合
     */
    public List<Fee> selectFeeList(Fee fee);

    /**
     * 新增费用
     * 
     * @param fee 费用
     * @return 结果
     */
    public int insertFee(Fee fee);

    /**
     * 修改费用
     * 
     * @param fee 费用
     * @return 结果
     */
    public int updateFee(Fee fee);

    /**
     * 批量删除费用
     * 
     * @param ids 需要删除的费用主键集合
     * @return 结果
     */
    public int deleteFeeByIds(Long[] ids);

    /**
     * 删除费用信息
     * 
     * @param id 费用主键
     * @return 结果
     */
    public int deleteFeeById(Long id);
}
