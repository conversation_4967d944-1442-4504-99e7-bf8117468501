package com.yunchuang.wxapp.service.client.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.device.domain.CameraSysImage;
import com.ruoyi.device.mapper.CameraSysImageMapper;
import com.yunchuang.wxapp.mapper.PhotoFrameSeriesMapper;
import com.yunchuang.wxapp.model.constant.CommonConstant;
import com.yunchuang.wxapp.model.domain.PhotoFrameSeries;
import com.yunchuang.wxapp.model.enums.common.CommonEnableStatus;
import com.yunchuang.wxapp.model.resp.CCommonGetFrameListResp;
import com.yunchuang.wxapp.service.client.ICPhotoFrameSeriesService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 相框系列 Service 实现类
 */
@Service
@Transactional
public class CPhotoFrameSeriesServiceImpl extends ServiceImpl<PhotoFrameSeriesMapper, PhotoFrameSeries> implements ICPhotoFrameSeriesService {

    @Resource
    private PhotoFrameSeriesMapper photoFrameSeriesMapper;

    @Resource
    private CameraSysImageMapper cameraSysImageMapper;

    /**
     * 获取相框列表
     *
     * @param photoFrameIdStr 相框ID字符串
     * @return 相框列表
     */
    @Override
    public List<CameraSysImage> getFrameList(String photoFrameIdStr) {
        LambdaQueryWrapper<CameraSysImage> queryWrapper = new LambdaQueryWrapper<>();
        // 根据photoFrameIdStr筛选
        if (photoFrameIdStr != null && !photoFrameIdStr.isEmpty()) {
            List<Integer> photoFrameIdList = new ArrayList<>();
            for (String photoFrameId : photoFrameIdStr.split(",")) {
                photoFrameIdList.add(Integer.parseInt(photoFrameId));
            }
            queryWrapper.in(CameraSysImage::getId, photoFrameIdList);
        }
        // 通过createBy字段 模糊查询ypjh_开头的相框
        queryWrapper.likeRight(CameraSysImage::getCreateBy, CommonConstant.PHOTO_FRAME_PREFIX);
        return cameraSysImageMapper.selectList(queryWrapper);
    }

    /**
     * 获取相框列表 - 系列分组
     *
     * @return 相框列表
     */
    @Override
    public List<CCommonGetFrameListResp> getFrameListGroup() {
        List<CCommonGetFrameListResp> respList = new ArrayList<>();

        // 1. 查询相框系列列表
        LambdaQueryWrapper<PhotoFrameSeries> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PhotoFrameSeries::getStatus, CommonEnableStatus.ENABLE.getValue());
        queryWrapper.orderByAsc(PhotoFrameSeries::getSort);
        List<PhotoFrameSeries> frameSeriesList = photoFrameSeriesMapper.selectList(queryWrapper);
        if (frameSeriesList == null || frameSeriesList.isEmpty()) {
            return respList;
        }

        // 2. 查询相框系列下的相框列表
        LambdaQueryWrapper<CameraSysImage> imageQueryWrapper = new LambdaQueryWrapper<>();
        // TODO: 暂未添加系列ID字段 ,默认查询前8个相框
        imageQueryWrapper.last("limit 8");
        // 通过createBy字段 模糊查询ypjh_开头的相框
        imageQueryWrapper.likeRight(CameraSysImage::getCreateBy, CommonConstant.PHOTO_FRAME_PREFIX);
        List<CameraSysImage> frameList = cameraSysImageMapper.selectList(imageQueryWrapper);

        // 3. 组装返回数据
        frameSeriesList.forEach(frameSeries -> {
            CCommonGetFrameListResp resp = new CCommonGetFrameListResp();
            resp.setSeriesId(frameSeries.getId());
            resp.setSeriesName(frameSeries.getSeriesName());
            // TODO: 暂未添加系列ID字段，暂时不做筛选
            resp.setFrameList(frameList);
            respList.add(resp);
        });
        return respList;
    }

}
