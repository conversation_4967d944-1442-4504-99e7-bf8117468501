# 获取用户订单详情接口返回数据结构

## 接口信息
- **接口路径**: `GET /order/printer/detail/{orderId}`
- **接口描述**: 获取用户订单详情（包含打印任务）
- **请求参数**: 
  - `orderId` (路径参数): 订单ID
  - `openid` (查询参数，可选): 用户openid，用于权限验证

## 返回数据结构

### 响应格式
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "order": {订单信息对象},
    "tasks": [打印任务数组]
  }
}
```

### 详细字段说明

#### 1. 订单信息对象 (order)

| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| **基本信息** |
| orderId | String | 订单ID | "abc123def456" |
| deviceId | String | 打印机设备ID | "PRINTER_001" |
| deviceName | String | 打印机名称 | "办公室打印机" |
| userId | Long | 用户ID | 1001 |
| openid | String | 微信用户openid | "oABC123..." |
| phone | String | 手机号 | "***********" |
| **订单状态** |
| orderStatus | Integer | 订单状态 | 1 |
| orderPrice | Long | 订单金额（分） | 300 |
| **支付信息** |
| transactionId | String | 三方订单ID | "wx123456789" |
| mchid | String | 商户ID | "**********" |
| appid | String | 小程序appid | "wx123abc456" |
| payWay | Integer | 支付方式 | 1 |
| payTime | String | 支付时间 | "2025-01-25 14:30:00" |
| **财务信息** |
| moneyReceived | Long | 实收金额（分） | 280 |
| commission | Long | 手续费（分） | 15 |
| interfaceFee | Long | 接口费（分） | 5 |
| account | Long | 到账金额（分） | 260 |
| interfaceType | Integer | 技术接口类型 | 1 |
| **优惠信息** |
| voucherCode | String | 优惠券码 | "COUPON123" |
| **退款信息** |
| refundId | String | 退款ID | "refund123" |
| refundTime | String | 退款时间 | "2025-01-25 15:00:00" |
| **打印信息** |
| printTime | String | 打印时间 | "2025-01-25 14:35:00" |
| **其他信息** |
| roleId | Long | 代理商ID | 2001 |
| hide | Integer | 是否隐藏 | 0 |
| **基础字段（继承自BaseEntity）** |
| createBy | String | 创建者 | "system" |
| createTime | String | 创建时间 | "2025-01-25 14:25:00" |
| updateBy | String | 更新者 | "admin" |
| updateTime | String | 更新时间 | "2025-01-25 14:30:00" |
| remark | String | 备注 | "测试订单" |

#### 订单状态说明
| 状态值 | 状态名称 | 说明 |
|--------|----------|------|
| 0 | 未支付 | 订单已创建，等待支付 |
| 1 | 已支付 | 支付成功，等待打印 |
| 2 | 已取消 | 订单已取消 |
| 3 | 已退款 | 订单已退款 |
| 4 | 已打印 | 打印完成 |
| 5 | 打印中 | 正在打印 |
| 6 | 打印失败 | 打印失败 |

#### 支付方式说明
| 支付方式值 | 支付方式名称 |
|------------|--------------|
| 1 | 微信支付 |
| 2 | 支付宝 |
| 3 | 现金 |

#### 2. 打印任务数组 (tasks)

每个任务对象包含以下字段：

| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| **基本信息** |
| taskId | String | 任务ID | "task123456" |
| orderId | String | 关联订单ID | "abc123def456" |
| deviceId | String | 打印机设备ID | "PRINTER_001" |
| **文件信息** |
| fileUrl | String | 文件URL | "/profile/upload/2025/01/25/xxx.pdf" |
| fileName | String | 文件名称 | "document.pdf" |
| fileType | String | 文件类型 | "pdf" |
| fileSize | Long | 文件大小（字节） | 1024000 |
| **打印参数** |
| printStatus | Integer | 打印状态 | 0 |
| pageRange | String | 页码范围 | "1-5" |
| copies | Integer | 打印份数 | 2 |
| colorMode | Integer | 颜色模式 | 0 |
| duplexMode | Integer | 双面模式 | 1 |
| paperType | Integer | 纸张类型 | 1 |
| **价格信息** |
| taskPrice | Long | 任务价格（分） | 150 |
| **状态信息** |
| errorMsg | String | 错误信息 | null |
| retryCount | Integer | 重试次数 | 0 |
| startTime | String | 开始打印时间 | "2025-01-25 14:35:00" |
| endTime | String | 完成打印时间 | "2025-01-25 14:36:00" |
| **基础字段（继承自BaseEntity）** |
| createBy | String | 创建者 | "system" |
| createTime | String | 创建时间 | "2025-01-25 14:30:00" |
| updateBy | String | 更新者 | "system" |
| updateTime | String | 更新时间 | "2025-01-25 14:35:00" |
| remark | String | 备注 | "打印任务" |

#### 打印状态说明
| 状态值 | 状态名称 | 说明 |
|--------|----------|------|
| 0 | 待打印 | 任务已创建，等待打印 |
| 1 | 打印中 | 正在打印 |
| 2 | 打印完成 | 打印成功完成 |
| 3 | 打印失败 | 打印失败 |

#### 颜色模式说明
| 模式值 | 模式名称 |
|--------|----------|
| 0 | 黑白 |
| 1 | 彩色 |

#### 双面模式说明
| 模式值 | 模式名称 |
|--------|----------|
| 0 | 单面 |
| 1 | 双面 |

#### 纸张类型说明
| 类型值 | 类型名称 |
|--------|----------|
| 1 | A4 |
| 2 | A5 |
| 3 | 照片纸 |

## 完整响应示例

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "order": {
      "orderId": "abc123def456",
      "deviceId": "PRINTER_001",
      "deviceName": "办公室打印机",
      "userId": 1001,
      "openid": "oABC123def456ghi789",
      "phone": "***********",
      "transactionId": "wx**********12345",
      "mchid": "**********",
      "appid": "wx123abc456def789",
      "orderStatus": 1,
      "refundId": null,
      "refundTime": null,
      "payWay": 1,
      "voucherCode": null,
      "orderPrice": 300,
      "moneyReceived": 280,
      "commission": 15,
      "interfaceFee": 5,
      "account": 260,
      "interfaceType": 1,
      "payTime": "2025-01-25 14:30:00",
      "printTime": "2025-01-25 14:35:00",
      "roleId": 2001,
      "hide": 0,
      "createBy": "system",
      "createTime": "2025-01-25 14:25:00",
      "updateBy": "admin",
      "updateTime": "2025-01-25 14:30:00",
      "remark": "测试订单"
    },
    "tasks": [
      {
        "taskId": "task123456",
        "orderId": "abc123def456",
        "deviceId": "PRINTER_001",
        "fileUrl": "/profile/upload/2025/01/25/document_20250125143000.pdf",
        "fileName": "document.pdf",
        "fileType": "pdf",
        "fileSize": 1024000,
        "printStatus": 2,
        "pageRange": "1-5",
        "copies": 2,
        "colorMode": 0,
        "duplexMode": 1,
        "paperType": 1,
        "taskPrice": 150,
        "errorMsg": null,
        "retryCount": 0,
        "startTime": "2025-01-25 14:35:00",
        "endTime": "2025-01-25 14:36:00",
        "createBy": "system",
        "createTime": "2025-01-25 14:30:00",
        "updateBy": "system",
        "updateTime": "2025-01-25 14:35:00",
        "remark": "打印任务1"
      },
      {
        "taskId": "task789012",
        "orderId": "abc123def456",
        "deviceId": "PRINTER_001",
        "fileUrl": "/profile/upload/2025/01/25/report_20250125143100.pdf",
        "fileName": "report.pdf",
        "fileType": "pdf",
        "fileSize": 512000,
        "printStatus": 2,
        "pageRange": "1-3",
        "copies": 1,
        "colorMode": 1,
        "duplexMode": 0,
        "paperType": 1,
        "taskPrice": 150,
        "errorMsg": null,
        "retryCount": 0,
        "startTime": "2025-01-25 14:36:00",
        "endTime": "2025-01-25 14:37:00",
        "createBy": "system",
        "createTime": "2025-01-25 14:31:00",
        "updateBy": "system",
        "updateTime": "2025-01-25 14:36:00",
        "remark": "打印任务2"
      }
    ]
  }
}
```

## 注意事项

1. **时间格式**: 所有时间字段都使用 `yyyy-MM-dd HH:mm:ss` 格式
2. **金额单位**: 所有金额字段都以分为单位
3. **空值处理**: 某些字段可能为 `null`，前端需要做空值判断
4. **权限验证**: 如果提供了 `openid` 参数，会验证订单是否属于该用户
5. **数据完整性**: 返回的数据包含订单的完整信息和所有关联的打印任务
