package com.ruoyi.device.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.device.domain.DeviceLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface DeviceLogMapper extends BaseMapper<DeviceLog> {
    public int addLog(DeviceLog deviceLog);

    TableDataInfo selectLog(@Param("deviceId") String deviceId, @Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 根据设备id删除
     * @param deviceId
     * @return
     */
    int deleteLog(String[] deviceIds);
}
