package com.ruoyi.dto;

/**
 * VIETQR生成VietQR码请求
 */
public class VIETQRGenerateCustomerReq {

    /**
     * 银行代码
     */
    private String bankCode;

    /**
     * VietQR账号
     */
    private String bankAccount;

    /**
     * 账号持有人全名
     */
    private String userBankName;

    /**
     * 汇款内容
     */
    private String content;

    /**
     * 二维码类型 0：动态 1：静态 3：半动态
     */
    private Integer qrType;

    /**
     * 金额
     */
    private Long amount;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * D:借记卡 C:贷记卡
     */
    private String transType;

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankAccount() {
        return bankAccount;
    }

    public void setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount;
    }

    public String getUserBankName() {
        return userBankName;
    }

    public void setUserBankName(String userBankName) {
        this.userBankName = userBankName;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getQrType() {
        return qrType;
    }

    public void setQrType(Integer qrType) {
        this.qrType = qrType;
    }

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getTransType() {
        return transType;
    }

    public void setTransType(String transType) {
        this.transType = transType;
    }

    @Override
    public String toString() {
        return "VIETQRGenerateCustomerReq{" +
                "bankCode='" + bankCode + '\'' +
                ", bankAccount='" + bankAccount + '\'' +
                ", userBankName='" + userBankName + '\'' +
                ", content='" + content + '\'' +
                ", qrType=" + qrType +
                ", amount=" + amount +
                ", orderId='" + orderId + '\'' +
                ", transType='" + transType + '\'' +
                '}';
    }
}
