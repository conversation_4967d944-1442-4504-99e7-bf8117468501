<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yunchuang.wxapp.mapper.CarouselImageMapper">

    <resultMap type="CarouselImage" id="CarouselImageResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="beEnabled" column="be_enabled"/>
        <result property="imageUrl" column="image_url"/>
        <result property="sort" column="sort"/>
    </resultMap>

    <sql id="selectCarouselImageVo">
        select id, name, be_enabled, image_url, sort
        from wxapp_carousel_image
    </sql>

    <select id="selectCarouselImageList" parameterType="CarouselImage" resultMap="CarouselImageResult">
        <include refid="selectCarouselImageVo"/>
        <where>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="beEnabled != null ">and be_enabled = #{beEnabled}</if>
        </where>
    </select>

    <select id="selectCarouselImageById" parameterType="Long" resultMap="CarouselImageResult">
        <include refid="selectCarouselImageVo"/>
        where id = #{id}
    </select>

    <insert id="insertCarouselImage" parameterType="CarouselImage" useGeneratedKeys="true" keyProperty="id">
        insert into wxapp_carousel_image
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="beEnabled != null">be_enabled,</if>
            <if test="imageUrl != null">image_url,</if>
            <if test="sort != null">sort,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="beEnabled != null">#{beEnabled},</if>
            <if test="imageUrl != null">#{imageUrl},</if>
            <if test="sort != null">#{sort},</if>
        </trim>
    </insert>

    <update id="updateCarouselImage" parameterType="CarouselImage">
        update wxapp_carousel_image
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="beEnabled != null">be_enabled = #{beEnabled},</if>
            <if test="imageUrl != null">image_url = #{imageUrl},</if>
            <if test="sort != null">sort = #{sort},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCarouselImageById" parameterType="Long">
        delete
        from wxapp_carousel_image
        where id = #{id}
    </delete>

    <delete id="deleteCarouselImageByIds" parameterType="String">
        delete from wxapp_carousel_image where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>