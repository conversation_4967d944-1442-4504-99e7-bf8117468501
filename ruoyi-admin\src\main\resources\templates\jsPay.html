<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0"/>
    <link rel="stylesheet" href="/css/index.css">
    <title>拍照支付</title>
</head>
<body id="body"
      style="background-image: url(/js_pay_background.jpg);background-size: cover;background-position: center center">

<div class="wrap" style="display: flex;flex-direction: column;align-items: center;justify-content: center">
    <div style="font-weight: bold;font-size: 5vh;color: black;margin-top: 10vh">自拍机</div>
    <form>
        <div class="item">
            <div class="right">
                <input id="totalAmount" th:value="${totalAmount}/100"
                       style=";font-size: 25px;text-align: center;width: 30vh;background: none;border: none;color: black"
                       disabled type="text" name="totalAmount">
                <input style="display: none" id="openId" type="text" th:value="${openId}" name="openId">
                <input style="display: none" id="userId" type="text" th:value="${userId}" name="userId">
                <input style="display: none" id="orderId" type="text" th:value="${orderId}" name="orderId">
                <input style="display: none" id="mchid" type="text" th:value="${mchid}" name="mchid">
            </div>
        </div>
        <div class="btn">
            <img style="width: 30vh;height: 10vh;margin-top: 50px" onclick="doPay()" type="button" class="submit_btn"
                 src="/sure_pay.png">
        </div>
    </form>
</div>

</body>

<script type="text/javascript" src="/js/jquery.js"></script>
<script type="text/javascript">

    // var mchid = $('#mchid').val();
    // if (mchid.length === 15) {
    //     let elementById = document.getElementById("body");
    //     let url = "/yc_pay_background.jpg";
    //     elementById.style.backgroundImage = `url(${url})`;
    // }


    var payInfo = null;

    function doPay() {
        var totalAmount = $('#totalAmount').val();
        var openId = $('#openId').val();
        var userId = $('#userId').val();
        var orderId = $('#orderId').val();
        var mchid = $('#mchid').val();
        $.ajax({
            url: "/pay/jsPay",
            type: "POST",
            dataType: "json",
            data: {
                "totalAmount": Math.round(Number(totalAmount * 100)),
                "openId": openId,
                "userId": userId,
                "outTradeNo": orderId,
                "mchid": mchid,
            },
            success: function (data) {
                if (data.channelType == "WX") {
                    payInfo = JSON.parse(data.data);
                    onBridgeReady();
                } else {
                    tradePay(data.data);
                }
            }, error: function (msg) {
                alert(msg);
                alert(JSON.parse(msg));
            }
        });
    }

    // 由于js的载入是异步的，所以可以通过该方法，当AlipayJSBridgeReady事件发生后，再执行callback方法
    function ready(callback) {
        if (window.AlipayJSBridge) {
            callback && callback();
        } else {
            document.addEventListener('AlipayJSBridgeReady', callback, false);
        }
    }

    function tradePay(tradeNO) {
        ready(function () {
            // 通过传入交易号唤起快捷调用方式(注意tradeNO大小写严格)
            AlipayJSBridge.call("tradePay", {
                tradeNO: tradeNO
            }, function (data) {
                $(".safe-pay").css({"pointer-events": "auto"});
                if ("9000" == data.resultCode) {
                    window.location = "/success";
                }
            });
        });
    }

    function onBridgeReady() {
        WeixinJSBridge.invoke(
            'getBrandWCPayRequest', {
                "appId": payInfo.appId,
                "timeStamp": payInfo.timeStamp,
                "nonceStr": payInfo.nonceStr,
                "package": payInfo.package,
                "signType": payInfo.signType,
                "paySign": payInfo.paySign
            },
            function (res) {
                $(".safe-pay").css({"pointer-events": "auto"});
                if (res.err_msg == "get_brand_wcpay_request:ok") {
                    window.location = "/success";
                } else {
                    alert(JSON.stringify(res));
                }
            });
    }
</script>
</html>
