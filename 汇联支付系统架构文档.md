# 汇联支付系统架构文档

## 📋 目录

- [系统概述](#系统概述)
- [架构设计](#架构设计)
- [支付流程](#支付流程)
- [配置说明](#配置说明)
- [接口文档](#接口文档)
- [代码示例](#代码示例)
- [部署指南](#部署指南)

## 🏗️ 系统概述

### 系统简介

本系统采用汇联支付作为统一支付网关，支持国内外多种支付方式，包括微信支付、支付宝、以及国际支付解决方案。通过智能路由机制，自动选择最优支付通道，确保高成功率和低成本。

### 核心特性

- ✅ **多通道支持**: 集成10+个支付服务商
- ✅ **智能路由**: 自动选择最优支付通道
- ✅ **国际化**: 支持国内外主流支付方式
- ✅ **高可用**: 多重容灾和故障切换
- ✅ **统一接口**: 一套API支持所有支付方式
- ✅ **安全可靠**: RSA签名验证，确保交易安全

### 技术栈

- **后端框架**: Spring Boot + MyBatis-Plus
- **支付网关**: 汇联支付
- **签名算法**: RSA SHA256WithRSA
- **数据格式**: JSON
- **通信协议**: HTTPS

## 🏗️ 架构设计

### 整体架构图

```mermaid
graph TB
    A[微信小程序/H5前端] --> B[系统后端服务]
    B --> C[汇联支付网关]
    C --> D[智能路由引擎]
    
    D --> E[网商银行 mybank]
    D --> F[随行付 vbill]
    D --> G[新大陆 starpos]
    D --> H[汇付天下 huifu]
    D --> I[合利宝 helipay]
    D --> J[易宝 yeepay]
    D --> K[乐刷 yeahka]
    D --> L[网商云资金]
    
    E --> M[微信支付API]
    F --> M
    G --> M
    H --> M
    
    E --> N[支付宝API]
    F --> N
    G --> N
    H --> N
    
    I --> O[国际支付通道]
    J --> O
    K --> O
    L --> O
```

### 支付服务商

| 服务商代码 | 服务商名称 | 支持渠道 | 特点 |
|-----------|-----------|---------|------|
| `mybank` | 网商银行 | 微信、支付宝 | 阿里系，稳定性高 |
| `vbill` | 随行付 | 微信、支付宝 | 覆盖面广，费率优惠 |
| `starpos` | 新大陆 | 微信、支付宝 | 技术先进，处理速度快 |
| `huifu` | 汇付天下 | 微信、支付宝 | 老牌支付，经验丰富 |
| `helipay` | 合利宝 | 微信、支付宝 | 多通道，高成功率 |
| `yeepay` | 易宝支付 | 微信、支付宝 | 行业专业，定制化强 |
| `yeahka` | 乐刷 | 微信、支付宝 | 移动支付专家 |
| `mybank_cloud` | 网商云资金 | 微信、支付宝 | 汇联云，资金安全 |

### 支付渠道类型

| 渠道代码 | 渠道名称 | 说明 |
|---------|---------|------|
| `WX` | 微信支付 | 支持JSAPI、Native、APP、H5等 |
| `ALI` | 支付宝 | 支持网页、手机网站、APP、扫码等 |

## 🔄 支付流程

### 完整支付时序图

```mermaid
sequenceDiagram
    participant Client as 前端客户端
    participant Backend as 系统后端
    participant HuiLian as 汇联网关
    participant Router as 智能路由
    participant PSP as 支付服务商
    participant WeChat as 微信/支付宝

    Client->>Backend: 1. 发起支付请求
    Backend->>Backend: 2. 构建支付参数
    Backend->>Backend: 3. 选择机构号和私钥
    Backend->>Backend: 4. RSA签名
    Backend->>HuiLian: 5. 发送支付请求
    HuiLian->>Router: 6. 智能路由选择
    Router->>PSP: 7. 调用最优通道
    PSP->>WeChat: 8. 调用微信/支付宝API
    WeChat-->>PSP: 9. 返回支付参数
    PSP-->>Router: 10. 返回支付参数
    Router-->>HuiLian: 11. 返回支付参数
    HuiLian-->>Backend: 12. 返回支付参数
    Backend-->>Client: 13. 返回支付参数
    Client->>WeChat: 14. 调用支付
    WeChat->>PSP: 15. 支付结果回调
    PSP->>HuiLian: 16. 支付结果通知
    HuiLian->>Backend: 17. 支付成功回调
    Backend->>Backend: 18. 更新订单状态
```

### 支付流程步骤

#### 1. 支付发起阶段
```java
// 构建支付参数
JsPay jsPay = new JsPay();
jsPay.setOutTradeNo(orderId);                    // 订单号
jsPay.setWxAppId(MyConfig.WX_APPID);            // 微信AppID
jsPay.setBody("商品描述");                       // 商品描述
jsPay.setHlMerchantId(merchantId);              // 汇联商户号
jsPay.setTotalAmount(String.valueOf(amount));   // 支付金额(分)
jsPay.setChannelType(channelType);              // 支付渠道
jsPay.setOpenId(openId);                        // 用户标识
jsPay.setNotifyUrl(notifyUrl);                  // 回调地址
```

#### 2. 机构路由选择
```java
// 根据用户部门选择机构号
String agencyNo = MyConfig.AgencyNo_yc;         // 默认云创机构号
String privateKey = MyConfig.PrivateKey_yc;     // 默认云创私钥

if (isPDLUser) {
    agencyNo = MyConfig.AgencyNo;               // 潘朵拉机构号
    privateKey = MyConfig.PrivateKey;           // 潘朵拉私钥
}
```

#### 3. 请求签名和发送
```java
// 构建请求参数并签名
String param = HttpsMain.format(jsPay, FunctionEnum.JS_PAY, agencyNo, privateKey);

// 发送到汇联网关
String response = HttpsMain.httpReq(MyConfig.PayUrl, param);

// 验证返回签名
if (RsaUtil.verifyResponseSign(response)) {
    return parsePaymentParams(response);
}
```

#### 4. 支付回调处理
```java
@PostMapping("/notice")
public String paymentNotify(@RequestBody String requestBody) {
    // 解析回调数据
    JSONObject data = JSONObject.parseObject(requestBody);
    String orderId = data.getString("outTradeNo");
    String transactionId = data.getString("payChannelOrderNo");
    
    // 更新订单状态
    updateOrderStatus(orderId, transactionId);
    
    // 通知相关系统
    notifyOrderSuccess(orderId);
    
    return "success";
}
```

## ⚙️ 配置说明

### 核心配置文件

#### MyConfig.java
```java
public interface MyConfig {
    // 支付公众号配置
    String WX_APPID = "wx8f7a9346308ee56f";
    
    // 汇联机构号配置
    String AgencyNo = "1228026";        // 潘朵拉机构号
    String AgencyNo_yc = "11301";       // 云创机构号
    
    // 商户号配置
    String HlMerchant = "1366993712427";
    
    // 回调地址配置
    String Notice_URL = "https://fotoboxserve.cameraon.store/pay/notice";
    
    // 汇联支付网关
    String PayUrl = "https://open.huilian.cloud/pay";
    
    // RSA密钥配置
    String PrivateKey = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSk...";
    String PrivateKey_yc = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKUwggSh...";
    String huilian_publicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAs53yaPCd...";
}
```

### 环境配置

#### application.yml
```yaml
# 微信小程序配置
wxapp:
  wechat:
    applets:
      app-id: wxb4c2d62fc737311f
      app-secret: f37690c219544053db752bbe423d4080

# 支付配置
payment:
  huilian:
    gateway-url: https://open.huilian.cloud/pay
    agency-no: 1228026
    agency-no-yc: 11301
    merchant-id: 1366993712427
    notify-url: https://fotoboxserve.cameraon.store/pay/notice
```

### 商户号配置

| 商户类型 | 商户号 | 说明 |
|---------|--------|------|
| 潘朵拉云创 | 1386125312629 | 强哥名下商户号 |
| 云创 | 123802148113840 | 凤凤名下商户号 |
| 乐高定制 | 1398949712874 | 乐高专用商户号 |

## 📡 接口文档

### 1. 发起支付接口

**接口地址**: `POST /pay/jsPay`

**请求参数**:
```json
{
    "totalAmount": 100,           // 支付金额(分)
    "openId": "openid_123",       // 用户openId
    "userId": "user_123",         // 用户ID
    "outTradeNo": "order_123",    // 订单号
    "mchid": "1366993712427"      // 商户号
}
```

**响应参数**:
```json
{
    "channelType": "WX",
    "data": {
        "appId": "wx8f7a9346308ee56f",
        "timeStamp": "1640995200",
        "nonceStr": "abc123",
        "package": "prepay_id=wx123",
        "signType": "RSA",
        "paySign": "signature"
    }
}
```

### 2. 支付回调接口

**接口地址**: `POST /pay/notice`

**回调参数**:
```json
{
    "status": "S",
    "code": "0000",
    "msg": "成功",
    "data": {
        "outTradeNo": "order_123",
        "channelType": "WX",
        "payChannelOrderNo": "wx_transaction_123",
        "gmtPayment": "2023-01-01 12:00:00",
        "totalAmount": "100"
    },
    "sign": "signature"
}
```

### 3. 订单查询接口

**接口地址**: `POST /pay/orderQuery`

**请求参数**:
```json
{
    "outTradeNo": "order_123"
}
```

**响应参数**:
```json
{
    "status": "S",
    "code": "0000",
    "data": {
        "outTradeNo": "order_123",
        "orderStatus": "SUCCESS",
        "totalAmount": "100",
        "payTime": "2023-01-01 12:00:00"
    }
}
```

## 💻 代码示例

### 前端调用示例

#### 微信小程序
```javascript
// 发起支付
wx.request({
    url: 'https://api.example.com/pay/jsPay',
    method: 'POST',
    data: {
        totalAmount: 100,
        openId: 'openid_123',
        userId: 'user_123',
        outTradeNo: 'order_123',
        mchid: '1366993712427'
    },
    success: function(res) {
        if (res.data.channelType === 'WX') {
            const payInfo = JSON.parse(res.data.data);
            wx.requestPayment({
                ...payInfo,
                success: function() {
                    wx.showToast({ title: '支付成功' });
                },
                fail: function() {
                    wx.showToast({ title: '支付失败' });
                }
            });
        }
    }
});
```

#### H5网页
```javascript
// 微信H5支付
function wxPay() {
    $.ajax({
        url: '/pay/jsPay',
        type: 'POST',
        data: {
            totalAmount: Math.round(amount * 100),
            openId: openId,
            outTradeNo: orderId,
            mchid: merchantId
        },
        success: function(data) {
            if (data.channelType === 'WX') {
                const payInfo = JSON.parse(data.data);
                WeixinJSBridge.invoke('getBrandWCPayRequest', {
                    appId: payInfo.appId,
                    timeStamp: payInfo.timeStamp,
                    nonceStr: payInfo.nonceStr,
                    package: payInfo.package,
                    signType: payInfo.signType,
                    paySign: payInfo.paySign
                }, function(res) {
                    if (res.err_msg === "get_brand_wcpay_request:ok") {
                        window.location = "/success";
                    }
                });
            }
        }
    });
}
```

### 后端实现示例

#### 支付控制器
```java
@RestController
@RequestMapping("/pay")
public class PaymentController {
    
    @PostMapping("/jsPay")
    public ResponseEntity<String> createPayment(@RequestBody PaymentRequest request) {
        try {
            // 1. 验证订单
            Order order = orderService.getByOrderId(request.getOutTradeNo());
            if (order == null || order.getStatus() != 0) {
                return ResponseEntity.badRequest().body("订单状态错误");
            }
            
            // 2. 构建支付参数
            JsPay jsPay = buildPaymentParams(request);
            
            // 3. 选择机构号
            PaymentConfig config = selectPaymentConfig(request.getUserId());
            
            // 4. 发送支付请求
            String response = huiLianPayService.createPayment(jsPay, config);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("支付创建失败", e);
            return ResponseEntity.status(500).body("支付创建失败");
        }
    }
    
    @PostMapping("/notice")
    public String paymentNotify(HttpServletRequest request) {
        try {
            // 1. 读取回调数据
            String requestBody = getRequestBody(request);
            
            // 2. 验证签名
            if (!rsaUtil.verifyResponseSign(requestBody)) {
                log.error("支付回调验签失败");
                return "fail";
            }
            
            // 3. 处理支付结果
            PaymentNotifyData data = parseNotifyData(requestBody);
            orderService.updatePaymentStatus(data);
            
            // 4. 通知相关系统
            notificationService.notifyPaymentSuccess(data.getOutTradeNo());
            
            return "success";
        } catch (Exception e) {
            log.error("支付回调处理失败", e);
            return "fail";
        }
    }
}
```

#### 汇联支付服务
```java
@Service
public class HuiLianPayService {
    
    public String createPayment(JsPay jsPay, PaymentConfig config) throws Exception {
        // 1. 构建请求参数
        String param = HttpsMain.format(jsPay, FunctionEnum.JS_PAY, 
                                      config.getAgencyNo(), config.getPrivateKey());
        
        // 2. 发送请求
        String response = HttpsMain.httpReq(MyConfig.PayUrl, param);
        
        // 3. 验证响应
        if (!RsaUtil.verifyResponseSign(response)) {
            throw new Exception("响应验签失败");
        }
        
        // 4. 解析响应
        return parsePaymentResponse(response);
    }
    
    private String parsePaymentResponse(String response) {
        JSONObject jsonObject = JSON.parseObject(response);
        String status = jsonObject.getString("status");
        String code = jsonObject.getString("code");
        
        if ("S".equals(status) && "0000".equals(code)) {
            Map<String, String> result = JSON.parseObject(
                jsonObject.getString("data"), 
                new TypeReference<Map<String, String>>() {}
            );
            
            Map<String, String> paymentData = new HashMap<>();
            paymentData.put("channelType", jsPay.getChannelType());
            
            if (ChannelTypeEnum.ALI.getCode().equals(jsPay.getChannelType())) {
                paymentData.put("data", result.get("prePayId"));
            } else {
                paymentData.put("data", result.get("payInfo"));
            }
            
            return JSON.toJSONString(paymentData);
        } else {
            throw new RuntimeException(jsonObject.getString("msg"));
        }
    }
}
```

## 🚀 部署指南

### 环境要求

- **Java**: JDK 1.8+
- **Spring Boot**: 2.x
- **数据库**: MySQL 5.7+
- **Redis**: 6.0+
- **SSL证书**: 支付回调需要HTTPS

### 部署步骤

#### 1. 配置文件设置
```bash
# 复制配置文件
cp application-prod.yml.template application-prod.yml

# 修改配置
vim application-prod.yml
```

#### 2. 数据库初始化
```sql
-- 创建支付相关表
CREATE TABLE `payment_orders` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `order_id` varchar(64) NOT NULL COMMENT '订单号',
    `transaction_id` varchar(64) DEFAULT NULL COMMENT '交易号',
    `amount` int(11) NOT NULL COMMENT '支付金额(分)',
    `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '支付状态',
    `pay_way` tinyint(4) DEFAULT NULL COMMENT '支付方式',
    `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_order_id` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### 3. 启动应用
```bash
# 打包应用
mvn clean package -P prod

# 启动应用
java -jar target/payment-service.jar --spring.profiles.active=prod
```

#### 4. 配置反向代理
```nginx
server {
    listen 443 ssl;
    server_name api.example.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    location /pay/ {
        proxy_pass http://localhost:8080/pay/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 监控和日志

#### 1. 日志配置
```xml
<!-- logback-spring.xml -->
<configuration>
    <appender name="PAYMENT_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/payment.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/payment.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
    
    <logger name="com.ruoyi.web.controller.pay" level="INFO" additivity="false">
        <appender-ref ref="PAYMENT_FILE"/>
    </logger>
</configuration>
```

#### 2. 健康检查
```java
@RestController
public class HealthController {
    
    @GetMapping("/health")
    public Map<String, Object> health() {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "UP");
        result.put("timestamp", System.currentTimeMillis());
        
        // 检查汇联网关连通性
        try {
            String response = HttpsMain.httpReq(MyConfig.PayUrl + "/health", "{}");
            result.put("huilian", "UP");
        } catch (Exception e) {
            result.put("huilian", "DOWN");
        }
        
        return result;
    }
}
```

## 🔒 安全机制

### RSA签名验证

#### 签名生成
```java
public static String sign(String source, String privateKey) {
    try {
        PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(Base64.decodeBase64(privateKey));
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PrivateKey privateK = keyFactory.generatePrivate(pkcs8KeySpec);

        Signature signature = Signature.getInstance("SHA256WithRSA");
        signature.initSign(privateK);
        signature.update(source.getBytes("UTF-8"));

        return Base64.encodeBase64String(signature.sign());
    } catch (Exception e) {
        throw new RuntimeException("RSA签名失败", e);
    }
}
```

#### 签名验证
```java
public static boolean verifyResponseSign(String response) {
    JSONObject json = JSONObject.parseObject(response, Feature.OrderedField);
    String sign = json.getString("sign");
    json.remove("sign");

    return verifySign(JSON.toJSONString(json), sign, MyConfig.huilian_publicKey);
}
```

### 数据加密

- **传输加密**: 全程HTTPS通信
- **签名算法**: SHA256WithRSA
- **密钥管理**: 公私钥分离存储
- **参数验证**: 严格的参数校验机制

## 🚨 错误处理

### 常见错误码

| 错误码 | 错误信息 | 解决方案 |
|--------|---------|---------|
| 0000 | 成功 | 正常处理 |
| 1001 | 签名验证失败 | 检查私钥和签名算法 |
| 1002 | 参数错误 | 检查必填参数 |
| 1003 | 商户不存在 | 检查商户号配置 |
| 1004 | 订单已存在 | 使用新的订单号 |
| 1005 | 金额错误 | 检查金额格式和范围 |
| 2001 | 支付渠道不可用 | 切换其他支付渠道 |
| 2002 | 支付超时 | 重新发起支付 |
| 3001 | 系统繁忙 | 稍后重试 |

### 异常处理策略

```java
@ControllerAdvice
public class PaymentExceptionHandler {

    @ExceptionHandler(PaymentException.class)
    public ResponseEntity<ErrorResponse> handlePaymentException(PaymentException e) {
        ErrorResponse error = new ErrorResponse();
        error.setCode(e.getCode());
        error.setMessage(e.getMessage());
        error.setTimestamp(System.currentTimeMillis());

        log.error("支付异常: {}", e.getMessage(), e);
        return ResponseEntity.status(500).body(error);
    }

    @ExceptionHandler(SignatureException.class)
    public ResponseEntity<ErrorResponse> handleSignatureException(SignatureException e) {
        ErrorResponse error = new ErrorResponse();
        error.setCode("SIGNATURE_ERROR");
        error.setMessage("签名验证失败");
        error.setTimestamp(System.currentTimeMillis());

        log.error("签名验证失败: {}", e.getMessage(), e);
        return ResponseEntity.status(400).body(error);
    }
}
```

## � 性能优化

### 连接池配置

```java
@Configuration
public class HttpClientConfig {

    @Bean
    public CloseableHttpClient httpClient() {
        PoolingHttpClientConnectionManager connectionManager =
            new PoolingHttpClientConnectionManager();
        connectionManager.setMaxTotal(200);
        connectionManager.setDefaultMaxPerRoute(50);

        RequestConfig requestConfig = RequestConfig.custom()
            .setConnectTimeout(5000)
            .setSocketTimeout(10000)
            .setConnectionRequestTimeout(3000)
            .build();

        return HttpClients.custom()
            .setConnectionManager(connectionManager)
            .setDefaultRequestConfig(requestConfig)
            .build();
    }
}
```

### 缓存策略

```java
@Service
public class PaymentCacheService {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    // 缓存支付参数，避免重复请求
    public void cachePaymentParams(String orderId, Object params) {
        String key = "payment:params:" + orderId;
        redisTemplate.opsForValue().set(key, params, 10, TimeUnit.MINUTES);
    }

    // 缓存支付结果，避免重复处理
    public void cachePaymentResult(String orderId, Object result) {
        String key = "payment:result:" + orderId;
        redisTemplate.opsForValue().set(key, result, 1, TimeUnit.HOURS);
    }
}
```

## 🧪 测试指南

### 单元测试

```java
@SpringBootTest
public class PaymentServiceTest {

    @Autowired
    private HuiLianPayService paymentService;

    @Test
    public void testCreatePayment() {
        // 构建测试数据
        JsPay jsPay = new JsPay();
        jsPay.setOutTradeNo("TEST_" + System.currentTimeMillis());
        jsPay.setTotalAmount("1");
        jsPay.setChannelType("WX");
        jsPay.setOpenId("test_openid");

        PaymentConfig config = new PaymentConfig();
        config.setAgencyNo(MyConfig.AgencyNo);
        config.setPrivateKey(MyConfig.PrivateKey);

        // 执行测试
        String result = paymentService.createPayment(jsPay, config);

        // 验证结果
        assertNotNull(result);
        JSONObject json = JSON.parseObject(result);
        assertEquals("WX", json.getString("channelType"));
    }
}
```

### 集成测试

```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class PaymentIntegrationTest {

    @Autowired
    private TestRestTemplate restTemplate;

    @Test
    public void testPaymentFlow() {
        // 1. 创建支付
        PaymentRequest request = new PaymentRequest();
        request.setTotalAmount(100);
        request.setOutTradeNo("TEST_ORDER_" + System.currentTimeMillis());

        ResponseEntity<String> response = restTemplate.postForEntity(
            "/pay/jsPay", request, String.class);

        assertEquals(HttpStatus.OK, response.getStatusCode());

        // 2. 模拟支付回调
        PaymentNotifyData notifyData = new PaymentNotifyData();
        notifyData.setOutTradeNo(request.getOutTradeNo());
        notifyData.setStatus("SUCCESS");

        ResponseEntity<String> notifyResponse = restTemplate.postForEntity(
            "/pay/notice", notifyData, String.class);

        assertEquals("success", notifyResponse.getBody());
    }
}
```

## 📈 监控告警

### 关键指标监控

```java
@Component
public class PaymentMetrics {

    private final MeterRegistry meterRegistry;
    private final Counter paymentSuccessCounter;
    private final Counter paymentFailureCounter;
    private final Timer paymentTimer;

    public PaymentMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.paymentSuccessCounter = Counter.builder("payment.success")
            .description("支付成功次数")
            .register(meterRegistry);
        this.paymentFailureCounter = Counter.builder("payment.failure")
            .description("支付失败次数")
            .register(meterRegistry);
        this.paymentTimer = Timer.builder("payment.duration")
            .description("支付处理时间")
            .register(meterRegistry);
    }

    public void recordPaymentSuccess() {
        paymentSuccessCounter.increment();
    }

    public void recordPaymentFailure() {
        paymentFailureCounter.increment();
    }

    public Timer.Sample startPaymentTimer() {
        return Timer.start(meterRegistry);
    }
}
```

### 告警配置

```yaml
# Prometheus告警规则
groups:
  - name: payment.rules
    rules:
      - alert: PaymentFailureRateHigh
        expr: rate(payment_failure_total[5m]) / rate(payment_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "支付失败率过高"
          description: "支付失败率超过10%，当前值: {{ $value }}"

      - alert: PaymentResponseTimeSlow
        expr: histogram_quantile(0.95, rate(payment_duration_seconds_bucket[5m])) > 10
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "支付响应时间过慢"
          description: "95%的支付请求响应时间超过10秒"
```

---

## �📞 技术支持

### 联系方式

- **技术文档**: [内部文档系统]
- **问题反馈**: [JIRA系统]
- **紧急联系**: 技术支持热线

### 版本信息

- **文档版本**: v1.0
- **系统版本**: v2.1.0
- **最后更新**: 2024-01-15
- **维护团队**: 支付系统开发组

### 更新日志

| 版本 | 日期 | 更新内容 |
|------|------|---------|
| v1.0 | 2024-01-15 | 初始版本，包含完整架构文档 |
| v1.1 | 2024-01-20 | 新增国际支付MAYA接入 |
| v1.2 | 2024-02-01 | 优化智能路由算法 |

如有问题，请联系技术支持团队或查看相关API文档。
