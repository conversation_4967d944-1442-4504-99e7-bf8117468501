<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ruoyi.device.mapper.DeviceLogMapper">

    <resultMap type="DeviceLog" id="DeviceLogResult">
        <result property="id"    column="id"    />
        <result property="deviceId"    column="device_id"    />
        <result property="log"    column="log"    />
        <result property="time"    column="time"    />
        <result property="startTime"    column="startTime"    />
        <result property="endTime"    column="endTime"    />
    </resultMap>
    <insert id="addLog">
        insert into device_log (device_id,log,time) values (#{deviceId},#{log},#{time})
    </insert>
    <delete id="deleteLog" parameterType="String">
        delete from device_log where device_id in
        <foreach collection="array" item="deviceId" open="(" close=")" separator=",">
            #{deviceId}
        </foreach>
    </delete>
    <select id="selectLog" resultType="com.ruoyi.common.core.page.TableDataInfo">
        select * from device_log
        <where>
            <if test="deviceId != null"> and device_id = #{deviceId}</if>
            <if test="startTime != null and endTime != null"> and time BETWEEN  #{startTime} AND  #{endTime}</if>
        </where>
    </select>


</mapper>