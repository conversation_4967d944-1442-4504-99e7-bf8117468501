<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.voucher.mapper.VoucherOrderMapper">

    <resultMap type="VoucherOrder" id="VoucherOrderResult">
        <result property="id" column="id"/>
        <result property="customerId" column="customer_id"/>
        <result property="voucherCode" column="voucher_code"/>
        <result property="voucherId" column="voucher_id"/>
        <result property="status" column="status"/>
        <result property="useCount" column="use_count"/>
        <result property="payTime" column="pay_time"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectVoucherOrderVo">
        select id,
               customer_id,
               voucher_code,
               voucher_id,
               status,
               use_count,
               pay_time,
               create_time,
               create_by,
               update_time,
               update_by,
               remark
        from voucher_order
    </sql>

    <select id="selectVoucherOrderList" parameterType="VoucherOrder" resultMap="VoucherOrderResult">
        <include refid="selectVoucherOrderVo"/>
        <where>
            <if test="customerId != null  and customerId != ''">and customer_id = #{customerId}</if>
            <if test="voucherCode != null  and voucherCode != ''">and voucher_code = #{voucherCode}</if>
            <if test="voucherId != null  and voucherId != ''">and voucher_id = #{voucherId}</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''">
                and create_time between #{params.beginCreateTime} and #{params.endCreateTime}
            </if>
        </where>
    </select>

    <select id="selectVoucherOrderById" parameterType="String" resultMap="VoucherOrderResult">
        <include refid="selectVoucherOrderVo"/>
        where id = #{id}
    </select>

    <insert id="insertVoucherOrder" parameterType="VoucherOrder">
        insert into voucher_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="customerId != null">customer_id,</if>
            <if test="voucherCode != null">voucher_code,</if>
            <if test="voucherId != null and voucherId != ''">voucher_id,</if>
            <if test="status != null">status,</if>
            <if test="payTime != null">pay_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="customerId != null">#{customerId},</if>
            <if test="voucherCode != null">#{voucherCode},</if>
            <if test="voucherId != null and voucherId != ''">#{voucherId},</if>
            <if test="status != null">#{status},</if>
            <if test="payTime != null">#{payTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateVoucherOrder" parameterType="VoucherOrder">
        update voucher_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="voucherCode != null">voucher_code = #{voucherCode},</if>
            <if test="voucherId != null and voucherId != ''">voucher_id = #{voucherId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="payTime != null">pay_time = #{payTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteVoucherOrderById" parameterType="String">
        delete
        from voucher_order
        where id = #{id}
    </delete>

    <delete id="deleteVoucherOrderByIds" parameterType="String">
        delete from voucher_order where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>