package com.yunchuang.wxapp.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yunchuang.wxapp.model.domain.Point;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 点位Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
@Mapper
public interface PointMapper extends BaseMapper<Point> {
    /**
     * 查询点位
     *
     * @param id 点位主键
     * @return 点位
     */
    public Point selectPointById(Long id);

    /**
     * 查询点位列表
     *
     * @param point 点位
     * @return 点位集合
     */
    public List<Point> selectPointList(Point point);

    /**
     * 新增点位
     *
     * @param point 点位
     * @return 结果
     */
    public int insertPoint(Point point);

    /**
     * 修改点位
     *
     * @param point 点位
     * @return 结果
     */
    public int updatePoint(Point point);

    /**
     * 删除点位
     *
     * @param id 点位主键
     * @return 结果
     */
    public int deletePointById(Long id);

    /**
     * 批量删除点位
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePointByIds(Long[] ids);

    @Select("SELECT *, " +
            "ST_Distance_Sphere(POINT(#{longitude}, #{latitude}), location) AS distance " +
            "FROM wxapp_point " +
            "${ew.customSqlSegment} " + // 引用 LambdaQueryWrapper 生成的查询条件
            "ORDER BY distance " +
            "LIMIT #{limit}")
    List<Point> selectNearestPointsWithWrapper(@Param("longitude") Double longitude,
                                               @Param("latitude") Double latitude,
                                               @Param("limit") int limit,
                                               @Param("ew") LambdaQueryWrapper<Point> queryWrapper);
}
