package com.ruoyi.dto;

import lombok.Data;

@Data
public class UserBeauty {
    private String userName;//用户名
    private Long loginTime; //登录时间戳

    //拥有美颜的种类
    private Boolean mopi;
    private Boolean meibai;
    private Boolean wuguanliti;
    private Boolean liangyan;
    private Boolean hongrun;
    private Boolean shoulian;
    private Boolean dayan;

    //拥有美颜的范围
    private int mopiMax;
    private int mopiMin;
    private int meibaiMax;
    private int meibaiMin;
    private int wuguanlitiMax;
    private int wuguanlitiMin;
    private int liangyanMax;
    private int liangyanMin;
    private int hongrunMax;
    private int hongrunMin;
    private int shoulianMax;
    private int shoulianMin;
    private int dayanMax;
    private int dayanMin;
}
