package com.ruoyi.wxservice.model.dto.template;

import com.ruoyi.wxservice.model.dto.TemplateDataItem;
import lombok.Data;

/**
 * 设备补货通知模板 DTO
 * <p>
 * 该类用于封装设备补货通知的模板消息数据
 * </p>
 */
@Data
public class DeviceReplTplDTO {

    /**
     * 设备名称
     */
    private TemplateDataItem<String> thing5;

    /**
     * 设备ID
     */
    private TemplateDataItem<String> character_string4;

    /**
     * 设备地址
     */
    private TemplateDataItem<String> thing8;

    /**
     * 剩余库存
     */
    private TemplateDataItem<Long> character_string25;

    /**
     * 构建
     *
     * @param deviceName          设备名
     * @param deviceId            设备ID
     * @param deviceAddress       设备地址
     * @param consumablesQuantity 耗材余量
     * @return 设备补货通知模板 DTO
     */
    public static DeviceReplTplDTO build(String deviceName, String deviceId, String deviceAddress, Long consumablesQuantity) {
        DeviceReplTplDTO deviceReplTplDTO = new DeviceReplTplDTO();
        deviceReplTplDTO.thing5 = new TemplateDataItem<>(deviceName);
        deviceReplTplDTO.character_string4 = new TemplateDataItem<>(deviceId);
        deviceReplTplDTO.thing8 = new TemplateDataItem<>(deviceAddress);
        deviceReplTplDTO.character_string25 = new TemplateDataItem<>(consumablesQuantity);
        return deviceReplTplDTO;
    }
}
