package com.ruoyi.wxservice.model.dto.template;

import com.ruoyi.wxservice.model.dto.TemplateDataItem;
import lombok.Data;

/**
 * 耗材余量预警消息模板
 */
@Data
public class ConsumQtyWarnTplDTO {

    /**
     * 设备名
     */
    private TemplateDataItem<String> deviceName;

    /**
     * 设备ID
     */
    private TemplateDataItem<String> deviceId;

    /**
     * 耗材余量
     */
    private TemplateDataItem<Long> consumablesQuantity;

    /**
     * 简易构建
     *
     * @param deviceName          设备名
     * @param deviceId            设备ID
     * @param consumablesQuantity 耗材余量
     * @param color               颜色
     * @return 耗材余量预警消息模板
     */
    public static ConsumQtyWarnTplDTO easyBuild(String deviceName, String deviceId, Long consumablesQuantity, String color) {
        ConsumQtyWarnTplDTO consumQtyWarnTplDTO = new ConsumQtyWarnTplDTO();
        consumQtyWarnTplDTO.deviceName = new TemplateDataItem<>(deviceName, color);
        consumQtyWarnTplDTO.deviceId = new TemplateDataItem<>(deviceId, color);
        consumQtyWarnTplDTO.consumablesQuantity = new TemplateDataItem<>(consumablesQuantity, color);
        return consumQtyWarnTplDTO;
    }

    /**
     * 简易构建
     *
     * @param deviceName          设备名
     * @param deviceId            设备ID
     * @param consumablesQuantity 耗材余量
     * @return 耗材余量预警消息模板
     */
    public static ConsumQtyWarnTplDTO easyBuild(String deviceName, String deviceId, Long consumablesQuantity) {
        ConsumQtyWarnTplDTO consumQtyWarnTplDTO = new ConsumQtyWarnTplDTO();
        consumQtyWarnTplDTO.deviceName = new TemplateDataItem<>(deviceName);
        consumQtyWarnTplDTO.deviceId = new TemplateDataItem<>(deviceId);
        consumQtyWarnTplDTO.consumablesQuantity = new TemplateDataItem<>(consumablesQuantity);
        return consumQtyWarnTplDTO;
    }

}
