package com.ruoyi.wxservice.model.enums.exception;

import lombok.Getter;

/**
 * 微信服务号 - 业务异常枚举
 */
@Getter
public enum WxserviceBusinessExceptionCode {

    EC_60101(60101, "调用微信接口失败"),
    EC_60102(60102, "获取openid失败"),
    EC_60103(60103, "获取access_token失败"),
    EC_60104(60104, "生成临时二维码失败"),

    EC_60201(60201, "注册失败"),
    EC_60202(60202, "设备不存在"),
    EC_60203(60203, "服务号配置不存在"),

    EC_60301(60301, "数据不存在"),
    EC_60302(60302, "数据插入失败"),
    EC_60303(60303, "数据更新失败"),
    EC_60304(60304, "数据删除失败"),
    EC_60305(60305, "数据已存在"),


    // 未知异常
    UNKNOWN_EXCEPTION(500, "未知异常");

    private final Integer code;
    private final String message;

    WxserviceBusinessExceptionCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
}
