package com.ruoyi.web.controller.pay;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.config.MyConfig;
import com.ruoyi.device.domain.DeviceCamera;
import com.ruoyi.device.service.IDeviceCameraService;
import com.ruoyi.device.service.IDevicePrinterService;
import com.ruoyi.enums.ChannelTypeEnum;
import com.ruoyi.enums.FunctionEnum;
import com.ruoyi.fee.domain.Fee;
import com.ruoyi.fee.service.IFeeService;
import com.ruoyi.order.domain.OrderCamera;
import com.ruoyi.order.domain.OrderPrinter;
import com.ruoyi.order.domain.OrderCollect;
import com.ruoyi.order.service.IOrderCameraService;
import com.ruoyi.order.service.********************;
import com.ruoyi.order.service.IOrderCollectService;
import com.ruoyi.photo.domain.UserPhoto;
import com.ruoyi.photo.service.IUserPhotoService;
import com.ruoyi.po.JsPay;
import com.ruoyi.po.SplitBunch;
import com.ruoyi.po.SplitInfo;
import com.ruoyi.socket.WebSocketServer;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.utils.HttpsMain;
import com.ruoyi.utils.RsaUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;

import static com.ruoyi.common.constant.OrderConstants.*;


@Slf4j
@Controller
@RequestMapping("/pay")
public class HLPay {
    @Autowired
    private IOrderCameraService orderCameraService;
    @Autowired
    private ******************** orderPrinterService;
    @Autowired
    private ISysUserService userService;
    @Autowired
    private IOrderCollectService orderCollectService;
    @Autowired
    private IDeviceCameraService deviceCameraService;
    @Autowired
    private IFeeService feeService;
    @Autowired
    private IUserPhotoService userPhotoService;
    @Autowired
    private IDevicePrinterService devicePrinterService;

    //汇联获取openid才能创建订单
    @GetMapping("/getOpenId")
    public Object getOpenId(HttpServletRequest request) throws UnsupportedEncodingException {

        String attach = request.getParameter("attach");
        String userInfo = request.getParameter("userInfo");
        JSONObject jsonObject = JSONObject.parseObject(userInfo);

        System.out.println(userInfo);

//        String unionid = null;
        String openId = null;
        String userId = null;
        if (jsonObject != null) {
//            unionid = jsonObject.getString("unionid");
            openId = jsonObject.getString("openid");
            userId = jsonObject.getString("userid");
        }

        if (attach == null || (openId == null && userId == null)) return null;
        OrderCamera orderCamera = orderCameraService.selectOrderCameraByOrderId(attach);

        WebSocketServer.sendInfo("smcg", orderCamera.getDeviceId()); // 主推信息->扫码成功

        SysUser sysUser = userService.selectUserById(orderCamera.getUserId());
        if (sysUser.getDeptId() != 201 && !sysUser.getDept().getAncestors().contains("201")) {
            // TODO: 一拍即合 云创 支付http://ypjh.cameraon.store
            String url = "http://ypjh.cameraon.store/pay";
            url += "?totalAmount=" + orderCamera.getOrderPrice();
            url += "&openId=" + openId;
            url += "&userId=" + userId;
            url += "&orderId=" + attach;
            url += "&photoTypeName=" + URLEncoder.encode(orderCamera.getProductDescription(),"UTF-8") ;
            url += "&mchid=" + orderCamera.getMchid();

            // 重定向到前端支付页面
            return "redirect:" + url;
        }


        ModelAndView modelAndView = null;

        if (orderCamera.getMchid().equals("1398949712874")) //乐高的商户号有定制的支付页面
            modelAndView = new ModelAndView("jsPayLego");
        else
            modelAndView = new ModelAndView("jsPay");

        modelAndView.addObject("totalAmount", orderCamera.getOrderPrice());
        modelAndView.addObject("openId", openId);
        modelAndView.addObject("userId", userId);
        modelAndView.addObject("orderId", attach);
        modelAndView.addObject("mchid", orderCamera.getMchid());

        return modelAndView;
    }

    /**
     * 发起JS支付
     *
     * @param totalAmount
     * @return
     * @throws IOException
     */

    @ResponseBody
    @PostMapping("/jsPay")
    public String doJsPay(int totalAmount, String openId, String userId, String outTradeNo, String mchid) throws Exception {

        OrderCamera orderCamera = orderCameraService.selectOrderCameraByOrderId(outTradeNo);
        if (orderCamera == null) {
            return "订单号错误";
        }

        SysUser user = userService.selectUserById(orderCamera.getUserId());
        if (user == null) {
            return "用户绑定错误";
        }

        boolean isPDL = false;
        String ancestors = user.getDept().getAncestors();
        Long deptId = user.getDept().getDeptId();
            if (ancestors.contains("201") || deptId == 201) {
                isPDL = true;
            }


        if (orderCamera.getOrderStatus() != 0) {
            return "订单状态错误" + orderCamera.getOrderStatus();
        }

        JsPay jsPay = new JsPay();
        jsPay.setOutTradeNo(outTradeNo);
        jsPay.setWxAppId(MyConfig.WX_APPID);
        jsPay.setBody("拍照付款");
        jsPay.setHlMerchantId(mchid);
        /**去掉小数点后两位*/
        jsPay.setTotalAmount(String.valueOf(totalAmount));

        //支付宝openId 支付类型 支付宝
        boolean aNull = openId == null || openId.equals("") || openId.equals("null");
        jsPay.setOpenId(aNull ? userId : openId);
        orderCamera.setOpenid(aNull ? userId : openId);
        jsPay.setChannelType(aNull ? ChannelTypeEnum.ALI.getCode() : ChannelTypeEnum.WX.getCode());


        jsPay.setNotifyUrl(MyConfig.Notice_URL);   //回调地址
        jsPay.setSucUrl("https://fotobox.cameraon.store/digitalPhotoTongNiu?orderId=" + outTradeNo);  //支付成功跳转页面 todo 需动态设置


        DeviceCamera device = deviceCameraService.getById(orderCamera.getDeviceId());

        if (device == null)
            return "设备绑定异常";


        String subAccount = device.getSubAccount();
        Fee photoFee = feeService.query().eq("photo_type", orderCamera.getPhotoType()).one();

        if (subAccount != null && !subAccount.equals("")) {// 正常分账


            String[] FZitem = subAccount.split(",");
            int length = FZitem.length;


            //新分账逻辑//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
            String[] peopleMchids = new String[length];
            int[] peopleRates = new int[length];


            for (int i = 0; i < length; i++) {
                String[] people = FZitem[i].split(":");
                peopleMchids[i] = people[0];                       //商户号列表
                peopleRates[i] = Integer.parseInt(people[1]);      //分账比例列表
            }
            int[] moneys = splitInt(totalAmount, peopleRates);     //分钱列表

            Map<String, Integer> peopleMoneyMap = new HashMap<>();
            for (int i = 0; i < length; i++) {
                peopleMoneyMap.put(peopleMchids[i], moneys[i]);      // key：商户    value：分账金额
            }

            //如果特定类型收取手续费
            int fee = 0;
            if (photoFee != null && (photoFee.getExcludeDevice() == null || !photoFee.getExcludeDevice().contains(orderCamera.getDeviceId()))) {  //todo 不生效 ！！！！！！
                Integer chargeType = photoFee.getChargeType();

                if (chargeType == 1) { //金额
                    fee = photoFee.getFeeAmount();
                } else {               //比例
                    float feeRate = photoFee.getFeeRate();
                    fee = (int) (totalAmount * feeRate);
                }
            }
            int i = 0;
            if (fee > 0) {
                if (peopleMoneyMap.containsKey(YC_MCHID)) {
                    peopleMoneyMap.put(YC_MCHID, peopleMoneyMap.get(YC_MCHID) + fee);
                } else if (peopleMoneyMap.containsKey(PDL_YC_MCHID)) {
                    peopleMoneyMap.put(PDL_YC_MCHID, peopleMoneyMap.get(PDL_YC_MCHID) + fee);
                } else {
                    peopleMoneyMap.put(!isPDL ? YC_MCHID : PDL_YC_MCHID, fee);
                }
                peopleMoneyMap.put(mchid, peopleMoneyMap.get(mchid) - fee);
                createInterfaceFeeOrder(orderCamera, !isPDL ? YC_MCHID : PDL_YC_MCHID, fee, i++);
            }

            List<SplitInfo> acctInfos = new ArrayList<>();

            for (Map.Entry<String, Integer> entry : peopleMoneyMap.entrySet()) {
                System.out.println("Key: " + entry.getKey() + ", Value: " + entry.getValue());
                acctInfos.add(new SplitInfo(String.valueOf(entry.getValue()), entry.getKey()));

                if (entry.getKey().equals(mchid)) {              //订单主人
                    orderCamera.setMoneyReceived(Long.valueOf(entry.getValue()));     //实收
                    orderCamera.setInterfaceFee((long) fee);//接口费
                    orderCamera.setCommission((long) Math.round(totalAmount * 0.006f));//手续费
                    orderCamera.setAccount(orderCamera.getMoneyReceived() - orderCamera.getInterfaceFee() - orderCamera.getCommission());  //到账
                    orderCameraService.updateOrderCamera(orderCamera);
                } else {
                    createFZOrder(orderCamera, entry.getKey(), entry.getValue() - fee, i++);
                }

            }

            //新分账逻辑///////////////////////////////////////////////////////////////

            SplitBunch splitBunch = new SplitBunch(String.valueOf(length), mchid, acctInfos);
            String jsonString = JSON.toJSONString(splitBunch);
            String encode = Base64.getEncoder().encodeToString(jsonString.getBytes());
            jsPay.setSplitBunch(encode);
        } else { //不分账
            if (photoFee != null && (photoFee.getExcludeDevice() == null || !photoFee.getExcludeDevice().equals("all") || !photoFee.getExcludeDevice().contains(orderCamera.getDeviceId()))) {  //特定类型收取手续费
                Integer chargeType = photoFee.getChargeType();
                int fee;
                if (chargeType == 1) { //金额
                    fee = photoFee.getFeeAmount();
                } else {                    //比例
                    float feeRate = photoFee.getFeeRate();
                    fee = (int) (totalAmount * feeRate);
                }
                List<SplitInfo> acctInfos = new ArrayList<>();
                acctInfos.add(new SplitInfo(String.valueOf(totalAmount - fee), mchid));
                acctInfos.add(new SplitInfo(String.valueOf(fee), !isPDL ? YC_MCHID : PDL_YC_MCHID));
                createInterfaceFeeOrder(orderCamera, !isPDL ? YC_MCHID : PDL_YC_MCHID, fee, 0);

                orderCamera.setMoneyReceived((long) (totalAmount));//实收
                orderCamera.setInterfaceFee((long) fee);//接口费
                orderCamera.setCommission((long) Math.round(totalAmount * 0.006f));//手续费
                orderCamera.setAccount(orderCamera.getMoneyReceived() - orderCamera.getInterfaceFee() - orderCamera.getCommission()); //到账
                orderCameraService.updateOrderCamera(orderCamera);

                SplitBunch splitBunch = new SplitBunch("2", mchid, acctInfos);
                String jsonString = JSON.toJSONString(splitBunch);
                String encode = Base64.getEncoder().encodeToString(jsonString.getBytes());
                jsPay.setSplitBunch(encode);
            } else {
                orderCamera.setMoneyReceived((long) totalAmount);
                orderCamera.setCommission((long) Math.round(totalAmount * 0.006f));//手续费
                orderCamera.setAccount(orderCamera.getMoneyReceived() - orderCamera.getCommission());
                orderCameraService.updateOrderCamera(orderCamera);
            }
        }


        String myAgencyNo = MyConfig.AgencyNo_yc;
        String myPrivateKey = MyConfig.PrivateKey_yc;
        if (isPDL) {
            myAgencyNo = MyConfig.AgencyNo;
            myPrivateKey = MyConfig.PrivateKey;
        }


        String param = HttpsMain.format(jsPay, FunctionEnum.JS_PAY, myAgencyNo, myPrivateKey);
        log.info("请求报文{}", param);

        String response = HttpsMain.httpReq(MyConfig.PayUrl, param);
        log.info("响应报文{}", response);
        //验签
        if (!RsaUtil.verifyResponseSign(response)) {
            throw new Exception("验签失败");
        }

        JSONObject jsonObject = JSON.parseObject(response);

        String status = jsonObject.getString("status");
        String code = jsonObject.getString("code");
        if ("S".equals(status) && "0000".equals(code)) {
            //JSON.parseObject(response).getString("data"),取得返回结果的其中一个值 转化为Map
            Map<String, String> res = JSON.parseObject(jsonObject.getString("data"), new TypeReference<Map<String, String>>() {
            });
            Map<String, String> map = new HashMap<>();
            map.put("channelType", jsPay.getChannelType());
            if (jsPay.getChannelType().equals(ChannelTypeEnum.ALI.getCode())) {
                map.put("data", res.get("prePayId"));
            } else {
                map.put("data", res.get("payInfo"));
            }
            log.info("返回出去的数据{}", map);

            return JSON.toJSONString(map);
        } else {
            String msg = jsonObject.getString("msg");
            throw new Exception(msg);
        }

    }

    private int[] splitInt(int total, int[] ratios) {
        int length = ratios.length;
        int[] results = new int[length];

        // 计算总比例
        int sumOfRatios = Arrays.stream(ratios).sum();

        // 初步分配
        int allocatedSum = 0;
        for (int i = 0; i < length; i++) {
            results[i] = total * ratios[i] / sumOfRatios;
            allocatedSum += results[i];
        }

        // 分配余数
        int remainder = total - allocatedSum;
        for (int i = 0; remainder > 0; i = (i + 1) % length) {
            results[i]++;
            remainder--;
        }
        return results;
    }


//    public static void main(String[] args) {
//        int total = 1000;
//        int[] ratios = {65, 5,15, 10,5};
//        int[] result = splitInt(total, ratios);
//        System.out.println("Result: " + Arrays.toString(result));
//        System.out.println("Sum: " + Arrays.stream(result).sum());
//    }


    private void createFZOrder(OrderCamera orderCamera, String mchId, int moneyReceived, int i) {
        List<SysUser> users = userService.query().eq("merchant_id", mchId).list();


        OrderCamera fzOrder = new OrderCamera();
        BeanUtils.copyProperties(orderCamera, fzOrder);
        fzOrder.setOrderId(i + "_" + orderCamera.getOrderId());
        fzOrder.setMchid(mchId);
        if (users.size() > 0)
            fzOrder.setUserId(users.get(0).getUserId());

        fzOrder.setMoneyReceived((long) moneyReceived);
        fzOrder.setCommission(0L);
        fzOrder.setInterfaceFee(0L);
        fzOrder.setAccount((long) moneyReceived);

        fzOrder.setProductDescription(fzOrder.getProductDescription() + "_分账订单");

        if (orderCameraService.getById(fzOrder.getOrderId()) == null)
            orderCameraService.save(fzOrder);
    }

    private void createInterfaceFeeOrder(OrderCamera orderCamera, String mchId, int interfaceFee, int i) {
        List<SysUser> users = userService.query().eq("merchant_id", mchId).list();


        OrderCamera InterfaceFeeOrder = new OrderCamera();
        BeanUtils.copyProperties(orderCamera, InterfaceFeeOrder);
        InterfaceFeeOrder.setOrderId(i + "_" + orderCamera.getOrderId());
        InterfaceFeeOrder.setMchid(mchId);
        if (users.size() > 0)
            InterfaceFeeOrder.setUserId(users.get(0).getUserId());

        InterfaceFeeOrder.setMoneyReceived(0L);
        InterfaceFeeOrder.setInterfaceFee((long) interfaceFee);
        InterfaceFeeOrder.setCommission(0L);
        InterfaceFeeOrder.setAccount((long) interfaceFee);

        if (orderCamera.getPhotoType() == PHOTO_TYPE_AI) {
            InterfaceFeeOrder.setProductDescription(InterfaceFeeOrder.getProductDescription() + "_AI-手续费");
        } else if (orderCamera.getPhotoType() == PHOTO_TYPE_MHF) {
            InterfaceFeeOrder.setProductDescription(InterfaceFeeOrder.getProductDescription() + "_漫画风-手续费");
        } else if (orderCamera.getPhotoType() == PHOTO_TYPE_ZJZ) {
            InterfaceFeeOrder.setProductDescription(InterfaceFeeOrder.getProductDescription() + "_证件照-手续费");
        }

        if (orderCameraService.getById(InterfaceFeeOrder.getOrderId()) == null)
            orderCameraService.save(InterfaceFeeOrder);
    }

    @ResponseBody
    @PostMapping("/notice")
    public String notice(HttpServletRequest request) throws Exception {
        BufferedReader reader = new BufferedReader(new InputStreamReader(request.getInputStream()));
        String tempLine = "";
        StringBuffer resultBuffer = new StringBuffer();
        while ((tempLine = reader.readLine()) != null) {
            resultBuffer.append(tempLine).append(System.getProperty("line.separator"));
        }
        String response = resultBuffer.toString();
        com.alibaba.fastjson2.JSONObject jsonObject = com.alibaba.fastjson2.JSONObject.parseObject(response);
        com.alibaba.fastjson2.JSONObject data = jsonObject.getJSONObject("data");
        System.out.println("回调报文：" + data);
        String orderId = data.getString("outTradeNo");
        String channelType = data.getString("channelType");
        String transactionId = data.getString("payChannelOrderNo");
        Date payTime = data.getDate("gmtPayment");

        // 判断是拍照机订单还是打印机订单
        OrderCamera orderCamera = orderCameraService.selectOrderCameraByOrderId(orderId);
        OrderPrinter orderPrinter = orderPrinterService.selectOrderPrinterByOrderId(orderId);

        if (orderCamera != null) {
            // 处理拍照机订单（原有逻辑）
            log.info("处理拍照机订单回调: orderId={}", orderId);
            orderCamera.setPayTime(payTime);
            orderCamera.setTransactionId(transactionId);
            orderCamera.setOrderStatus(1L);
            orderCamera.setPayWay(channelType.equals("WX") ? 1L : 2L);
        } else if (orderPrinter != null) {
            // 处理打印机订单，调用CPrinterPayController的静态方法
            log.info("处理打印机订单回调: orderId={}", orderId);
            return com.ruoyi.web.controller.wxapp.client.CPrinterPayController.handlePrinterOrderCallback(
                orderPrinter, channelType, transactionId, payTime,
                orderPrinterService, orderCollectService, devicePrinterService);
        } else {
            log.error("订单不存在: orderId={}", orderId);
            throw new Exception("订单不存在");
        }

        // 以下是拍照机订单的处理逻辑（只有orderCamera不为null时才执行）
        if (orderCamera == null) {
            return "SUCCESS"; // 打印机订单已在上面处理完成
        }

        /*
         * 回调报文：
         * {"agencyNo":"1228026","bankType":"","buyerLogonId":"oi4DO54BwIRSAyu9ffbQfAZI4z9o","buyerUserId":"oi4DO54BwIRSAyu9ffbQfAZI4z9o",
         * "channelType":"WX","function":"notice.pay","gmtPayment":"2024-01-03 23:44:43","hlMerchantId":"*************","merchantId":"E1807937218",
         * "merchantOrderNo":"17042966785449750310308","notifyTime":"2024-01-03 23:44:43","openId":"","orderNo":"17042966785449750310308","outTradeNo":"*****************",
         * "payChannelOrderNo":"4200002135202401033721717060","payType":"PUBLIC","subAppId":"wx8f7a9346308ee56f","totalAmount":"1","tradeStatus":"succ"}
         *
         *
         * 回调报文：{"agencyNo":"1228026","bankType":"","buyerLogonId":"oi4DO54BwIRSAyu9ffbQfAZI4z9o","buyerUserId":"oi4DO54BwIRSAyu9ffbQfAZI4z9o",
         * "channelType":"WX","function":"notice.pay","gmtPayment":"2024-01-04 22:42:39","hlMerchantId":"*************","merchantId":"E1808062449",
         * "merchantOrderNo":"24010422423121942828010","notifyTime":"2024-01-04 22:42:39","openId":"","orderNo":"24010422423121942828010",
         * "outTradeNo":"*****************","payChannelOrderNo":"4200002129202401044575934627","payType":"PUBLIC","subAppId":"wx8f7a9346308ee56f",
         * "totalAmount":"10","tradeStatus":"succ"}
         *
         * */


//        String merchantId = data.getString("hlMerchantId");

        orderCameraService.updateOrderCamera(orderCamera);
        WebSocketServer.sendInfo("zfcg", orderCamera.getDeviceId()); // 主推支付成功

        List<OrderCamera> _orders = orderCameraService.query().like("order_id", "_" + orderCamera.getOrderId()).list();
        if (_orders.size() > 0)//分账已支付
            orderCameraService.update().like("order_id", "_" + orderCamera.getOrderId()).set("order_status", 1).set("transaction_id", transactionId).set("pay_time", payTime).update();


        try {
            //汇总（主订单）
            OrderCollect collect = orderCollectService.query().eq("merchant_id", orderCamera.getMchid()).eq("time", DateUtils.parseDateToStr(DateUtils.YYYY_MM, orderCamera.getCreateTime())).one();

            if (collect == null) {
                collect = new OrderCollect();
            }

            Integer photoType = orderCamera.getPhotoType();
            setCollect(photoType, orderCamera, collect);

            //如果存在分账订单

            if (_orders.size() > 0) {
                //（汇总 分账人）
                for (OrderCamera _order : _orders) {
                    OrderCollect _collect = orderCollectService.query().eq("merchant_id", _order.getMchid()).eq("time", DateUtils.parseDateToStr(DateUtils.YYYY_MM, _order.getCreateTime())).one();
                    if (_collect == null) {
                        _collect = new OrderCollect();
                    }
                    setCollect(photoType, _order, _collect);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }


        //更新设备表中的设备总收入
        deviceCameraService.update().eq("device_id", orderCamera.getDeviceId()).setSql("count_price = count_price + " + orderCamera.getOrderPrice()).update();

        if (!RsaUtil.verifyResponseSign(response)) {
            throw new Exception("验签失败");
        }
        return "SUCCESS";
    }

    private void setCollect(Integer photoType, OrderCamera order, OrderCollect collect) {
        if (photoType == 1 || photoType == 2) {
            collect.setDttIncome(collect.getDttIncome() + order.getAccount());
            collect.setDttOrderCount(collect.getDttOrderCount() + 1);
        } else if (photoType == 3) {
            collect.setIdIncome(collect.getIdIncome() + order.getAccount());
            collect.setIdOrderCount(collect.getIdOrderCount() + 1);
        } else if (photoType == 4) {
            collect.setUploadIncome(collect.getUploadIncome() + order.getAccount());
            collect.setUploadOrderCount(collect.getUploadOrderCount() + 1);
        } else if (photoType == 5 || photoType == 100) {
            collect.setAiIncome(collect.getAiIncome() + order.getAccount());
            collect.setAiOrderCount(collect.getAiOrderCount() + 1);
        } else if (photoType == 10) {
            collect.setAddIncome(collect.getAddIncome() + order.getAccount());
            collect.setAddOrderCount(collect.getAddOrderCount() + 1);
        } else {
            collect.setOtherIncome(collect.getOtherIncome() + order.getAccount());
            collect.setOtherOrderCount(collect.getOtherOrderCount() + 1);
        }
        collect.setCount(collect.getCount() + order.getAccount());


        if (collect.getId() != null) {
            orderCollectService.updateById(collect);
        } else {
            orderCollectService.save(collect);
        }
    }

}
