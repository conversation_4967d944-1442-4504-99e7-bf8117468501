package com.yunchuang.wxapp.service.admin.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yunchuang.wxapp.mapper.CarouselImageMapper;
import com.yunchuang.wxapp.model.domain.CarouselImage;
import com.yunchuang.wxapp.service.admin.ICarouselImageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 轮播图Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-21
 */
@Service
public class CarouselImageServiceImpl extends ServiceImpl<CarouselImageMapper, CarouselImage> implements ICarouselImageService {
    @Autowired
    private CarouselImageMapper carouselImageMapper;

    /**
     * 查询轮播图
     *
     * @param id 轮播图主键
     * @return 轮播图
     */
    @Override
    public CarouselImage selectCarouselImageById(Long id) {
        return carouselImageMapper.selectCarouselImageById(id);
    }

    /**
     * 查询轮播图列表
     *
     * @param carouselImage 轮播图
     * @return 轮播图
     */
    @Override
    public List<CarouselImage> selectCarouselImageList(CarouselImage carouselImage) {
        return carouselImageMapper.selectCarouselImageList(carouselImage);
    }

    /**
     * 新增轮播图
     *
     * @param carouselImage 轮播图
     * @return 结果
     */
    @Override
    public int insertCarouselImage(CarouselImage carouselImage) {
        return carouselImageMapper.insertCarouselImage(carouselImage);
    }

    /**
     * 修改轮播图
     *
     * @param carouselImage 轮播图
     * @return 结果
     */
    @Override
    public int updateCarouselImage(CarouselImage carouselImage) {
        return carouselImageMapper.updateCarouselImage(carouselImage);
    }

    /**
     * 批量删除轮播图
     *
     * @param ids 需要删除的轮播图主键
     * @return 结果
     */
    @Override
    public int deleteCarouselImageByIds(Long[] ids) {
        return carouselImageMapper.deleteCarouselImageByIds(ids);
    }

    /**
     * 删除轮播图信息
     *
     * @param id 轮播图主键
     * @return 结果
     */
    @Override
    public int deleteCarouselImageById(Long id) {
        return carouselImageMapper.deleteCarouselImageById(id);
    }
}
