package com.ruoyi.voucher.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.voucher.domain.Voucher;
import com.ruoyi.voucher.mapper.VoucherMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.voucher.mapper.VoucherOrderMapper;
import com.ruoyi.voucher.domain.VoucherOrder;
import com.ruoyi.voucher.service.IVoucherOrderService;

/**
 * 优惠券订单Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-04-16
 */
@Service
public class VoucherOrderServiceImpl extends ServiceImpl<VoucherOrderMapper, VoucherOrder> implements IVoucherOrderService
{
    @Autowired
    private VoucherOrderMapper voucherOrderMapper;

    /**
     * 查询优惠券订单
     * 
     * @param id 优惠券订单主键
     * @return 优惠券订单
     */
    @Override
    public VoucherOrder selectVoucherOrderById(String id)
    {
        return voucherOrderMapper.selectVoucherOrderById(id);
    }

    /**
     * 查询优惠券订单列表
     * 
     * @param voucherOrder 优惠券订单
     * @return 优惠券订单
     */
    @Override
    public List<VoucherOrder> selectVoucherOrderList(VoucherOrder voucherOrder)
    {
        return voucherOrderMapper.selectVoucherOrderList(voucherOrder);
    }

    /**
     * 新增优惠券订单
     * 
     * @param voucherOrder 优惠券订单
     * @return 结果
     */
    @Override
    public int insertVoucherOrder(VoucherOrder voucherOrder)
    {
        voucherOrder.setCreateTime(DateUtils.getNowDate());
        return voucherOrderMapper.insertVoucherOrder(voucherOrder);
    }

    /**
     * 修改优惠券订单
     * 
     * @param voucherOrder 优惠券订单
     * @return 结果
     */
    @Override
    public int updateVoucherOrder(VoucherOrder voucherOrder)
    {
        voucherOrder.setUpdateTime(DateUtils.getNowDate());
        return voucherOrderMapper.updateVoucherOrder(voucherOrder);
    }

    /**
     * 批量删除优惠券订单
     * 
     * @param ids 需要删除的优惠券订单主键
     * @return 结果
     */
    @Override
    public int deleteVoucherOrderByIds(String[] ids)
    {
        return voucherOrderMapper.deleteVoucherOrderByIds(ids);
    }

    /**
     * 删除优惠券订单信息
     * 
     * @param id 优惠券订单主键
     * @return 结果
     */
    @Override
    public int deleteVoucherOrderById(String id)
    {
        return voucherOrderMapper.deleteVoucherOrderById(id);
    }
}
