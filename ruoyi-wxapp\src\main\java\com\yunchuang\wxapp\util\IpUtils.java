//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by <PERSON><PERSON><PERSON>lower decompiler)
//

package com.yunchuang.wxapp.util;

import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.common.utils.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.regex.Pattern;

public class IpUtils {
    public static final String REGX_0_255 = "(25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]\\d|\\d)";
    public static final String REGX_IP = "(((25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]\\d|\\d)\\.){3}(25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]\\d|\\d))";
    public static final String REGX_IP_WILDCARD = "(((\\*\\.){3}\\*)|((25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]\\d|\\d)(\\.\\*){3})|((25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]\\d|\\d)\\.(25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]\\d|\\d))(\\.\\*){2}|(((25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]\\d|\\d)\\.){3}\\*))";
    public static final String REGX_IP_SEG = "((((25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]\\d|\\d)\\.){3}(25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]\\d|\\d))\\-(((25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]\\d|\\d)\\.){3}(25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]\\d|\\d)))";
    private static final String IPv4_REGEX = "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$";
    private static final String IPv6_REGEX = "^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1[0-9]|[1-9]?[0-9]){0,1}[0-9])\\.){3,3}(25[0-5]|(2[0-4]|1[0-9]|[1-9]?[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1[0-9]|[1-9]?[0-9]){0,1}[0-9])\\.){3,3}(25[0-5]|(2[0-4]|1[0-9]|[1-9]?[0-9]){0,1}[0-9]))$";

    public IpUtils() {
    }

    public static String getClientIpAddr() {
        return getIpAddr(ServletUtils.getRequest());
    }

    public static String getIpAddr(HttpServletRequest request) {
        if (request == null) {
            return "unknown";
        } else {
            String ip = request.getHeader("x-forwarded-for");
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("Proxy-Client-IP");
            }

            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("X-Forwarded-For");
            }

            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("WL-Proxy-Client-IP");
            }

            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("X-Real-IP");
            }

            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("CF-Connecting-IP");
            }

            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getRemoteAddr();
            }

            return "0:0:0:0:0:0:0:1".equals(ip) ? "127.0.0.1" : getMultistageReverseProxyIp(ip);
        }
    }

    public static boolean isIpv4(String ip) {
        return Pattern.compile("^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$").matcher(ip).matches();
    }

    public static boolean isIpv6(String ip) {
        return Pattern.compile("^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1[0-9]|[1-9]?[0-9]){0,1}[0-9])\\.){3,3}(25[0-5]|(2[0-4]|1[0-9]|[1-9]?[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1[0-9]|[1-9]?[0-9]){0,1}[0-9])\\.){3,3}(25[0-5]|(2[0-4]|1[0-9]|[1-9]?[0-9]){0,1}[0-9]))$").matcher(ip).matches();
    }

//    public static boolean internalIp(String ip) {
//        byte[] addr = textToNumericFormatV4(ip);
//        return internalIp(addr) || "127.0.0.1".equals(ip);
//    }

    private static boolean internalIp(byte[] addr) {
        if (!StringUtils.isNull(addr) && addr.length >= 2) {
            byte b0 = addr[0];
            byte b1 = addr[1];
            byte SECTION_1 = 10;
            byte SECTION_2 = -84;
            byte SECTION_3 = 16;
            byte SECTION_4 = 31;
            byte SECTION_5 = -64;
            byte SECTION_6 = -88;
            switch (b0) {
                case -84:
                    if (b1 >= 16 && b1 <= 31) {
                        return true;
                    }
                case -64:
                    switch (b1) {
                        case -88:
                            return true;
                    }
                default:
                    return false;
                case 10:
                    return true;
            }
        } else {
            return true;
        }
    }

//    public static byte[] textToNumericFormatV4(String text) {
//        if (text.length() == 0) {
//            return null;
//        } else {
//            byte[] bytes = new byte[4];
//            String[] elements = text.split("\\.", -1);
//
//            try {
//                switch (elements.length) {
//                    case 1:
//                        long l = Long.parseLong(elements[0]);
//                        if (l < 0L || l > 4294967295L) {
//                            return null;
//                        }
//
//                        bytes[0] = (byte)((int)(l >> 24 & 255L));
//                        bytes[1] = (byte)((int)((l & 16777215L) >> 16 & 255L));
//                        bytes[2] = (byte)((int)((l & 65535L) >> 8 & 255L));
//                        bytes[3] = (byte)((int)(l & 255L));
//                        break;
//                    case 2:
//                        long l = (long)Integer.parseInt(elements[0]);
//                        if (l < 0L || l > 255L) {
//                            return null;
//                        }
//
//                        bytes[0] = (byte)((int)(l & 255L));
//                        l = (long)Integer.parseInt(elements[1]);
//                        if (l < 0L || l > 16777215L) {
//                            return null;
//                        }
//
//                        bytes[1] = (byte)((int)(l >> 16 & 255L));
//                        bytes[2] = (byte)((int)((l & 65535L) >> 8 & 255L));
//                        bytes[3] = (byte)((int)(l & 255L));
//                        break;
//                    case 3:
//                        for(int i = 0; i < 2; ++i) {
//                            long l = (long)Integer.parseInt(elements[i]);
//                            if (l < 0L || l > 255L) {
//                                return null;
//                            }
//
//                            bytes[i] = (byte)((int)(l & 255L));
//                        }
//
//                        long l = (long)Integer.parseInt(elements[2]);
//                        if (l < 0L || l > 65535L) {
//                            return null;
//                        }
//
//                        bytes[2] = (byte)((int)(l >> 8 & 255L));
//                        bytes[3] = (byte)((int)(l & 255L));
//                        break;
//                    case 4:
//                        for(int i = 0; i < 4; ++i) {
//                            long l = (long)Integer.parseInt(elements[i]);
//                            if (l < 0L || l > 255L) {
//                                return null;
//                            }
//
//                            bytes[i] = (byte)((int)(l & 255L));
//                        }
//                        break;
//                    default:
//                        return null;
//                }
//
//                return bytes;
//            } catch (NumberFormatException var6) {
//                return null;
//            }
//        }
//    }

    public static String getHostIp() {
        try {
            return InetAddress.getLocalHost().getHostAddress();
        } catch (UnknownHostException var1) {
            return "127.0.0.1";
        }
    }

    public static String getHostName() {
        try {
            return InetAddress.getLocalHost().getHostName();
        } catch (UnknownHostException var1) {
            return "未知";
        }
    }

    public static String getMultistageReverseProxyIp(String ip) {
        if (ip != null && ip.indexOf(",") > 0) {
            String[] ips = ip.trim().split(",");

            for (String subIp : ips) {
                if (!isUnknown(subIp)) {
                    ip = subIp;
                    break;
                }
            }
        }

        return StringUtils.substring(ip, 0, 255);
    }

    public static boolean isUnknown(String checkString) {
        return StringUtils.isBlank(checkString) || "unknown".equalsIgnoreCase(checkString);
    }

    public static boolean isIP(String ip) {
        return StringUtils.isNotBlank(ip) && ip.matches("(((25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]\\d|\\d)\\.){3}(25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]\\d|\\d))");
    }

    public static boolean isIpWildCard(String ip) {
        return StringUtils.isNotBlank(ip) && ip.matches("(((\\*\\.){3}\\*)|((25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]\\d|\\d)(\\.\\*){3})|((25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]\\d|\\d)\\.(25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]\\d|\\d))(\\.\\*){2}|(((25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]\\d|\\d)\\.){3}\\*))");
    }

    public static boolean ipIsInWildCardNoCheck(String ipWildCard, String ip) {
        String[] s1 = ipWildCard.split("\\.");
        String[] s2 = ip.split("\\.");
        boolean isMatchedSeg = true;

        for (int i = 0; i < s1.length && !s1[i].equals("*"); ++i) {
            if (!s1[i].equals(s2[i])) {
                isMatchedSeg = false;
                break;
            }
        }

        return isMatchedSeg;
    }

    public static boolean isIPSegment(String ipSeg) {
        return StringUtils.isNotBlank(ipSeg) && ipSeg.matches("((((25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]\\d|\\d)\\.){3}(25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]\\d|\\d))\\-(((25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]\\d|\\d)\\.){3}(25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]\\d|\\d)))");
    }

    public static boolean ipIsInNetNoCheck(String iparea, String ip) {
        int idx = iparea.indexOf(45);
        String[] sips = iparea.substring(0, idx).split("\\.");
        String[] sipe = iparea.substring(idx + 1).split("\\.");
        String[] sipt = ip.split("\\.");
        long ips = 0L;
        long ipe = 0L;
        long ipt = 0L;

        for (int i = 0; i < 4; ++i) {
            ips = ips << 8 | (long) Integer.parseInt(sips[i]);
            ipe = ipe << 8 | (long) Integer.parseInt(sipe[i]);
            ipt = ipt << 8 | (long) Integer.parseInt(sipt[i]);
        }

        if (ips > ipe) {
            long t = ips;
            ips = ipe;
            ipe = t;
        }

        return ips <= ipt && ipt <= ipe;
    }

    public static boolean isMatchedIp(String filter, String ip) {
        if (!StringUtils.isEmpty(filter) && !StringUtils.isEmpty(ip)) {
            String[] ips = filter.split(";");

            for (String iStr : ips) {
                if (isIP(iStr) && iStr.equals(ip)) {
                    return true;
                }

                if (isIpWildCard(iStr) && ipIsInWildCardNoCheck(iStr, ip)) {
                    return true;
                }

                if (isIPSegment(iStr) && ipIsInNetNoCheck(iStr, ip)) {
                    return true;
                }
            }

            return false;
        } else {
            return false;
        }
    }
}
