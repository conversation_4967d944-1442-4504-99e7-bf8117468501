package com.ruoyi.enums;

/**
 * Created by jingzhu.zr on 2017/8/15.
 */
public enum MerchantTypeEnum {
  Individual("02", "个体工商户"),
  Natural("01", "自然人"),
  Enterprising("03", "企业商户")
  ;

  private String code;
  private String desc;

  MerchantTypeEnum(String code, String desc) {
    this.code = code;
    this.desc = desc;
  }

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public String getDesc() {
    return desc;
  }

  public void setDesc(String desc) {
    this.desc = desc;
  }
}
