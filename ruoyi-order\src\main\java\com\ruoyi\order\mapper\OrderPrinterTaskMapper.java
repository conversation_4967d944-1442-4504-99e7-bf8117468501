package com.ruoyi.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.yulichang.base.MPJBaseMapper;
import com.ruoyi.order.domain.OrderPrinterTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 订单打印任务Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-15
 */
@Mapper
public interface OrderPrinterTaskMapper extends MPJBaseMapper<OrderPrinterTask> {
    /**
     * 查询订单打印任务
     *
     * @param taskId 订单打印任务主键
     * @return 订单打印任务
     */
    public OrderPrinterTask selectOrderPrinterTaskByTaskId(String taskId);

    /**
     * 查询订单打印任务列表
     *
     * @param orderPrinterTask 订单打印任务
     * @return 订单打印任务集合
     */
    public List<OrderPrinterTask> selectOrderPrinterTaskList(OrderPrinterTask orderPrinterTask);

    /**
     * 新增订单打印任务
     *
     * @param orderPrinterTask 订单打印任务
     * @return 结果
     */
    public int insertOrderPrinterTask(OrderPrinterTask orderPrinterTask);

    /**
     * 修改订单打印任务
     *
     * @param orderPrinterTask 订单打印任务
     * @return 结果
     */
    public int updateOrderPrinterTask(OrderPrinterTask orderPrinterTask);

    /**
     * 删除订单打印任务
     *
     * @param taskId 订单打印任务主键
     * @return 结果
     */
    public int deleteOrderPrinterTaskByTaskId(String taskId);

    /**
     * 批量删除订单打印任务
     *
     * @param taskIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOrderPrinterTaskByTaskIds(String[] taskIds);
    
    /**
     * 根据订单ID查询打印任务
     *
     * @param orderId 订单ID
     * @return 打印任务列表
     */
    public List<OrderPrinterTask> selectOrderPrinterTasksByOrderId(@Param("orderId") String orderId);
} 