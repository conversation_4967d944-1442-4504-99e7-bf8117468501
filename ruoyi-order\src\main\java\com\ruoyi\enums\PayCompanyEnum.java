package com.ruoyi.enums;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/5/26 13:24
 */
public enum PayCompanyEnum {

    MYBANK("mybank","网商"),
    VBILL("vbill","随行付"),
    STARPOS("starpos","新大陆"),
    HUIFU("huifu","汇付天下"),
    HELIPAY("helipay","合利宝"),
    HELIPAY_02("helipay_02","合利宝2"),
    HELIPAY_03("helipay_03","合利宝3"),
    YEEPAY("yeepay","易宝"),
    YEAHKA("yeahka","乐刷"),
    MYBANK_CLOUD("mybank_cloud","网商云资金（汇联云）"),


            ;
    private String code;
    private String desc;

    PayCompanyEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
