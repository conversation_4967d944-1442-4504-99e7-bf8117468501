package com.ruoyi.po;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/7/25 15:20
 */
@Data
public class CloudBalancePay {
    private Integer id;
    /**
     * 收款方ID，如果是商户，填入merchantId，如果是平台，则填入IsvOrgId
     */
    private String payeeId;
    /**
     * 付款方商户号merchantId
     */
    private String payerMerchantId;
    /**
     * 收款方类型
     */
    private String payeeType;
    /**
     * 外部单号
     */
    private String outTradeNo;
    /**
     * 关联原订单id，用于以后拓展
     */
    private Integer orderId;
    /**
     * 原支付订单号
     */
    private String orderNo;
    /**
     *订单金额(金额为分)
     */
    private String totalAmount;
    private String body;
    private String goodsTag;
    private String goodsDetail;
    /**
     *备注
     */
    private String remark;
    private Date createTime;
    private Date updateTime;
    private String memo;

    /**
     * 汇联商户号
     */
    private String hlMerchantId;

    /**
     *  动态验证码
     */
    private String smsCode;

}
