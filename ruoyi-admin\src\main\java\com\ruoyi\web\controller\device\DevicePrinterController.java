package com.ruoyi.web.controller.device;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.device.domain.DevicePrinter;
import com.ruoyi.device.service.IDevicePrinterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 打印机设备Controller
 * 
 * <AUTHOR>
 * @date 2024-07-24
 */
@RestController
@RequestMapping("/device/printer")
public class DevicePrinterController extends BaseController
{
    @Autowired
    private IDevicePrinterService devicePrinterService;

    /**
     * 查询打印机设备列表
     */
    @PreAuthorize("@ss.hasPermi('device:printer:list')")
    @GetMapping("/list")
    public TableDataInfo list(DevicePrinter devicePrinter)
    {
        startPage();
        List<DevicePrinter> list = devicePrinterService.selectDevicePrinterList(devicePrinter);
        return getDataTable(list);
    }

    /**
     * 导出打印机设备列表
     */
    @PreAuthorize("@ss.hasPermi('device:printer:export')")
    @Log(title = "打印机设备", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DevicePrinter devicePrinter)
    {
        List<DevicePrinter> list = devicePrinterService.selectDevicePrinterList(devicePrinter);
        ExcelUtil<DevicePrinter> util = new ExcelUtil<DevicePrinter>(DevicePrinter.class);
        util.exportExcel(response, list, "打印机设备数据");
    }

    /**
     * 获取打印机设备详细信息
     */
    @PreAuthorize("@ss.hasPermi('device:printer:query')")
    @GetMapping(value = "/{deviceId}")
    public AjaxResult getInfo(@PathVariable("deviceId") String deviceId)
    {
        return success(devicePrinterService.selectDevicePrinterByDeviceId(deviceId));
    }

    /**
     * 新增打印机设备
     */
    @PreAuthorize("@ss.hasPermi('device:printer:add')")
    @Log(title = "打印机设备", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DevicePrinter devicePrinter)
    {
        // 生成唯一设备ID
        if (devicePrinter.getDeviceId() == null || devicePrinter.getDeviceId().isEmpty()) {
            devicePrinter.setDeviceId(IdUtils.fastSimpleUUID());
        }
        
        // 设置默认值
        if (devicePrinter.getDeviceStatus() == null) {
            devicePrinter.setDeviceStatus(1); // 默认启用
        }
        if (devicePrinter.getOnlineStatus() == null) {
            devicePrinter.setOnlineStatus(0); // 默认离线
        }
        if (devicePrinter.getTotalPrints() == null) {
            devicePrinter.setTotalPrints(0);
        }
        if (devicePrinter.getMonthPrints() == null) {
            devicePrinter.setMonthPrints(0);
        }
        if (devicePrinter.getCountPrice() == null) {
            devicePrinter.setCountPrice(0L);
        }
        if (devicePrinter.getIsWarning() == null) {
            devicePrinter.setIsWarning(true);
        }
        if (devicePrinter.getWarningThreshold() == null) {
            devicePrinter.setWarningThreshold(10);
        }
        
        return toAjax(devicePrinterService.insertDevicePrinter(devicePrinter));
    }

    /**
     * 修改打印机设备
     */
    @PreAuthorize("@ss.hasPermi('device:printer:edit')")
    @Log(title = "打印机设备", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DevicePrinter devicePrinter)
    {
        return toAjax(devicePrinterService.updateDevicePrinter(devicePrinter));
    }

    /**
     * 删除打印机设备
     */
    @PreAuthorize("@ss.hasPermi('device:printer:remove')")
    @Log(title = "打印机设备", businessType = BusinessType.DELETE)
    @DeleteMapping("/{deviceIds}")
    public AjaxResult remove(@PathVariable String[] deviceIds)
    {
        return toAjax(devicePrinterService.deleteDevicePrinterByDeviceIds(deviceIds));
    }
    
    /**
     * 更新设备在线状态
     */
    @Log(title = "更新打印机在线状态", businessType = BusinessType.UPDATE)
    @PostMapping("/updateOnlineStatus")
    public AjaxResult updateOnlineStatus(@RequestBody Map<String, Object> params)
    {
        String deviceId = (String) params.get("deviceId");
        Integer onlineStatus = (Integer) params.get("onlineStatus");
        
        if (deviceId == null || deviceId.isEmpty()) {
            return AjaxResult.error("设备ID不能为空");
        }
        
        if (onlineStatus == null) {
            return AjaxResult.error("在线状态不能为空");
        }
        
        return toAjax(devicePrinterService.updateDeviceOnlineStatus(deviceId, onlineStatus));
    }
    
    /**
     * 更新设备耗材信息
     */
    @Log(title = "更新打印机耗材信息", businessType = BusinessType.UPDATE)
    @PostMapping("/updateConsumables")
    public AjaxResult updateConsumables(@RequestBody DevicePrinter devicePrinter)
    {
        if (devicePrinter.getDeviceId() == null || devicePrinter.getDeviceId().isEmpty()) {
            return AjaxResult.error("设备ID不能为空");
        }
        
        // 设置最后心跳时间
        devicePrinter.setLastHeartbeat(new Date());
        
        // 检查是否需要预警
        if (devicePrinter.getTonerLevel() != null || devicePrinter.getPaperRemain() != null) {
            DevicePrinter existDevice = devicePrinterService.selectDevicePrinterByDeviceId(devicePrinter.getDeviceId());
            if (existDevice != null && existDevice.getIsWarning() && existDevice.getWarningThreshold() != null) {
                int threshold = existDevice.getWarningThreshold();
                
                // 检查墨粉剩余量是否低于阈值
                if (devicePrinter.getTonerLevel() != null && devicePrinter.getTonerLevel() <= threshold) {
                    devicePrinter.setErrorCode("LOW_TONER");
                    devicePrinter.setErrorMessage("墨粉剩余量低于阈值：" + devicePrinter.getTonerLevel() + "%");
                }
                
                // 检查纸张剩余量是否低于阈值（假设每100张为100%）
                if (devicePrinter.getPaperRemain() != null && devicePrinter.getPaperRemain() <= threshold) {
                    devicePrinter.setErrorCode("LOW_PAPER");
                    devicePrinter.setErrorMessage("纸张剩余量低于阈值：" + devicePrinter.getPaperRemain() + "张");
                }
            }
        }
        
        return toAjax(devicePrinterService.updateDeviceConsumables(devicePrinter));
    }
    
    /**
     * 获取打印机设备（不需要权限）
     */
    @GetMapping(value = "/info/{deviceId}")
    public AjaxResult getDeviceInfo(@PathVariable("deviceId") String deviceId)
    {
        if (deviceId == null || deviceId.isEmpty()) {
            return AjaxResult.error("设备ID不能为空");
        }
        
        DevicePrinter devicePrinter = devicePrinterService.selectDevicePrinterByDeviceId(deviceId);
        if (devicePrinter == null) {
            return AjaxResult.error("设备不存在");
        }
        
        return AjaxResult.success(devicePrinter);
    }
} 