package com.yunchuang.wxapp.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.DateUtils;
import com.yunchuang.wxapp.mapper.IdPhotoPrintParamsMapper;
import com.yunchuang.wxapp.model.domain.IdPhotoPrintParams;
import com.yunchuang.wxapp.service.IIdPhotoPrintParamsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 证件照打印参数Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
@Service
public class IdPhotoPrintParamsServiceImpl extends ServiceImpl<IdPhotoPrintParamsMapper, IdPhotoPrintParams> implements IIdPhotoPrintParamsService {

    @Autowired
    private IdPhotoPrintParamsMapper idPhotoPrintParamsMapper;

    /**
     * 查询证件照打印参数
     * 
     * @param id 证件照打印参数主键
     * @return 证件照打印参数
     */
    @Override
    public IdPhotoPrintParams selectIdPhotoPrintParamsById(Long id) {
        return idPhotoPrintParamsMapper.selectIdPhotoPrintParamsById(id);
    }

    /**
     * 查询证件照打印参数列表
     * 
     * @param idPhotoPrintParams 证件照打印参数
     * @return 证件照打印参数
     */
    @Override
    public List<IdPhotoPrintParams> selectIdPhotoPrintParamsList(IdPhotoPrintParams idPhotoPrintParams) {
        return idPhotoPrintParamsMapper.selectIdPhotoPrintParamsList(idPhotoPrintParams);
    }

    /**
     * 新增证件照打印参数
     * 
     * @param idPhotoPrintParams 证件照打印参数
     * @return 结果
     */
    @Override
    public int insertIdPhotoPrintParams(IdPhotoPrintParams idPhotoPrintParams) {
        idPhotoPrintParams.setCreateTime(DateUtils.getNowDate());
        return idPhotoPrintParamsMapper.insertIdPhotoPrintParams(idPhotoPrintParams);
    }

    /**
     * 修改证件照打印参数
     * 
     * @param idPhotoPrintParams 证件照打印参数
     * @return 结果
     */
    @Override
    public int updateIdPhotoPrintParams(IdPhotoPrintParams idPhotoPrintParams) {
        idPhotoPrintParams.setUpdateTime(DateUtils.getNowDate());
        return idPhotoPrintParamsMapper.updateIdPhotoPrintParams(idPhotoPrintParams);
    }

    /**
     * 批量删除证件照打印参数
     * 
     * @param ids 需要删除的证件照打印参数主键
     * @return 结果
     */
    @Override
    public int deleteIdPhotoPrintParamsByIds(Long[] ids) {
        return idPhotoPrintParamsMapper.deleteIdPhotoPrintParamsByIds(ids);
    }

    /**
     * 删除证件照打印参数信息
     * 
     * @param id 证件照打印参数主键
     * @return 结果
     */
    @Override
    public int deleteIdPhotoPrintParamsById(Long id) {
        return idPhotoPrintParamsMapper.deleteIdPhotoPrintParamsById(id);
    }

    /**
     * 根据类型查询证件照打印参数
     *
     * @param type 类型
     * @return 证件照打印参数
     */
    @Override
    public IdPhotoPrintParams selectIdPhotoPrintParamsByType(Integer type) {
        return idPhotoPrintParamsMapper.selectIdPhotoPrintParamsByType(type);
    }

    /**
     * 查询启用状态的证件照打印参数并按类型分组
     *
     * @return 按类型分组的证件照打印参数
     */
    @Override
    public Map<String, List<IdPhotoPrintParams>> selectEnabledParamsGroupByType() {
        // 查询启用状态的参数
        IdPhotoPrintParams queryParams = new IdPhotoPrintParams();
        queryParams.setStatus(IdPhotoPrintParams.STATUS_ENABLED);
        List<IdPhotoPrintParams> enabledParams = idPhotoPrintParamsMapper.selectIdPhotoPrintParamsList(queryParams);

        // 按类型分组
        Map<String, List<IdPhotoPrintParams>> groupedParams = new HashMap<>();

        // 按类型分组并排序
        Map<Integer, List<IdPhotoPrintParams>> typeGrouped = enabledParams.stream()
                .collect(Collectors.groupingBy(IdPhotoPrintParams::getType));

        // 转换为字符串键值并添加到结果Map
        typeGrouped.forEach((type, params) -> {
            String typeName = getTypeName(type);
            // 按排序权重排序
            params.sort((a, b) -> {
                int sortA = a.getSortOrder() != null ? a.getSortOrder() : 0;
                int sortB = b.getSortOrder() != null ? b.getSortOrder() : 0;
                return Integer.compare(sortA, sortB);
            });
            groupedParams.put(typeName, params);
        });

        return groupedParams;
    }

    /**
     * 根据类型值获取类型名称
     *
     * @param type 类型值
     * @return 类型名称
     */
    private String getTypeName(Integer type) {
        if (type == null) {
            return "unknown";
        }
        switch (type) {
            case 0:
                return "common";
            case 1:
                return "identity";
            case 2:
                return "education";
            case 3:
                return "qualification";
            case 4:
                return "visa";
            default:
                return "unknown";
        }
    }

}
