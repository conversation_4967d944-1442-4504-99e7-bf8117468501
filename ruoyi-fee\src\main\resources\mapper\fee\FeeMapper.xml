<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.fee.mapper.FeeMapper">
    
    <resultMap type="Fee" id="FeeResult">
        <result property="id"    column="id"    />
        <result property="photoType"    column="photo_type"    />
        <result property="chargeType"    column="charge_type"    />
        <result property="feeAmount"    column="fee_amount"    />
        <result property="feeRate"    column="fee_rate"    />
        <result property="cost"    column="cost"    />
        <result property="excludeDevice"    column="exclude_device"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectFeeVo">
        select id, photo_type, charge_type, fee_amount, fee_rate, cost, exclude_device, create_time, create_by, update_time, update_by, remark from fee
    </sql>

    <select id="selectFeeList" parameterType="Fee" resultMap="FeeResult">
        <include refid="selectFeeVo"/>
        <where>  
            <if test="photoType != null "> and photo_type = #{photoType}</if>
            <if test="chargeType != null "> and charge_type = #{chargeType}</if>
            <if test="feeAmount != null "> and fee_amount = #{feeAmount}</if>
            <if test="feeRate != null "> and fee_rate = #{feeRate}</if>
            <if test="cost != null "> and cost = #{cost}</if>
            <if test="excludeDevice != null  and excludeDevice != ''"> and exclude_device = #{excludeDevice}</if>
        </where>
    </select>
    
    <select id="selectFeeById" parameterType="Long" resultMap="FeeResult">
        <include refid="selectFeeVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertFee" parameterType="Fee">
        insert into fee
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="photoType != null">photo_type,</if>
            <if test="chargeType != null">charge_type,</if>
            <if test="feeAmount != null">fee_amount,</if>
            <if test="feeRate != null">fee_rate,</if>
            <if test="cost != null">cost,</if>
            <if test="excludeDevice != null">exclude_device,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="photoType != null">#{photoType},</if>
            <if test="chargeType != null">#{chargeType},</if>
            <if test="feeAmount != null">#{feeAmount},</if>
            <if test="feeRate != null">#{feeRate},</if>
            <if test="cost != null">#{cost},</if>
            <if test="excludeDevice != null">#{excludeDevice},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateFee" parameterType="Fee">
        update fee
        <trim prefix="SET" suffixOverrides=",">
            <if test="photoType != null">photo_type = #{photoType},</if>
            <if test="chargeType != null">charge_type = #{chargeType},</if>
            <if test="feeAmount != null">fee_amount = #{feeAmount},</if>
            <if test="feeRate != null">fee_rate = #{feeRate},</if>
            <if test="cost != null">cost = #{cost},</if>
            <if test="excludeDevice != null">exclude_device = #{excludeDevice},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFeeById" parameterType="Long">
        delete from fee where id = #{id}
    </delete>

    <delete id="deleteFeeByIds" parameterType="String">
        delete from fee where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>