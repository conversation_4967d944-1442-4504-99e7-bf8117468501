package com.ruoyi.common.exception;

/**
 * 自定义业务异常类
 */
public class BusinessException extends RuntimeException {
    private Boolean success;
    private String message;
    private Integer code;
    private Object result;
    private String timestamp;
    private Object[] records;


    public BusinessException(Boolean success, String message, Integer code, Object result, String timestamp, Object[] records) {
        this.success = success;
        this.message = message;
        this.code = code;
        this.result = result;
        this.timestamp = timestamp;
        this.records = records;
    }

    public BusinessException(Boolean success, String message, Integer code, Object result) {
        this.success = success;
        this.message = message;
        this.code = code;
        this.result = result;
        this.timestamp = String.valueOf(System.currentTimeMillis());
        this.records = null;
    }

    public BusinessException(Boolean success, String message, Integer code) {
        this.success = success;
        this.message = message;
        this.code = code;
        this.result = null;
        this.timestamp = String.valueOf(System.currentTimeMillis());
        this.records = null;
    }

    public BusinessException(Boolean success, String message) {
        this.success = success;
        this.message = message;
        this.code = 500;
        this.result = null;
        this.timestamp = String.valueOf(System.currentTimeMillis());
        this.records = null;
    }

    public BusinessException(String message) {
        this.success = false;
        this.message = message;
        this.code = 500;
        this.result = null;
        this.timestamp = String.valueOf(System.currentTimeMillis());
        this.records = null;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public Object getResult() {
        return result;
    }

    public void setResult(Object result) {
        this.result = result;
    }

    public Object[] getRecords() {
        return records;
    }

    public void setRecords(Object[] records) {
        this.records = records;
    }
}
