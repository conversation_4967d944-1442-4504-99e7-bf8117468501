package com.ruoyi.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.utils.oss.ALY_OSS;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.SysPost;
import com.ruoyi.system.service.ISysPostService;
import org.springframework.web.multipart.MultipartFile;

/**
 * 软件信息操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/post")
public class SysPostController extends BaseController {
    @Autowired
    private ISysPostService postService;

    /**
     * 获取软件列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SysPost post) {
        startPage();
        List<SysPost> list = postService.selectPostList(post);
        return getDataTable(list);
    }

    @Log(title = "软件管理", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('system:post:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysPost post) {
        List<SysPost> list = postService.selectPostList(post);
        ExcelUtil<SysPost> util = new ExcelUtil<SysPost>(SysPost.class);
        util.exportExcel(response, list, "软件数据");
    }

    /**
     * 根据软件编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:post:query')")
    @GetMapping(value = "/{postId}")
    public AjaxResult getInfo(@PathVariable Long postId) {
        return success(postService.selectPostById(postId));
    }

    /**
     * 新增软件
     */
    @PreAuthorize("@ss.hasPermi('system:post:add')")
    @Log(title = "软件管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated SysPost post, @RequestParam(value = "image", required = false) MultipartFile image) {
        if (!postService.checkPostNameUnique(post)) {
            return error("新增软件'" + post.getPostName() + "'失败，软件名称已存在");
        }
        if (image != null) {
            String originalFilename = image.getOriginalFilename();
            String imageName = "app/ui/" + System.currentTimeMillis();
            if (originalFilename.contains(".png")) {
                imageName += ".png";
            } else {
                imageName += ".jpg";
            }
            String url = ALY_OSS.uploadImage(image, imageName);
            url = url.substring(0, url.indexOf("?"));
            post.setPostUi(url);
        }
        post.setCreateBy(getUsername());
        System.out.println(image);
        System.out.println(post);
        return toAjax(postService.insertPost(post));
    }

    /**
     * 修改软件
     */
    @PreAuthorize("@ss.hasPermi('system:post:edit')")
    @Log(title = "软件管理", businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    public AjaxResult edit(@Validated SysPost post, @RequestParam(value = "image", required = false) MultipartFile image) {
        if (!postService.checkPostNameUnique(post)) {
            return error("修改软件'" + post.getPostName() + "'失败，软件名称已存在");
        }
        if (image != null) {
            String originalFilename = image.getOriginalFilename();
            if (post.getPostUi() != null) {
                ALY_OSS.deleteImage(post.getPostUi().substring(51));
            }
            String imageName = "app/ui/" + System.currentTimeMillis();
            if (originalFilename.contains(".png")) {
                imageName += ".png";
            } else {
                imageName += ".jpg";
            }

            String url = ALY_OSS.uploadImage(image, imageName);
            url = url.substring(0, url.indexOf("?"));
            post.setPostUi(url);
        }
        post.setUpdateBy(getUsername());
        return toAjax(postService.updatePost(post));
    }

    /**
     * 删除软件
     */
    @PreAuthorize("@ss.hasPermi('system:post:remove')")
    @Log(title = "软件管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{postIds}")
    public AjaxResult remove(@PathVariable Long[] postIds) {

        return toAjax(postService.deletePostByIds(postIds));
    }

    /**
     * 获取软件选择框列表
     */
    @GetMapping("/optionselect")
    public AjaxResult optionselect() {
        List<SysPost> posts = postService.selectPostAll();
        return success(posts);
    }
}
