package com.ruoyi.web.controller.voucher;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.conditions.query.QueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.device.domain.DeviceCamera;
import com.ruoyi.device.service.IDeviceCameraService;
import com.ruoyi.dto.Result;
import com.ruoyi.order.domain.OrderCamera;
import com.ruoyi.order.domain.OrderCollect;
import com.ruoyi.order.service.IOrderCameraService;
import com.ruoyi.order.service.IOrderCollectService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.utils.MyRandom;
import com.ruoyi.voucher.domain.Voucher;
import com.ruoyi.voucher.service.IVoucherService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.voucher.domain.VoucherOrder;
import com.ruoyi.voucher.service.IVoucherOrderService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 优惠券订单Controller
 *
 * <AUTHOR>
 * @date 2024-04-16
 */
@RestController
@RequestMapping("/voucher/voucher_order")
public class VoucherOrderController extends BaseController {
    @Autowired
    private IVoucherOrderService voucherOrderService;
    @Autowired
    private IVoucherService voucherService;
    @Autowired
    private IDeviceCameraService deviceCameraService;
    @Autowired
    private ISysUserService userService;
    @Autowired
    private IOrderCollectService orderCollectService;
    @Autowired
    private IOrderCameraService orderCameraService;

    /**
     * 查询优惠券订单列表
     */
    @PreAuthorize("@ss.hasPermi('voucher:voucher_order:list')")
    @GetMapping("/list")
    public TableDataInfo list(VoucherOrder voucherOrder, Integer pageNum, Integer pageSize) {
        LoginUser user = getLoginUser();
        if (user == null) return new TableDataInfo(null, 0L);
//        List<Voucher> vouchers = voucherService.query().eq("user_id", user.getUserId()).list();


//        if (vouchers.size() == 0) return new TableDataInfo(null, 0);
//        List<Integer> voucherIds = CollUtil.getFieldValues(vouchers, "id", Integer.class);
        QueryChainWrapper<VoucherOrder> voucherOrderQueryChainWrapper = voucherOrderService.query();

        if (voucherOrder.getVoucherCode() != null) {
            voucherOrderQueryChainWrapper.eq("voucher_code", voucherOrder.getVoucherCode());
        }
        if (voucherOrder.getStatus() != null) {
            voucherOrderQueryChainWrapper.eq("status", voucherOrder.getStatus());
        }
        if (voucherOrder.getVoucherId() != null) {
            voucherOrderQueryChainWrapper.eq("voucher_id", voucherOrder.getVoucherId());
        }else {
            return new TableDataInfo();
        }
        if (voucherOrder.getCustomerId() != null) {
            voucherOrderQueryChainWrapper.eq("customer_id", voucherOrder.getCustomerId());
        }
        if (voucherOrder.getBeginCreateTime() != null && voucherOrder.getEndCreateTime() != null) {
            voucherOrderQueryChainWrapper.between("create_time", voucherOrder.getBeginCreateTime(), voucherOrder.getEndCreateTime());
        }

        Page<VoucherOrder> page = voucherOrderQueryChainWrapper.page(new Page<>(pageNum, pageSize));
        List<VoucherOrder> voucherOrderList = page.getRecords();
        return new TableDataInfo(voucherOrderList, page.getTotal());
    }

    /**
     * 导出优惠券订单列表
     */
    @PreAuthorize("@ss.hasPermi('voucher:voucher_order:export')")
    @Log(title = "优惠券订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, VoucherOrder voucherOrder) {
        List<VoucherOrder> list = voucherOrderService.selectVoucherOrderList(voucherOrder);
        try {
            for (VoucherOrder order : list) {
                byte[] qrCodeBytes = generateQRCodeAsBytes(order.getVoucherCode(), 300, 300);
                order.setQrCode(qrCodeBytes);
            }
        }catch (Exception e){
            System.err.println("Error occurred while generating QR Code: " + e.getMessage());
        }
        ExcelUtil<VoucherOrder> util = new ExcelUtil<VoucherOrder>(VoucherOrder.class);
        util.exportExcel(response, list, "优惠券订单数据");
    }


    public static byte[] generateQRCodeAsBytes(String data, int width, int height) throws Exception{
        QRCodeWriter qrCodeWriter = new QRCodeWriter();
        BitMatrix bitMatrix = qrCodeWriter.encode(data, BarcodeFormat.QR_CODE, width, height);

        // 将二维码写入字节输出流
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        MatrixToImageWriter.writeToStream(bitMatrix, "PNG", byteArrayOutputStream);

        // 返回字节数组
        return byteArrayOutputStream.toByteArray();
    }

    /**
     * 获取优惠券订单详细信息
     */
    @PreAuthorize("@ss.hasPermi('voucher:voucher_order:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(voucherOrderService.selectVoucherOrderById(id));
    }

    /**
     * 新增优惠券订单
     */
    @PreAuthorize("@ss.hasPermi('voucher:voucher_order:add')")
    @Log(title = "优惠券订单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody VoucherOrder voucherOrder) {
        if (voucherOrder.getCount() >= 200) {
            return new AjaxResult(201, "最多生成200张");
        }

        Long count = voucherOrderService.query().eq("voucher_id", voucherOrder.getVoucherId()).count();
        if ((count + voucherOrder.getCount()) > 200) {
            return new AjaxResult(201, "最多生成200张");
        }

        List<VoucherOrder> voucherOrderList = new ArrayList<>();
        SysUser user = getLoginUser().getUser();
        Date nowDate = DateUtils.getNowDate();
        voucherOrder.setCreateBy(user.getNickName());
        voucherOrder.setUpdateBy(user.getNickName());
        voucherOrder.setCreateTime(nowDate);
        voucherOrder.setUpdateTime(nowDate);
        voucherOrder.setPayTime(nowDate);
        voucherOrder.setStatus(5);

        if (voucherOrder.getCustomerId() == null)
            voucherOrder.setCustomerId(String.valueOf(user.getUserId()));

        for (int i = 0; i < voucherOrder.getCount(); i++) {
            voucherOrder.setVoucherCode(MyRandom.generateRandomString(12));
            voucherOrder.setId(MyRandom.orderId());
            VoucherOrder order = new VoucherOrder();
            BeanUtil.copyProperties(voucherOrder, order);
            voucherOrderList.add(order);
        }

        return toAjax(voucherOrderService.saveBatch(voucherOrderList));

    }

    /**
     * 修改优惠券订单
     */
    @PreAuthorize("@ss.hasPermi('voucher:voucher_order:edit')")
    @Log(title = "优惠券订单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody VoucherOrder voucherOrder) {

        return toAjax(voucherOrderService.updateVoucherOrder(voucherOrder));
    }

    /**
     * 删除优惠券订单
     */
    @PreAuthorize("@ss.hasPermi('voucher:voucher_order:remove')")
    @Log(title = "优惠券订单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(voucherOrderService.deleteVoucherOrderByIds(ids));
    }


    @GetMapping("/wipedOut_DaiJinQuan")
    public Result wipedOut_DaiJinQuan(@RequestParam String voucherCode) {
        VoucherOrder voucherOrder = voucherOrderService.query().eq("voucher_code", voucherCode).one();
        boolean update;
        if (voucherOrder.getUseCount() > 1) {
            update = voucherOrderService.update().eq("voucher_code", voucherCode).setSql("use_count = use_count - 1").update();
        } else {
            update = voucherOrderService.update().eq("voucher_code", voucherCode).set("status", 4).setSql("use_count = use_count - 1").update();
        }
        return update ? Result.ok(200, "核销成功", String.valueOf(System.currentTimeMillis()), null) : Result.fail(500, "核销失败", String.valueOf(System.currentTimeMillis()));
    }

    @GetMapping("/check_DaiJinQuan")
    public Result check_DaiJinQuan(@RequestParam String voucherCode, @RequestParam String deviceId, @RequestParam int pay) {
        VoucherOrder voucherOrder = voucherOrderService.query().eq("voucher_code", voucherCode).one();
        if (voucherOrder == null) {
            return Result.fail(404, "券码不存在", "");
        }
        if (voucherOrder.getStatus() != 1 && voucherOrder.getStatus() != 5) {
            return Result.fail(400, "券码状态错误", "");
        }
        if (voucherOrder.getUseCount() < 1) {
            return Result.fail(400, "优惠券试用次数上限", "");
        }
        Voucher voucher = voucherService.getById(voucherOrder.getVoucherId());

        if (voucher == null) return Result.fail(404, "优惠券不存在", "");
        if (voucher.getStatus() == 3) {
            return Result.fail(403, "优惠券已过期", "");
        }
//        if (voucher.getType() != 2) {
//            return Result.fail(403, "优惠券类型错误", "");
//        }
//        if (voucher.getActualPrice() > pay) {
//            return Result.fail(403, "消费金额不足", "");
//        }
//        return Result.ok(200, "券码合法", "", voucher);
        String[] deviceIds = voucher.getDeviceId().split(",");
        for (String id : deviceIds) {
            if (id.equals(deviceId)) {
                return Result.ok(200, "券码合法", "", voucher);
            }
        }
        return Result.fail(401, "设备不支持该券", "");
    }

    /**
     * 核销优惠券
     */
    @GetMapping("/wipedOut")
    public Result wipedOut(@RequestParam String voucherCode,
                           @RequestParam String deviceId,
                           @RequestParam String orderId,
                           @RequestParam int photoType) {
        VoucherOrder voucherOrder = voucherOrderService.query().eq("voucher_code", voucherCode).one();
        if (voucherOrder == null) {
            return Result.fail(404, "券码不存在", "");
        }
        if (voucherOrder.getStatus() != 1 && voucherOrder.getStatus() != 5) {
            return Result.fail(400, "券码状态错误", "");
        }
        if (voucherOrder.getUseCount() < 1) {
            return Result.fail(400, "优惠券试用次数上限", "");
        }
        Voucher voucher = voucherService.getById(voucherOrder.getVoucherId());

        if (voucher == null) return Result.fail(404, "优惠券不存在", "");
        if (voucher.getStatus() == 3) {
            return Result.fail(403, "优惠券已过期", "");
        }
        if (voucher.getType() != 1) {
            return Result.fail(403, "优惠券类型错误", "");
        }
        String[] deviceIds = voucher.getDeviceId().split(",");
        for (String id : deviceIds) {
            if (id.equals(deviceId)) {
                boolean success = createVoucherPhotoOrder(voucherCode, deviceId, orderId, voucher, photoType, voucherOrder.getUseCount());
                if (success) {
                    return Result.ok(200, "验券成功", "", null);
                } else
                    return Result.fail(402, "核销失败，请联系客服", "");
            }
        }
        return Result.fail(401, "设备不支持该券", "");
    }

    public boolean createVoucherPhotoOrder(String voucherCode, String deviceId, String orderId, Voucher voucher, int photoType, int useCount) {
        DeviceCamera camera = deviceCameraService.selectDeviceCameraByDeviceId(deviceId);
        if (camera == null) {
            return false;
        }
        if (camera.getDeviceStatus() == 0) {
            return false;
        }
        SysUser sysUser = userService.selectUserById(camera.getUserId());
        if (sysUser == null) {
            return false;
        }
        OrderCamera orderCamera = new OrderCamera();
        orderCamera.setOrderStatus(1L);
        orderCamera.setPayWay(4L);
        orderCamera.setOrderId(orderId);
        orderCamera.setOrderPrice(voucher.getCellPrice());
        orderCamera.setMoneyReceived(voucher.getCellPrice());
        orderCamera.setDeviceId(deviceId);
        orderCamera.setMchid((sysUser.getMerchantId() == null || sysUser.getMerchantId().equals("")) ? String.valueOf(sysUser.getUserId()) : sysUser.getMerchantId());
        orderCamera.setDeviceName(camera.getDeviceName());
        orderCamera.setPhotoType(photoType);
        orderCamera.setCreateTime(DateUtils.getNowDate());
        orderCamera.setPayTime(DateUtils.getNowDate());
        orderCamera.setProductDescription(voucher.getTitle());
        //汇总(优惠券订单)

        OrderCollect orderCollect = new OrderCollect();
        orderCollect.setTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM, orderCamera.getCreateTime()));
        orderCollect.setMerchantId(orderCamera.getMchid());
        Long count = orderCollectService.query().eq("merchant_id", orderCollect.getMerchantId()).eq("time", orderCollect.getTime()).count();
        if (count > 0) {
            orderCollectService.update()
                    .setSql("count = count + " + orderCamera.getMoneyReceived())
                    .eq("merchant_id", orderCollect.getMerchantId())
                    .eq("time", orderCollect.getTime()).update();
        } else {
            orderCollect.setCount(orderCamera.getMoneyReceived());
            orderCollectService.save(orderCollect);
        }

        //更新设备表中的设备总收入
        deviceCameraService.update().eq("device_id", orderCamera.getDeviceId()).setSql("count_price = count_price + " + orderCamera.getOrderPrice()).update();


        boolean save = orderCameraService.save(orderCamera);
        if (!save) return false;
        if (useCount > 1)
            return voucherOrderService.update().eq("voucher_code", voucherCode).setSql("use_count = use_count - 1").update();
        else
            return voucherOrderService.update().eq("voucher_code", voucherCode).set("status", 4).setSql("use_count = use_count - 1").update();
    }
}
