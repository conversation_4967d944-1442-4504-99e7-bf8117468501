package com.ruoyi.quartz.task;


import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class accessTokenTask {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;
// 1小时定时任务
    @Scheduled(cron = "0 0 0/1 * * ?")
    public void getAccessToken() {
        String accessToken = null;
//        if (!StringUtils.isNotBlank(accessToken)) {
        //获取微信相关的配置
        String appId = "wx8c60a303bfee25df";
        String secret = "f0efa66554cdcf9cad57fbdf66fada23";
        //缓存无，重新获取token
        System.out.println(appId + "&secret=" + secret);
        String url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=" + appId + "&secret=" + secret;
        JSONObject json = com.ruoyi.quartz.util.WxShareUtil.doGet(url);
        if (json != null) {
            System.out.println(json);
            accessToken = json.getString("access_token");
            int time = Integer.parseInt(json.getString("expires_in"));
            redisTemplate.opsForValue().set("wx_access_token", accessToken, 90, TimeUnit.MINUTES);
        }
//        }
        log.info("getAccessToken access_token: {}", accessToken);
    }
}
