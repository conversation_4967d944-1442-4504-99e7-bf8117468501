package com.ruoyi.device.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.device.handler.WarningRecipientsTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 管理拍照机设备对象 device_camera
 *
 * <AUTHOR>
 * @date 2023-07-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(value = "device_camera", autoResultMap = true)
public class DeviceCamera extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 设备id
     */
    @Excel(name = "设备id")
    @TableId(value = "device_id", type = IdType.INPUT)
    private String deviceId;

    /**
     * 设备名字
     */
    @Excel(name = "设备名字")
    private String deviceName;

    /**
     * 软件id
     */
    @Excel(name = "软件id")
    private Long softwareId;

    /**
     * 详细地址
     */
    @Excel(name = "详细地址")
    private String detailAddress;

    /**
     * 设备状态
     */
    @Excel(name = "设备状态")
    private Integer deviceStatus;

    /**
     * 经度
     */
    @Excel(name = "经度")
    private Double lng;

    /**
     * 纬度
     */
    @Excel(name = "纬度")
    private Double lat;

    /**
     * 代理商id
     */
    @Excel(name = "代理商")
    private Long agentUser;

    private Long userId;

    /**
     * 是否在线
     */
    @Excel(name = "是否在线")
    @TableField(exist = false)
    private String isOnLine;

    /**
     * 打印机状态
     */
    @Excel(name = "打印机状态")
    private Integer printerStatus;

    /**
     * 美颜服务状态
     */
    @Excel(name = "美颜服务状态")
    private Integer beautyStatus;

    /**
     * 摄像头状态
     */
    @Excel(name = "摄像头状态")
    private Integer cameraStatus;

    /**
     * 材料余量
     */
    @Excel(name = "色彩余量")
    private Integer consumables;

    @Excel(name = "相纸余量")
    private Integer paperConsumables;

    @Excel(name = "设备负责人")
    @TableField(exist = false)
    private String nickName;

    @Excel(name = "销售额")
    private Long countPrice;

    @Excel(name = "分账")
    private String subAccount;

    /**
     * 是否预警 0：否 1：是
     */
    @Excel(name = "是否预警 0：否 1：是")
    private Boolean isWarning;

    /**
     * 预警阀值
     */
    @Excel(name = "预警阀值")
    private Long warningThreshold;

    /**
     * 预警接收人
     */
    @Excel(name = "预警接收人")
    @TableField(typeHandler = WarningRecipientsTypeHandler.class)
    private WarningRecipients warningRecipients;


    /////////todo 暂时弃用
//    /** 省 */
//    @Excel(name = "省")
//    @TableField(exist = false)
//    private String province;
//
//    /** 市 */
//    @Excel(name = "市")
//    @TableField(exist = false)
//    private String city;
//
//    /** 区、县 */
//    @Excel(name = "区、县")
//    @TableField(exist = false)
//    private String district;
}
