<div id="bcd"></div>
<input type="file" id="abc" value="预览">

<input type="button" id="button" value="上传">

<script src="https://www.jq22.com/js/jquery.min.js"></script>

<script type="text/javascript">
    $(function () {
        let imgdata;
        $("#abc").change(function (e) {
            let imgBox = e.target;
            uploadImg($('#bcd'), imgBox)
        });

        function uploadImg(element, tag) {
            let file = tag.files[0];
            if (!/image\/\w+/.test(file.type)) {
                alert("文件格式错误！");
                return false;
            }
            console.log(file)
            let reader = new FileReader();
            reader.readAsDataURL(file);
        }

        $("#button").click(function () {

            $.post('http://fotoboxserve.yunchuang.store/photo/photo/upload', {
                deviceId: "ads1231231231231",
                file: (imgdata)
            }, function () {
                // body...
            });


        });
    });
</script>