package com.ruoyi.photo.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.conditions.query.QueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.oss.ALY_OSS;
import com.ruoyi.order.domain.OrderCamera;
import com.ruoyi.photo.domain.ImageBase64;
import com.ruoyi.photo.domain.UserPhoto;
import com.ruoyi.photo.mapper.ImageBase64Mapper;
import com.ruoyi.photo.mapper.UserPhotoMapper;
import com.ruoyi.photo.service.IUserPhotoService;
import com.ruoyi.photo.service.ImageBase64Service;
import com.ruoyi.photo.util.HttpClientSslUtils;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * photoService业务层处理
 *
 * <AUTHOR>
 * @date 2023-09-05
 */
@Service
public class ImageBase64ServiceImpl extends ServiceImpl<ImageBase64Mapper, ImageBase64> implements ImageBase64Service {

}
