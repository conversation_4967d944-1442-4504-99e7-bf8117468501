<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.resource.mapper.ModelPackageMapper">
    
    <resultMap type="ModelPackage" id="ModelPackageResult">
        <result property="id"    column="id"    />
        <result property="packageName"    column="package_name"    />
        <result property="type"    column="type"    />
        <result property="modelIds"    column="model_ids"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectModelPackageVo">
        select id, package_name, type, model_ids, create_by, create_time, update_by, update_time, remark from model_package
    </sql>

    <select id="selectModelPackageList" parameterType="ModelPackage" resultMap="ModelPackageResult">
        <include refid="selectModelPackageVo"/>
        <where>  
            <if test="packageName != null  and packageName != ''"> and package_name like concat('%', #{packageName}, '%')</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="modelIds != null  and modelIds != ''"> and model_ids = #{modelIds}</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
        </where>
    </select>
    
    <select id="selectModelPackageById" parameterType="Long" resultMap="ModelPackageResult">
        <include refid="selectModelPackageVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertModelPackage" parameterType="ModelPackage" useGeneratedKeys="true" keyProperty="id">
        insert into model_package
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="packageName != null">package_name,</if>
            <if test="type != null">type,</if>
            <if test="modelIds != null">model_ids,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="packageName != null">#{packageName},</if>
            <if test="type != null">#{type},</if>
            <if test="modelIds != null">#{modelIds},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateModelPackage" parameterType="ModelPackage">
        update model_package
        <trim prefix="SET" suffixOverrides=",">
            <if test="packageName != null">package_name = #{packageName},</if>
            <if test="type != null">type = #{type},</if>
            <if test="modelIds != null">model_ids = #{modelIds},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteModelPackageById" parameterType="Long">
        delete from model_package where id = #{id}
    </delete>

    <delete id="deleteModelPackageByIds" parameterType="String">
        delete from model_package where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>