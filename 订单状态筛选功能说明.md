# 订单状态筛选功能说明

## 功能概述

为`/order/printer/user`接口添加状态筛选功能，支持按支付状态和打印状态筛选用户订单。

## 接口参数

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| payStatus | Integer | 否 | 支付状态筛选：1-已支付，3-已退款，不传查询所有 |
| printStatus | String | 否 | 打印状态筛选：pending-待打印，completed-已完成，不传查询所有 |

### 请求示例
```
GET /order/printer/user                           # 查询所有订单
GET /order/printer/user?payStatus=1               # 查询已支付订单
GET /order/printer/user?payStatus=3               # 查询已退款订单
GET /order/printer/user?printStatus=pending       # 查询待打印订单
GET /order/printer/user?printStatus=completed     # 查询已完成订单
GET /order/printer/user?payStatus=1&printStatus=pending  # 查询已支付且待打印的订单
```

## 状态定义

### 支付状态（orderStatus）
- **1**: 已支付
- **3**: 已退款

### 打印状态（基于任务状态计算）
- **pending**: 待打印
  - 订单没有任务
  - 或者存在任务状态为：0-待打印、1-打印中、3-打印失败
- **completed**: 已完成
  - 订单有任务且所有任务状态都为：2-打印完成

## 打印状态计算逻辑

### SQL计算逻辑
```sql
CASE 
    WHEN COUNT(t.task_id) = 0 THEN 'pending'                    -- 没有任务
    WHEN COUNT(CASE WHEN t.print_status IN (0, 1, 3) THEN 1 END) > 0 THEN 'pending'  -- 有未完成任务
    ELSE 'completed'                                             -- 所有任务都完成
END as print_status_computed
```

### 任务状态说明
- **0**: 待打印
- **1**: 打印中
- **2**: 打印完成
- **3**: 打印失败

## 响应数据

### 响应格式
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "orderId": "order123",
      "deviceId": "device001",
      "deviceName": "打印机A",
      "orderStatus": 1,
      "totalAmount": 1.5,
      "printStatus": "pending",
      "createTime": "2025-01-25 10:00:00",
      // ... 其他订单字段
    }
  ]
}
```

### 新增字段说明
- **totalAmount**: 订单总金额（所有任务价格之和）
- **printStatus**: 打印状态（pending/completed）

## 使用场景

### 1. 订单管理
- 查看所有订单：不传任何参数
- 查看待处理订单：`printStatus=pending`
- 查看已完成订单：`printStatus=completed`

### 2. 财务管理
- 查看已支付订单：`payStatus=1`
- 查看退款订单：`payStatus=3`

### 3. 运营分析
- 查看已支付但未完成的订单：`payStatus=1&printStatus=pending`
- 查看已支付且已完成的订单：`payStatus=1&printStatus=completed`

## 性能优化

### 单次SQL查询
- 使用LEFT JOIN关联订单和任务表
- 通过GROUP BY和聚合函数一次性计算总金额和打印状态
- 避免N+1查询问题

### 索引建议
```sql
-- 订单表索引
CREATE INDEX idx_order_printer_openid_status ON order_printer(openid, order_status);
CREATE INDEX idx_order_printer_create_time ON order_printer(create_time);

-- 任务表索引
CREATE INDEX idx_order_printer_tasks_order_id ON order_printer_tasks(order_id);
CREATE INDEX idx_order_printer_tasks_print_status ON order_printer_tasks(print_status);
```

## 数据流程

### 1. 参数验证
- 验证payStatus是否为有效值（1或3）
- 验证printStatus是否为有效值（pending或completed）

### 2. SQL查询
- 根据openid查询用户订单
- 应用支付状态筛选（WHERE条件）
- 计算总金额和打印状态（聚合函数）
- 应用打印状态筛选（HAVING条件）

### 3. 结果返回
- 返回符合条件的订单列表
- 包含总金额和打印状态信息

## 测试用例

### 1. 基础功能测试
- 不传参数：返回所有订单
- 传入有效参数：返回筛选后的订单
- 传入无效参数：正常处理（忽略无效参数）

### 2. 状态计算测试
- 订单无任务：printStatus = "pending"
- 订单有待打印任务：printStatus = "pending"
- 订单有打印中任务：printStatus = "pending"
- 订单有打印失败任务：printStatus = "pending"
- 订单所有任务都完成：printStatus = "completed"

### 3. 组合筛选测试
- 同时传入payStatus和printStatus
- 验证筛选结果正确性

## 注意事项

1. **向后兼容**：不传参数时行为与原接口一致
2. **性能考虑**：使用单次SQL查询，避免循环查询
3. **状态一致性**：打印状态基于实时任务状态计算
4. **参数校验**：对无效参数进行容错处理

## 扩展功能

### 未来可扩展的筛选条件
- 时间范围筛选（创建时间、支付时间）
- 设备筛选（deviceId）
- 金额范围筛选
- 排序方式选择
