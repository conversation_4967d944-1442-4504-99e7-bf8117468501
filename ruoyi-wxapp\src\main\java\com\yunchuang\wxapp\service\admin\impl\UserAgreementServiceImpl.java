package com.yunchuang.wxapp.service.admin.impl;


import com.yunchuang.wxapp.mapper.UserAgreementMapper;
import com.yunchuang.wxapp.model.domain.UserAgreement;
import com.yunchuang.wxapp.service.admin.IUserAgreementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户协议Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@Service
public class UserAgreementServiceImpl implements IUserAgreementService {

    @Autowired
    private UserAgreementMapper userAgreementMapper;

    /**
     * 查询用户协议
     *
     * @param id 用户协议主键
     * @return 用户协议
     */
    @Override
    public UserAgreement selectUserAgreementById(Long id) {
        return userAgreementMapper.selectUserAgreementById(id);
    }

    /**
     * 查询用户协议列表
     *
     * @param userAgreement 用户协议
     * @return 用户协议
     */
    @Override
    public List<UserAgreement> selectUserAgreementList(UserAgreement userAgreement) {
        return userAgreementMapper.selectUserAgreementList(userAgreement);
    }

    /**
     * 新增用户协议
     *
     * @param userAgreement 用户协议
     * @return 结果
     */
    @Override
    public int insertUserAgreement(UserAgreement userAgreement) {
        return userAgreementMapper.insertUserAgreement(userAgreement);
    }

    /**
     * 修改用户协议
     *
     * @param userAgreement 用户协议
     * @return 结果
     */
    @Override
    public int updateUserAgreement(UserAgreement userAgreement) {
        return userAgreementMapper.updateUserAgreement(userAgreement);
    }

    /**
     * 批量删除用户协议
     *
     * @param ids 需要删除的用户协议主键
     * @return 结果
     */
    @Override
    public int deleteUserAgreementByIds(Long[] ids) {
        return userAgreementMapper.deleteUserAgreementByIds(ids);
    }

    /**
     * 删除用户协议信息
     *
     * @param id 用户协议主键
     * @return 结果
     */
    @Override
    public int deleteUserAgreementById(Long id) {
        return userAgreementMapper.deleteUserAgreementById(id);
    }
}
