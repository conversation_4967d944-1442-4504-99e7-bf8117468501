package com.ruoyi.dto;

import lombok.Data;

@Data
public class WSSPayOpenNativePayReq {
    /**
     * 服务商编号(银行提供)
     */
    private String isv_no;

    /**
     * 银行编号
     */
    private String bank_code;

    /**
     * 银行商户号(银行提供)
     */
    private String bank_mer_no;

    /**
     * 门店编号(银行提供)
     */
    private String bank_shop_no;

    /**
     * 商户订单号
     */
    private String mer_order_no;

    /**
     * 金额(单位分)
     */
    private String amount;

    /**
     * 支付源
     */
    private String pay_source;

    /**
     * 后台通知地址
     */
    private String notify_url;

    /**
     * 机构号
     */
    private String bank_org_no;

    /**
     * 设备号
     */
    private String device_no;

    /**
     * 随机字符串
     */
    private String nonce_str;

    /**
     * 签名算法类型
     */
    private String sign_type;

    /**
     * 签名
     */
    private String sign;

}
