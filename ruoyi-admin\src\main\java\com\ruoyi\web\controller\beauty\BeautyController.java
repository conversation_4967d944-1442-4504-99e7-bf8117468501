package com.ruoyi.web.controller.beauty;

import com.ruoyi.beauty.domain.Beauty;
import com.ruoyi.beauty.domain.BeautyValue;
import com.ruoyi.beauty.service.IBeautyService;
import com.ruoyi.beauty.service.IBeautyValueService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.dto.Result;
import com.ruoyi.dto.UserBeauty;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 美颜Controller
 * 
 * <AUTHOR>
 * @date 2023-10-15
 */
@RestController
@RequestMapping("/beauty/beauty")
public class BeautyController extends BaseController
{
    @Autowired
    private ISysUserService userService;
    @Autowired
    private IBeautyService beautyService;
    @Autowired
    private IBeautyValueService beautyValueService;
    /**
     * 查询美颜列表
     */
    @PreAuthorize("@ss.hasPermi('beauty:beauty:list')")
    @GetMapping("/list")
    public TableDataInfo list(Beauty beauty)
    {
        startPage();
        List<Beauty> list = beautyService.selectBeautyList(beauty);
        return getDataTable(list);
    }
    /**
     * 导出美颜列表
     */
    @PreAuthorize("@ss.hasPermi('beauty:beauty:export')")
    @Log(title = "美颜", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Beauty beauty)
    {
        List<Beauty> list = beautyService.selectBeautyList(beauty);
        ExcelUtil<Beauty> util = new ExcelUtil<Beauty>(Beauty.class);
        util.exportExcel(response, list, "美颜数据");
    }

    /**
     * 获取美颜详细信息
     */
    @PreAuthorize("@ss.hasPermi('beauty:beauty:query')")
    @GetMapping(value = "/{useeId}")
    public AjaxResult getInfo(@PathVariable("useeId") Long useeId)
    {
        return success(beautyService.selectBeautyByUseeId(useeId));
    }

    /**
     * 新增美颜
     */
    @PreAuthorize("@ss.hasPermi('beauty:beauty:add')")
    @Log(title = "美颜", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Beauty beauty)
    {
        return toAjax(beautyService.insertBeauty(beauty));
    }

    /**
     * 修改美颜
     */
    @PreAuthorize("@ss.hasPermi('beauty:beauty:edit')")
    @Log(title = "美颜", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Beauty beauty)
    {
        return toAjax(beautyService.updateBeauty(beauty));
    }

    /**
     * 删除美颜
     */
    @PreAuthorize("@ss.hasPermi('beauty:beauty:remove')")
    @Log(title = "美颜", businessType = BusinessType.DELETE)
	@DeleteMapping("/{useeIds}")
    public AjaxResult remove(@PathVariable Long[] useeIds)
    {
        return toAjax(beautyService.deleteBeautyByUseeIds(useeIds));
    }

    /**
     * 设备登录使用
     */
    @GetMapping("/devLogin")
    public Result devLogin(@RequestParam String userName)
    {
        SysUser sysUser = userService.selectUserByUserName(userName);
        if (sysUser == null) return Result.fail(401,"用户不存在", String.valueOf(System.currentTimeMillis()));
        Beauty beauty = beautyService.selectBeautyByUseeId(sysUser.getUserId());
        BeautyValue beautyValue = beautyValueService.selectBeautyValueById(1L);
        UserBeauty userBeauty = new UserBeauty();

        userBeauty.setDayan(beauty.getDayan());
        userBeauty.setMeibai(beauty.getMeibai());
        userBeauty.setHongrun(beauty.getHongrun());
        userBeauty.setLiangyan(beauty.getLiangyan());
        userBeauty.setShoulian(beauty.getShoulian());
        userBeauty.setWuguanliti(beauty.getWuguanliti());
        userBeauty.setMopi(beauty.getMopi());

        userBeauty.setMopiMax(beautyValue.getMopiMax());
        userBeauty.setMopiMin(beautyValue.getMopiMin());

        userBeauty.setMeibaiMax(beautyValue.getMeibaiMax());
        userBeauty.setMeibaiMin(beautyValue.getMeibaiMin());

        userBeauty.setHongrunMax(beautyValue.getHongrunMax());
        userBeauty.setHongrunMin(beautyValue.getHongrunMin());

        userBeauty.setShoulianMax(beautyValue.getShoulianMax());
        userBeauty.setShoulianMin(beautyValue.getShoulianMin());

        userBeauty.setDayanMax(beautyValue.getDayanMax());
        userBeauty.setDayanMin(beautyValue.getDayanMin());

        userBeauty.setLiangyanMax(beautyValue.getLiangyanMax());
        userBeauty.setLiangyanMin(beautyValue.getLiangyanMin());

        userBeauty.setWuguanlitiMax(beautyValue.getWuguanlitiMax());
        userBeauty.setWuguanlitiMin(beautyValue.getWuguanlitiMin());

        userBeauty.setUserName(sysUser.getUserName());
        userBeauty.setLoginTime(System.currentTimeMillis());

        return Result.ok(200,"获取成功", String.valueOf(System.currentTimeMillis()),userBeauty);
    }

}
