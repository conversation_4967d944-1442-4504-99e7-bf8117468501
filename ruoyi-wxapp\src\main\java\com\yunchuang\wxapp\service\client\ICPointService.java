package com.yunchuang.wxapp.service.client;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yunchuang.wxapp.model.domain.Point;

import java.util.List;
import java.util.Map;

/**
 * 点位 Service 接口
 */
public interface ICPointService extends IService<Point> {

    /**
     * 查询点位列表
     *
     * @param pageNum  页码
     * @param pageSize 每页数量
     * @return 点位列表
     */
    Map<String, Object> getPointList(Integer pageNum, Integer pageSize);

    /**
     * 查询附近的点位列表
     *
     * @param longitude 经度
     * @param latitude  纬度
     * @param number    数量
     * @return 点位列表
     */
    List<Point> getNearbyPointList(Double longitude, Double latitude, Integer number);
}
