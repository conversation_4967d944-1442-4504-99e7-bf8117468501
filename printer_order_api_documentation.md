# 打印订单系统接口文档

## 1. 文件上传与订单创建

### 1.1 上传文件并创建打印订单（整合接口）

- **URL**: `/order/printer/uploadAndOrder`
- **方法**: POST
- **Content-Type**: multipart/form-data
- **描述**: 上传文件并同时创建打印订单，支持单文件和多文件上传
- **认证**: 需要微信小程序认证，接口会自动从认证信息中获取用户openid

**重要说明**:
- 此接口已被微信小程序认证拦截器拦截，前端请求时必须在请求头中携带有效的 `Authorization` token
- 系统会自动从认证信息中获取用户的openid，无需在请求参数中传递
- 如果认证失败或token无效，接口会返回认证错误

**请求头**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| Authorization | String | 是 | 微信小程序用户认证token，格式：Bearer {token} |

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| file | File | 否 | 单个文件上传（与files二选一） |
| files | File[] | 否 | 多个文件上传（与file二选一） |
| deviceId | String | 是 | 打印机设备ID |
| deviceName | String | 否 | 打印机名称 |
| ~~openid~~ | ~~String~~ | ~~否~~ | ~~微信用户openid（已废弃，系统自动获取）~~ |
| phone | String | 否 | 用户手机号 |
| orderPrice | Long | 否 | 订单金额(分)，默认0 |
| copies | Integer | 否 | 打印份数，默认1 |
| colorMode | Integer | 否 | 颜色模式，0-黑白，1-彩色，默认0 |
| duplexMode | Integer | 否 | 双面模式，0-单面，1-双面，默认0 |
| paperType | Integer | 否 | 纸张类型，1-A4，2-A5，3-照片纸，默认1 |
| pageRange | String | 否 | 页码范围，如"1-3,5" |

**响应示例**:

```json
{
  "code": 200,
  "msg": "创建预订单成功",
  "data": {
    "orderId": "a1b2c3d4e5f6",
    "orderPrice": 500,
    "deviceId": "printer001",
    "deviceName": "一楼打印机",
    "files": [
      {
        "fileName": "document.pdf",
        "fileUrl": "http://example.com/profile/upload/document.pdf",
        "fileType": "pdf",
        "fileSize": 1024000
      }
    ]
  }
}
```

## 2. 订单管理接口

### 2.1 查询订单打印机列表

- **URL**: `/order/printer/list`
- **方法**: GET
- **描述**: 分页查询订单打印机列表
- **权限**: `order:printer:list`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| pageNum | Integer | 否 | 页码，默认1 |
| pageSize | Integer | 否 | 每页记录数，默认10 |
| deviceId | String | 否 | 设备ID，用于筛选 |
| deviceName | String | 否 | 设备名称，用于筛选 |
| orderStatus | Integer | 否 | 订单状态，用于筛选 |
| payWay | Integer | 否 | 支付方式，用于筛选 |
| startTime | Date | 否 | 开始时间，用于筛选 |
| endTime | Date | 否 | 结束时间，用于筛选 |

**响应示例**:

```json
{
  "code": 200,
  "msg": "操作成功",
  "total": 100,
  "rows": [
    {
      "orderId": "a1b2c3d4e5f6",
      "deviceId": "printer001",
      "deviceName": "一楼打印机",
      "orderStatus": 1,
      "orderPrice": 500,
      "payWay": 1,
      "payTime": "2024-06-15 10:30:00",
      "createTime": "2024-06-15 10:25:00"
    }
  ]
}
```

### 2.2 导出订单打印机列表

- **URL**: `/order/printer/export`
- **方法**: POST
- **描述**: 导出订单打印机数据
- **权限**: `order:printer:export`

**请求参数**: 与查询列表接口相同

**响应**: Excel文件下载

### 2.3 获取订单打印机详细信息

- **URL**: `/order/printer/{orderId}`
- **方法**: GET
- **描述**: 获取单个订单的详细信息
- **权限**: `order:printer:query`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| orderId | String | 是 | 订单ID |

**响应示例**:

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "orderId": "a1b2c3d4e5f6",
    "deviceId": "printer001",
    "deviceName": "一楼打印机",
    "userId": 10001,
    "openid": "wx_openid_123456",
    "phone": "13800138000",
    "orderStatus": 1,
    "orderPrice": 500,
    "payWay": 1,
    "payTime": "2024-06-15 10:30:00",
    "printTime": null,
    "createTime": "2024-06-15 10:25:00"
  }
}
```

### 2.4 新增订单打印机

- **URL**: `/order/printer`
- **方法**: POST
- **描述**: 手动新增订单打印机记录
- **权限**: `order:printer:add`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| deviceId | String | 是 | 打印机设备ID |
| deviceName | String | 否 | 打印机名称 |
| openid | String | 否 | 微信用户openid |
| phone | String | 否 | 用户手机号 |
| orderStatus | Integer | 否 | 订单状态 |
| orderPrice | Long | 否 | 订单金额(分) |
| payWay | Integer | 否 | 支付方式 |

**响应示例**:

```json
{
  "code": 200,
  "msg": "操作成功"
}
```

### 2.5 修改订单打印机

- **URL**: `/order/printer`
- **方法**: PUT
- **描述**: 修改订单打印机记录
- **权限**: `order:printer:edit`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| orderId | String | 是 | 订单ID |
| deviceId | String | 否 | 打印机设备ID |
| deviceName | String | 否 | 打印机名称 |
| openid | String | 否 | 微信用户openid |
| phone | String | 否 | 用户手机号 |
| orderStatus | Integer | 否 | 订单状态 |
| orderPrice | Long | 否 | 订单金额(分) |
| payWay | Integer | 否 | 支付方式 |

**响应示例**:

```json
{
  "code": 200,
  "msg": "操作成功"
}
```

### 2.6 删除订单打印机

- **URL**: `/order/printer/{orderIds}`
- **方法**: DELETE
- **描述**: 删除订单打印机记录
- **权限**: `order:printer:remove`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| orderIds | String[] | 是 | 订单ID数组，以逗号分隔 |

**响应示例**:

```json
{
  "code": 200,
  "msg": "操作成功"
}
```

### 2.7 查询用户订单列表

- **URL**: `/order/printer/user`
- **方法**: GET
- **描述**: 查询用户的所有订单

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| openid | String | 是 | 微信用户openid |

**响应示例**:

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "orderId": "a1b2c3d4e5f6",
      "deviceId": "printer001",
      "deviceName": "一楼打印机",
      "orderStatus": 1,
      "orderPrice": 500,
      "payWay": 1,
      "payTime": "2024-06-15 10:30:00",
      "createTime": "2024-06-15 10:25:00"
    },
    {
      "orderId": "b2c3d4e5f6g7",
      "deviceId": "printer002",
      "deviceName": "二楼打印机",
      "orderStatus": 4,
      "orderPrice": 300,
      "payWay": 1,
      "payTime": "2024-06-14 15:20:00",
      "createTime": "2024-06-14 15:15:00"
    }
  ]
}
```

## 3. 支付与状态管理

### 3.1 支付回调处理

- **URL**: `/order/printer/payNotify`
- **方法**: POST
- **描述**: 处理支付成功的回调通知，更新订单支付状态

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| orderId | String | 是 | 订单ID |
| transactionId | String | 否 | 第三方支付交易ID |

**响应示例**:

```json
{
  "code": 200,
  "msg": "支付处理成功"
}
```

### 3.2 查询订单打印任务

- **URL**: `/order/printer/tasks/{orderId}`
- **方法**: GET
- **描述**: 查询指定订单的打印任务列表

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| orderId | String | 是 | 订单ID |

**响应示例**:

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "taskId": "t1a2b3c4d5e6",
      "orderId": "a1b2c3d4e5f6",
      "deviceId": "printer001",
      "fileUrl": "http://example.com/profile/upload/document.pdf",
      "fileName": "document.pdf",
      "fileType": "pdf",
      "fileSize": 1024000,
      "printStatus": 0,
      "copies": 1,
      "colorMode": 0,
      "duplexMode": 0,
      "paperType": 1,
      "createTime": "2024-06-15 10:25:00"
    }
  ]
}
```

### 3.3 更新打印任务状态

- **URL**: `/order/printer/task/status`
- **方法**: POST
- **描述**: 更新打印任务的状态

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| taskId | String | 是 | 任务ID |
| status | Integer | 是 | 打印状态，0-待打印，1-打印中，2-打印完成，3-打印失败 |
| errorMsg | String | 否 | 错误信息，当状态为打印失败时使用 |

**响应示例**:

```json
{
  "code": 200,
  "msg": "更新状态成功"
}
```

## 4. 状态码说明

### 4.1 订单状态

| 状态码 | 描述 |
|--------|------|
| 0 | 未支付 |
| 1 | 已支付 |
| 2 | 已取消 |
| 3 | 已退款 |
| 4 | 已打印 |
| 5 | 打印中 |
| 6 | 打印失败 |

### 4.2 支付方式

| 状态码 | 描述 |
|--------|------|
| 1 | 微信 |
| 2 | 支付宝 |
| 3 | 现金 |
| 4 | 优惠券 |

### 4.3 打印任务状态

| 状态码 | 描述 |
|--------|------|
| 0 | 待打印 |
| 1 | 打印中 |
| 2 | 打印完成 |
| 3 | 打印失败 |

### 4.4 颜色模式

| 状态码 | 描述 |
|--------|------|
| 0 | 黑白 |
| 1 | 彩色 |

### 4.5 双面模式

| 状态码 | 描述 |
|--------|------|
| 0 | 单面 |
| 1 | 双面 |

### 4.6 纸张类型

| 状态码 | 描述 |
|--------|------|
| 1 | A4 |
| 2 | A5 |
| 3 | 照片纸 | 