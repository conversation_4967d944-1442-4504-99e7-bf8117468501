-- 数据库迁移脚本：添加status状态字段
-- 执行时间：2025-01-29
-- 说明：为用户表和打印任务表添加逻辑删除状态字段

-- 1. 为wxapp_user表添加status字段
ALTER TABLE `wxapp_user` 
ADD COLUMN `status` INT(1) NOT NULL DEFAULT 0 COMMENT '用户状态（0-正常 1-删除）' AFTER `mobile`;

-- 2. 为order_printer表添加status字段
ALTER TABLE `order_printer`
ADD COLUMN `status` INT(1) NOT NULL DEFAULT 0 COMMENT '订单状态（0-正常 1-删除）' AFTER `hide`;

-- 3. 为order_printer_tasks表添加status字段
ALTER TABLE `order_printer_tasks`
ADD COLUMN `status` INT(1) NOT NULL DEFAULT 0 COMMENT '任务状态（0-正常 1-删除）' AFTER `end_time`;

-- 4. 为现有数据设置默认状态
UPDATE `wxapp_user` SET `status` = 0 WHERE `status` IS NULL;
UPDATE `order_printer` SET `status` = 0 WHERE `status` IS NULL;
UPDATE `order_printer_tasks` SET `status` = 0 WHERE `status` IS NULL;

-- 5. 创建索引以提高查询性能
CREATE INDEX `idx_wxapp_user_status` ON `wxapp_user` (`status`);
CREATE INDEX `idx_wxapp_user_openid_status` ON `wxapp_user` (`openid`, `status`);
CREATE INDEX `idx_order_printer_status` ON `order_printer` (`status`);
CREATE INDEX `idx_order_printer_openid_status` ON `order_printer` (`openid`, `status`);
CREATE INDEX `idx_order_printer_tasks_status` ON `order_printer_tasks` (`status`);
CREATE INDEX `idx_order_printer_tasks_order_status` ON `order_printer_tasks` (`order_id`, `status`);

-- 验证脚本执行结果
SELECT 'wxapp_user表结构' as table_info;
DESCRIBE `wxapp_user`;

SELECT 'order_printer表结构' as table_info;
DESCRIBE `order_printer`;

SELECT 'order_printer_tasks表结构' as table_info;
DESCRIBE `order_printer_tasks`;

SELECT 'wxapp_user数据统计' as data_info;
SELECT
    COUNT(*) as total_users,
    SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as normal_users,
    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as deleted_users
FROM `wxapp_user`;

SELECT 'order_printer数据统计' as data_info;
SELECT
    COUNT(*) as total_orders,
    SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as normal_orders,
    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as deleted_orders
FROM `order_printer`;

SELECT 'order_printer_tasks数据统计' as data_info;
SELECT
    COUNT(*) as total_tasks,
    SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as normal_tasks,
    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as deleted_tasks
FROM `order_printer_tasks`;
