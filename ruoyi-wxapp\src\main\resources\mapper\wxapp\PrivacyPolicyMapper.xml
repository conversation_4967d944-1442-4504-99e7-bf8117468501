<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yunchuang.wxapp.mapper.PrivacyPolicyMapper">

    <resultMap type="PrivacyPolicy" id="PrivacyPolicyResult">
        <result property="id" column="id"/>
        <result property="releaseDate" column="release_date"/>
        <result property="effectiveDate" column="effective_date"/>
        <result property="policyContent" column="policy_content"/>
        <result property="beEnabled" column="be_enabled"/>
    </resultMap>

    <sql id="selectPrivacyPolicyVo">
        select id, release_date, effective_date, policy_content, be_enabled
        from wxapp_privacy_policy
    </sql>

    <select id="selectPrivacyPolicyList" parameterType="PrivacyPolicy" resultMap="PrivacyPolicyResult">
        <include refid="selectPrivacyPolicyVo"/>
        <where>
            <if test="releaseDate != null ">and release_date = #{releaseDate}</if>
            <if test="effectiveDate != null ">and effective_date = #{effectiveDate}</if>
            <if test="policyContent != null  and policyContent != ''">and policy_content like concat('%',
                #{policyContent}, '%')
            </if>
            <if test="beEnabled != null ">and be_enabled = #{beEnabled}</if>
        </where>
    </select>

    <select id="selectPrivacyPolicyById" parameterType="Long" resultMap="PrivacyPolicyResult">
        <include refid="selectPrivacyPolicyVo"/>
        where id = #{id}
    </select>

    <insert id="insertPrivacyPolicy" parameterType="PrivacyPolicy" useGeneratedKeys="true" keyProperty="id">
        insert into wxapp_privacy_policy
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="releaseDate != null">release_date,</if>
            <if test="effectiveDate != null">effective_date,</if>
            <if test="policyContent != null and policyContent != ''">policy_content,</if>
            <if test="beEnabled != null">be_enabled,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="releaseDate != null">#{releaseDate},</if>
            <if test="effectiveDate != null">#{effectiveDate},</if>
            <if test="policyContent != null and policyContent != ''">#{policyContent},</if>
            <if test="beEnabled != null">#{beEnabled},</if>
        </trim>
    </insert>

    <update id="updatePrivacyPolicy" parameterType="PrivacyPolicy">
        update wxapp_privacy_policy
        <trim prefix="SET" suffixOverrides=",">
            <if test="releaseDate != null">release_date = #{releaseDate},</if>
            <if test="effectiveDate != null">effective_date = #{effectiveDate},</if>
            <if test="policyContent != null and policyContent != ''">policy_content = #{policyContent},</if>
            <if test="beEnabled != null">be_enabled = #{beEnabled},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePrivacyPolicyById" parameterType="Long">
        delete
        from wxapp_privacy_policy
        where id = #{id}
    </delete>

    <delete id="deletePrivacyPolicyByIds" parameterType="String">
        delete from wxapp_privacy_policy where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>