<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yunchuang.wxapp.mapper.PhotoFrameSeriesMapper">

    <resultMap type="PhotoFrameSeries" id="PhotoFrameSeriesResult">
        <result property="id" column="id"/>
        <result property="seriesName" column="series_name"/>
        <result property="sort" column="sort"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectPhotoFrameSeriesVo">
        select id,
               series_name,
               sort,
               status,
               create_by,
               create_time,
               update_by,
               update_time,
               remark
        from wxapp_photo_frame_series
    </sql>

    <select id="selectPhotoFrameSeriesList" parameterType="PhotoFrameSeries" resultMap="PhotoFrameSeriesResult">
        <include refid="selectPhotoFrameSeriesVo"/>
        <where>
            <if test="seriesName != null  and seriesName != ''">and series_name like concat('%', #{seriesName}, '%')
            </if>
            <if test="sort != null ">and sort = #{sort}</if>
            <if test="status != null ">and status = #{status}</if>
        </where>
        order by sort
    </select>

    <select id="selectPhotoFrameSeriesById" parameterType="Long" resultMap="PhotoFrameSeriesResult">
        <include refid="selectPhotoFrameSeriesVo"/>
        where id = #{id}
    </select>

    <insert id="insertPhotoFrameSeries" parameterType="PhotoFrameSeries" useGeneratedKeys="true" keyProperty="id">
        insert into wxapp_photo_frame_series
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="seriesName != null and seriesName != ''">series_name,</if>
            <if test="sort != null">sort,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="seriesName != null and seriesName != ''">#{seriesName},</if>
            <if test="sort != null">#{sort},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updatePhotoFrameSeries" parameterType="PhotoFrameSeries">
        update wxapp_photo_frame_series
        <trim prefix="SET" suffixOverrides=",">
            <if test="seriesName != null and seriesName != ''">series_name = #{seriesName},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePhotoFrameSeriesById" parameterType="Long">
        delete
        from wxapp_photo_frame_series
        where id = #{id}
    </delete>

    <delete id="deletePhotoFrameSeriesByIds" parameterType="String">
        delete from wxapp_photo_frame_series where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>