package com.ruoyi.wxservice.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.wxservice.model.domain.WxServiceConfig;

/**
 * 微信服务号配置 Service接口
 */
public interface IWxServiceConfigService extends IService<WxServiceConfig> {

    /**
     * 删除微信服务号配置
     *
     * @param configId 配置ID
     * @param deptId   部门ID
     * @return 是否成功
     */
    boolean deleteWxService(Long configId, Long deptId);
}
