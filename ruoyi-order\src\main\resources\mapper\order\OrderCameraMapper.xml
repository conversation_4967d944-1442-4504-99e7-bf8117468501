<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.order.mapper.OrderCameraMapper">
    
    <resultMap type="OrderCamera" id="OrderCameraResult">
        <result property="orderId"    column="order_id"    />
        <result property="deviceId"    column="device_id"    />
        <result property="deviceName"    column="device_name"    />
        <result property="openid"    column="openid"    />
        <result property="phone"    column="phone"    />
        <result property="transactionId"    column="transaction_id"    />
        <result property="mchid"    column="mchid"    />
        <result property="appid"    column="appid"    />
        <result property="orderStatus"    column="order_status"    />
        <result property="payWay"    column="pay_way"    />
        <result property="productQuantity"    column="product_quantity"    />
        <result property="productDescription"    column="product_description"    />
        <result property="orderPrice"    column="order_price"    />
        <result property="payTime"    column="pay_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="moneyReceived"    column="money_received"    />
    </resultMap>

    <sql id="selectOrderCameraVo">
        select order_id, device_id, device_name, openid, phone, transaction_id, mchid, appid, order_status, pay_way, product_quantity, product_description, order_price, pay_time, create_by, create_time, update_by, update_time, remark,money_received from order_camera
    </sql>

    <select id="selectOrderCameraList" parameterType="OrderCamera" resultMap="OrderCameraResult">
        <include refid="selectOrderCameraVo"/>
        <where>  
            <if test="deviceId != null  and deviceId != ''"> and device_id = #{deviceId}</if>
            <if test="orderStatus != null"> and order_status = #{orderStatus}</if>
            <if test="deviceName != null  and deviceName != ''"> and device_name like concat('%', #{deviceName}, '%')</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="payWay != null "> and pay_way = #{payWay}</if>
            <if test="productDescription != null  and productDescription != ''"> and product_description = #{productDescription}</if>
            <if test="payTime != null "> and pay_time = #{payTime}</if>
            <if test="createTime != null "> and create_time = #{createTime}</if>
        </where>
    </select>

    <select id="selectOrderCameraByOrderId" parameterType="Long" resultMap="OrderCameraResult">
        <include refid="selectOrderCameraVo"/>
        where order_id = #{orderId}
    </select>
    <select id="selectTwodByDeviceName" resultType="java.lang.Integer">
        select count(*) from order_camera where product_description like '%2D%'
        <if test="deviceId != null">and device_id = #{deviceId}</if>
        <if test="startTime != null and endTime != null"> and pay_time &gt;= #{startTime} and pay_time &lt;= #{endTime} </if>
        and order_status = 1
    </select>
    <select id="selectAlbumByDeviceName" resultType="java.lang.Integer">
        select count(*) from order_camera where product_description like '%写真照%'
        and order_status = 1
        <if test="deviceId != null">and device_id = #{deviceId}</if>
        <if test="startTime != null and endTime != null"> and pay_time &gt;= #{startTime} and pay_time &lt;= #{endTime} </if>
        and order_status = 1
    </select>
    <select id="selectStickerByDeviceName" resultType="java.lang.Integer">
        select count(*) from order_camera where product_description like '%大头贴%'
        <if test="deviceId != null">and device_id = #{deviceId}</if>
        <if test="startTime != null and endTime != null"> and pay_time &gt;= #{startTime} and pay_time &lt;= #{endTime} </if>
        and order_status = 1
    </select>
    <select id="selectIdentificationByDeviceName" resultType="java.lang.Integer">
        select count(*) from order_camera where product_description like '%证件照%'
        <if test="deviceId != null">and device_id = #{deviceId}</if>
        <if test="startTime != null and endTime != null"> and pay_time &gt;= #{startTime} and pay_time &lt;= #{endTime} </if>
        and order_status = 1
    </select>
    <select id="selectComicByDeviceName" resultType="java.lang.Integer">
        select count(*) from order_camera where product_description like '%漫画风%'
        <if test="deviceId != null">and device_id = #{deviceId}</if>
        <if test="startTime != null and endTime != null"> and pay_time &gt;= #{startTime} and pay_time &lt;= #{endTime} </if>
        and order_status = 1
    </select>
    <select id="selectSticerPriceByName" resultType="java.lang.Integer">
        select sum(order_price) from order_camera where order_status = 1
                                                    and product_description like '%大头贴%'
        <if test="deviceId != null">and device_id = #{deviceId}</if>
        <if test="startTime != null and endTime != null"> and pay_time &gt;= #{startTime} and pay_time &lt;= #{endTime} </if>
    </select>
    <select id="selectAlbumPriceByName" resultType="java.lang.Integer">
        select sum(order_price) from order_camera where order_status = 1
        and product_description like '%写真照%'
        <if test="deviceId != null ">and device_id = #{deviceId}</if>
        <if test="startTime != null and endTime != null"> and pay_time &gt;= #{startTime} and pay_time &lt;= #{endTime} </if>
    </select>
    <select id="selectTwodPriceByName" resultType="java.lang.Integer">
        select sum(order_price) from order_camera where order_status = 1
        and product_description like '%2D%'
        <if test="deviceId != null ">and device_id = #{deviceId}</if>
        <if test="startTime != null and endTime != null"> and pay_time &gt;= #{startTime} and pay_time &lt;= #{endTime} </if>
    </select>
    <select id="selectComicPriceByName" resultType="java.lang.Integer">
        select sum(order_price) from order_camera where order_status = 1
        and product_description like '%漫画风%'
        <if test="deviceId != null ">and device_id = #{deviceId}</if>
        <if test="startTime != null and endTime != null"> and pay_time &gt;= #{startTime} and pay_time &lt;= #{endTime} </if>
    </select>
    <select id="selectidentificationPriceByName" resultType="java.lang.Integer">
        select sum(order_price) from order_camera where order_status = 1
        and product_description like '%证件照%'
        <if test="deviceId != null ">and device_id = #{deviceId}</if>
        <if test="startTime != null and endTime != null"> and pay_time &gt;= #{startTime} and pay_time &lt;= #{endTime} </if>
    </select>
    <select id="selectMonthIncome" resultType="java.lang.Integer">
        select sum(order_price) from order_camera where order_status = 1
        <if test="deviceId != null ">and device_id = #{deviceId}</if>
        <if test="startTime != null and endTime != null">  and pay_time &gt;= #{startTime} and pay_time &lt;= #{endTime} </if>
    </select>
    <select id="selectOrderCameraOrderPrice" resultType="java.lang.Float">
        select order_price from order_camera where device_id = #{deviceId}
    </select>
    <select id="selectAllByTime" resultType="com.ruoyi.order.domain.OrderCamera">
        select * from order_camera where order_status = 1
        <if test="deviceId != null"> and device_id = #{deviceId} </if>
        <if test="formattedStartTime != null and formattedEndTime != null"> and pay_time &gt;= #{formattedStartTime} and pay_time &lt;= #{formattedEndTime} </if>
    </select>

    <insert id="insertOrderCamera" parameterType="OrderCamera">
        insert into order_camera
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">order_id,</if>
            <if test="deviceId != null and deviceId != ''">device_id,</if>
            <if test="deviceName != null and deviceName != ''">device_name,</if>
            <if test="openid != null">openid,</if>
            <if test="phone != null">phone,</if>
            <if test="transactionId != null">transaction_id,</if>
            <if test="mchid != null">mchid,</if>
            <if test="appid != null">appid,</if>
            <if test="orderStatus != null">order_status,</if>
            <if test="payWay != null">pay_way,</if>
            <if test="productQuantity != null">product_quantity,</if>
            <if test="productDescription != null">product_description,</if>
            <if test="orderPrice != null">order_price,</if>
            <if test="payTime != null">pay_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">#{orderId},</if>
            <if test="deviceId != null and deviceId != ''">#{deviceId},</if>
            <if test="deviceName != null and deviceName != ''">#{deviceName},</if>
            <if test="openid != null">#{openid},</if>
            <if test="phone != null">#{phone},</if>
            <if test="transactionId != null">#{transactionId},</if>
            <if test="mchid != null">#{mchid},</if>
            <if test="appid != null">#{appid},</if>
            <if test="orderStatus != null">#{orderStatus},</if>
            <if test="payWay != null">#{payWay},</if>
            <if test="productQuantity != null">#{productQuantity},</if>
            <if test="productDescription != null">#{productDescription},</if>
            <if test="orderPrice != null">#{orderPrice},</if>
            <if test="payTime != null">#{payTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateOrderCamera" parameterType="OrderCamera">
        update order_camera
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceId != null and deviceId != ''">device_id = #{deviceId},</if>
            <if test="deviceName != null and deviceName != ''">device_name = #{deviceName},</if>
            <if test="openid != null">openid = #{openid},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="transactionId != null">transaction_id = #{transactionId},</if>
            <if test="mchid != null">mchid = #{mchid},</if>
            <if test="appid != null">appid = #{appid},</if>
            <if test="orderStatus != null">order_status = #{orderStatus},</if>
            <if test="payWay != null">pay_way = #{payWay},</if>
            <if test="productQuantity != null">product_quantity = #{productQuantity},</if>
            <if test="productDescription != null">product_description = #{productDescription},</if>
            <if test="orderPrice != null">order_price = #{orderPrice},</if>
            <if test="payTime != null">pay_time = #{payTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where order_id = #{orderId}
    </update>

    <delete id="deleteOrderCameraByOrderId" parameterType="Long">
        delete from order_camera where order_id = #{orderId}
    </delete>

    <delete id="deleteOrderCameraByOrderIds" parameterType="String">
        delete from order_camera where order_id in 
        <foreach item="orderId" collection="array" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </delete>

    <select id="getHistoryTotalIncome" resultType="java.lang.Long">
        SELECT SUM(account) AS total_account
        FROM (
        <foreach collection="tables" item="table" separator="UNION ALL">
            SELECT account FROM ${table} where user_id = #{userId} and (order_status = 1 or order_status = 4)
        </foreach>
        ) AS all_orders
    </select>


</mapper>