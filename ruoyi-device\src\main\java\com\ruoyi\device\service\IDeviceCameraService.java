package com.ruoyi.device.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.device.domain.DeviceCamera;
import com.ruoyi.system.domain.SysPost;

/**
 * 管理拍照机设备Service接口
 *
 * <AUTHOR>
 * @date 2023-07-14
 */
public interface IDeviceCameraService extends IService<DeviceCamera> {
    /**
     * 查询管理拍照机设备
     *
     * @param deviceId 管理拍照机设备主键
     * @return 管理拍照机设备
     */
    public DeviceCamera selectDeviceCameraByDeviceId(String deviceId);

    /**
     * 查询管理拍照机设备列表
     *
     * @param deviceCamera 管理拍照机设备
     * @return 管理拍照机设备集合
     */
    public TableDataInfo selectDeviceCameraList(DeviceCamera deviceCamera, int pageNum, int pageSize);

    /**
     * 新增管理拍照机设备
     *
     * @param deviceCamera 管理拍照机设备
     * @return 结果
     */
    public int insertDeviceCamera(DeviceCamera deviceCamera);

    /**
     * 修改管理拍照机设备
     *
     * @param deviceCamera 管理拍照机设备
     * @return 结果
     */
    public int updateDeviceCamera(DeviceCamera deviceCamera);


    /**
     * 批量删除管理拍照机设备
     *
     * @param deviceIds 需要删除的管理拍照机设备主键集合
     * @return 结果
     */
    public int deleteDeviceCameraByDeviceIds(String[] deviceIds);

    /**
     * 删除管理拍照机设备信息
     *
     * @param deviceId 管理拍照机设备主键
     * @return 结果
     */
    public int deleteDeviceCameraByDeviceId(String deviceId);


    /**
     * 根据设备id查询用户id
     *
     * @param deviceId
     * @return
     */
    int selectUserIDByDeviceId(String deviceId);


    SysPost getSoftware(String deviceId);

    boolean updateDeviceStatus(String deviceId, String printerStatus, int cameraStatus, int beautyStatus, int consumables, int paperConsumables);
}
