package com.ruoyi.dto;

public class Subscription {
    private String notification_method;
    private String notification_address;
    private String notification_payload;

    public Subscription() {
    }

    public Subscription(String notification_method, String notification_address, String notification_payload) {
        this.notification_method = notification_method;
        this.notification_address = notification_address;
        this.notification_payload = notification_payload;
    }

    public String getNotification_method() {
        return notification_method;
    }

    public void setNotification_method(String notification_method) {
        this.notification_method = notification_method;
    }

    public String getNotification_address() {
        return notification_address;
    }

    public void setNotification_address(String notification_address) {
        this.notification_address = notification_address;
    }

    public String getNotification_payload() {
        return notification_payload;
    }

    public void setNotification_payload(String notification_payload) {
        this.notification_payload = notification_payload;
    }
}
