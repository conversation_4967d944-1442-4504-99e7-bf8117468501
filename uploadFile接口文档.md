# /user/uploadFile 接口文档

## 接口概述

**接口名称**: 上传文件并创建打印订单  
**接口路径**: `/order/printer/user/uploadFile`  
**请求方式**: `POST`  
**Content-Type**: `multipart/form-data`  
**功能描述**: 上传打印文件，创建或更新打印订单，计算打印价格，支持多次上传

## 请求参数

### 表单参数 (multipart/form-data)

| 参数名 | 类型 | 必填 | 默认值 | 说明 | 示例值 |
|--------|------|------|--------|------|--------|
| **file** | File | ✅ | - | 要打印的文件 | document.pdf |
| **orderId** | String | ✅ | - | 订单ID（前端生成） | ORDER_20250130_001 |
| **deviceId** | String | ✅ | - | 打印设备ID | PRINTER_001 |
| **deviceName** | String | ❌ | null | 设备名称 | 图书馆打印机 |
| **openid** | String | ❌ | null | 微信用户openid | wx_abc123def456 |
| **phone** | String | ❌ | null | 用户手机号 | 13800138000 |
| **copies** | Integer | ❌ | 1 | 打印份数 | 2 |
| **colorMode** | Integer | ❌ | 0 | 颜色模式 | 0 |
| **duplexMode** | Integer | ❌ | 0 | 双面模式 | 0 |
| **paperType** | Integer | ❌ | 1 | 纸张类型 | 1 |
| **pageRange** | String | ❌ | null | 页码范围 | 1-5,8-10 |
| **isLastUpload** | Boolean | ✅ | false | 是否最后一次上传 | true |
| **isPhoto** | Integer | ❌ | 0 | 是否为照片 | 0 |
| **sizeSpec** | String | ❌ | null | 尺寸大小 | A4 |

### 参数详细说明

#### 颜色模式 (colorMode)
- `0`: 黑白打印
- `1`: 彩色打印

#### 双面模式 (duplexMode)
- `0`: 单面打印
- `1`: 双面打印

#### 纸张类型 (paperType)
- `1`: A4纸
- `2`: A5纸
- `3`: 照片纸

#### 页码范围 (pageRange)
- 格式: `起始页-结束页,单独页,起始页-结束页`
- 示例: `1-3,5,7-9` (打印第1-3页、第5页、第7-9页)
- 留空表示打印全部页面

#### 是否为照片 (isPhoto)
- `0`: 文档类型
- `1`: 照片类型

#### 尺寸大小 (sizeSpec)
- 文档类: `A4`, `A5`, `A3`
- 照片类: `4寸`, `6寸`, `7寸`, `8寸`, `10寸`

#### 是否最后一次上传 (isLastUpload)
- `true`: 最后一次上传，会计算订单总金额
- `false`: 还有文件要上传，不计算总金额

## 请求示例

### cURL 示例

#### 文档打印
```bash
curl -X POST "http://localhost:8081/order/printer/user/uploadFile" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@document.pdf" \
  -F "orderId=ORDER_20250130_001" \
  -F "deviceId=PRINTER_001" \
  -F "deviceName=图书馆打印机" \
  -F "openid=wx_abc123def456" \
  -F "phone=13800138000" \
  -F "copies=2" \
  -F "colorMode=0" \
  -F "duplexMode=1" \
  -F "paperType=1" \
  -F "pageRange=1-10" \
  -F "isLastUpload=true" \
  -F "isPhoto=0" \
  -F "sizeSpec=A4"
```

#### 照片打印
```bash
curl -X POST "http://localhost:8081/order/printer/user/uploadFile" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@photo.jpg" \
  -F "orderId=ORDER_20250130_002" \
  -F "deviceId=PRINTER_002" \
  -F "copies=1" \
  -F "colorMode=1" \
  -F "paperType=3" \
  -F "isLastUpload=true" \
  -F "isPhoto=1" \
  -F "sizeSpec=6寸"
```

### JavaScript 示例

```javascript
// 创建表单数据
const formData = new FormData();
formData.append('file', selectedFile);
formData.append('orderId', 'ORDER_' + Date.now());
formData.append('deviceId', 'PRINTER_001');
formData.append('deviceName', '图书馆打印机');
formData.append('openid', userOpenid);
formData.append('phone', '13800138000');
formData.append('copies', 2);
formData.append('colorMode', 0);
formData.append('duplexMode', 1);
formData.append('paperType', 1);
formData.append('pageRange', '1-10');
formData.append('isLastUpload', true);
formData.append('isPhoto', 0);
formData.append('sizeSpec', 'A4');

// 发送请求
fetch('/order/printer/user/uploadFile', {
  method: 'POST',
  body: formData
})
.then(response => response.json())
.then(data => {
  console.log('上传成功:', data);
})
.catch(error => {
  console.error('上传失败:', error);
});
```

## 响应格式

### 成功响应

```json
{
  "code": 200,
  "msg": "上传文件成功",
  "data": {
    "taskId": "TASK_abc123def456",
    "taskPrice": 1.5,
    "pageCount": 5,
    "fileName": "document.pdf",
    "fileUrl": "https://oss.example.com/files/20250130/abc123def456.pdf",
    "totalAmount": 3.0,
    "isOrderComplete": true,
    "isPhoto": 0,
    "sizeSpec": "A4"
  }
}
```

### 响应字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `code` | Integer | 响应状态码，200表示成功 |
| `msg` | String | 响应消息 |
| `data` | Object | 响应数据 |
| `data.taskId` | String | 打印任务ID |
| `data.taskPrice` | Double | 单个任务价格（元） |
| `data.pageCount` | Integer | 文件页数 |
| `data.fileName` | String | 文件名称 |
| `data.fileUrl` | String | 文件存储URL |
| `data.totalAmount` | Double | 订单总金额（元） |
| `data.isOrderComplete` | Boolean | 订单是否完成（最后一次上传时为true） |
| `data.isPhoto` | Integer | 是否为照片 |
| `data.sizeSpec` | String | 尺寸大小 |

### 错误响应

```json
{
  "code": 500,
  "msg": "具体错误信息",
  "data": null
}
```

### 常见错误码

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 400 | 参数验证失败 | 必填参数缺失或格式错误 |
| 404 | 设备不存在 | 指定的设备ID不存在 |
| 500 | 文件上传失败 | 文件上传到OSS失败 |
| 500 | 订单创建失败 | 数据库操作失败 |

## 业务流程

### 1. 单文件上传流程
```mermaid
sequenceDiagram
    participant 前端 as 前端
    participant 后端 as 后端系统
    participant OSS as 文件存储
    participant DB as 数据库

    前端->>后端: 上传文件 (isLastUpload=true)
    后端->>后端: 验证参数
    后端->>DB: 检查/创建订单
    后端->>OSS: 上传文件
    OSS->>后端: 返回文件URL
    后端->>后端: 解析页数，计算价格
    后端->>DB: 创建打印任务
    后端->>DB: 计算订单总金额
    后端->>前端: 返回结果
```

### 2. 多文件上传流程
```mermaid
sequenceDiagram
    participant 前端 as 前端
    participant 后端 as 后端系统
    participant OSS as 文件存储
    participant DB as 数据库

    前端->>后端: 上传文件1 (isLastUpload=false)
    后端->>DB: 创建订单和任务1
    后端->>前端: 返回任务1结果

    前端->>后端: 上传文件2 (isLastUpload=false)
    后端->>DB: 创建任务2
    后端->>前端: 返回任务2结果

    前端->>后端: 上传文件3 (isLastUpload=true)
    后端->>DB: 创建任务3
    后端->>DB: 计算订单总金额
    后端->>前端: 返回完整订单结果
```

## 注意事项

### 1. 文件限制
- **支持格式**: PDF, DOC, DOCX, JPG, PNG, BMP
- **文件大小**: 最大50MB
- **页数限制**: 最大100页

### 2. 订单管理
- 同一个 `orderId` 可以多次调用接口上传不同文件
- 只有 `isLastUpload=true` 时才会计算订单总金额
- 订单创建后状态为 `0`（未支付）

### 3. 价格计算
- 基于页数、份数、颜色模式、双面模式、纸张类型计算
- 照片打印和文档打印可能有不同的定价策略
- 价格单位为元，保留2位小数

### 4. 安全考虑
- 文件会上传到OSS进行安全存储
- 支持用户身份验证（通过openid）
- 文件URL包含安全令牌

## 后续操作

上传成功后，通常需要进行以下操作：

1. **发起支付**: 调用支付接口 `/client/wxapp/printer/pay/getOpenId`
2. **查询订单**: 查看订单状态和详情
3. **修改订单**: 如需修改，可调用订单更新接口

## 测试建议

### 功能测试
1. **单文件上传**: 测试各种文件格式和参数组合
2. **多文件上传**: 测试多次上传的流程
3. **参数验证**: 测试必填参数和参数格式验证
4. **边界测试**: 测试文件大小限制、页数限制等

### 异常测试
1. **文件格式错误**: 上传不支持的文件格式
2. **文件过大**: 上传超过大小限制的文件
3. **设备不存在**: 使用不存在的设备ID
4. **网络异常**: 模拟网络中断情况

## 版本历史

- **v1.0**: 基础上传功能
- **v1.1**: 新增 `isPhoto` 和 `sizeSpec` 参数，支持照片打印
