package com.ruoyi.utils;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.config.MyConfig;
import com.ruoyi.enums.FunctionEnum;
import com.ruoyi.enums.PayCompanyEnum;
import com.ruoyi.po.RequestModel;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.util.EntityUtils;

import javax.net.ssl.*;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.sql.Timestamp;

public class HttpsMain {


    public static String httpsReq(String reqUrl, String param) throws NoSuchAlgorithmException,
            NoSuchProviderException, IOException,
            KeyManagementException {
        URL url = new URL(reqUrl);
        HttpsURLConnection httpsConn = (HttpsURLConnection) url.openConnection();
        httpsConn.setHostnameVerifier(new HostnameVerifier() {
            public boolean verify(String paramString, SSLSession paramSSLSession) {
                return true;
            }
        });

        //创建SSLContext对象，并使用我们指定的信任管理器初始化
        TrustManager tm = new X509TrustManager() {
            public void checkClientTrusted(X509Certificate[] paramArrayOfX509Certificate,
                                           String paramString) throws CertificateException {
            }

            public void checkServerTrusted(X509Certificate[] paramArrayOfX509Certificate,
                                           String paramString) throws CertificateException {
            }

            public X509Certificate[] getAcceptedIssuers() {
                return null;
            }
        };

        SSLContext sslContext = SSLContext.getInstance("SSL", "SunJSSE");
        sslContext.init(null, new TrustManager[]{tm}, new java.security.SecureRandom());

        //从上述SSLContext对象中得到SSLSocketFactory对象
        SSLSocketFactory ssf = sslContext.getSocketFactory();

        //创建HttpsURLConnection对象，并设置其SSLSocketFactory对象
        httpsConn.setSSLSocketFactory(ssf);

        httpsConn.setDoOutput(true);
        httpsConn.setRequestMethod("POST");
        httpsConn.setRequestProperty("Content-Type", "application/json;charset=UTF-8");
        httpsConn.setRequestProperty("Content-Length", String.valueOf(param.length()));

        OutputStreamWriter out = new OutputStreamWriter(httpsConn.getOutputStream(), "UTF-8");
        out.write(param);
        out.flush();
        out.close();

        BufferedReader reader = new BufferedReader(new InputStreamReader(
                httpsConn.getInputStream(), "UTF-8"));
        String tempLine = "";
        StringBuffer resultBuffer = new StringBuffer();
        while ((tempLine = reader.readLine()) != null) {
            resultBuffer.append(tempLine).append(System.getProperty("line.separator"));
        }
        return resultBuffer.toString();
    }

    public static String httpReq(String urlStr, String param) throws IOException {
        URL url = new URL(urlStr);
        HttpURLConnection httpConn = (HttpURLConnection) url.openConnection();

        httpConn.setDoOutput(true);
        httpConn.setRequestMethod("POST");
        httpConn.setRequestProperty("Content-Type", "application/json;charset=UTF-8");
        httpConn.setRequestProperty("Content-Length", String.valueOf(param.length()));

        OutputStreamWriter out = new OutputStreamWriter(httpConn.getOutputStream(), "UTF-8");
        out.write(param);
        out.flush();
        out.close();

        BufferedReader reader = new BufferedReader(new InputStreamReader(
                httpConn.getInputStream(), "UTF-8"));
        String tempLine = "";
        StringBuffer resultBuffer = new StringBuffer();
        while ((tempLine = reader.readLine()) != null) {
            resultBuffer.append(tempLine).append(System.getProperty("line.separator"));
        }
        return resultBuffer.toString();
    }

    public static String format(Object reqParam, FunctionEnum functionEnum,String AgencyNo,String PrivateKey) {
        RequestModel requestModel = new RequestModel();
        requestModel.setAgencyNo(AgencyNo);
        requestModel.setTimestamp(new Timestamp(System.currentTimeMillis()).toString());
        requestModel.setFunction(functionEnum.getCode());
        requestModel.setContent(JSON.toJSONString(reqParam));
        //以json格式按照首字母a-z排序，签名方式：SHA256WithRSA
        String body = JSON.toJSONString(requestModel);
        String sign = RsaUtil.sign(body, PrivateKey);
        requestModel.setSign(sign);
        return JSON.toJSONString(requestModel);
    }
    public static String format(Object reqParam, FunctionEnum functionEnum, PayCompanyEnum payCompanyEnum,String AgencyNo,String PrivateKey) {
        RequestModel requestModel = new RequestModel();
        requestModel.setAgencyNo(AgencyNo);
        requestModel.setTimestamp(new Timestamp(System.currentTimeMillis()).toString());
        requestModel.setFunction(functionEnum.getCode());
        requestModel.setContent(JSON.toJSONString(reqParam));
        requestModel.setPayCompany(payCompanyEnum.getCode());
        //以json格式按照首字母a-z排序，签名方式：SHA256WithRSA
        String body = JSON.toJSONString(requestModel);
        String sign = RsaUtil.sign(body, PrivateKey);
        requestModel.setSign(sign);
        return JSON.toJSONString(requestModel);
    }

    public static String formatComplaint(Object reqParam, FunctionEnum functionEnum) {
        RequestModel requestModel = new RequestModel();
        requestModel.setAgencyNo(MyConfig.AgencyNo);
        requestModel.setTimestamp(new Timestamp(System.currentTimeMillis()).toString());
        requestModel.setFunction(functionEnum.getCode());
        requestModel.setContent(JSON.toJSONString(reqParam));
        //以json格式按照首字母a-z排序，签名方式：SHA256WithRSA
        String body = JSON.toJSONString(requestModel);
        String sign = RsaUtil.sign(body, MyConfig.PrivateKey);
        requestModel.setSign(sign);
        return JSON.toJSONString(requestModel);
    }

    private static HttpClient httpclient = new DefaultHttpClient();
    public static String httpPost(String url, HttpEntity reqEntity) {
        String result = "";
        HttpPost httppost = new HttpPost(url);
        HttpResponse response = null;
        try {
            httppost.setEntity(reqEntity);
            response = httpclient.execute(httppost);
        } catch (ClientProtocolException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        HttpEntity entity = null;
        if (response != null) {
            entity = response.getEntity();
        }

        if (entity != null) {
            try {
                String content = EntityUtils.toString(entity);
                result = content;
            } catch (Exception e1) {
                e1.printStackTrace();
            }
        }
        return result;
    }
}
