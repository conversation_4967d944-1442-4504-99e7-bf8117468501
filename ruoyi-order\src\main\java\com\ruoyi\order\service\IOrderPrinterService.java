package com.ruoyi.order.service;

import com.ruoyi.order.domain.OrderPrinter;
import com.ruoyi.order.domain.OrderPrinterTask;

import java.util.List;
import java.util.Map;

/**
 * 订单打印机服务接口
 * 
 * <AUTHOR>
 * @date 2024-06-15
 */
public interface IOrderPrinterService 
{
    /**
     * 查询订单打印机
     * 
     * @param orderId 订单打印机主键
     * @return 订单打印机
     */
    public OrderPrinter selectOrderPrinterByOrderId(String orderId);

    /**
     * 查询订单打印机列表
     * 
     * @param orderPrinter 订单打印机
     * @return 订单打印机集合
     */
    public List<OrderPrinter> selectOrderPrinterList(OrderPrinter orderPrinter);

    /**
     * 新增订单打印机
     * 
     * @param orderPrinter 订单打印机
     * @return 结果
     */
    public int insertOrderPrinter(OrderPrinter orderPrinter);

    /**
     * 修改订单打印机
     * 
     * @param orderPrinter 订单打印机
     * @return 结果
     */
    public int updateOrderPrinter(OrderPrinter orderPrinter);

    /**
     * 批量删除订单打印机
     * 
     * @param orderIds 需要删除的订单打印机主键集合
     * @return 结果
     */
    public int deleteOrderPrinterByOrderIds(String[] orderIds);

    /**
     * 删除订单打印机信息
     * 
     * @param orderId 订单打印机主键
     * @return 结果
     */
    public int deleteOrderPrinterByOrderId(String orderId);
    
    /**
     * 创建打印预订单
     *
     * @param params 预订单参数
     * @return 预订单信息
     */
    public Map<String, Object> createPreOrder(Map<String, Object> params);
    
    /**
     * 支付成功后处理
     *
     * @param orderId 订单ID
     * @param transactionId 交易ID
     * @return 结果
     */
    public boolean paySuccess(String orderId, String transactionId);
    
    /**
     * 添加打印任务
     *
     * @param task 打印任务
     * @return 结果
     */
    public int addPrintTask(OrderPrinterTask task);
    
    /**
     * 查询订单的打印任务
     *
     * @param orderId 订单ID
     * @return 打印任务列表
     */
    public List<OrderPrinterTask> getOrderTasks(String orderId);
    
    /**
     * 更新打印任务状态
     *
     * @param taskId 任务ID
     * @param status 状态
     * @param errorMsg 错误信息
     * @return 结果
     */
    public int updateTaskStatus(String taskId, Integer status, String errorMsg);

    /**
     * 创建订单（不包含文件）
     *
     * @param params 订单参数
     * @return 订单信息
     */
    public Map<String, Object> createOrder(Map<String, Object> params);

    /**
     * 上传文件并计算价格
     *
     * @param orderId 订单ID
     * @param fileParams 文件和打印参数
     * @return 任务信息和价格
     */
    public Map<String, Object> uploadFileAndCalculatePrice(String orderId, Map<String, Object> fileParams);

    /**
     * 计算打印价格
     *
     * @param pageCount 页数
     * @param copies 份数
     * @param colorMode 颜色模式 0-黑白 1-彩色
     * @param duplexMode 双面模式 0-单面 1-双面
     * @param paperType 纸张类型 1-A4 2-A5 3-照片纸
     * @param deviceId 设备ID
     * @return 价格（元）
     */
    public Double calculatePrintPrice(int pageCount, int copies, int colorMode, int duplexMode, int paperType, String deviceId);

    /**
     * 解析页码范围，计算总页数
     *
     * @param pageRange 页码范围，如"1-3,5,7-9"
     * @return 总页数
     */
    public int parsePageRange(String pageRange);

    /**
     * 计算订单总金额
     *
     * @param orderId 订单ID
     * @return 总金额（元）
     */
    public Double calculateOrderTotalAmount(String orderId);

    /**
     * 查询用户订单列表（包含总金额）- 性能优化版本
     *
     * @param openid 用户openid
     * @return 订单列表（包含总金额）
     */
    public List<OrderPrinter> selectOrderPrinterListWithTotalAmount(String openid);

    /**
     * 查询用户订单列表（支持状态筛选）
     *
     * @param openid 用户openid
     * @param payStatus 支付状态筛选：1-已支付，3-已退款，null-不筛选
     * @param printStatus 打印状态筛选：pending-待打印，completed-已完成，null-不筛选
     * @return 订单列表（包含总金额和打印状态）
     */
    public List<OrderPrinter> selectOrderPrinterListWithStatusFilter(String openid, Integer payStatus, String printStatus);

    /**
     * 删除用户的所有订单数据（逻辑删除）
     * @param openid 用户openid
     * @return 删除结果
     */
    public boolean deleteUserOrdersByOpenid(String openid);

    /**
     * 更新打印任务信息
     * @param task 打印任务对象
     * @return 更新结果
     */
    public int updateTask(OrderPrinterTask task);
}