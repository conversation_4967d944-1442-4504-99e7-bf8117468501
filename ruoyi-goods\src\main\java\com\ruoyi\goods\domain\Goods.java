package com.ruoyi.goods.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * goods对象 goods
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("goods")
public class Goods extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 商品id */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /** 商品图片 */
    @Excel(name = "商品图片")
    private String goodsImage;
    /** 图片路径 */
    @Excel(name = "图片路径")
    private String objectName;

    /** 商品名称 */
    @Excel(name = "商品名称")
    private String goodsName;

    /** 价格（分） */
    @Excel(name = "价格", readConverterExp = "分=")
    private Long price;

    /** 出货指令 */
    private String code;

    /** 商品描述 */
    @Excel(name = "商品描述")
    private String goodsDescribe;

    /** 0 下架   1 上架 */
    @Excel(name = "0 下架   1 上架")
    private Long status;

    /** 1普通    2配方 */
    @Excel(name = "商品类型")
    private Long type;

    /** 1冷    2热       10其他 */
    @Excel(name = "咖啡类型")
    private Integer coffeeType;


    @Excel(name = "热销")
    private Boolean hot;



}
