package com.ruoyi.po;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class Merchant<PERSON>ee implements Serializable {

    private static final long serialVersionUID = -6909525272813504053L;
    private Integer id;
    private Integer mchId;
    private String channelType;
    private String feeType;
    private String feeValue;
    private Date createTime;
    private Date updateTime;

    public MerchantFee(String channelType, String feeType, String feeValue) {
        this.channelType = channelType;
        this.feeType = feeType;
        this.feeValue = feeValue;
    }
}
