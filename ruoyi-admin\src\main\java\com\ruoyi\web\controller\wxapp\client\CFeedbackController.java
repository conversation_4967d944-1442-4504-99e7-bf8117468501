package com.ruoyi.web.controller.wxapp.client;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.utils.MyResultUtil;
import com.yunchuang.wxapp.model.domain.WxappLoginUser;
import com.yunchuang.wxapp.model.req.CFeedbackAddOneReq;
import com.yunchuang.wxapp.service.client.ICFeedbackService;
import com.yunchuang.wxapp.util.UserContext;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 意见反馈Controller
 */
@RestController
@RequestMapping("/client/wxapp/feedback")
public class CFeedbackController extends BaseController {

    @Resource
    private ICFeedbackService cFeedbackService;

    /**
     * 新增意见反馈
     */
    @PostMapping("/add")
    public Map<String, Object> addFeedback(@RequestBody CFeedbackAddOneReq addOneReq) {
        System.out.println("addOneReq: " + addOneReq);
        WxappLoginUser loginUser = UserContext.getCurrentUser();
        boolean isSuccess = cFeedbackService.addFeedback(loginUser, addOneReq);
        return isSuccess ? MyResultUtil.success() : MyResultUtil.error("新增失败");
    }

}
