package com.yunchuang.wxapp.service.client.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yunchuang.wxapp.mapper.UserAgreementMapper;
import com.yunchuang.wxapp.model.domain.UserAgreement;
import com.yunchuang.wxapp.service.client.ICUserAgreementService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

@Service
@Transactional
public class CUserAgreementServiceImpl extends ServiceImpl<UserAgreementMapper, UserAgreement> implements ICUserAgreementService {

    @Resource
    private UserAgreementMapper userAgreementMapper;

    /**
     * 查询启用的用户协议
     *
     * @return 用户协议
     */
    @Override
    public UserAgreement getEnabledUserAgreement() {
        LambdaQueryWrapper<UserAgreement> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(UserAgreement::getReleaseDate, UserAgreement::getEffectiveDate, UserAgreement::getAgreementContent);
        queryWrapper.eq(UserAgreement::getBeEnabled, 1);
        return userAgreementMapper.selectOne(queryWrapper);
    }

}
