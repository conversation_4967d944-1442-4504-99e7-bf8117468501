package com.yunchuang.wxapp.service.client;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yunchuang.wxapp.model.domain.WishList;
import com.yunchuang.wxapp.model.req.CWishListAddReq;
import com.yunchuang.wxapp.model.req.CWishListDeleteReq;
import com.yunchuang.wxapp.model.resp.CWishListDetailListResp;

import java.util.List;

/**
 * 心愿单 Service 接口
 */
public interface ICWishListService extends IService<WishList> {

    /**
     * 查询心愿单列表
     *
     * @param userId 用户ID
     * @return 心愿单列表
     */
    List<WishList> getWishListList(Long userId);

    /**
     * 新增心愿单
     *
     * @param userId 用户ID
     * @param addReq 新增心愿单请求
     * @return 是否成功
     */
    boolean addWishList(Long userId, CWishListAddReq addReq);

    /**
     * 删除心愿单
     *
     * @param userId    用户ID
     * @param deleteReq 删除心愿单请求
     * @return 是否成功
     */
    boolean deleteWishList(Long userId, CWishListDeleteReq deleteReq);

    /**
     * 查询心愿单详情列表
     *
     * @param userId 用户ID
     * @return 心愿单详情列表
     */
    List<CWishListDetailListResp> getWishListDetailList(Long userId);
}
