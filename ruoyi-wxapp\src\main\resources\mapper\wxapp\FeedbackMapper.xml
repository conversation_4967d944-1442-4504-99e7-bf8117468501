<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yunchuang.wxapp.mapper.FeedbackMapper">

    <resultMap type="Feedback" id="FeedbackResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="nickname" column="nickname"/>
        <result property="content" column="content"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="feedbackType" column="feedback_type"/>
    </resultMap>

    <sql id="selectFeedbackVo">
        select id,
               user_id,
               nickname,
               content,
               status,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               feedback_type
        from wxapp_feedback
    </sql>

    <select id="selectFeedbackList" parameterType="Feedback" resultMap="FeedbackResult">
        <include refid="selectFeedbackVo"/>
        <where>
            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="nickname != null  and nickname != ''">and nickname like concat('%', #{nickname}, '%')</if>
            <if test="content != null  and content != ''">and content like concat('%', #{content}, '%')</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="feedbackType != null ">and feedback_type = #{feedbackType}</if>
        </where>
    </select>

    <select id="selectFeedbackById" parameterType="Long" resultMap="FeedbackResult">
        <include refid="selectFeedbackVo"/>
        where id = #{id}
    </select>

    <insert id="insertFeedback" parameterType="Feedback" useGeneratedKeys="true" keyProperty="id">
        insert into wxapp_feedback
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="nickname != null and nickname != ''">nickname,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="feedbackType != null">feedback_type,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="nickname != null and nickname != ''">#{nickname},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="feedbackType != null">#{feedbackType},</if>
        </trim>
    </insert>

    <update id="updateFeedback" parameterType="Feedback">
        update wxapp_feedback
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="nickname != null and nickname != ''">nickname = #{nickname},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="feedbackType != null">feedback_type = #{feedbackType},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFeedbackById" parameterType="Long">
        delete
        from wxapp_feedback
        where id = #{id}
    </delete>

    <delete id="deleteFeedbackByIds" parameterType="String">
        delete from wxapp_feedback where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>