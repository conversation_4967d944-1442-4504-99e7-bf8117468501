package com.ruoyi.web.controller.wxapp.client;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.utils.MyResultUtil;
import com.yunchuang.wxapp.model.domain.WxappLoginUser;
import com.yunchuang.wxapp.model.req.CWishListAddReq;
import com.yunchuang.wxapp.model.req.CWishListDeleteReq;
import com.yunchuang.wxapp.service.client.ICWishListService;
import com.yunchuang.wxapp.util.UserContext;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 心愿单Controller
 */
@RestController
@RequestMapping("/client/wxapp/wishlist")
public class CWishListController extends BaseController {

    @Resource
    private ICWishListService cWishListService;

    /**
     * 查询心愿单列表
     */
    @GetMapping("/list")
    public Map<String, Object> getWishList() {
        WxappLoginUser loginUser = UserContext.getCurrentUser();
        return MyResultUtil.success(cWishListService.getWishListList(loginUser.getId()));
    }

    /**
     * 新增心愿单
     */
    @PostMapping("/add")
    public Map<String, Object> addWishList(@RequestBody CWishListAddReq addReq) {
        WxappLoginUser loginUser = UserContext.getCurrentUser();
        boolean isSuccess = cWishListService.addWishList(loginUser.getId(), addReq);
        return isSuccess ? MyResultUtil.success() : MyResultUtil.error();
    }

    /**
     * 删除心愿单
     */
    @PostMapping("/delete")
    public Map<String, Object> deleteWishList(@RequestBody CWishListDeleteReq deleteReq) {
        WxappLoginUser loginUser = UserContext.getCurrentUser();
        boolean isSuccess = cWishListService.deleteWishList(loginUser.getId(), deleteReq);
        return isSuccess ? MyResultUtil.success() : MyResultUtil.error();
    }

    /**
     * 查询心愿单详情列表
     */
    @GetMapping("/detail_list")
    public Map<String, Object> getWishListDetailList() {
        WxappLoginUser loginUser = UserContext.getCurrentUser();
        return MyResultUtil.success(cWishListService.getWishListDetailList(loginUser.getId()));
    }

}
