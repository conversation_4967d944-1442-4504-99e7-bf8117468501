package com.ruoyi.generator.service;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.generator.domain.*;
import com.ruoyi.generator.util.HttpUtils;
import com.ruoyi.generator.util.WechatMessageUtil;
import com.ruoyi.photo.domain.UserPhoto;
import com.ruoyi.photo.service.IUserPhotoService;
import lombok.extern.slf4j.Slf4j;
import org.jboss.logging.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;

import static com.ruoyi.generator.util.WechatMessageUtil.textMessageToXml;
import static com.ruoyi.generator.util.WechatMessageUtil.xmlToMap;

@Service
@Slf4j
public class WechatService {
    private static Logger logger = Logger.getLogger(WechatService.class);
    @Autowired
    private IUserPhotoService userPhotoService;


    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Resource
    private ImageDownloaderService imageDownloaderService;

    public String processRequest(HttpServletRequest request) {

        Map<String, String> map = WechatMessageUtil.xmlToMap(request);
        log.info("微信公众号接收消息22," + map);


        // 发送方帐号（一个OpenID）
        String fromUserName = map.get("FromUserName");
        // 开发者微信号
        String toUserName = map.get("ToUserName");
        // 消息类型
        String msgType = map.get("MsgType");
        // 默认回复一个"success"
        String responseMessage = "success";
        // 对消息进行处理
        if (WechatMessageUtil.MESSAGE_TEXT.equals(msgType)) {// 文本消息
            TextMessage textMessage = new TextMessage();
            textMessage.setMsgType(WechatMessageUtil.MESSAGE_TEXT);
            textMessage.setToUserName(fromUserName);
            textMessage.setFromUserName(toUserName);
            textMessage.setCreateTime(System.currentTimeMillis());
            textMessage.setContent("我已经受到你发来的消息了");
            responseMessage = WechatMessageUtil.textMessageToXml2(textMessage);
        }
        log.info(responseMessage);
        // 去掉开头的-
//        responseMessage = responseMessage.substring(1);
        return responseMessage;

    }


    public String processRequest2(HttpServletRequest request, PrintWriter out) throws Exception {

        log.info("进入推送消息方法11111");
// 获得微信端返回的xml数据
        ServletInputStream is = null;
        InputStreamReader isr = null;
        BufferedReader br = null;

//        getAccessToken();

//        OutputStream os = response.getOutputStream();
        try {
            is = request.getInputStream();
            isr = new InputStreamReader(is, "utf-8");
            br = new BufferedReader(isr);
            String str = null;

            Map<String, String> encryptMap = xmlToMap(request);
            log.info("接受消息：" + encryptMap.toString());
            // 得到公众号传来的加密信息并解密,得到的是明文xml数据
            //String decryptXml = WXPublicUtils.decrypt(encryptMap.get("Encrypt"));
            // 将xml数据转换为map
            /*log.info(decryptXml.toString());
            Map<String, String> decryptMap = VatifyToken.xmlToMap(decryptXml.toString());*/
            Map<String, String> decryptMap = encryptMap;
            // 区分消息类型
            String msgType = decryptMap.get("MsgType");
            //场景信息
            String eventKey = decryptMap.get("EventKey");
            log.info("MsgType:" + msgType);
            log.info("EventKey:" + eventKey);

            PicMessage picMessage = new PicMessage();
            picMessage.setCreateTime(new Date().getTime());
            picMessage.setToUserName(decryptMap.get("FromUserName"));
            picMessage.setFromUserName(decryptMap.get("ToUserName"));
            picMessage.setMsgType("text");
            picMessage.setMediaId("成功");
            String mapToXml = textMessageToXml(picMessage);

            logger.info("发送数据:" + mapToXml);
//                os.write(mapToXml.getBytes());
            out.print(mapToXml);
            out.flush();

            // 普通消息
            if ("text".equals(msgType)) { // 文本消息
                if (!decryptMap.get("Content").equals("电子照")) {

                    sendText(decryptMap, "您好，如需获取电子照，请回复：电子照");

                    return mapToXml;
                }

                sendText(decryptMap, "正在获取照片...");

//                String image = saveImage("https://jkht.wteam.club/file/a.png");
//                sendImage(decryptMap,image);
                new Thread(() -> {

                    String sendGet = HttpUtils.sendGet("https://api.weixin.qq.com/cgi-bin/user/info?access_token=" + redisTemplate.opsForValue().get("wx_access_token") + "&openid=" + decryptMap.get("FromUserName") + "&lang=zh_CN", "");
                    log.info("获取用户信息：" + sendGet);
                    JSONObject jsonObject = JSONObject.parseObject(sendGet);
                    String PhotoUrl = null;

                    try {
                        PhotoUrl = connectHttpsByPost(jsonObject.getString("unionid"));
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }

                    if (Objects.equals(PhotoUrl, "无照片")) {
                        sendText(decryptMap, "您好，你当前暂时没有照片~");

                    } else {

                        sendImage(decryptMap, PhotoUrl);
                    }

                }).start();


//                HttpConnectionUtil.AccessUrl(customerUrl,json);
//
//
//                CustomerServiceArticle a1 = new CustomerServiceArticle();
//                a1.setTitle("你好，欢迎您的加入！");
//                a1.setDescription("描述1");
//                a1.setPicurl("http://xxx//res//prod//SSEP/uploadfiles/knowledgeCourseImage/2020729407976775.png");
//                a1.setUrl("http://www.baidu.com");
//                CustomerServiceArticle a2 = new CustomerServiceArticle();
//                a2.setTitle("你好，欢迎您的致电！");
//                a2.setDescription("描述2");
//                a2.setPicurl("http://xxx//res//prod//SSEP/uploadfiles/knowledgeCourseImage/2020729407976775.png");
//                a2.setUrl("http://www.jd.com");
//                List<CustomerServiceArticle> list = new ArrayList<>();
//                list.add(a1);
//                //list.add(a2);
//                CustomerServiceImageText it = new CustomerServiceImageText();
//                it.setTouser(decryptMap.get("FromUserName"));
//                it.setMsgtype("news");
//                Map<String,List<CustomerServiceArticle>> map = new HashMap<>();
//                map.put("articles",list);
//                it.setNews(map);
//
//                String json2 =  JSONObject.toJSONString(it);
//                log.info(json2);
//                HttpConnectionUtil.AccessUrl(customerUrl,json2);
                return mapToXml;
            } else if ("image".equals(msgType)) { // 图片消息
                // todo 处理图片消息
            } else if ("voice".equals(msgType)) { //语音消息
                // todo 处理语音消息
            } else if ("video".equals(msgType)) { // 视频消息
                // todo 处理视频消息
            } else if ("shortvideo".equals(msgType)) { // 小视频消息
                // todo 处理小视频消息
            } else if ("location".equals(msgType)) { // 地理位置消息
                // todo 处理地理位置消息
            } else if ("link".equals(msgType)) { // 链接消息
                // todo 处理链接消息
            }
            // 事件推送
            else if ("event".equals(msgType)) { // 事件消息
                // 区分事件推送
                String event = decryptMap.get("Event");
                log.info("区分事件推送:" + event);
                if ("subscribe".equals(event)) {
                    // 关注事件 或 未关注扫描二维码事件
                    sendText(decryptMap, "感谢您的关注，如需获取电子照，请回复电子照~");

//                    os.write(mapToXml.getBytes());
                    return mapToXml;
                } else if ("unsubscribe".equals(event)) {
                    // 取消订阅事件
                    // todo 处理取消订阅事件
                } else if ("SCAN".equals(event)) { // 已关注扫描二维码事件
//                    String mapToXml = getReturnMessage1(decryptMap,eventKey);
//                    log.info("发送数据:"+mapToXml);
//                    os.write(mapToXml.getBytes());

                    sendText(decryptMap, "感谢您的关注，如需获取电子照，请回复电子照~");
                    return mapToXml;
                } else if ("LOCATION".equals(event)) { // 上报地理位置事件
                    // todo 处理上报地理位置事件
                } else if ("CLICK".equals(event)) { // 点击菜单拉取消息时的事件推送事件
                    // todo 处理点击菜单拉取消息时的事件推送事件
                } else if ("VIEW".equals(event)) { // 点击菜单跳转链接时的事件推送
                    // todo 处理点击菜单跳转链接时的事件推送
                }
            }
        } catch (Exception e) {
            log.info("处理微信公众号请求信息，失败", e);
        } finally {
            if (null != is) {
                is.close();
            }
            if (null != isr) {
                isr.close();
            }
            if (null != br) {
                br.close();
            }
        }
//        os.write("".getBytes());
        return "";

    }

    private void sendText(Map<String, String> decryptMap, String content) {
        // 发文字
        CustomerServiceText text = new CustomerServiceText();
        text.setTouser(decryptMap.get("FromUserName"));
        text.setMsgtype("text");
        Map<String, String> text1 = new HashMap<>();
        text1.put("content", content);
        text.setText(text1);
        String json = JSONObject.toJSONString(text);
        log.info(json);
        String token = redisTemplate.opsForValue().get("wx_access_token");
        String customerUrl = "https://api.weixin.qq.com/cgi-bin/message/custom/send?access_token=" + token;
        // 发POST请求
        HttpUtils.sendPost(customerUrl, json);
    }

    private void sendImage(Map<String, String> decryptMap, String content) {
        // 发图片
        CustomerServiceImage text = new CustomerServiceImage();
        text.setTouser(decryptMap.get("FromUserName"));
        text.setMsgtype("image");
        Map<String, String> text1 = new HashMap<>();
        text1.put("media_id", content);
        text.setImage(text1);
        String json = JSONObject.toJSONString(text);
        log.info(json);
        String token = redisTemplate.opsForValue().get("wx_access_token");
        String customerUrl = "https://api.weixin.qq.com/cgi-bin/message/custom/send?access_token=" + token;
        // 发POST请求
        HttpUtils.sendPost(customerUrl, json);
    }

    /**
     * 上传文件方法
     */
    public String connectHttpsByPost(String unionId) throws Exception {

        UserPhoto userPhoto = new UserPhoto();
        userPhoto.setOpenId(unionId);

        List<UserPhoto> userPhotos = userPhotoService.selectUserPhotoList(userPhoto);
        if (userPhotos.size() == 0) {
            return "无照片";
        }

        for (UserPhoto userPhoto1 : userPhotos) {
            if (userPhoto1.getUrl() != null && !userPhoto1.getUrl().equals(""))
                return saveImage(userPhoto1.getUrl());
        }

        return "无照片";


    }

    private String saveImage(String imageUrl) throws IOException {
        File file = downloadAndSaveImage(imageUrl);
        String path = "https://api.weixin.qq.com/cgi-bin/material/add_material?access_token=" + redisTemplate.opsForValue().get("wx_access_token") + "&type=image";

        URL urlObj = new URL(path);
        //连接
        HttpURLConnection con = (HttpURLConnection) urlObj.openConnection();
        String result = null;
        con.setDoInput(true);
        con.setDoOutput(true);
        con.setUseCaches(false); // post方式不能使用缓存
        // 设置请求头信息
        con.setRequestProperty("Connection", "Keep-Alive");
        con.setRequestProperty("Charset", "UTF-8");
        // 设置边界
        String BOUNDARY = "----------" + System.currentTimeMillis();
        con.setRequestProperty("Content-Type",
                "multipart/form-data; boundary="
                        + BOUNDARY);
        // 请求正文信息
        // 第一部分：
        StringBuilder sb = new StringBuilder();
        sb.append("--"); // 必须多两道线
        sb.append(BOUNDARY);
        sb.append("\r\n");
        sb.append("Content-Disposition: form-data;name=\"media\";filelength=\"" + file.length() + "\";filename=\""
                + file.getName() + "\"\r\n");
        sb.append("Content-Type:application/octet-stream\r\n\r\n");
        byte[] head = sb.toString().getBytes("utf-8");
        // 获得输出流
        OutputStream out = new DataOutputStream(con.getOutputStream());
        // 输出表头
        out.write(head);
        // 文件正文部分
        // 把文件已流文件的方式 推入到url中
        DataInputStream in = new DataInputStream(new FileInputStream(file));
        int bytes = 0;
        byte[] bufferOut = new byte[1024];
        while ((bytes = in.read(bufferOut)) != -1) {
            out.write(bufferOut, 0, bytes);
        }
        in.close();
        // 结尾部分
        byte[] foot = ("\r\n--" + BOUNDARY + "--\r\n").getBytes("utf-8");// 定义最后数据分隔线
        out.write(foot);
        out.flush();
        out.close();
        StringBuffer buffer = new StringBuffer();
        BufferedReader reader = null;
        try {
            // 定义BufferedReader输入流来读取URL的响应
            reader = new BufferedReader(new InputStreamReader(con.getInputStream()));
            String line = null;
            while ((line = reader.readLine()) != null) {
                buffer.append(line);
            }
            if (result == null) {
                result = buffer.toString();
            }
        } catch (IOException e) {
            System.out.println("发送POST请求出现异常！" + e);
            e.printStackTrace();
        } finally {
            if (reader != null) {
                reader.close();
            }
        }

        log.info("上传素材返回结果：" + result);
        JSONObject jsonObject = JSONObject.parseObject(result);

        return jsonObject.getString("media_id");
    }


    public File downloadAndSaveImage(String imageUrl) {
//        String saveFilePath = "path/to/save/image.jpg"; // 替换为保存图像的文件路径
        String fileName = UUID.randomUUID() + ".jpg";
        String parent = System.getProperty("user.dir") + "/file/" + fileName;
        ;
        File imageFile = null;
        try {
            imageFile = imageDownloaderService.downloadImageToFile(imageUrl, parent);
            System.out.println("Image downloaded and saved as: " + imageFile.getAbsolutePath());
        } catch (IOException e) {
            e.printStackTrace();
        }
        return imageFile;

    }


    // EncodingAESKey: 4QBlKsGIrOmdubaVdEeNupn41UnXy5C4WzAeLGfDKBX
    // Token: fotoboxserve


    public void wxCallBack(HttpServletRequest request, HttpServletResponse response) {


        Map<String, String> encryptMap = xmlToMap(request);
        log.info("接受消息：" + encryptMap.toString());
        // 得到公众号传来的加密信息并解密,得到的是明文xml数据
//        String decryptXml = WXPublicUtils.decrypt(encryptMap.get("Encrypt"));
        // 将xml数据转换为map
            /*log.info(decryptXml.toString());
            Map<String, String> decryptMap = VatifyToken.xmlToMap(decryptXml.toString());*/
        Map<String, String> decryptMap = encryptMap;
        // 区分消息类型


    }













//     appid: wx8c60a303bfee25df
//    secret: f0efa66554cdcf9cad57fbdf66fada23

//    /**
//     * @param
//     * @Description: 获取token
//     */
//    public static String getAccessToken() {
//        String accessToken = null;
////        if (!org.apache.commons.lang3.StringUtils.isNotBlank(accessToken)) {
//        //获取微信相关的配置
//        String appId = "wx8c60a303bfee25df";
//        String secret = "f0efa66554cdcf9cad57fbdf66fada23";
//        //缓存无，重新获取token
//        String url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=" + appId + "&secret=" + secret;
//        JSONObject json = WxShareUtil.doGet(url);
//        if (json != null) {
//            System.out.println(json);
//            accessToken = json.getString("access_token");
//            int time = Integer.parseInt(json.getString("expires_in"));
////            redisTemplate.opsForValue().set(RedisKeyConstant.ACCESS_TOKEN, accessToken, 60, TimeUnit.MINUTES);
//        }
////        }
//        if (StringUtils.isEmpty(accessToken)) {
//            return getAccessToken();
//        }
//        log.info("accessToken:{}", accessToken);
//        return accessToken.trim();
//    }


}
