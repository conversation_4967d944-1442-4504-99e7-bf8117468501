package com.ruoyi.order.mapper;

import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.yulichang.base.MPJBaseMapper;
import com.ruoyi.order.domain.OrderCamera;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 订单管理Mapper接口
 *
 * <AUTHOR>
 * @date 2023-07-13
 */
@Mapper
public interface OrderCameraMapper extends MPJBaseMapper<OrderCamera> {
    /**
     * 查询订单管理
     *
     * @param orderId 订单管理主键
     * @return 订单管理
     */
    public OrderCamera selectOrderCameraByOrderId(Long orderId);

    /**
     * 查询订单管理列表
     *
     * @param orderCamera 订单管理
     * @return 订单管理集合
     */
    public List<OrderCamera> selectOrderCameraList(OrderCamera orderCamera);

    /**
     * 新增订单管理
     *
     * @param orderCamera 订单管理
     * @return 结果
     */
    public int insertOrderCamera(OrderCamera orderCamera);

    /**
     * 修改订单管理
     *
     * @param orderCamera 订单管理
     * @return 结果
     */
    public int updateOrderCamera(OrderCamera orderCamera);

    /**
     * 删除订单管理
     *
     * @param orderId 订单管理主键
     * @return 结果
     */
    public int deleteOrderCameraByOrderId(String orderId);

    /**
     * 批量删除订单管理
     *
     * @param orderIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOrderCameraByOrderIds(String[] orderIds);


    @Select("SELECT SUM(account) AS totalAccount FROM order_camera WHERE user_id = #{userId} AND (order_status = 1 or order_status = 4)")
    Long countAllIncome(@Param("userId") Long userId);

    Long getHistoryTotalIncome(@Param("userId") Long userId, @Param("tables") List<String> tableNames);

    @Select("SELECT SUM(account) AS totalAccount FROM order_camera WHERE user_id = #{userId} AND (order_status = 1 or order_status = 4) AND create_time >= #{startTime} AND create_time < #{endTime}")
    Long countMonthIncome(@Param("userId") Long userId, @Param("startTime") String startTime, @Param("endTime") String endTime);
}
