package com.ruoyi.web.controller.taiwan;


import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
@Slf4j
public class AwsS3ServiceImpl implements AwsS3Service {

    @Value("${aws.bucketName}")
    private String bucketName;

    @Value("${aws.endpoint}")
    private String endpoint;

    @Resource
    private AmazonS3 amazonS3;

    @Override
    public String uploadFile(MultipartFile multipartFile) throws Exception {
        if (multipartFile.isEmpty()) {
            throw new RuntimeException("File not exist ！");
        }

        ObjectMetadata objectMetadata = new ObjectMetadata();
        objectMetadata.setContentType(multipartFile.getContentType());
        objectMetadata.setContentLength(multipartFile.getSize());
        objectMetadata.setHeader("Vary", "Origin");
        objectMetadata.setHeader("Access-Control-Allow-Origin", "*");
        objectMetadata.setHeader("Access-Control-Allow-Credentials", "true");

        String originalFilename = multipartFile.getOriginalFilename();;
        String ext = originalFilename.substring(originalFilename.lastIndexOf(".")).toLowerCase();
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("YYYYMMdd");
        String dateString = dateFormat.format(date);
        String key = dateString + "/" + UUID.randomUUID().toString() + ext;

        PutObjectRequest request = new PutObjectRequest(bucketName, key, multipartFile.getInputStream(), objectMetadata);
        request.setCannedAcl(CannedAccessControlList.PublicRead);

        PutObjectResult putObjectResult = amazonS3.putObject(request);
        if (putObjectResult != null) {
            String path = endpoint + "/" + key;
            return path;
        }

        return null;
    }

    @Override
    public void deleteFile(String fileUrl) throws Exception {
        String fileBucket = getFileBucket(fileUrl);
        String fileKey = getFileKey(fileUrl);

        DeleteObjectRequest request = new DeleteObjectRequest(fileBucket, fileKey);
        amazonS3.deleteObject(request);
    }

    public String getFileBucket(String fileUrl) throws Exception {
        String fileBucket = "";

        if (fileUrl.startsWith("https://")) {
            fileUrl = fileUrl.substring(8);
            String baseUrl = fileUrl.substring(0, fileUrl.indexOf("/"));
            fileBucket = baseUrl.substring(0, fileUrl.indexOf("."));
        }

        return fileBucket;
    }

    public String getFileKey(String fileUrl) throws Exception {
        String fileKey = "";

        if (fileUrl.startsWith("https://")) {
            fileUrl = fileUrl.substring(8);
            fileKey = fileUrl.substring(fileUrl.indexOf("/"));
        }
        return fileKey;
    }
}
