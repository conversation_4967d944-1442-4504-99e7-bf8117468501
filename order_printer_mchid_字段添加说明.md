# OrderPrinter 表添加 mchid 字段说明

## 问题背景

在 `CPrinterPayController` 中发现 `orderPrinter.getMchid()` 可能返回 `null`，因为 `OrderPrinter` 表缺少 `mchid` 字段的数据。

## 解决方案

### 1. 数据库字段添加

执行 SQL 脚本为 `order_printer` 表添加 `mchid` 字段。

### 2. 提供的 SQL 脚本

#### **生产环境推荐脚本**：`add_mchid_to_order_printer_simple.sql`
- 简洁安全，只包含必要操作
- 适合生产环境执行

#### **完整功能脚本**：`add_mchid_to_order_printer.sql`
- 包含多种数据迁移策略
- 适合开发环境测试

## 执行步骤

### 第一步：备份数据库
```sql
-- 备份 order_printer 表
CREATE TABLE order_printer_backup_20250130 AS SELECT * FROM order_printer;
```

### 第二步：执行字段添加脚本
```bash
# 生产环境推荐
mysql -u username -p database_name < sql/add_mchid_to_order_printer_simple.sql

# 或者开发环境完整版
mysql -u username -p database_name < sql/add_mchid_to_order_printer.sql
```

### 第三步：验证结果
```sql
-- 检查字段是否添加成功
DESCRIBE order_printer;

-- 检查数据迁移情况
SELECT 
    COUNT(*) as total_orders,
    COUNT(mchid) as orders_with_mchid,
    COUNT(*) - COUNT(mchid) as orders_without_mchid
FROM order_printer;
```

## 数据迁移策略

### 策略1：从用户商户号获取（推荐）
```sql
UPDATE order_printer op
INNER JOIN sys_user su ON op.user_id = su.user_id
SET op.mchid = su.merchant_id
WHERE op.mchid IS NULL 
  AND su.merchant_id IS NOT NULL 
  AND su.merchant_id != '';
```

### 策略2：从设备分账信息获取
```sql
UPDATE order_printer op
INNER JOIN device_printer dp ON op.device_id = dp.device_id
SET op.mchid = SUBSTRING_INDEX(dp.sub_account, ':', 1)
WHERE op.mchid IS NULL 
  AND dp.sub_account IS NOT NULL 
  AND dp.sub_account LIKE '%:%';
```

### 策略3：根据部门设置默认值
```sql
UPDATE order_printer op
INNER JOIN sys_user su ON op.user_id = su.user_id
INNER JOIN sys_dept sd ON su.dept_id = sd.dept_id
SET op.mchid = CASE 
    WHEN sd.dept_id = 201 OR sd.ancestors LIKE '%201%' THEN '*************'
    ELSE '**********'
END
WHERE op.mchid IS NULL;
```

## 代码层面的修改

### 1. 实体类已就绪
`OrderPrinter.java` 中已包含 `mchid` 字段：
```java
/** 商户id */
private String mchid;
```

### 2. Mapper 配置已就绪
`OrderPrinterMapper.xml` 中已包含 `mchid` 字段的映射。

### 3. 需要修改的地方

#### 在订单创建时设置 mchid
```java
// OrderPrinterServiceImpl.java - createOrder 方法
// 需要添加商户号设置逻辑
if (userId != null) {
    SysUser sysUser = userService.selectUserById(userId);
    if (sysUser != null && StringUtils.isNotEmpty(sysUser.getMerchantId())) {
        orderPrinter.setMchid(sysUser.getMerchantId());
    }
}
```

## 验证测试

### 1. 数据库验证
```sql
-- 查看字段信息
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'order_printer' AND COLUMN_NAME = 'mchid';

-- 查看数据分布
SELECT mchid, COUNT(*) FROM order_printer GROUP BY mchid;
```

### 2. 功能验证
1. 创建新订单，检查 `mchid` 是否正确设置
2. 支付流程测试，确认 `orderPrinter.getMchid()` 不为空
3. 分账逻辑测试，确认商户号正确传递

## 注意事项

### 1. 数据一致性
- 确保所有订单都有对应的商户号
- 商户号必须在支付系统中有效

### 2. 业务逻辑
- 新订单创建时自动设置商户号
- 支付时验证商户号的有效性

### 3. 回滚方案
如果出现问题，可以通过备份表恢复：
```sql
-- 恢复数据（如果需要）
DROP TABLE order_printer;
RENAME TABLE order_printer_backup_20250130 TO order_printer;
```

## 预期效果

执行完成后：
1. ✅ `order_printer` 表包含 `mchid` 字段
2. ✅ 现有订单根据用户商户号设置 `mchid`
3. ✅ `CPrinterPayController` 中 `orderPrinter.getMchid()` 不再返回 `null`
4. ✅ 支付流程正常工作
5. ✅ 分账逻辑正确执行

## 后续优化建议

1. **订单创建优化**：在创建订单时自动设置商户号
2. **数据验证**：添加商户号有效性验证
3. **监控告警**：监控没有商户号的订单
4. **业务规则**：制定商户号分配的业务规则
