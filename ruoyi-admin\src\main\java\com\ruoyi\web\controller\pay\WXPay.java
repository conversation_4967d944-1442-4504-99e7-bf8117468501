//package com.ruoyi.web.controller.pay;
//
//
//import com.ruoyi.common.annotation.Log;
//import com.ruoyi.common.core.domain.entity.SysUser;
//import com.ruoyi.common.enums.BusinessType;
//import com.ruoyi.common.utils.DateUtils;
//import com.ruoyi.device.domain.DeviceCamera;
//import com.ruoyi.device.service.IDeviceCameraService;
//import com.ruoyi.order.domain.OrderCamera;
//import com.ruoyi.order.dto.Result;
//import com.ruoyi.order.service.IOrderCameraService;
//import com.ruoyi.system.service.ISysUserService;
//import com.wechat.pay.java.core.Config;
//import com.wechat.pay.java.core.RSAAutoCertificateConfig;
//import com.wechat.pay.java.service.payments.jsapi.JsapiService;
//import com.wechat.pay.java.service.payments.jsapi.model.Amount;
//import com.wechat.pay.java.service.payments.jsapi.model.PrepayRequest;
//import com.wechat.pay.java.service.payments.jsapi.model.PrepayResponse;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Controller;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestParam;
//
//import java.util.Random;
//
//import static com.ruoyi.utils.OrderConstant.*;
//
//@Slf4j
//@Controller
//@RequestMapping("WXPay")
//public class WXPay {
//    @Autowired
//    private IDeviceCameraService deviceCameraService;
//
//    @Autowired
//    private IOrderCameraService orderCameraService;
//    @Autowired
//    private ISysUserService sysUserService;
//
//    private static Config config = null;
//
//    static {
//        // 使用自动更新平台证书的RSA配置
//        // 一个商户号只能初始化一个配置，否则会因为重复的下载任务报错
//        if (config == null) {
//            config = new RSAAutoCertificateConfig.Builder()
//                    .merchantId(MERCHANT_ID)
//                    .privateKey(PRIVATE_KEY)
//                    .merchantSerialNumber(MERCHANT_SERIAL_NUMBER)
//                    .apiV3Key(API_V3_KEY)
//                    .build();
//        }
//    }
//
//    /**
//     * 订单预创建微信
//     */
//    @Log(title = "订单预创建", businessType = BusinessType.OTHER)
//    @GetMapping("/createJSPay")
//    public Result getPreOrderId(@RequestParam String deviceId,
//                                @RequestParam Integer pay,
//                                @RequestParam Integer type,
//                                @RequestParam Integer productQuantity) {
//        if (deviceId == null) {
//            return Result.fail(500, "设备id不能为空", String.valueOf(System.currentTimeMillis()));
//        }
//        DeviceCamera camera = deviceCameraService.selectDeviceCameraByDeviceId(deviceId);
//        if (camera == null) {
//            return Result.fail(500, "设备id不存在", String.valueOf(System.currentTimeMillis()));
//        }
//        if (camera.getDeviceStatus() == 0) {
//            return Result.fail(500, "设备已停用", String.valueOf(System.currentTimeMillis()));
//        }
//        Long userId = camera.getUserId();
//        SysUser sysUser = sysUserService.selectUserById(userId);
//        String merchantId = sysUser.getMerchantId();
//        String appId = sysUser.getAppId();
//
//        JsapiService jsapiService = new JsapiService.Builder().config(config).build();
//        PrepayRequest prepayRequest = new PrepayRequest();
//        Amount amount = new Amount();
//        amount.setTotal(pay);
//        prepayRequest.setAmount(amount);
//        prepayRequest.setAppid(appId);
//        prepayRequest.setMchid(merchantId);
//
//        prepayRequest.setDescription("大头贴拍照");
//        prepayRequest.setNotifyUrl(NOTIFY_URL);
//
//        Random random = new Random();
//        int randomNumber = random.nextInt(10000); // 生成0到9999的随机数
//        long timeMillis = System.currentTimeMillis();
//        long orderId = timeMillis * 10000 + randomNumber;
//        prepayRequest.setOutTradeNo(String.valueOf(orderId));
//
//        PrepayResponse prepayResponse = jsapiService.prepay(prepayRequest);
//
//        String prepayId = prepayResponse.getPrepayId();
//
//        if (prepayId == null || prepayId.equals(""))
//            return new Result(false,200,"发起失败",String.valueOf(System.currentTimeMillis()) ,prepayId);
//
//        OrderCamera orderCamera = new OrderCamera();
//        orderCamera.setDeviceId(deviceId);
//
//        orderCamera.setDeviceName(camera.getDeviceName());
//
//        orderCamera.setProductDescription("大头贴拍照");
//        orderCamera.setCreateTime(DateUtils.getNowDate());
//
//        orderCamera.setMchid(merchantId);
//        orderCamera.setAppid(appId);
//        orderCamera.setProductQuantity(Long.valueOf(productQuantity));
//        orderCamera.setOrderPrice(Long.valueOf(pay));
//        orderCamera.setPhotoType(type);
//        boolean save = orderCameraService.save(orderCamera);
//        if (!save)
//            return new Result(false,200,"订单创建失败",String.valueOf(System.currentTimeMillis()) ,prepayId);
//        return new Result(true,200,"发起成功",String.valueOf(System.currentTimeMillis()),prepayId);
//    }
//}
