package com.ruoyi.common.utils;

import com.ruoyi.common.enums.WeiXinBusinessExceptionCode;
import com.ruoyi.common.exception.WeiXinBusinessException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Map;
import java.util.Optional;

/**
 * 微信请求工具类
 */
@Component
public class WeixinRequestUtil {

    private static final Logger log = LoggerFactory.getLogger(WeixinRequestUtil.class);

    private final RestTemplate weixinRestTemplate;

    public WeixinRequestUtil(@Qualifier("weixinRestTemplate") RestTemplate weixinRestTemplate) {
        this.weixinRestTemplate = weixinRestTemplate;
    }

    /**
     * 发送微信 GET 请求
     *
     * @param url          微信接口地址
     * @param queryParams  请求参数
     * @param responseType 响应类型
     * @param <T>          响应类型
     * @return 响应结果
     * @throws WeiXinBusinessException 业务异常
     */
    public <T> T sendWxGetRequest(String url, Map<String, String> queryParams, Class<T> responseType) throws WeiXinBusinessException {
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);
        if (queryParams != null) {
            queryParams.forEach(builder::queryParam);
        }
        String requestUrl = builder.build().toUriString();

        log.info("请求微信 GET 接口，URL：{}", requestUrl);

        try {
            ResponseEntity<T> response = weixinRestTemplate.getForEntity(requestUrl, responseType);

            return handleResponse(response);
        } catch (HttpClientErrorException e) {
            log.error("调用微信接口客户端错误：", e);
            throw new WeiXinBusinessException(WeiXinBusinessExceptionCode.EC_60101, "调用微信接口客户端错误");
        } catch (HttpServerErrorException e) {
            log.error("调用微信接口服务器错误：", e);
            throw new WeiXinBusinessException(WeiXinBusinessExceptionCode.EC_60101, "调用微信接口服务器错误");
        } catch (ResourceAccessException e) {
            log.error("调用微信接口网络异常：", e);
            throw new WeiXinBusinessException(WeiXinBusinessExceptionCode.EC_60101, "调用微信接口网络异常");
        } catch (Exception e) {
            log.error("调用微信接口发生未知异常：", e);
            throw new WeiXinBusinessException(WeiXinBusinessExceptionCode.EC_60101, "调用微信接口发生未知异常");
        }
    }

    /**
     * 发送微信 POST 请求
     *
     * @param url          微信接口地址
     * @param requestBody  请求体
     * @param responseType 响应类型
     * @param <T>          响应类型
     * @return 响应结果
     * @throws WeiXinBusinessException 业务异常
     */
    public <T> T sendWxPostRequest(String url, Map<String, String> queryParams, Object requestBody, Class<T> responseType) throws WeiXinBusinessException {
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);
        if (queryParams != null) {
            queryParams.forEach(builder::queryParam);
        }
        String requestUrl = builder.build().toUriString();

        log.info("请求微信 POST 接口，URL：{}", requestUrl);
        log.debug("请求体：{}", requestBody);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> httpEntity = new HttpEntity<>(requestBody, headers);

        try {
            ResponseEntity<T> response = weixinRestTemplate.postForEntity(requestUrl, httpEntity, responseType);

            return handleResponse(response);
        } catch (HttpClientErrorException e) {
            log.error("调用微信接口客户端错误：", e);
            throw new WeiXinBusinessException(WeiXinBusinessExceptionCode.EC_60101, "调用微信接口客户端错误");
        } catch (HttpServerErrorException e) {
            log.error("调用微信接口服务器错误：", e);
            throw new WeiXinBusinessException(WeiXinBusinessExceptionCode.EC_60101, "调用微信接口服务器错误");
        } catch (ResourceAccessException e) {
            log.error("调用微信接口网络异常：", e);
            throw new WeiXinBusinessException(WeiXinBusinessExceptionCode.EC_60101, "调用微信接口网络异常");
        } catch (Exception e) {
            log.error("调用微信接口发生未知异常：", e);
            throw new WeiXinBusinessException(WeiXinBusinessExceptionCode.EC_60101, "调用微信接口发生未知异常");
        }
    }

    /**
     * 处理微信接口响应
     *
     * @param response 响应
     * @param <T>      响应类型
     * @return 响应结果
     * @throws WeiXinBusinessException 业务异常
     */
    private <T> T handleResponse(ResponseEntity<T> response) throws WeiXinBusinessException {
        log.debug("微信接口响应：{}", response);

        if (response.getStatusCode() != HttpStatus.OK) {
            log.error("调用微信接口失败，HTTP状态码：{}", response.getStatusCode());
            throw new WeiXinBusinessException(WeiXinBusinessExceptionCode.EC_60101, "调用微信接口失败，HTTP状态码：" + response.getStatusCode());
        }

        T body = response.getBody();
        if (body == null) {
            log.error("调用微信接口返回空");
            throw new WeiXinBusinessException(WeiXinBusinessExceptionCode.EC_60101, "调用微信接口返回空");
        }

        if (body instanceof Map) {
            Map<String, Object> mapBody = (Map<String, Object>) body;
            Optional<Integer> errcode = Optional.ofNullable((Integer) mapBody.get("errcode"));
            if (errcode.isPresent() && errcode.get() != 0) {
                String errmsg = (String) mapBody.getOrDefault("errmsg", "未知错误");
                log.error("调用微信接口失败，错误码：{}，错误信息：{}", errcode.get(), errmsg);
                if (errcode.get() == 43004) { // 用户未关注公众号
                    return body;
                }
                throw new WeiXinBusinessException(WeiXinBusinessExceptionCode.EC_60101, errmsg);
            }
        }

        return body;
    }
}