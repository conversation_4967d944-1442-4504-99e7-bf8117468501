-- 为 order_printer 表添加 mchid 字段
-- 执行时间：2025-01-30

-- 1. 添加 mchid 字段
ALTER TABLE order_printer 
ADD COLUMN mchid VARCHAR(50) DEFAULT NULL COMMENT '商户号';

-- 2. 添加字段注释
ALTER TABLE order_printer 
MODIFY COLUMN mchid VARCHAR(50) DEFAULT NULL COMMENT '商户号';

-- 3. 为现有订单设置商户号（从设备负责人获取）
UPDATE order_printer op
INNER JOIN sys_user su ON op.user_id = su.user_id
SET op.mchid = su.merchant_id
WHERE op.mchid IS NULL 
  AND su.merchant_id IS NOT NULL 
  AND su.merchant_id != '';

-- 4. 为没有设置商户号的用户设置默认商户号（可选）
-- 根据用户部门设置默认商户号
-- 注意：请根据实际的商户号替换下面的值
UPDATE order_printer op
INNER JOIN sys_user su ON op.user_id = su.user_id
INNER JOIN sys_dept sd ON su.dept_id = sd.dept_id
SET op.mchid = CASE
    WHEN sd.dept_id = 201 OR sd.ancestors LIKE '%,201,%' OR sd.ancestors LIKE '201,%' OR sd.ancestors LIKE '%,201' THEN '*************'  -- 潘朵拉商户号（乐高定制）
    ELSE '**********'  -- 云创默认商户号（请替换为实际值）
END
WHERE op.mchid IS NULL;

-- 5. 为设备分账信息中的订单设置商户号（从设备的分账信息获取主商户号）
UPDATE order_printer op
INNER JOIN device_printer dp ON op.device_id = dp.device_id
SET op.mchid = SUBSTRING_INDEX(dp.sub_account, ':', 1)
WHERE op.mchid IS NULL
  AND dp.sub_account IS NOT NULL
  AND dp.sub_account != ''
  AND dp.sub_account LIKE '%:%';

-- 6. 为剩余订单设置通用默认商户号
UPDATE order_printer
SET mchid = '**********'  -- 请替换为实际的默认商户号
WHERE mchid IS NULL;

-- 7. 验证字段是否添加成功
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'order_printer' 
    AND COLUMN_NAME = 'mchid';

-- 8. 检查数据更新情况
SELECT 
    COUNT(*) as total_orders,
    COUNT(mchid) as orders_with_mchid,
    COUNT(*) - COUNT(mchid) as orders_without_mchid
FROM order_printer;

-- 9. 查看商户号分布情况
SELECT 
    mchid,
    COUNT(*) as order_count
FROM order_printer 
WHERE mchid IS NOT NULL
GROUP BY mchid
ORDER BY order_count DESC;

-- 10. 查看没有商户号的订单（如果有的话）
SELECT 
    order_id,
    device_id,
    user_id,
    create_time
FROM order_printer 
WHERE mchid IS NULL
LIMIT 10;
