package com.yunchuang.wxapp.service.client.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yunchuang.wxapp.mapper.CarouselImageMapper;
import com.yunchuang.wxapp.model.domain.CarouselImage;
import com.yunchuang.wxapp.model.enums.CarouselImageStatus;
import com.yunchuang.wxapp.service.client.ICCarouselImageService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 轮播图Service业务层处理
 */
@Service
@Transactional
public class CCarouselImageServiceImpl extends ServiceImpl<CarouselImageMapper, CarouselImage> implements ICCarouselImageService {

    @Resource
    private CarouselImageMapper carouselImageMapper;


    /**
     * 查询已启用的轮播图列表
     *
     * @return 轮播图集合
     */
    @Override
    public List<String> getEnabledList() {
        // 使用 Wrappers.lambdaQuery() 构建 LambdaQueryWrapper，更加简洁
        List<Object> CarouselImageList = carouselImageMapper.selectObjs(
                Wrappers.<CarouselImage>lambdaQuery()
                        .eq(CarouselImage::getBeEnabled, CarouselImageStatus.ENABLE.getValue())
                        .orderByAsc(CarouselImage::getSort)
                        .select(CarouselImage::getImageUrl)
        );

        // 安全处理空列表的情况，返回空集合而不是 null
        return CarouselImageList == null ? Collections.emptyList() : CarouselImageList.stream().map(String::valueOf).collect(Collectors.toList());
    }
}
