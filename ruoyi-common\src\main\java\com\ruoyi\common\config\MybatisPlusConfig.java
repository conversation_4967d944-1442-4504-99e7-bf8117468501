package com.ruoyi.common.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.DynamicTableNameInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@EnableTransactionManagement
@Configuration
public class MybatisPlusConfig {
    // 定义动态表名
    public static final ThreadLocal<String> TABLE_NAME_HOLDER = new ThreadLocal<>();

    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        // 创建动态表名拦截器
        DynamicTableNameInnerInterceptor dynamicTableNameInterceptor = new DynamicTableNameInnerInterceptor();

        // 设置动态表名解析器
        dynamicTableNameInterceptor.setTableNameHandler((sql, tableName) -> {
            String dynamicTableName = TABLE_NAME_HOLDER.get();
            return dynamicTableName != null ? dynamicTableName : tableName; // 使用动态表名
        });
        interceptor.addInnerInterceptor(dynamicTableNameInterceptor);
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));

        return interceptor;
    }
}