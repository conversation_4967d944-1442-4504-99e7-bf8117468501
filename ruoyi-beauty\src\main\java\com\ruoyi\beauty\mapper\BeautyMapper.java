package com.ruoyi.beauty.mapper;

import java.util.List;
import com.ruoyi.beauty.domain.Beauty;

/**
 * 美颜Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-10-15
 */
public interface BeautyMapper 
{
    /**
     * 查询美颜
     * 
     * @param useeId 美颜主键
     * @return 美颜
     */
    public Beauty selectBeautyByUseeId(Long useeId);

    /**
     * 查询美颜列表
     * 
     * @param beauty 美颜
     * @return 美颜集合
     */
    public List<Beauty> selectBeautyList(Beauty beauty);

    /**
     * 新增美颜
     * 
     * @param beauty 美颜
     * @return 结果
     */
    public int insertBeauty(Beauty beauty);

    /**
     * 修改美颜
     * 
     * @param beauty 美颜
     * @return 结果
     */
    public int updateBeauty(Beauty beauty);

    /**
     * 删除美颜
     * 
     * @param useeId 美颜主键
     * @return 结果
     */
    public int deleteBeautyByUseeId(Long useeId);

    /**
     * 批量删除美颜
     * 
     * @param useeIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBeautyByUseeIds(Long[] useeIds);
}
