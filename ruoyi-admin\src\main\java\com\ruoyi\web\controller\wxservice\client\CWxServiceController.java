package com.ruoyi.web.controller.wxservice.client;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.utils.MyResultUtil;
import com.ruoyi.wxservice.service.impl.WxServiceCommonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 微信服务号 Controller
 */
@Slf4j
@RestController
@RequestMapping("/client/wxservice")
public class CWxServiceController extends BaseController {

    @Resource
    private WxServiceCommonService wxServiceCommonService;

    /**
     * 发送模板消息
     */
    @PostMapping("/sendT")
    public Map<String, Object> likeOrUnlike(@RequestParam String deviceId) {
        boolean isSuccess = wxServiceCommonService.sendWarningMessage(deviceId, 0L);
        return isSuccess ? MyResultUtil.success() : MyResultUtil.error();
    }

    /**
     * 微信服务器触发get请求用于检测签名
     */
    @GetMapping("/receiveWxMessage")
    @ResponseBody
    public String receiveWxMessage(HttpServletRequest request) {
        // 获取微信请求参数
        String signature = request.getParameter("signature");
        String timestamp = request.getParameter("timestamp");
        String nonce = request.getParameter("nonce");
        String echostr = request.getParameter("echostr");
        return wxServiceCommonService.checkWxServerSign(signature, timestamp, nonce, echostr);
    }

    /**
     * 接收微信消息
     */
    @PostMapping("/receiveWxMessage")
    @ResponseBody
    public String receiveWxMessage(@RequestBody String param) {
        log.info("--------------------接收微信推送消息-----------------------");
        return wxServiceCommonService.handleWxPushMessage(param);
    }
}
