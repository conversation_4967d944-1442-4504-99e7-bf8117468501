package com.ruoyi.common.utils;

import lombok.extern.slf4j.Slf4j;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.Unmarshaller;
import java.io.StringReader;

/**
 * Jaxb工具类
 */
@Slf4j
public class JaxbUtil {

    /**
     * xml装换成JavaBean
     *
     * @param xml xml字符串
     * @param c   JavaBean类
     * @return JavaBean对象
     */
    @SuppressWarnings("unchecked")
    public static <T> T converyToJavaBean(String xml, Class<T> c) {
        T t = null;
        try {
            JAXBContext context = JAXBContext.newInstance(c);
            Unmarshaller unmarshaller = context.createUnmarshaller();
            t = (T) unmarshaller.unmarshal(new StringReader(xml));
        } catch (Exception e) {
            log.error("xml转换成JavaBean异常", e);
        }
        return t;
    }

}

