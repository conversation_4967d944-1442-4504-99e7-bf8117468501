package com.ruoyi.resource.service;

import java.util.List;
import com.ruoyi.resource.domain.ModelPackage;

/**
 * 模板库Service接口
 * 
 * <AUTHOR>
 * @date 2024-12-10
 */
public interface IModelPackageService 
{
    /**
     * 查询模板库
     * 
     * @param id 模板库主键
     * @return 模板库
     */
    public ModelPackage selectModelPackageById(Long id);

    /**
     * 查询模板库列表
     * 
     * @param modelPackage 模板库
     * @return 模板库集合
     */
    public List<ModelPackage> selectModelPackageList(ModelPackage modelPackage);

    /**
     * 新增模板库
     * 
     * @param modelPackage 模板库
     * @return 结果
     */
    public int insertModelPackage(ModelPackage modelPackage);

    /**
     * 修改模板库
     * 
     * @param modelPackage 模板库
     * @return 结果
     */
    public int updateModelPackage(ModelPackage modelPackage);

    /**
     * 批量删除模板库
     * 
     * @param ids 需要删除的模板库主键集合
     * @return 结果
     */
    public int deleteModelPackageByIds(Long[] ids);

    /**
     * 删除模板库信息
     * 
     * @param id 模板库主键
     * @return 结果
     */
    public int deleteModelPackageById(Long id);
}
