# 微信原生支付集成说明

## 📋 概述

本项目已为您集成了微信原生支付功能（API v2版本），支持JSAPI支付（小程序/公众号）和Native支付（扫码支付）。

**您的商户号**: `1631162326`
**证书序列号**: `671F99B4529CDF45E97F5CB341E02838E1DEE092`

## 🔧 配置说明

### 1. 需要您提供的配置信息

请在 `ruoyi-wxapp/src/main/resources/application-wxapp.yaml` 文件中完善以下配置：

```yaml
# 微信支付配置（API v2）
wxpay:
  # 微信支付AppID（需要您提供）
  app-id: 您的微信支付AppID
  # 微信支付商户号（已配置）
  mch-id: 1631162326
  # 微信支付API密钥（API v2密钥，需要您提供）
  api-key: 您的微信支付API密钥
  # 微信支付证书序列号（已配置）
  cert-serial-no: 671F99B4529CDF45E97F5CB341E02838E1DEE092
  # 微信支付证书文件路径（p12格式，需要您提供）
  cert-path: classpath:cert/apiclient_cert.p12
  # 微信支付证书密码（通常是商户号）
  cert-password: 1631162326
  # 微信支付回调地址（需要您确认域名）
  notify-url: https://您的域名/client/wxapp/wxpay/notify
```

### 2. 需要您提供的文件

请将以下文件放置到 `ruoyi-admin/src/main/resources/cert/` 目录下：

- `apiclient_cert.p12` - 微信支付商户证书文件（p12格式）

### 3. 微信支付配置获取方式

1. **微信支付AppID**: 登录微信支付商户平台 → 产品中心 → 开发配置 → 支付配置
2. **API密钥**: 微信支付商户平台 → 账户中心 → API安全 → 设置API密钥（API v2密钥）
3. **证书文件**: 微信支付商户平台 → 账户中心 → API安全 → 下载证书（选择p12格式）
4. **证书序列号**: 已为您配置为 `671F99B4529CDF45E97F5CB341E02838E1DEE092`

## 🚀 功能特性

### 1. 支持的支付方式

- ✅ **JSAPI支付**: 适用于微信小程序、微信公众号内支付
- ✅ **Native支付**: 适用于PC端扫码支付
- ✅ **订单查询**: 查询支付订单状态
- ✅ **批量查询**: 批量查询多个订单状态
- ✅ **支付回调**: 处理微信支付成功回调
- ✅ **定时任务**: 每分钟自动查询未支付订单状态

### 2. 已实现的接口

| 接口路径 | 方法 | 功能 | 参数 |
|---------|------|------|------|
| `/client/wxapp/wxpay/jsapi` | POST | JSAPI支付 | totalAmount, openId, outTradeNo, body |
| `/client/wxapp/wxpay/native` | POST | Native支付 | totalAmount, outTradeNo, body |
| `/client/wxapp/wxpay/query/{outTradeNo}` | GET | 订单查询 | outTradeNo |
| `/client/wxapp/wxpay/notify` | POST | 支付回调 | 微信回调数据 |

## 📱 使用示例

### 1. JSAPI支付（小程序）

```javascript
// 前端调用示例
wx.request({
    url: '/client/wxapp/wxpay/jsapi',
    method: 'POST',
    header: {
        'Content-Type': 'application/x-www-form-urlencoded'
    },
    data: {
        totalAmount: 100,  // 支付金额（分）
        openId: 'user_openid',  // 用户OpenID
        outTradeNo: 'ORDER_' + Date.now(),  // 商户订单号
        body: '商品描述'  // 商品描述
    },
    success: function(res) {
        if (res.data.code === 200) {
            // 调用微信支付
            wx.requestPayment({
                ...res.data.data,
                success: function() {
                    console.log('支付成功');
                },
                fail: function(err) {
                    console.log('支付失败', err);
                }
            });
        }
    }
});
```

### 2. Native支付（扫码）

```javascript
// 前端调用示例
fetch('/client/wxapp/wxpay/native', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: new URLSearchParams({
        totalAmount: 100,  // 支付金额（分）
        outTradeNo: 'ORDER_' + Date.now(),  // 商户订单号
        body: '商品描述'  // 商品描述
    })
})
.then(response => response.json())
.then(data => {
    if (data.code === 200) {
        // 生成二维码显示给用户扫描
        const codeUrl = data.data.codeUrl;
        // 使用二维码库生成二维码图片
    }
});
```

### 3. 订单查询

```javascript
// 查询订单状态
fetch('/client/wxapp/wxpay/query/ORDER_123456')
.then(response => response.json())
.then(data => {
    if (data.code === 200) {
        console.log('订单状态:', data.data);
    }
});
```

## 🧪 测试页面

访问 `/wxpay-test.html` 可以使用测试页面进行功能测试。

## ⚠️ 注意事项

### 1. 安全配置

- 请妥善保管微信支付API密钥和证书文件
- 生产环境中请使用HTTPS协议
- 回调地址必须是外网可访问的HTTPS地址

### 2. 开发调试

- 开发环境可以使用微信支付沙箱进行测试
- 注意区分小程序AppID和支付AppID
- 确保回调地址配置正确

### 3. 业务集成

- 支付成功后的业务逻辑需要在 `handlePaymentSuccess` 方法中实现
- 建议添加订单状态管理和重复支付检查
- 可以根据业务需求扩展支付方式

## 🔨 待完善功能

由于缺少具体的微信支付配置信息，以下功能需要您提供配置后完善：

1. **RSA签名**: 需要商户私钥进行请求签名
2. **回调验签**: 需要微信支付平台证书验证回调签名
3. **数据解密**: 需要API密钥解密回调数据

## 📞 技术支持

如果您在配置过程中遇到问题，请提供：

1. 微信支付商户平台的配置截图
2. 具体的错误信息
3. 相关日志内容

我将协助您完成配置和调试。

## 📚 相关文档

- [微信支付官方文档](https://pay.weixin.qq.com/wiki/doc/apiv3/index.shtml)
- [微信支付API v3](https://pay.weixin.qq.com/wiki/doc/apiv3/apis/index.shtml)
- [微信小程序支付](https://developers.weixin.qq.com/miniprogram/dev/api/payment/wx.requestPayment.html)
