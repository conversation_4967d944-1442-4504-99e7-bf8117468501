package com.ruoyi.photo.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 素材管理对象 xb_resource
 * 
 * <AUTHOR>
 * @date 2024-05-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("xb_resource")
public class XbResource extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;


    /** 腾讯 活动id */
    @Excel(name = "腾讯 活动id")
    private String projectId;

    /** 腾讯 素材id */
    @Excel(name = "腾讯 素材id")
    private String modelId;

    /** 素材图片地址 */
    @Excel(name = "素材图片地址")
    private String url;

    /** 文件夹位置 */
    @Excel(name = "文件夹位置")
    private String objectName;

    /** 素材标题 */
    @Excel(name = "素材标题")
    private String title;

    /** 素材介绍内容 */
    @Excel(name = "素材介绍内容")
    private String content;

    /** 价格 */
    @Excel(name = "价格")
    private Integer pay;

    /** 1男 2女 3孩提 */
    @Excel(name = "1男 2女 3孩提")
    private String people;

    /**  */
    @Excel(name = "风格")
    private String style;

    /** 0下线 1上线 */
    @Excel(name = "0下线 1上线")
    private Integer status;

    /** 父id */
    @Excel(name = "父id")
    private Integer parentId;


    /**
     * 1 高级版  否则0 普通版
     */
    private Integer type;

    /**
     * 拉脸强度
     */
    private Float warpRadio;

    /**
     * 人脸增强强度
     */
    private Float enhanceRadio;

    /**
     * 磨皮强度
     */
    private Float mpRadio;

    /**
     * 牙齿增强开关
     */
    private Float teethEnhanceRadio;


    /**
     * 融合模型参数
     */
    private Long swapModelType;



    @Excel(name = "风格标题")
    @TableField(exist = false)
    private String styleTitle;



    /** 代理id */
    private Long deptId;
}
