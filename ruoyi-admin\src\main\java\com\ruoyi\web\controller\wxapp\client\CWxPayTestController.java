package com.ruoyi.web.controller.wxapp.client;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.task.WxPayQueryTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 微信支付测试控制器
 * 用于测试和管理微信支付相关功能
 * 
 * <AUTHOR>
 * @date 2025-01-30
 */
@Slf4j
@RestController
@RequestMapping("/client/wxapp/wxpay/test")
public class CWxPayTestController extends BaseController {

    @Autowired
    private WxPayQueryTask wxPayQueryTask;

    /**
     * 手动触发支付状态查询任务
     * 用于测试定时任务功能
     */
    @PostMapping("/trigger-query-task")
    public AjaxResult triggerQueryTask() {
        log.info("=== 手动触发微信支付状态查询任务 ===");
        
        try {
            wxPayQueryTask.manualQuery();
            return AjaxResult.success("定时任务执行完成");
        } catch (Exception e) {
            log.error("手动触发定时任务失败", e);
            return AjaxResult.error("定时任务执行失败: " + e.getMessage());
        }
    }

    /**
     * 获取定时任务状态信息
     */
    @GetMapping("/task-status")
    public AjaxResult getTaskStatus() {
        try {
            // 返回定时任务相关信息
            return AjaxResult.success("定时任务状态", 
                "微信支付状态查询定时任务已启动，每分钟执行一次");
        } catch (Exception e) {
            log.error("获取定时任务状态失败", e);
            return AjaxResult.error("获取状态失败: " + e.getMessage());
        }
    }

    /**
     * 生成测试订单号
     */
    @PostMapping("/generate-order")
    public AjaxResult generateTestOrder() {
        try {
            String timestamp = String.valueOf(System.currentTimeMillis());
            String orderNo = "TEST_" + timestamp;
            
            return AjaxResult.success("测试订单号生成成功", orderNo);
        } catch (Exception e) {
            log.error("生成测试订单号失败", e);
            return AjaxResult.error("生成失败: " + e.getMessage());
        }
    }
}
