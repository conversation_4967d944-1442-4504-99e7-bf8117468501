package com.ruoyi.common.utils.oss;

import com.aliyun.oss.ClientException;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.common.auth.CredentialsProvider;
import com.aliyun.oss.common.auth.DefaultCredentialProvider;
import com.aliyun.oss.model.ObjectMetadata;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

import static com.ruoyi.common.constant.Constant.*;

@Slf4j
public class ALY_OSS {


    public static void deleteImage(String objectName) {
        System.out.println("=================deleteImage===============");
        // 创建OSSClient实例。

        CredentialsProvider credentialsProvider = new DefaultCredentialProvider(ACCESS_KEYId, ACCESS_KEY_SECRET);
        OSS ossClient = new OSSClientBuilder().build(ENDPOINT, credentialsProvider);


        try {
            // 删除文件。
            ossClient.deleteObject(BUCKET_NAME, objectName);
        } catch (Exception e) {
            log.error(e.getMessage());
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }

    public static String uploadImage(MultipartFile image, String imageName) {

        CredentialsProvider credentialsProvider = new DefaultCredentialProvider(ACCESS_KEYId, ACCESS_KEY_SECRET);
        OSS ossClient = new OSSClientBuilder().build(ENDPOINT, credentialsProvider);


        ObjectMetadata objectMetadata = new ObjectMetadata();
        objectMetadata.setContentType(getcontentType(imageName.substring(imageName.lastIndexOf("."))));

        // 填写Object完整路径，例如exampledir/exampleobject.txt。Object完整路径中不能包含Bucket名称。

        try {
            ossClient.putObject(BUCKET_NAME, imageName, new ByteArrayInputStream(image.getBytes()), objectMetadata);
        } catch (Exception e) {
            log.error(e.getMessage());
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        // 把上传的文件路径返回 （手动拼接）
        // 这里设置图片有效时间 我设置了1 day

        LocalDateTime localDateTime = LocalDateTime.now();
        LocalDateTime tomorrow = localDateTime.plusDays(1);
        Date expiration = Date.from(tomorrow.atZone(ZoneId.systemDefault()).toInstant());

        return ossClient.generatePresignedUrl(BUCKET_NAME, imageName, expiration).toString();
    }

    // 实现图片的预览功能
    private static String getcontentType(String FilenameExtension) {
        if (FilenameExtension.equalsIgnoreCase(".bmp")) {
            return "image/bmp";
        }
        if (FilenameExtension.equalsIgnoreCase(".mp4")) {
            return "video/mp4";
        }
        if (FilenameExtension.equalsIgnoreCase(".gif")) {
            return "image/gif";
        }
        if (FilenameExtension.equalsIgnoreCase(".jpeg") ||
                FilenameExtension.equalsIgnoreCase(".jpg") ||
                FilenameExtension.equalsIgnoreCase(".png")) {
            return "image/jpg";
        }
        if (FilenameExtension.equalsIgnoreCase(".html")) {
            return "text/html";
        }
        if (FilenameExtension.equalsIgnoreCase(".txt")) {
            return "text/plain";
        }
        if (FilenameExtension.equalsIgnoreCase(".vsd")) {
            return "application/vnd.visio";
        }
        if (FilenameExtension.equalsIgnoreCase(".pptx") ||
                FilenameExtension.equalsIgnoreCase(".ppt")) {
            return "application/vnd.ms-powerpoint";
        }
        if (FilenameExtension.equalsIgnoreCase(".docx") ||
                FilenameExtension.equalsIgnoreCase(".doc")) {
            return "application/msword";
        }
        if (FilenameExtension.equalsIgnoreCase(".xml")) {
            return "text/xml";
        }
        return "image/jpg";
    }
}
