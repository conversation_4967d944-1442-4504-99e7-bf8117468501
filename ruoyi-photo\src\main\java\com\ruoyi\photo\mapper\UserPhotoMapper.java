package com.ruoyi.photo.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.photo.domain.UserPhoto;
import org.apache.ibatis.annotations.Mapper;

/**
 * photoMapper接口
 * 
 * <AUTHOR>
 * @date 2023-09-05
 */
@Mapper
public interface UserPhotoMapper extends BaseMapper<UserPhoto>
{
    /**
     * 查询photo
     * 
     * @param id photo主键
     * @return photo
     */
    public UserPhoto selectUserPhotoById(Long id);

    /**
     * 查询photo列表
     * 
     * @param userPhoto photo
     * @return photo集合
     */
    public List<UserPhoto> selectUserPhotoList(UserPhoto userPhoto);

    /**
     * 新增photo
     * 
     * @param userPhoto photo
     * @return 结果
     */
    public int insertUserPhoto(UserPhoto userPhoto);

    /**
     * 修改photo
     * 
     * @param userPhoto photo
     * @return 结果
     */
    public int updateUserPhoto(UserPhoto userPhoto);

    /**
     * 删除photo
     * 
     * @param id photo主键
     * @return 结果
     */
    public int deleteUserPhotoById(Long id);

    /**
     * 批量删除photo
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteUserPhotoByIds(Long[] ids);

}
