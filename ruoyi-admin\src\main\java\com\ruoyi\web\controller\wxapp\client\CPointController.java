package com.ruoyi.web.controller.wxapp.client;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.utils.MyResultUtil;
import com.yunchuang.wxapp.service.client.ICPointService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 点位Controller
 */
@RestController
@RequestMapping("/client/wxapp/point")
public class CPointController extends BaseController {

    @Resource
    private ICPointService cPointService;

    /**
     * 查询点位列表
     */
    @GetMapping("/list")
    public Map<String, Object> list(@RequestParam(required = false, defaultValue = "1") Integer pageNum,
                                    @RequestParam(required = false, defaultValue = "20") Integer pageSize) {
        return MyResultUtil.success(cPointService.getPointList(pageNum, pageSize));
    }

    /**
     * 查询附近的点位列表
     */
    @GetMapping("/nearby_list")
    public Map<String, Object> nearbyList(@RequestParam Double longitude,
                                          @RequestParam Double latitude,
                                          @RequestParam Integer number) {
        return MyResultUtil.success(cPointService.getNearbyPointList(longitude, latitude, number));
    }

}
