package com.ruoyi.wxservice.model.dto.template;

import com.ruoyi.wxservice.model.dto.TemplateDataItem;

import java.util.ArrayList;
import java.util.List;

/**
 * 基础消息模板
 */
public class BaseTplDTO {

    /**
     * 模板数据项列表
     */
    private List<TemplateDataItem<?>> dataItems;

    public BaseTplDTO() {
        this.dataItems = new ArrayList<>();
    }

    /**
     * 通用构建方法
     *
     * @param items 数据项
     * @return BaseTplDTO
     */
    public static BaseTplDTO build(Object... items) {
        BaseTplDTO baseTplDTO = new BaseTplDTO();
        for (Object item : items) {
            if (item instanceof String) {
                baseTplDTO.dataItems.add(new TemplateDataItem<>((String) item));
            } else if (item instanceof Long) {
                baseTplDTO.dataItems.add(new TemplateDataItem<>((Long) item));
            } else if (item instanceof TemplateDataItem) {
                baseTplDTO.dataItems.add((TemplateDataItem<?>) item);
            }
        }
        return baseTplDTO;
    }
}
