package com.ruoyi.beauty.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.beauty.mapper.BeautyValueMapper;
import com.ruoyi.beauty.domain.BeautyValue;
import com.ruoyi.beauty.service.IBeautyValueService;

/**
 * 美颜强度Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-10-15
 */
@Service
public class BeautyValueServiceImpl implements IBeautyValueService 
{
    @Autowired
    private BeautyValueMapper beautyValueMapper;

    /**
     * 查询美颜强度
     * 
     * @param id 美颜强度主键
     * @return 美颜强度
     */
    @Override
    public BeautyValue selectBeautyValueById(Long id)
    {
        return beautyValueMapper.selectBeautyValueById(id);
    }

    /**
     * 查询美颜强度列表
     * 
     * @param beautyValue 美颜强度
     * @return 美颜强度
     */
    @Override
    public List<BeautyValue> selectBeautyValueList(BeautyValue beautyValue)
    {
        return beautyValueMapper.selectBeautyValueList(beautyValue);
    }

    /**
     * 新增美颜强度
     * 
     * @param beautyValue 美颜强度
     * @return 结果
     */
    @Override
    public int insertBeautyValue(BeautyValue beautyValue)
    {
        return beautyValueMapper.insertBeautyValue(beautyValue);
    }

    /**
     * 修改美颜强度
     * 
     * @param beautyValue 美颜强度
     * @return 结果
     */
    @Override
    public int updateBeautyValue(BeautyValue beautyValue)
    {
        return beautyValueMapper.updateBeautyValue(beautyValue);
    }

    /**
     * 批量删除美颜强度
     * 
     * @param ids 需要删除的美颜强度主键
     * @return 结果
     */
    @Override
    public int deleteBeautyValueByIds(Long[] ids)
    {
        return beautyValueMapper.deleteBeautyValueByIds(ids);
    }

    /**
     * 删除美颜强度信息
     * 
     * @param id 美颜强度主键
     * @return 结果
     */
    @Override
    public int deleteBeautyValueById(Long id)
    {
        return beautyValueMapper.deleteBeautyValueById(id);
    }
}
