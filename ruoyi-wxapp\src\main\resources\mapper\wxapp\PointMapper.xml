<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yunchuang.wxapp.mapper.PointMapper">

    <resultMap type="Point" id="PointResult">
        <result property="id" column="id"/>
        <result property="pointName" column="point_name"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="district" column="district"/>
        <result property="detailAddress" column="detail_address"/>
        <result property="longitude" column="longitude"/>
        <result property="latitude" column="latitude"/>
        <result property="openingHours" column="opening_hours"/>
        <result property="favoriteCount" column="favorite_count"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectPointVo">
        select id,
               point_name,
               province,
               city,
               district,
               detail_address,
               longitude,
               latitude,
               opening_hours,
               favorite_count,
               status,
               create_by,
               create_time,
               update_by,
               update_time,
               remark
        from wxapp_point
    </sql>

    <select id="selectPointList" parameterType="Point" resultMap="PointResult">
        <include refid="selectPointVo"/>
        <where>
            <if test="pointName != null  and pointName != ''">and point_name like concat('%', #{pointName}, '%')</if>
            <if test="province != null  and province != ''">and province like concat('%', #{province}, '%')</if>
            <if test="city != null  and city != ''">and city like concat('%', #{city}, '%')</if>
            <if test="district != null  and district != ''">and district like concat('%', #{district}, '%')</if>
            <if test="detailAddress != null  and detailAddress != ''">and detail_address like concat('%',
                #{detailAddress}, '%')
            </if>
            <if test="openingHours != null  and openingHours != ''">and opening_hours like concat('%', #{openingHours},
                '%')
            </if>
            <if test="status != null ">and status = #{status}</if>
        </where>
    </select>

    <select id="selectPointById" parameterType="Long" resultMap="PointResult">
        <include refid="selectPointVo"/>
        where id = #{id}
    </select>

    <insert id="insertPoint" parameterType="Point" useGeneratedKeys="true" keyProperty="id">
        insert into wxapp_point
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="pointName != null and pointName != ''">point_name,</if>
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="district != null">district,</if>
            <if test="detailAddress != null">detail_address,</if>
            <if test="longitude != null">longitude,</if>
            <if test="latitude != null">latitude,</if>
            <if test="openingHours != null and openingHours != ''">opening_hours,</if>
            <if test="favoriteCount != null">favorite_count,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            # 设置location字段
            location
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="pointName != null and pointName != ''">#{pointName},</if>
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="district != null">#{district},</if>
            <if test="detailAddress != null">#{detailAddress},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="openingHours != null and openingHours != ''">#{openingHours},</if>
            <if test="favoriteCount != null">#{favoriteCount},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            # 设置location字段
            point(#{longitude}, #{latitude})
        </trim>
    </insert>

    <update id="updatePoint" parameterType="Point">
        update wxapp_point
        <trim prefix="SET" suffixOverrides=",">
            <if test="pointName != null and pointName != ''">point_name = #{pointName},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="district != null">district = #{district},</if>
            <if test="detailAddress != null">detail_address = #{detailAddress},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="openingHours != null and openingHours != ''">opening_hours = #{openingHours},</if>
            <if test="favoriteCount != null">favorite_count = #{favoriteCount},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            # 设置location字段
            location = point(#{longitude}, #{latitude})
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePointById" parameterType="Long">
        delete
        from wxapp_point
        where id = #{id}
    </delete>

    <delete id="deletePointByIds" parameterType="String">
        delete from wxapp_point where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>