package com.yunchuang.wxapp.model.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 首页设置对象 wxapp_home_set
 *
 * <AUTHOR>
 * @date 2025-02-07
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("wxapp_home_set")
public class HomeSet extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 设置ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 类型
     */
    @Excel(name = "类型")
    private Integer type;

    /**
     * 图片名称
     */
    @Excel(name = "图片名称")
    private String name;

    /**
     * 图片地址
     */
    @Excel(name = "图片地址")
    private String imageUrl;

    /**
     * 排序
     */
    @Excel(name = "排序")
    private Long sort;

    /**
     * 跳转类型
     */
    @Excel(name = "跳转类型")
    private Integer jumpType;

    /**
     * 跳转值
     */
    @Excel(name = "跳转值")
    private String jumpValue;

    /**
     * 状态
     */
    @Excel(name = "状态")
    private Integer status;

}
