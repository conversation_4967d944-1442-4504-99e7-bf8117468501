package com.yunchuang.wxapp.service.client;


import com.baomidou.mybatisplus.extension.service.IService;
import com.yunchuang.wxapp.model.domain.Feedback;
import com.yunchuang.wxapp.model.domain.WxappLoginUser;
import com.yunchuang.wxapp.model.req.CFeedbackAddOneReq;

/**
 * 意见反馈 Service 接口
 */
public interface ICFeedbackService extends IService<Feedback> {

    /**
     * 添加意见反馈
     *
     * @param loginUser 当前登录用户
     * @param addOneReq 新增请求
     * @return 是否添加成功
     */
    boolean addFeedback(WxappLoginUser loginUser, CFeedbackAddOneReq addOneReq);
}
