package com.ruoyi.web.controller.wxservice.admin;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.utils.MyResultUtil;
import com.ruoyi.dto.wxservice.UnbindDeviceReqDTO;
import com.ruoyi.wxservice.service.impl.WxServiceCommonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 微信服务号 Controller
 */
@Slf4j
@RestController
@RequestMapping("/wxservice")
public class AWxServiceController extends BaseController {

    @Resource
    private WxServiceCommonService wxServiceCommonService;

    /**
     * 测试问题 - 生成绑定二维码
     */
    @GetMapping("/generateBindQrCode")
    public Map<String, Object> generateBindQrCode(@RequestParam String deviceId) {
//        // 前面加上 data:image/png;base64, 以便前端直接显示
//        String qrCodeBase64 = "data:image/png;base64," + messageService.generateWarningBindQrCode("test01");

        String qrCodeUrl = wxServiceCommonService.generateWarningBindQrCode(deviceId);
        return MyResultUtil.success(qrCodeUrl);
    }

    /**
     * 创建菜单
     */
    @PostMapping("/createMenu")
    public Map<String, Object> createMenu(@RequestParam Long configId) {
        boolean isSuccess = wxServiceCommonService.createCustomMenu(configId);
        return isSuccess ? MyResultUtil.success() : MyResultUtil.error();
    }

    /**
     * 设备预警解绑
     */
    @PostMapping("/unbindDevice")
    public Map<String, Object> unbindDevice(@RequestBody UnbindDeviceReqDTO unbindReq) {
        boolean isUpdate = wxServiceCommonService.unbindWarningRecipient(unbindReq.getDeviceId(), unbindReq.getWxOpenid());
        return isUpdate ? MyResultUtil.success() : MyResultUtil.error();
    }
}