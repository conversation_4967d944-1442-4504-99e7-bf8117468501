package com.ruoyi.device.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.device.domain.DevicePrinter;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 打印机设备Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-24
 */
public interface DevicePrinterMapper extends BaseMapper<DevicePrinter>
{
    /**
     * 查询打印机设备
     * 
     * @param deviceId 打印机设备ID
     * @return 打印机设备
     */
    public DevicePrinter selectDevicePrinterByDeviceId(String deviceId);

    /**
     * 查询打印机设备列表
     * 
     * @param devicePrinter 打印机设备
     * @return 打印机设备集合
     */
    public List<DevicePrinter> selectDevicePrinterList(DevicePrinter devicePrinter);

    /**
     * 新增打印机设备
     * 
     * @param devicePrinter 打印机设备
     * @return 结果
     */
    public int insertDevicePrinter(DevicePrinter devicePrinter);

    /**
     * 修改打印机设备
     * 
     * @param devicePrinter 打印机设备
     * @return 结果
     */
    public int updateDevicePrinter(DevicePrinter devicePrinter);

    /**
     * 删除打印机设备
     * 
     * @param deviceId 打印机设备ID
     * @return 结果
     */
    public int deleteDevicePrinterByDeviceId(String deviceId);

    /**
     * 批量删除打印机设备
     * 
     * @param deviceIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteDevicePrinterByDeviceIds(String[] deviceIds);
    
    /**
     * 更新设备在线状态
     * 
     * @param deviceId 设备ID
     * @param onlineStatus 在线状态
     * @return 结果
     */
    public int updateDeviceOnlineStatus(@Param("deviceId") String deviceId, @Param("onlineStatus") Integer onlineStatus);
    
    /**
     * 更新设备耗材信息
     *
     * @param devicePrinter 打印机设备
     * @return 结果
     */
    public int updateDeviceConsumables(DevicePrinter devicePrinter);

    /**
     * 查询打印机设备列表（带距离计算）
     *
     * @param longitude 用户经度
     * @param latitude  用户纬度
     * @param queryWrapper 查询条件
     * @return 设备列表（包含距离字段）
     */
    @Select("SELECT *, " +
            "CASE " +
            "  WHEN lng IS NOT NULL AND lat IS NOT NULL THEN " +
            "    ST_Distance_Sphere(POINT(#{longitude}, #{latitude}), POINT(lng, lat)) " +
            "  ELSE NULL " +
            "END AS distance " +
            "FROM device_printer " +
            "${ew.customSqlSegment} " +
            "ORDER BY " +
            "CASE " +
            "  WHEN lng IS NOT NULL AND lat IS NOT NULL THEN distance " +
            "  ELSE 999999999 " +
            "END, create_time DESC")
    Page<DevicePrinter> selectDevicePrinterPageWithDistance(@Param("longitude") Double longitude,
                                                           @Param("latitude") Double latitude,
                                                           @Param("ew") LambdaQueryWrapper<DevicePrinter> queryWrapper,
                                                           Page<DevicePrinter> page);
}