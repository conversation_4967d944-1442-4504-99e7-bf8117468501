package com.ruoyi.goods.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.extension.conditions.query.QueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.goods.mapper.GoodsMapper;
import com.ruoyi.goods.domain.Goods;
import com.ruoyi.goods.service.IGoodsService;

/**
 * goodsService业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Service
public class GoodsServiceImpl extends ServiceImpl<GoodsMapper, Goods> implements IGoodsService
{
    @Autowired
    private GoodsMapper goodsMapper;

    /**
     * 查询goods
     * 
     * @param id goods主键
     * @return goods
     */
    @Override
    public Goods selectGoodsById(Long id)
    {
        return goodsMapper.selectGoodsById(id);
    }

    /**
     * 查询goods列表
     * 
     * @param goods goods
     * @return goods
     */
    @Override
    public TableDataInfo selectGoodsList(Goods goods, int pageNum, int pageSize) {
        if (goods.getCreateBy() == null) return null;
        QueryChainWrapper<Goods> query = query();

        if (goods.getGoodsName() != null){
            query.like("goods_name",goods.getGoodsName());
        }
        if (goods.getStatus() != null){ 
            query.eq("status",goods.getStatus());
        }
        if (goods.getType() != null){
            query.eq("type",goods.getType());
        }
        if (goods.getHot() != null){
            query.eq("hot",goods.getHot());
        }
        if (goods.getCoffeeType() != null){
            query.eq("coffeeType",goods.getCoffeeType());
        }

        // 获取当前页数据
        Page<Goods> page = query.orderByDesc("create_time").page(new Page<>(pageNum, pageSize));
        List<Goods> records = page.getRecords();
        return new TableDataInfo(records, page.getTotal());
    }

    /**
     * 新增goods
     * 
     * @param goods goods
     * @return 结果
     */
    @Override
    public int insertGoods(Goods goods)
    {
        return save(goods) ? 1 : 0;
    }

    /**
     * 修改goods
     * 
     * @param goods goods
     * @return 结果
     */
    @Override
    public int updateGoods(Goods goods)
    {
        return updateById(goods) ? 1 : 0;
    }

    /**
     * 批量删除goods
     * 
     * @param ids 需要删除的goods主键
     * @return 结果
     */
    @Override
    public int deleteGoodsByIds(Long[] ids)
    {
        return goodsMapper.deleteGoodsByIds(ids);
    }

    /**
     * 删除goods信息
     * 
     * @param id goods主键
     * @return 结果
     */
    @Override
    public int deleteGoodsById(Long id)
    {
        return goodsMapper.deleteGoodsById(id);
    }
}
