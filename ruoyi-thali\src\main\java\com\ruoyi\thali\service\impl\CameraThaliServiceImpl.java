package com.ruoyi.thali.service.impl;

import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.thali.dto.Result;
import org.springframework.stereotype.Service;
import com.ruoyi.thali.mapper.CameraThaliMapper;
import com.ruoyi.thali.domain.CameraThali;
import com.ruoyi.thali.service.ICameraThaliService;

/**
 * 套餐Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-07-27
 */
@Service
public class CameraThaliServiceImpl extends ServiceImpl<CameraThaliMapper, CameraThali> implements ICameraThaliService
{
    /**
     * 查询套餐
     * 
     * @param id 套餐主键
     * @return 套餐
     */
    @Override
    public CameraThali selectCameraThaliById(Long id)
    {
        return getById(id);
    }

    /**
     * 查询套餐列表 已废弃！！！！！！！！！
     * 
     * @param cameraThali 套餐
     * @return 套餐
     */
    @Override
    public List<CameraThali> selectCameraThaliList(CameraThali cameraThali)
    {
        return null;
    }

    /**
     * 新增套餐 已废弃！！！！！！！！！！！！！！！！！！
     * 
     * @param cameraThali 套餐
     * @return 结果
     */
    @Override
    public int insertCameraThali(CameraThali cameraThali)
    {
//        cameraThali.setCreateTime(DateUtils.getNowDate());
        return 0;
    }

    /**
     * 修改套餐
     * 
     * @param cameraThali 套餐
     * @return 结果
     */
    @Override
    public int updateCameraThali(CameraThali cameraThali)
    {
        cameraThali.setUpdateTime(DateUtils.getNowDate());
        boolean isUpdate = update().eq("device_id", cameraThali.getDeviceId()).update();
        return isUpdate ? 1 : 0;
    }

    /**
     * 批量删除套餐 已废弃！！！！！！！！！！！！！！！！！！！
     * 
     * @param ids 需要删除的套餐主键
     * @return 结果
     */
    @Override
    public int deleteCameraThaliByIds(Long[] ids)
    {
//        return cameraThaliMapper.deleteCameraThaliByIds(ids);
        return 0;
    }

    /**
     * 删除套餐信息 已废弃！！！！！！！！！！！！！！！！！！！！！
     * 
     * @param id 套餐主键
     * @return 结果
     */
    @Override
    public int deleteCameraThaliById(Long id)
    {
        return 0;
//        return cameraThaliMapper.deleteCameraThaliById(id);
    }

    /**
     * 已废弃！！！！！！！！！！！！！！！！！！！！！！
     * @param deviceId
     * @return
     */
    @Override
    public Result queryCameraThaliByDevice(String deviceId) {
//        List<CameraThali> cameraThalis = cameraThaliMapper.selectCameraThaliList(new CameraThali());
//        return Result.ok(200,"获取成功", String.valueOf(System.currentTimeMillis()),cameraThalis);
        return null;
    }

    /**
     *
     * @param deviceId 设备id
     * @return 套餐金额
     */
    @Override
    public CameraThali queryPackageType(String deviceId) {
        // 判断id是否为空(为空抛出异常)
        if (deviceId == null){
            System.out.println("设备id为空");
            return null;
        }
        CameraThali cameraThali = query().eq("device_id", deviceId).one();
        // 返回
        if (cameraThali == null) {
            //创建这个设备的套餐
            cameraThali = new CameraThali();
            cameraThali.setDeviceId(deviceId);
            cameraThali.setPayOne(1800);
            cameraThali.setPayTwo(1800);
            cameraThali.setPayThree(1800);
            cameraThali.setPayFour(1800);
            cameraThali.setPayFive(1800);
            cameraThali.setPaySix(1800);
            cameraThali.setPaySeven(1800);
            cameraThali.setPayEight(1800);
            cameraThali.setPayNine(1800);
            cameraThali.setPayTen(1800);
            cameraThali.setCreateTime(new Date());
            cameraThali.setRemark(deviceId + "的套餐");
            boolean save = save(cameraThali);
            //然后返回这个套餐
            return cameraThali;
        }
        return cameraThali;
    }

    @Override
    public CameraThali updatePrice(CameraThali cameraThali) {
        // 判断是否有设备id
        if (cameraThali.getDeviceId() == null){
            System.out.println("异常");
            return null;
        }
        // 修改套餐价格
//        CameraThali cameraThali = cameraThaliMapper.updatePrice(cameraThali.getDeviceId());
        boolean b = updateById(cameraThali);
        // 返回
        return null;
    }
}
