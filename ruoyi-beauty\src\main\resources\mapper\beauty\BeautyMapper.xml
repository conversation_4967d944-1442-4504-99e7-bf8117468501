<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.beauty.mapper.BeautyMapper">
    
    <resultMap type="Beauty" id="BeautyResult">
        <result property="useeId"    column="usee_id"    />
        <result property="mopi"    column="mopi"    />
        <result property="meibai"    column="meibai"    />
        <result property="wuguanliti"    column="wuguanliti"    />
        <result property="liangyan"    column="liangyan"    />
        <result property="hongrun"    column="hongrun"    />
        <result property="shoulian"    column="shoulian"    />
        <result property="dayan"    column="dayan"    />
    </resultMap>

    <sql id="selectBeautyVo">
        select usee_id, mopi, meibai, wuguanliti, liangyan, hongrun, shoulian, dayan from beauty
    </sql>

    <select id="selectBeautyList" parameterType="Beauty" resultMap="BeautyResult">
        <include refid="selectBeautyVo"/>
        <where>  
            <if test="mopi != null "> and mopi = #{mopi}</if>
            <if test="meibai != null "> and meibai = #{meibai}</if>
            <if test="wuguanliti != null "> and wuguanliti = #{wuguanliti}</if>
            <if test="liangyan != null "> and liangyan = #{liangyan}</if>
            <if test="hongrun != null "> and hongrun = #{hongrun}</if>
            <if test="shoulian != null "> and shoulian = #{shoulian}</if>
            <if test="dayan != null "> and dayan = #{dayan}</if>
        </where>
    </select>
    
    <select id="selectBeautyByUseeId" parameterType="Long" resultMap="BeautyResult">
        <include refid="selectBeautyVo"/>
        where usee_id = #{useeId}
    </select>
        
    <insert id="insertBeauty" parameterType="Beauty">
        insert into beauty
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="useeId != null">usee_id,</if>
            <if test="mopi != null">mopi,</if>
            <if test="meibai != null">meibai,</if>
            <if test="wuguanliti != null">wuguanliti,</if>
            <if test="liangyan != null">liangyan,</if>
            <if test="hongrun != null">hongrun,</if>
            <if test="shoulian != null">shoulian,</if>
            <if test="dayan != null">dayan,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="useeId != null">#{useeId},</if>
            <if test="mopi != null">#{mopi},</if>
            <if test="meibai != null">#{meibai},</if>
            <if test="wuguanliti != null">#{wuguanliti},</if>
            <if test="liangyan != null">#{liangyan},</if>
            <if test="hongrun != null">#{hongrun},</if>
            <if test="shoulian != null">#{shoulian},</if>
            <if test="dayan != null">#{dayan},</if>
         </trim>
    </insert>

    <update id="updateBeauty" parameterType="Beauty">
        update beauty
        <trim prefix="SET" suffixOverrides=",">
            <if test="mopi != null">mopi = #{mopi},</if>
            <if test="meibai != null">meibai = #{meibai},</if>
            <if test="wuguanliti != null">wuguanliti = #{wuguanliti},</if>
            <if test="liangyan != null">liangyan = #{liangyan},</if>
            <if test="hongrun != null">hongrun = #{hongrun},</if>
            <if test="shoulian != null">shoulian = #{shoulian},</if>
            <if test="dayan != null">dayan = #{dayan},</if>
        </trim>
        where usee_id = #{useeId}
    </update>

    <delete id="deleteBeautyByUseeId" parameterType="Long">
        delete from beauty where usee_id = #{useeId}
    </delete>

    <delete id="deleteBeautyByUseeIds" parameterType="String">
        delete from beauty where usee_id in 
        <foreach item="useeId" collection="array" open="(" separator="," close=")">
            #{useeId}
        </foreach>
    </delete>
</mapper>