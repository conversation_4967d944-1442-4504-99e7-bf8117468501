package com.ruoyi.web.controller.util;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.json.JSONObject;
import cn.hutool.jwt.JWT;
import cn.hutool.jwt.JWTPayload;
import cn.hutool.jwt.JWTUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * JWT工具类
 */
public class MyJWTUtil {

    private static final Logger LOG = LoggerFactory.getLogger(MyJWTUtil.class);

    // 盐值
    private static final String KEY = "xjydev";

    /**
     * 创建JWT
     *
     * @param content JWT载荷内容
     * @return JWT
     */
    public static String createJWT(Map<String, Object> content,DateTime expireTime) {
        DateTime now = DateTime.now();
//        DateTime expireTime = now.offsetNew(DateField.YEAR, 99);
        Map<String, Object> payload = new HashMap<>(content);
        // 签发时间
        payload.put(JWTPayload.ISSUED_AT, now);
        // 过期时间
        payload.put(JWTPayload.EXPIRES_AT, expireTime);
        // 生效时间
        payload.put(JWTPayload.NOT_BEFORE, now);
        String token = JWTUtil.createToken(payload, KEY.getBytes());
        LOG.info("生成JWT：{}", token);
        return token;
    }

    /**
     * 验证JWT
     *
     * @param token JWT
     * @return 验证结果
     */
    public static boolean verifyJWT(String token) {
        try {
            JWT jwt = JWTUtil.parseToken(token).setKey(KEY.getBytes());
            boolean validate = jwt.validate(0);
            LOG.info("验证JWT：{}", validate);
            return validate;
        } catch (Exception e) {
            LOG.error("验证JWT异常", e);
            return false;
        }
    }

    /**
     * 解析JWT
     *
     * @param token JWT
     * @return JSON字符串
     */
    public static String parseJWT(String token) {
        try {
            JWT jwt = JWTUtil.parseToken(token).setKey(KEY.getBytes());
            JSONObject payload = jwt.getPayloads();
            payload.remove(JWTPayload.ISSUED_AT);
            payload.remove(JWTPayload.EXPIRES_AT);
            payload.remove(JWTPayload.NOT_BEFORE);
            LOG.info("解析JWT：{}", payload);
            return payload.toString();
        } catch (Exception e) {
            LOG.error("解析JWT异常", e);
            return null;
        }
    }


    public static void main(String[] args) {
        Map<String, Object> payload = new HashMap<>();
        payload.put("userId", 1);
        payload.put("username", "admin");
        payload.put("role", "admin");
        String token = createJWT(payload,DateTime.now().offsetNew(DateField.YEAR, 99));
        verifyJWT(token);
        parseJWT(token);
    }

}
