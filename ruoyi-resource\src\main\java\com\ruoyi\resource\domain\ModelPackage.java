package com.ruoyi.resource.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 模板库对象 model_package
 * 
 * <AUTHOR>
 * @date 2024-12-10
 */
@Data
public class ModelPackage extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 模板库命名 */
    @Excel(name = "模板库命名")
    private String packageName;


}
