<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0"/>
    <link rel="stylesheet" href="css/index.css">
    <title>简单支付</title>
</head>
<body>

<div class="wrap">
    <div>简单支付样例(用微信或支付宝客户端打开)</div>
    <form>
        <div class="item">
            <div class="left">金额：</div>
            <div class="right">
                <input id="totalAmount" type="text" value="0.3" name="totalAmount">
            </div>
        </div>
        <div class="btn">
            <button onclick="doPay()" class="submit_btn" type="button">支付</button>
        </div>
    </form>
</div>

</body>
<script type="text/javascript" src="js/jquery.js"></script>
<script type="text/javascript">
    function doPay() {
        var totalAmount = $('#totalAmount').val();
        $.ajax({
            url: "/simplePay",
            type: "POST",
            data: {"totalAmount": totalAmount},
            success: function (data) {
                // alert(data);
                window.location = data;
                // var res = JSON.parse(data);
                // alert(res);
                // alert(data);
                // var res = JSON.parse(data.data);
                // alert(res);
                // window.location.href = data;
            }
        });
    }
</script>
</html>