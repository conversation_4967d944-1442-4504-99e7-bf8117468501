package com.ruoyi.photo.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.order.domain.OrderCamera;
import com.ruoyi.photo.domain.ImageBase64;
import com.ruoyi.photo.domain.UserPhoto;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * photoService接口
 *
 * <AUTHOR>
 * @date 2023-09-05
 */
public interface ImageBase64Service extends IService<ImageBase64>
{

}
