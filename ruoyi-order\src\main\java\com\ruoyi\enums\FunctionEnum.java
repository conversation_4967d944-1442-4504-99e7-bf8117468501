package com.ruoyi.enums;

public enum FunctionEnum {
    SIMPLE_PAY( "simple.pay","简单支付接口"),
    JS_PAY("js.pay", "JS支付接口"),
    PAY_CODE_PAY("pay.code.pay","付款码支付"),
    ORDER_QUERY("order.query","订单查询接口"),
    ORDER_REFUND("order.refund","订单退款接口"),
    ORDER_REFUND_QUERY("order.refund.query","订单退款查询接口"),

    MERCHANT_CONFIG("merchant.config","配置微信AppId,和支付授权目录"),
    MERCHANT_UPDATE("merchant.update","简称，费率变更，结算信息"),
    MERCHANT_REGISTER("merchant.register", "商户注册接口"),
    QUALIFICATION_REGISTER("qualification.register", "资质进件接口"),
    MERCHANT_REGISTER_QUERY("merchant.register.query", "商户注册查询接口"),
    MERCHANT_WX_REGISTER_QUERY("merchant.wx.register.query", "商户微信注册查询接口"),
    MERCHANT_PAYRESULT_QUERY("merchant.payresult.query","商户结算查询"),
    MERCHANT_BALANCE_QUERY("merchant.balance.query", "商户余额查询接口"),
    MERCHANT_SELF_SETTLEMENT("merchant.self.settlement","商户自主结算"),
    MERCHANT_QUERY("merchant.merchantprod","商户信息查询接口"),
    MERCHANT_STATUS_QUERY("merchant.status.query", "商户状态查询"),

    DAIFU_PAY("daifu.pay","实时代付接口"),
    DAIFU_BALANCE("daifu.balance","代付余额接口"),
    DAIFU_QUERY("daifu.query","代付订单查询接口"),

    MERCHANT_UPLOADPHOTO("merchant.uploadphoto","商户图片上传"),

    MERCHANT_COMPLAINT_RESPONSE("merchant.complaint.response", "微信投诉订单回复"),
    MERCHANT_COMPLAINT_COMPLETE("merchant.complaint.complete", "微信投诉订单反馈处理完成"),

    // iot盒子
    IOTBOX_DEVICE_INFO("iotbox.query.device.info","盒子基础信息"),
    IOTBOX_UP_COIN("iotbox.up.coin","云上分"),
    IOTBOX_UP_COIN_NOTIFY("iotbox.up.coin.notify","云上分-回调"),
    IOTBOX_QUERY_CONSUMERECORD_ORDER("iotbox.query.consumeRecord.order","查询云上分订单"),
    IOTBOX_SET_PARAM("iotbox.set.param","盒子参数设置"),
    IOTBOX_SERIALPORT("iotbox.serialPort","透传"),
    IOTBOX_SERIALPORT_NOTIFY("iotbox.serialPort.notify","透传-回调"),

    // iot盒子
    CLOUDHORN_MESSAGE("cloudHorn.message.send","消息发送"),

    IOTBOX_VERIFY_CODE("iotbox.verify.code","iot盒子验券"),
    IOTBOX_CANCEL_VERIFY_CODE("iotbox.cancel.verify.code","iot盒子取消验券"),
    IOTBOX_MEITUAN_MERCHANT_AUTH("iotbox.meituan.merchant.auth","美团商家授权"),

    ;
    private String  code;
    private String desc;

    FunctionEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
