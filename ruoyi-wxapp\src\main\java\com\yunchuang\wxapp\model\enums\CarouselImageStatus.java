package com.yunchuang.wxapp.model.enums;

import lombok.Getter;

/**
 * 轮播图状态
 */
@Getter
public enum CarouselImageStatus {

    /**
     * 禁用
     */
    DISABLE(0, "禁用"),
    /**
     * 启用
     */
    ENABLE(10, "启用");

    private final Integer value;
    private final String name;

    CarouselImageStatus(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public static CarouselImageStatus getWxCarouselImageStatus(Integer value) {
        for (CarouselImageStatus carouselImageStatus : CarouselImageStatus.values()) {
            if (carouselImageStatus.getValue().equals(value)) {
                return carouselImageStatus;
            }
        }
        return null;
    }
}
