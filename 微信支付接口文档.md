# 微信支付接口文档

## 📋 概述

本文档描述了微信原生支付API v2版本的接口规范，包括JSAPI支付、Native支付、订单查询等功能。

**商户信息**:
- 商户号: `1631162326`
- 证书序列号: `671F99B4529CDF45E97F5CB341E02838E1DEE092`
- API版本: v2
- 签名方式: MD5

## 🔧 基础配置

### 请求域名
```
https://您的域名/client/wxapp/wxpay
```

### 通用响应格式
```json
{
    "code": 200,           // 响应码，200表示成功
    "msg": "操作成功",      // 响应消息
    "data": {}            // 响应数据
}
```

### 错误响应格式
```json
{
    "code": 500,           // 错误码
    "msg": "错误描述",      // 错误消息
    "data": null
}
```

## 📡 接口列表

### 1. JSAPI支付接口

**接口地址**: `POST /jsapi`

**功能说明**: 创建微信JSAPI支付订单，适用于小程序和公众号内支付

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| totalAmount | Integer | 是 | 支付金额，单位：分 |
| openId | String | 是 | 用户微信OpenID |
| outTradeNo | String | 是 | 商户订单号，需保证唯一性 |
| body | String | 否 | 商品描述，默认"商品支付" |

**请求示例**:
```bash
curl -X POST "https://您的域名/client/wxapp/wxpay/jsapi" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "totalAmount=100&openId=oZIa06iUqoN...&outTradeNo=ORDER_123456&body=测试商品"
```

**响应示例**:
```json
{
    "code": 200,
    "msg": "支付订单创建成功",
    "data": {
        "appId": "wx8f7a9346308ee56f",
        "timeStamp": "1706598000",
        "nonceStr": "abc123def456",
        "package": "prepay_id=wx123456789",
        "signType": "MD5",
        "paySign": "C380BEC2BFD727A4B6845133519F3AD6"
    }
}
```

### 2. Native支付接口

**接口地址**: `POST /native`

**功能说明**: 创建微信Native支付订单，生成支付二维码

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| totalAmount | Integer | 是 | 支付金额，单位：分 |
| outTradeNo | String | 是 | 商户订单号，需保证唯一性 |
| body | String | 否 | 商品描述，默认"商品支付" |

**请求示例**:
```bash
curl -X POST "https://您的域名/client/wxapp/wxpay/native" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "totalAmount=100&outTradeNo=ORDER_123456&body=测试商品"
```

**响应示例**:
```json
{
    "code": 200,
    "msg": "支付二维码生成成功",
    "data": {
        "codeUrl": "weixin://wxpay/bizpayurl?pr=abc123def",
        "outTradeNo": "ORDER_123456"
    }
}
```

### 3. 订单查询接口

**接口地址**: `GET /query/{outTradeNo}`

**功能说明**: 查询支付订单状态

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| outTradeNo | String | 是 | 商户订单号 |

**请求示例**:
```bash
curl -X GET "https://您的域名/client/wxapp/wxpay/query/ORDER_123456"
```

**响应示例**:
```json
{
    "code": 200,
    "msg": "查询成功",
    "data": {
        "outTradeNo": "ORDER_123456",
        "returnCode": "SUCCESS",
        "resultCode": "SUCCESS",
        "tradeState": "SUCCESS",
        "payStatus": "SUCCESS",
        "payStatusDesc": "支付成功",
        "transactionId": "4200001234567890123",
        "totalFee": "100",
        "openid": "oZIa06iUqoN...",
        "timeEnd": "20250130143000"
    }
}
```

**支付状态说明**:
| 状态值 | 说明 |
|--------|------|
| SUCCESS | 支付成功 |
| REFUND | 转入退款 |
| NOTPAY | 未支付 |
| CLOSED | 已关闭 |
| REVOKED | 已撤销 |
| USERPAYING | 用户支付中 |
| PAYERROR | 支付失败 |

### 4. 批量查询接口

**接口地址**: `POST /query/batch`

**功能说明**: 批量查询多个订单的支付状态

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| outTradeNos | String | 是 | 订单号列表，用逗号分隔 |

**请求示例**:
```bash
curl -X POST "https://您的域名/client/wxapp/wxpay/query/batch" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "outTradeNos=ORDER_123456,ORDER_123457,ORDER_123458"
```

**响应示例**:
```json
{
    "code": 200,
    "msg": "批量查询成功",
    "data": [
        {
            "outTradeNo": "ORDER_123456",
            "payStatus": "SUCCESS",
            "transactionId": "4200001234567890123",
            "totalFee": "100"
        },
        {
            "outTradeNo": "ORDER_123457",
            "payStatus": "NOTPAY"
        },
        {
            "outTradeNo": "ORDER_123458",
            "payStatus": "QUERY_ERROR",
            "errorMsg": "订单不存在"
        }
    ]
}
```

### 5. 支付回调接口

**接口地址**: `POST /notify`

**功能说明**: 接收微信支付成功回调通知

**请求格式**: XML格式

**请求示例**:
```xml
<xml>
    <return_code><![CDATA[SUCCESS]]></return_code>
    <result_code><![CDATA[SUCCESS]]></result_code>
    <out_trade_no><![CDATA[ORDER_123456]]></out_trade_no>
    <transaction_id><![CDATA[4200001234567890123]]></transaction_id>
    <total_fee>100</total_fee>
    <openid><![CDATA[oZIa06iUqoN...]]></openid>
    <time_end><![CDATA[20250130143000]]></time_end>
    <sign><![CDATA[C380BEC2BFD727A4B6845133519F3AD6]]></sign>
</xml>
```

**响应格式**: XML格式
```xml
<xml>
    <code><![CDATA[SUCCESS]]></code>
    <message><![CDATA[SUCCESS]]></message>
</xml>
```

## 🔄 定时任务

### 支付状态查询定时任务

**功能说明**: 系统每分钟自动查询未支付订单状态，确保支付状态及时更新

**执行频率**: 每分钟执行一次

**处理逻辑**:
1. 查询最近30分钟内创建的未支付订单
2. 批量调用微信支付查询接口
3. 更新已支付订单状态
4. 触发支付成功业务逻辑
5. 处理订单关闭状态

**日志示例**:
```
2025-01-30 14:30:00 INFO  - === 开始执行微信支付状态查询定时任务 ===
2025-01-30 14:30:01 INFO  - 找到3个未支付订单需要查询状态
2025-01-30 14:30:02 INFO  - 订单ORDER_123456支付成功，已更新状态
2025-01-30 14:30:03 INFO  - === 微信支付状态查询定时任务完成 === 成功: 1, 失败: 0
```

## 🛠️ 前端集成

### 小程序支付示例

```javascript
// 1. 调用后端接口创建支付订单
wx.request({
    url: '/client/wxapp/wxpay/jsapi',
    method: 'POST',
    header: {
        'Content-Type': 'application/x-www-form-urlencoded'
    },
    data: {
        totalAmount: 100,
        openId: wx.getStorageSync('openid'),
        outTradeNo: 'ORDER_' + Date.now(),
        body: '商品描述'
    },
    success: function(res) {
        if (res.data.code === 200) {
            // 2. 调用微信支付
            wx.requestPayment({
                ...res.data.data,
                success: function() {
                    console.log('支付成功');
                    // 3. 查询订单状态确认
                    checkPaymentStatus();
                },
                fail: function(err) {
                    console.log('支付失败', err);
                }
            });
        }
    }
});

// 查询支付状态
function checkPaymentStatus() {
    wx.request({
        url: '/client/wxapp/wxpay/query/' + outTradeNo,
        method: 'GET',
        success: function(res) {
            if (res.data.code === 200 && res.data.data.payStatus === 'SUCCESS') {
                console.log('支付确认成功');
                // 跳转到成功页面
            }
        }
    });
}
```

### H5页面支付示例

```javascript
// Native支付（扫码支付）
function createNativePay() {
    fetch('/client/wxapp/wxpay/native', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
            totalAmount: 100,
            outTradeNo: 'ORDER_' + Date.now(),
            body: '商品描述'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            // 生成二维码显示给用户
            generateQRCode(data.data.codeUrl);
            // 开始轮询查询支付状态
            startPollingPaymentStatus(data.data.outTradeNo);
        }
    });
}

// 轮询查询支付状态
function startPollingPaymentStatus(outTradeNo) {
    const interval = setInterval(() => {
        fetch('/client/wxapp/wxpay/query/' + outTradeNo)
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                if (data.data.payStatus === 'SUCCESS') {
                    clearInterval(interval);
                    alert('支付成功！');
                } else if (data.data.payStatus === 'CLOSED') {
                    clearInterval(interval);
                    alert('订单已关闭');
                }
            }
        });
    }, 3000); // 每3秒查询一次
}
```

## ⚠️ 注意事项

### 安全要求
1. 所有接口必须使用HTTPS协议
2. 妥善保管API密钥和证书文件
3. 验证回调签名确保数据安全
4. 避免重复处理同一订单

### 性能优化
1. 合理设置查询频率，避免过于频繁的API调用
2. 使用批量查询接口减少请求次数
3. 实现幂等性处理，避免重复业务逻辑

### 错误处理
1. 网络异常时实现重试机制
2. 记录详细的错误日志便于排查
3. 对用户友好的错误提示

### 测试建议
1. 使用微信支付沙箱环境进行测试
2. 测试各种支付状态的处理逻辑
3. 验证定时任务的正确性

## 📞 技术支持

如有问题请联系技术支持团队，提供以下信息：
- 商户订单号
- 错误日志
- 请求参数
- 响应内容
