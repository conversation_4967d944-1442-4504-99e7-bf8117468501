package com.ruoyi.enums;

/**
 * Created by jingzhu.zr on 2017/8/15.
 */
public enum MccEnum {

  VendingMachine("2015091000052158","自动售货机"),
  PhoneRecharge("2015063000020190","话费充值"),
  GameRecharge("2015063000020191","游戏充值"),
  Cate("2015050700000000", "美食"),
  Supermarket("2015091000052157", "超市便利店"),
  LifeService("2015063000020189", "生活服务"),
  Education("2016042200000148", "教育培训"),
  HealthCare("2016062900190296", "医疗健康"),
  AirTravel("2015080600000001", "航旅"),
  WholeSale("2016062900190337", "专业销售/批发"),
  Government("2016062900190371", "政府/社会组织"),
  Entertainment("2015062600004525", "休闲娱乐"),
  Shopping("2015062600002758", "购物"),
  LovingCar("2016062900190124", "爱车"),
  ;

  private String mccCode;
  private String mccDesc;

  MccEnum(String mccCode, String mccDesc) {
    this.mccCode = mccCode;
    this.mccDesc = mccDesc;
  }

  public String getMccCode() {
    return mccCode;
  }

  public void setMccCode(String mccCode) {
    this.mccCode = mccCode;
  }

  public String getMccDesc() {
    return mccDesc;
  }

  public void setMccDesc(String mccDesc) {
    this.mccDesc = mccDesc;
  }
}
