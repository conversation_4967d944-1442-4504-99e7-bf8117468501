package com.ruoyi.web.controller.wxapp.admin;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.yunchuang.wxapp.model.domain.WishList;
import com.yunchuang.wxapp.service.admin.IWishListService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 心愿单Controller
 *
 * <AUTHOR>
 * @date 2025-02-06
 */
@RestController
@RequestMapping("/wxapp/wishlist")
public class WishListController extends BaseController {

    @Autowired
    private IWishListService wishListService;

    /**
     * 查询心愿单列表
     */
    @PreAuthorize("@ss.hasPermi('wxapp:wishlist:list')")
    @GetMapping("/list")
    public TableDataInfo list(WishList wishList) {
        startPage();
        List<WishList> list = wishListService.selectWishListList(wishList);
        return getDataTable(list);
    }

    /**
     * 导出心愿单列表
     */
    @PreAuthorize("@ss.hasPermi('wxapp:wishlist:export')")
    @Log(title = "心愿单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WishList wishList) {
        List<WishList> list = wishListService.selectWishListList(wishList);
        ExcelUtil<WishList> util = new ExcelUtil<WishList>(WishList.class);
        util.exportExcel(response, list, "心愿单数据");
    }

    /**
     * 获取心愿单详细信息
     */
    @PreAuthorize("@ss.hasPermi('wxapp:wishlist:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(wishListService.selectWishListById(id));
    }

    /**
     * 新增心愿单
     */
    @PreAuthorize("@ss.hasPermi('wxapp:wishlist:add')")
    @Log(title = "心愿单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WishList wishList) {
        return toAjax(wishListService.insertWishList(wishList));
    }

    /**
     * 修改心愿单
     */
    @PreAuthorize("@ss.hasPermi('wxapp:wishlist:edit')")
    @Log(title = "心愿单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WishList wishList) {
        return toAjax(wishListService.updateWishList(wishList));
    }

    /**
     * 删除心愿单
     */
    @PreAuthorize("@ss.hasPermi('wxapp:wishlist:remove')")
    @Log(title = "心愿单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(wishListService.deleteWishListByIds(ids));
    }
}
