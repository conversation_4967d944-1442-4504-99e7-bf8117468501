package com.ruoyi.utils;

import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.IOException;
import java.net.URI;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.SignatureException;
import java.security.spec.InvalidKeySpecException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Random;

import static com.ruoyi.utils.OrderConstant.*;


@Component
public class FuYouApi {

    private final RestTemplate restTemplate;
    @Autowired
    public FuYouApi(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    public String preCreate(Map<String, String> map) {
        String url = "https://fundwx.fuiou.com/preCreate";
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        HttpHeaders headers = new HttpHeaders();


        map.put("version","1.0");
        map.put("ins_cd",INS_CD);
        map.put("mchnt_cd",MCHNT_CD);
        map.put("term_id",TERM_ID);
        map.put("random_str","zouzefengyangzhiqiangliaomingzhe");
        map.put("order_type","WECHAT");
        map.put("goods_des","照片");
        map.put("goods_detail","");
        map.put("addn_inf","");
        map.put("curr_type","");
        map.put("term_ip",TERM_IP);
        map.put("goods_tag","");
        map.put("notify_url",FUYOU_NOTIFY_URL);

        try {
            String sign = ScanpayUtils.getSign(map);
            map.put("sign",sign);
        } catch (InvalidKeySpecException | SignatureException | NoSuchAlgorithmException | InvalidKeyException | IOException e) {
            e.printStackTrace();
        }

        Document doc = DocumentHelper.createDocument();
        Element root = doc.addElement("xml");
        Iterator it=map.keySet().iterator();
        while(it.hasNext()){
            String key = it.next().toString();
            String value = map.get(key);
            root.addElement(key).addText(value);
        }
        String reqBody = "<?xml version=\"1.0\" encoding=\"GBK\" standalone=\"yes\"?>" + doc.getRootElement().asXML();
        System.out.println("reqBody");
        System.out.println(reqBody);
        params.add("req",reqBody.replace("+", "%2B"));

        // 构建查询参数
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromUriString(url);
        uriBuilder.queryParams(params);
        // 构建请求URI
        URI uri = uriBuilder.build().toUri();
        // 构建HttpEntity，设置头部信息
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        HttpEntity<?> entity = new HttpEntity<>(headers);

        ResponseEntity<String> response = restTemplate.exchange(uri, HttpMethod.GET, entity, String.class);

        if (response.getStatusCode().is2xxSuccessful()) {
            return response.getBody();
        } else {
            return "Error: " + response.getStatusCodeValue();
        }
    }


}
