package com.yunchuang.wxapp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yunchuang.wxapp.model.domain.WishList;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 心愿单Mapper接口
 *
 * <AUTHOR>
 * @date 2025-02-06
 */
@Mapper
public interface WishListMapper extends BaseMapper<WishList> {

    /**
     * 查询心愿单
     *
     * @param id 心愿单主键
     * @return 心愿单
     */
    public WishList selectWishListById(Long id);

    /**
     * 查询心愿单列表
     *
     * @param wishList 心愿单
     * @return 心愿单集合
     */
    public List<WishList> selectWishListList(WishList wishList);

    /**
     * 新增心愿单
     *
     * @param wishList 心愿单
     * @return 结果
     */
    public int insertWishList(WishList wishList);

    /**
     * 修改心愿单
     *
     * @param wishList 心愿单
     * @return 结果
     */
    public int updateWishList(WishList wishList);

    /**
     * 删除心愿单
     *
     * @param id 心愿单主键
     * @return 结果
     */
    public int deleteWishListById(Long id);

    /**
     * 批量删除心愿单
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWishListByIds(Long[] ids);
}
