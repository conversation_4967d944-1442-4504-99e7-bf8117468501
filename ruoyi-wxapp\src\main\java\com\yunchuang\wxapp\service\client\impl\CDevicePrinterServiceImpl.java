package com.yunchuang.wxapp.service.client.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.device.domain.DevicePrinter;
import com.ruoyi.device.mapper.DevicePrinterMapper;
import com.yunchuang.wxapp.service.client.ICDevicePrinterService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 客户端
 * <br />
 * 打印机设备 Service 实现类
 */
@Service
@Transactional
public class CDevicePrinterServiceImpl extends ServiceImpl<DevicePrinterMapper, DevicePrinter> implements ICDevicePrinterService {

    @Resource
    private DevicePrinterMapper devicePrinterMapper;

    /**
     * 查询打印机设备列表（懒加载+搜索）
     * 支持按设备编号或地址搜索
     *
     * @param pageNum   页码
     * @param pageSize  每页数量
     * @param keyword   搜索关键字（设备编号或地址）
     * @param longitude 用户经度（可选，用于计算距离）
     * @param latitude  用户纬度（可选，用于计算距离）
     * @return 分页结果
     */
    @Override
    public Map<String, Object> getDevicePrinterList(Integer pageNum, Integer pageSize, String keyword, Double longitude, Double latitude) {
        // 构建查询条件
        LambdaQueryWrapper<DevicePrinter> queryWrapper = new LambdaQueryWrapper<>();

        // 只查询未删除的设备
        queryWrapper.eq(DevicePrinter::getDelFlag, 0);

        // 关键字搜索：设备编号或地址
        if (StringUtils.isNotEmpty(keyword)) {
            queryWrapper.and(wrapper -> wrapper
                .like(DevicePrinter::getDeviceCode, keyword)  // 设备编号
                .or()
                .like(DevicePrinter::getDetailAddress, keyword)  // 详细地址
                .or()
                .like(DevicePrinter::getProvince, keyword)  // 省
                .or()
                .like(DevicePrinter::getCity, keyword)  // 市
                .or()
                .like(DevicePrinter::getDistrict, keyword)  // 区/县
            );
        }

        // 分页查询
        Page<DevicePrinter> page = new Page<>(pageNum, pageSize);

        // 如果提供了经纬度，使用带距离计算的查询
        if (longitude != null && latitude != null) {
            devicePrinterMapper.selectDevicePrinterPageWithDistance(longitude, latitude, queryWrapper, page);
        } else {
            // 否则使用普通查询，按创建时间倒序排列
            queryWrapper.orderByDesc(DevicePrinter::getCreateTime);
            devicePrinterMapper.selectPage(page, queryWrapper);
        }

        // 直接返回分页数据，不使用MyResultUtil包装
        Map<String, Object> result = new HashMap<>();
        result.put("list", page.getRecords());
        result.put("total", page.getTotal());
        result.put("pageNum", page.getCurrent());
        result.put("pageSize", page.getSize());
        result.put("pages", page.getPages());

        return result;
    }
}
