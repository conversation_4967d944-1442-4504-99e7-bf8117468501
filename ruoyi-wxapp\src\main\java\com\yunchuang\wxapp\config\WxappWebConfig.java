package com.yunchuang.wxapp.config;

import com.yunchuang.wxapp.interceptor.WxappAuthInterceptor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;

/**
 * 微信小程序 - Web配置
 */
@Configuration
public class WxappWebConfig implements WebMvcConfigurer {

    @Resource
    private WxappAuthInterceptor wxappAuthInterceptor;

    // 从配置文件中读取拦截路径
    @Value("${wxapp.auth-interceptor.path-patterns:/client/wxapp/**}") // 设置默认值，防止配置文件缺失
    private String[] pathPatterns;

    // 从配置文件中读取排除路径
    @Value("${wxapp.auth-interceptor.exclude-path-patterns:/client/wxapp/common/**,/client/wxapp/user/login_or_register}")
    private String[] excludePathPatterns;

    // 添加拦截器
    @Override
    public void addInterceptors(InterceptorRegistry registry) {

        // 鉴权拦截器
        registry.addInterceptor(this.wxappAuthInterceptor)
                .addPathPatterns(this.pathPatterns) // 添加拦截路径
                .excludePathPatterns(this.excludePathPatterns); // 添加排除路径
    }

}
