package com.ruoyi.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.order.domain.OrderCamera;
import com.ruoyi.order.dto.DatStatisticsMobileDTO;
import com.ruoyi.order.dto.IndexMobileRespDTO;
import com.ruoyi.order.dto.Result;

/**
 * 订单管理Service接口
 *
 * <AUTHOR>
 * @date 2023-07-13
 */
public interface IOrderCameraService extends IService<OrderCamera> {
    /**
     * 查询订单管理
     *
     * @param orderId 订单管理主键
     * @return 订单管理
     */
    public OrderCamera selectOrderCameraByOrderId(String orderId);


    TableDataInfo selectOrderCameraList(OrderCamera orderCamera, int pageNum, int pageSize);

    /**
     * 新增订单管理(订单预创建)
     *
     * @param orderCamera 订单管理
     * @return 结果
     */
    public Result insertOrderCamera(OrderCamera orderCamera, Integer pay);

    /**
     * 修改订单管理
     *
     * @param orderCamera 订单管理
     * @return 结果
     */
    public int updateOrderCamera(OrderCamera orderCamera);

    /**
     * 批量删除订单管理
     *
     * @param orderIds 需要删除的订单管理主键集合
     * @return 结果
     */
    public int deleteOrderCameraByOrderIds(String[] orderIds);

    /**
     * 删除订单管理信息
     *
     * @param orderId 订单管理主键
     * @return 结果
     */
    public int deleteOrderCameraByOrderId(String orderId);

    public void cancelOrder(OrderCamera order);

    public Result updateStatus(OrderCamera orderCamera);

    public Result refunds(OrderCamera order);

    Result createOrderHL(OrderCamera order);

    Result createOrderCash(OrderCamera order);

    /**
     * 修改订单状态
     *
     * @param orderId
     * @param type
     * @return
     */
    int updateOrderCameraTypeById(String orderId, Integer type, Integer modelId);


    /**
     * 汇联退款接口
     *
     * @param orderCamera
     */
    AjaxResult refundsHL(OrderCamera orderCamera) throws Exception;

    Result refundsQuery(OrderCamera orderCamera) throws Exception;


    Long getHistoryTotalIncome(Long userId);

    Long countMonthIncome(Long userId,String startTime,String endTime);

    /**
     * 获取移动端数据统计
     *
     * @param merchantId 商户id
     * @param user       登录用户
     * @param dateFilter 日期过滤
     * @return 移动端首页数据
     * @
     */
    DatStatisticsMobileDTO getDataStatisticsMobile(String merchantId, LoginUser user, Integer dateFilter);

    /**
     * 获取移动端首页数据
     *
     * @param user     登录用户
     * @param deviceId 设备id
     * @return 移动端首页数据
     */
    IndexMobileRespDTO getIndexMobile(LoginUser user, String deviceId);
}
