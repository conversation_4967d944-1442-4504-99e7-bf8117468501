package com.ruoyi.order.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;

/**
 * 订单更新请求对象
 * 
 * <AUTHOR>
 * @date 2025-01-30
 */
@Data
public class OrderUpdateRequest {

    /** 订单ID */
    @NotBlank(message = "订单ID不能为空")
    private String orderId;

    /** 设备名称 */
    private String deviceName;

    /** 用户手机号 */
    private String phone;

    /** 优惠券码 */
    private String voucherCode;

    /** 备注信息 */
    private String remark;

    /** 订单状态 0-未支付 1-已支付 2-已取消 3-已退款 4-已完成 */
    private Integer orderStatus;

    /** 任务更新列表 */
    private List<TaskUpdateInfo> taskUpdates;

    /**
     * 任务更新信息
     */
    @Data
    public static class TaskUpdateInfo {
        
        /** 任务ID */
        @NotBlank(message = "任务ID不能为空")
        private String taskId;

        /** 打印份数 */
        private Integer copies;

        /** 颜色模式 0-黑白 1-彩色 */
        private Integer colorMode;

        /** 双面模式 0-单面 1-双面 */
        private Integer duplexMode;

        /** 纸张类型 1-A4 2-A5 3-照片纸 */
        private Integer paperType;

        /** 页码范围 */
        private String pageRange;
    }
}
