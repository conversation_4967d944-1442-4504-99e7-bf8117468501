package com.ruoyi.web.controller.video;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.oss.ALY_OSS;
import com.ruoyi.device.domain.DeviceCamera;
import com.ruoyi.dto.Result;
import com.ruoyi.order.domain.OrderCamera;
import com.ruoyi.order.service.IOrderCameraService;
import com.ruoyi.photo.domain.UserPhoto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

/**
 * photoController
 *
 * <AUTHOR>
 * @date 2023-09-05
 */
@RestController
@RequestMapping("/video")
public class VideoController {

    @PostMapping("/upload")
    public AjaxResult upload(@RequestParam(value = "video") MultipartFile video,
                           @RequestParam(value = "background") MultipartFile background,
                           @RequestParam(value = "orderId") String orderId) {
        if (video != null && background != null) {
            String videoName = "video/sfs/" + orderId + ".mp4";
            String url = ALY_OSS.uploadImage(video, videoName);
            url = url.substring(0, url.indexOf("?"));

            String imageName = "video/sfs/" + orderId+"-back" + ".jpg";
            String url1 = ALY_OSS.uploadImage(background, imageName);
            url1 = url1.substring(0, url1.indexOf("?"));

            List<String> urls = new ArrayList<>();

            urls.add(url);
            urls.add(url1);

            return AjaxResult.success("上传成功",urls);
        } else {
            return AjaxResult.error("请添加照片");
        }
    }

}
