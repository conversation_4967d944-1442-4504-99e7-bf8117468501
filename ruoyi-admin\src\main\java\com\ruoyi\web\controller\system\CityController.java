package com.ruoyi.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.City;
import com.ruoyi.system.service.ICityService;
import com.ruoyi.common.utils.poi.ExcelUtil;

/**
 * 城市信息Controller
 * 
 * <AUTHOR>
 * @date 2023-07-14
 */
@RestController
@RequestMapping("/system/city")
public class CityController extends BaseController
{
    @Autowired
    private ICityService cityService;

    /**
     * 查询城市信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:city:list')")
    @GetMapping("/list")
    public AjaxResult list(City city)
    {
        List<City> list = cityService.selectCityList(city);
        return success(list);
    }

    /**
     * 导出城市信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:city:export')")
    @Log(title = "城市信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, City city)
    {
        List<City> list = cityService.selectCityList(city);
        ExcelUtil<City> util = new ExcelUtil<City>(City.class);
        util.exportExcel(response, list, "城市信息数据");
    }

    /**
     * 获取城市信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:city:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(cityService.selectCityById(id));
    }

    /**
     * 新增城市信息
     */
    @PreAuthorize("@ss.hasPermi('system:city:add')")
    @Log(title = "城市信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody City city)
    {
        return toAjax(cityService.insertCity(city));
    }

    /**
     * 修改城市信息
     */
    @PreAuthorize("@ss.hasPermi('system:city:edit')")
    @Log(title = "城市信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody City city)
    {
        return toAjax(cityService.updateCity(city));
    }

    /**
     * 删除城市信息
     */
    @PreAuthorize("@ss.hasPermi('system:city:remove')")
    @Log(title = "城市信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(cityService.deleteCityByIds(ids));
    }


    /**
     * 查询城市信息
     */
	@GetMapping("/query")
    public AjaxResult queryByParent(@RequestParam String value)
    {
        return success(cityService.queryCityByValue(value));
    }


}
