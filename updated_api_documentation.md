# API接口文档

## 目录

1. [系统登录接口](#1-系统登录接口)
2. [微信小程序接口](#2-微信小程序接口)
3. [设备管理接口](#3-设备管理接口)
4. [照片管理接口](#4-照片管理接口)
5. [打印相关接口](#5-打印相关接口)
6. [订单管理接口](#6-订单管理接口)
7. [WebSocket通信](#7-websocket通信)
8. [意见反馈接口](#8-意见反馈接口)
9. [咨询服务接口](#9-咨询服务接口)
10. [支付相关接口](#10-支付相关接口)
11. [错误码说明](#11-错误码说明)
12. [数据结构](#12-数据结构)
13. [公共接口](#13-公共接口)
14. [代金券接口](#14-代金券接口)
15. [美颜接口](#15-美颜接口)
16. [视频接口](#16-视频接口)
17. [台湾AWS接口](#17-台湾aws接口)
18. [其他支付方式接口](#18-其他支付方式接口)

## 1. 系统登录接口

### 1.1 系统用户登录

**接口地址**：`http://[ip]:8081/login`

**请求方式**：POST

**接口说明**：系统用户登录接口

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| username | String | 是 | 用户名 |
| password | String | 是 | 密码 |
| code | String | 否 | 验证码 |
| uuid | String | 否 | 验证码标识 |

**请求示例**：
```json
{
    "username": "admin",
    "password": "admin123",
    "code": "1234",
    "uuid": "xxxxxxxx"
}
```

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| token | String | 认证token |

**响应示例**：
```json
{
    "code": 200,
    "msg": "操作成功",
    "token": "eyJhbGciOiJIUzUxMiJ9..."
}
```

### 1.2 微信小程序登录

**接口地址**：`http://[ip]:8081/client/wxapp/user/login_or_register`

**请求方式**：POST

**接口说明**：微信小程序用户登录或注册

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| code | String | 是 | 微信登录临时凭证 |

**请求示例**：
```json
{
    "code": "xxxxxx"
}
```

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | Object | 返回数据 |
| data.userInfo | Object | 用户信息 |
| data.token | String | 认证token |

**响应示例**：
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "userInfo": {
            "id": 1,
            "nickname": "用户12345",
            "avatar": "/profile/default/avatar.jpg",
            "shotNum": 0
        },
        "token": "eyJhbGciOiJIUzUxMiJ9..."
    }
}
```

### 1.3 微信登录

**接口地址**：`http://[ip]:8081/wx/user/login`

**请求方式**：GET

**接口说明**：微信登录接口

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| jsCode | String | 是 | 微信登录临时凭证 |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | Object | 微信返回的用户信息 |

**响应示例**：
```json
{
    "code": 200,
    "msg": "请求成功",
    "data": {
        "openid": "xxxxxxxxxxxxxxx",
        "session_key": "xxxxxxxxxxxxxxx"
    }
}
```

### 1.4 设备登录

**接口地址**：`http://[ip]:8081/device/device_camera/loginDevice`

**请求方式**：GET

**接口说明**：设备后台登录接口

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| deviceId | String | 是 | 设备ID |
| password | String | 是 | 密码 |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | Object | 空 |

**响应示例**：
```json
{
    "code": 200,
    "msg": "登录成功",
    "data": null
}
```

## 2. 微信小程序接口

### 2.1 获取用户心愿单列表

**接口地址**：`http://[ip]:8081/client/wxapp/wishlist/list`

**请求方式**：GET

**接口说明**：获取当前微信用户的心愿单列表

**请求头**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| Authorization | String | 是 | 认证token |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | Array | 心愿单列表 |

### 2.2 添加心愿单

**接口地址**：`http://[ip]:8081/client/wxapp/wishlist/add`

**请求方式**：POST

**接口说明**：添加商品到心愿单

**请求头**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| Authorization | String | 是 | 认证token |

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| goodsId | Long | 是 | 商品ID |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | Boolean | 是否成功 |

### 2.3 获取积分信息

**接口地址**：`http://[ip]:8081/client/wxapp/point/info`

**请求方式**：GET

**接口说明**：获取用户积分信息

**请求头**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| Authorization | String | 是 | 认证token |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | Object | 积分信息 |

### 2.4 获取公共信息

**接口地址**：`http://[ip]:8081/client/wxapp/common/info`

**请求方式**：GET

**接口说明**：获取小程序公共配置信息

**请求头**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| Authorization | String | 否 | 认证token |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | Object | 配置信息 | 

## 3. 设备管理接口

### 3.1 获取设备列表

**接口地址**：`http://[ip]:8081/device/device_camera/list`

**请求方式**：GET

**接口说明**：获取设备列表

**请求头**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| Authorization | String | 是 | 认证token |

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| pageNum | Integer | 是 | 页码 |
| pageSize | Integer | 是 | 每页大小 |
| deviceName | String | 否 | 设备名称，模糊查询 |
| deviceId | String | 否 | 设备ID |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| total | Integer | 总记录数 |
| rows | Array | 设备列表 |

**响应示例**：
```json
{
    "code": 200,
    "msg": "操作成功",
    "total": 10,
    "rows": [
        {
            "deviceId": "device123",
            "deviceName": "设备1",
            "deviceStatus": 0,
            "printerStatus": 0,
            "isOnLine": "在线"
        }
    ]
}
```

### 3.2 获取设备详情

**接口地址**：`http://[ip]:8081/device/device_camera/byId`

**请求方式**：GET

**接口说明**：根据设备ID获取设备详情

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| deviceId | String | 是 | 设备ID |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | Object | 设备详情 |

**响应示例**：
```json
{
    "code": 200,
    "msg": "查询成功",
    "data": {
        "deviceId": "device123",
        "deviceName": "设备1",
        "deviceStatus": 0,
        "printerStatus": 0,
        "cameraStatus": 0,
        "beautyStatus": 0,
        "consumables": 100,
        "paperConsumables": 100
    }
}
```

### 3.3 添加设备

**接口地址**：`http://[ip]:8081/device/device_camera`

**请求方式**：POST

**接口说明**：添加新设备

**请求头**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| Authorization | String | 是 | 认证token |

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| deviceId | String | 是 | 设备ID |
| deviceName | String | 是 | 设备名称 |
| softwareId | Long | 否 | 软件ID |
| detailAddress | String | 否 | 详细地址 |
| deviceStatus | Integer | 否 | 设备状态 |
| userId | Long | 否 | 用户ID |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |

### 3.4 更新设备状态

**接口地址**：`http://[ip]:8081/device/device_camera/updateDeviceStatus`

**请求方式**：GET

**接口说明**：更新设备和打印机状态

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| deviceId | String | 是 | 设备ID |
| printerStatus | String | 是 | 打印机状态 |
| cameraStatus | int | 是 | 相机状态 |
| beautyStatus | int | 是 | 美颜服务状态 |
| consumables | int | 是 | 色彩余量 |
| paperConsumables | int | 否 | 相纸余量 |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | Object | 空 |

**响应示例**：
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": null
}
```

### 3.5 控制设备

**接口地址**：`http://[ip]:8081/device/device_camera/control`

**请求方式**：GET

**接口说明**：发送控制命令到设备

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| deviceId | String | 是 | 设备ID |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | Integer | 成功数量 |

### 3.6 重启设备

**接口地址**：`http://[ip]:8081/device/device_camera/reboot`

**请求方式**：GET

**接口说明**：重启指定设备

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| deviceId | String | 是 | 设备ID |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | Integer | 成功数量 |

### 3.7 获取用户所拥有的设备

**接口地址**：`http://[ip]:8081/device/device_camera/getDeviceByUser`

**请求方式**：GET

**接口说明**：获取当前用户所拥有的设备列表

**请求头**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| Authorization | String | 是 | 认证token |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | Array | 设备列表 |

**响应示例**：
```json
{
    "code": 200,
    "msg": "1",
    "data": [
        {
            "deviceId": "device001",
            "deviceName": "一号机器",
            "deviceStatus": 0,
            "printerStatus": 0,
            "cameraStatus": 0,
            "beautyStatus": 0,
            "consumables": 80,
            "paperConsumables": 90,
            "detailAddress": "北京市朝阳区xxx大厦一层"
        }
    ]
}
``` 

## 4. 照片管理接口

### 4.1 上传照片（文件方式）

**接口地址**：`http://[ip]:8081/photo/photo/upload`

**请求方式**：POST

**接口说明**：上传照片文件用于打印

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| file | MultipartFile | 是 | 要上传的照片文件 |
| deviceId | String | 是 | 设备ID |
| openId | String | 否 | 微信用户openId |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | String | 时间戳 |

**响应示例**：
```json
{
    "code": 200,
    "msg": "上传成功",
    "data": "1625123456789"
}
```

### 4.2 上传照片（Base64方式）

**接口地址**：`http://[ip]:8081/photo/photo/upload_base64`

**请求方式**：POST

**接口说明**：上传Base64编码的照片用于打印

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| file_base64 | String | 是 | Base64编码的照片数据 |
| deviceId | String | 是 | 设备ID |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | String | 空 |

**响应示例**：
```json
{
    "code": 200,
    "msg": "上传成功",
    "data": ""
}
```

### 4.3 批量上传照片（Base64方式）

**接口地址**：`http://[ip]:8081/photo/photo/uploadPhoto`

**请求方式**：POST

**接口说明**：批量上传Base64编码的照片用于打印

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| base64s | String[] | 是 | Base64编码的照片数据数组 |
| deviceId | String | 是 | 设备ID |
| limit | Integer | 否 | 最大上传数量，默认为1 |

**请求示例**：
```json
{
    "base64s": ["data:image/jpeg;base64,/9j/4AAQSkZJRg...", "data:image/jpeg;base64,/9j/4AAQSkZJRg..."],
    "deviceId": "device123",
    "limit": 5
}
```

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | Integer | 上传总数 |

**响应示例**：
```json
{
    "code": 200,
    "msg": "已经上传：2张,失败0张",
    "data": 2
}
```

### 4.4 以色列版本照片上传

**接口地址**：`http://[ip]:8081/photo/photo/uploadPhoto_Israel`

**请求方式**：POST

**接口说明**：以色列版本的批量上传Base64编码的照片用于打印

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| base64s | String[] | 是 | Base64编码的照片数据数组 |
| deviceId | String | 是 | 设备ID |
| limit | Integer | 否 | 最大上传数量，默认为1 |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | Integer | 上传总数 |

### 4.5 查询已上传照片

**接口地址**：`http://[ip]:8081/photo/photo/check_uploads`

**请求方式**：GET

**接口说明**：查询已上传的照片

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| deviceId | String | 是 | 设备ID |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | List<String> | 照片Base64数据列表 |

### 4.6 删除已上传照片

**接口地址**：`http://[ip]:8081/photo/photo/delete_uploads`

**请求方式**：GET

**接口说明**：删除已上传的照片

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| deviceId | String | 是 | 设备ID |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | Object | 空 |

### 4.7 通过订单ID获取照片

**接口地址**：`http://[ip]:8081/photo/photo/getPhotoByOrderId`

**请求方式**：GET

**接口说明**：根据订单ID获取照片详情

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| orderId | String | 是 | 订单ID |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | Object | 照片信息 |

**响应示例**：
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "id": 1,
        "url": "http://example.com/images/photo1.jpg",
        "type": 1,
        "orderId": "order123456",
        "deviceId": "device001",
        "createTime": "2023-06-01 12:00:00"
    }
}
``` 

## 5. 打印相关接口

### 5.1 远程打印照片

**接口地址**：`http://[ip]:8081/device/device_camera/printPhoto`

**请求方式**：GET

**接口说明**：根据照片ID远程打印照片

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| deviceId | String | 是 | 设备ID |
| photoId | Long | 是 | 照片ID |
| num | Integer | 是 | 打印数量 |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | Integer | 成功数量 |

**响应示例**：
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": 1
}
```

### 5.2 设备间远程打印

**接口地址**：`http://[ip]:8081/device/device_camera/devicePrintPhoto`

**请求方式**：GET

**接口说明**：一个设备向另一个设备发送打印命令

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| deviceId | String | 是 | 目标设备ID |
| myDeviceId | String | 是 | 当前设备ID |
| photoUrl | String | 是 | 照片URL |
| num | Integer | 是 | 打印数量 |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | Integer | 成功数量 |

**响应示例**：
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": 1
}
``` 

## 6. 订单管理接口

### 6.1 微信支付预创建订单

**接口地址**：`http://[ip]:8081/order/camera_order/preOrder`

**请求方式**：GET

**接口说明**：创建微信支付预订单

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| deviceId | String | 是 | 设备ID |
| pay | Integer | 是 | 支付金额（单位：分） |
| productQuantity | Integer | 是 | 商品数量 |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | Object | 订单信息 |
| time | String | 时间戳 |

**响应示例**：
```json
{
    "code": 200,
    "msg": "请求成功",
    "time": "1625123456789",
    "data": {
        "codeUrl": "weixin://wxpay/bizpayurl?pr=xxxxxxxxxxxx",
        "orderId": "202306010001"
    }
}
```

### 6.2 点券充值订单预创建

**接口地址**：`http://[ip]:8081/order/camera_order/preStampsOrder`

**请求方式**：GET

**接口说明**：创建点券充值预订单

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| stamps | Integer | 是 | 点券数量 |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| time | String | 时间戳 |
| data | Object | 订单信息 |

### 6.3 汇联支付订单创建

**接口地址**：`http://[ip]:8081/order/camera_order/create`

**请求方式**：GET

**接口说明**：创建汇联支付订单

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| deviceId | String | 是 | 设备ID |
| orderId | String | 是 | 订单ID |
| totalAmount | Long | 是 | 订单金额（单位：分） |
| photoType | Integer | 是 | 照片类型 |
| body | String | 是 | 商品描述 |
| productQuantity | Long | 否 | 商品数量 |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| time | String | 时间戳 |
| data | Object | 订单信息 |

### 6.4 现金订单创建

**接口地址**：`http://[ip]:8081/order/camera_order/cash`

**请求方式**：GET

**接口说明**：创建现金支付订单

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| deviceId | String | 是 | 设备ID |
| pay | Integer | 是 | 支付金额（单位：分） |
| orderId | String | 是 | 订单ID |
| photoType | Integer | 是 | 照片类型 |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| time | String | 时间戳 |
| data | Object | 订单信息 |

### 6.5 查询支付状态

**接口地址**：`http://[ip]:8081/order/camera_order/payStatus`

**请求方式**：GET

**接口说明**：查询订单支付状态

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| orderId | String | 是 | 订单ID |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| time | String | 时间戳 |
| data | Long | 订单状态（0-未支付，1-已支付，2-已取消） |

**响应示例**：
```json
{
    "code": 200,
    "msg": "订单已支付",
    "time": "1625123456789",
    "data": 1
}
```

### 6.6 订单退款

**接口地址**：`http://[ip]:8081/order/camera_order/orderRefund`

**请求方式**：POST

**接口说明**：申请订单退款

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| orderId | String | 是 | 订单ID |
| reason | String | 否 | 退款原因 |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | Object | 退款结果 |

### 6.7 退款查询

**接口地址**：`http://[ip]:8081/order/camera_order/refundQuery`

**请求方式**：POST

**接口说明**：查询退款状态

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| orderId | String | 是 | 订单ID |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | Object | 退款结果 | 

## 7. WebSocket通信

### 7.1 WebSocket连接

**连接地址**：`ws://[ip]:8081/WebSocketServer/{sid}`

**说明**：建立与设备的WebSocket连接，sid为设备ID

### 7.2 打印命令格式

打印命令通过WebSocket发送，格式为：`print,照片URL,打印数量`

**示例**：`print,https://example.com/photo.jpg,1`

### 7.3 设备状态更新

设备状态更新通过WebSocket发送，包含以下字段：
- `printerStatus`: 打印机状态
- `cameraStatus`: 相机状态
- `beautyStatus`: 美颜服务状态
- `consumables`: 色彩余量
- `paperConsumables`: 相纸余量

### 7.4 支付成功通知

支付成功通知通过WebSocket发送，格式为：`zfcg`

### 7.5 扫码成功通知

扫码成功通知通过WebSocket发送，格式为：`smcg`

## 8. 意见反馈接口

### 8.1 添加意见反馈

**接口地址**：`http://[ip]:8081/client/wxapp/feedback/add`

**请求方式**：POST

**接口说明**：提交用户意见反馈

**请求头**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| Authorization | String | 是 | 认证token |

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| content | String | 是 | 反馈内容 |
| contactInfo | String | 否 | 联系方式 |
| images | String[] | 否 | 图片URL数组 |

**请求示例**：
```json
{
    "content": "使用过程中遇到了打印机卡纸问题",
    "contactInfo": "13800138000",
    "images": ["http://example.com/image1.jpg", "http://example.com/image2.jpg"]
}
```

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | Boolean | 是否成功 |

**响应示例**：
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": true
}
```

### 8.2 获取意见反馈列表

**接口地址**：`http://[ip]:8081/wxapp/feedback/list`

**请求方式**：GET

**接口说明**：管理员获取意见反馈列表

**请求头**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| Authorization | String | 是 | 认证token |

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| pageNum | Integer | 是 | 页码 |
| pageSize | Integer | 是 | 每页大小 |
| status | Integer | 否 | 状态（0-未处理，1-已处理） |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| total | Integer | 总记录数 |
| rows | Array | 反馈列表 |

**响应示例**：
```json
{
    "code": 200,
    "msg": "操作成功",
    "total": 10,
    "rows": [
        {
            "id": 1,
            "userId": 100,
            "content": "使用过程中遇到了打印机卡纸问题",
            "contactInfo": "13800138000",
            "status": 0,
            "createTime": "2023-06-01 12:00:00"
        }
    ]
}
``` 

## 9. 咨询服务接口

### 9.1 添加咨询

**接口地址**：`http://[ip]:8081/client/wxapp/consultation/add`

**请求方式**：POST

**接口说明**：提交用户咨询

**请求头**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| Authorization | String | 是 | 认证token |

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| title | String | 是 | 咨询标题 |
| content | String | 是 | 咨询内容 |
| contactInfo | String | 是 | 联系方式 |
| type | Integer | 否 | 咨询类型（1-产品咨询，2-技术支持，3-其他） |

**请求示例**：
```json
{
    "title": "关于打印机使用问题",
    "content": "我想了解如何调整打印机的色彩设置",
    "contactInfo": "13800138000",
    "type": 2
}
```

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | Boolean | 是否成功 |

**响应示例**：
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": true
}
```

### 9.2 获取咨询列表

**接口地址**：`http://[ip]:8081/client/wxapp/consultation/list`

**请求方式**：GET

**接口说明**：获取当前用户的咨询列表

**请求头**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| Authorization | String | 是 | 认证token |

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| pageNum | Integer | 否 | 页码，默认1 |
| pageSize | Integer | 否 | 每页大小，默认10 |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | Object | 返回数据 |
| data.total | Integer | 总记录数 |
| data.list | Array | 咨询列表 |

**响应示例**：
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "total": 2,
        "list": [
            {
                "id": 1,
                "title": "关于打印机使用问题",
                "content": "我想了解如何调整打印机的色彩设置",
                "status": 0,
                "replyContent": null,
                "createTime": "2023-06-01 12:00:00"
            }
        ]
    }
}
```

### 9.3 获取咨询详情

**接口地址**：`http://[ip]:8081/client/wxapp/consultation/detail`

**请求方式**：GET

**接口说明**：获取咨询详情

**请求头**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| Authorization | String | 是 | 认证token |

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| id | Long | 是 | 咨询ID |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | Object | 咨询详情 |

**响应示例**：
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "id": 1,
        "title": "关于打印机使用问题",
        "content": "我想了解如何调整打印机的色彩设置",
        "contactInfo": "13800138000",
        "type": 2,
        "status": 1,
        "replyContent": "您可以通过设备上的设置菜单进行调整，具体步骤如下...",
        "createTime": "2023-06-01 12:00:00",
        "replyTime": "2023-06-02 10:30:00"
    }
}
``` 

## 10. 支付相关接口

### 10.1 微信支付回调

**接口地址**：`http://[ip]:8081/pay/jsPay`

**请求方式**：POST

**接口说明**：微信支付回调接口，由微信支付服务器调用

**请求参数**：微信支付回调参数（XML格式）

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| return_code | String | 返回状态码 |
| return_msg | String | 返回信息 |

**响应示例**：
```xml
<xml>
  <return_code><![CDATA[SUCCESS]]></return_code>
  <return_msg><![CDATA[OK]]></return_msg>
</xml>
```

### 10.2 获取OpenID

**接口地址**：`http://[ip]:8081/pay/getOpenId`

**请求方式**：GET

**接口说明**：获取微信用户OpenID并跳转到支付页面

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| attach | String | 是 | 订单ID |
| userInfo | String | 是 | 用户信息JSON字符串 |

**响应**：重定向到支付页面

### 10.3 发起JS支付

**接口地址**：`http://[ip]:8081/pay/jsPay`

**请求方式**：POST

**接口说明**：发起微信JS支付

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| totalAmount | int | 是 | 支付金额（单位：分） |
| openId | String | 是 | 用户OpenID |
| userId | String | 是 | 用户ID |
| outTradeNo | String | 是 | 订单号 |
| mchid | String | 是 | 商户号 |

**响应参数**：微信支付参数JSON字符串

### 10.4 乐摇摇支付创建订单

**接口地址**：`http://[ip]:8081/LYYPay/createOrder`

**请求方式**：GET

**接口说明**：创建乐摇摇支付订单

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| deviceId | String | 是 | 设备ID |
| orderId | String | 是 | 订单ID |
| paymentAmount | int | 是 | 支付金额（单位：分） |
| productAbilityCode | String | 是 | 产品能力代码 |
| areaCode | String | 是 | 地区代码 |
| photoType | Integer | 是 | 照片类型 |
| productDescription | String | 是 | 商品描述 |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | Object | 乐摇摇支付响应 |

### 10.5 乐摇摇支付回调

**接口地址**：`http://[ip]:8081/LYYPay/notice`

**请求方式**：POST

**接口说明**：乐摇摇支付回调接口

**请求参数**：乐摇摇支付回调参数

**响应参数**：成功标识字符串 

## 11. 错误码说明

| 错误码 | 说明 |
| ------ | ---- |
| 200 | 成功 |
| 301 | 设备号错误 |
| 401 | 未登录 |
| 500 | 服务器内部错误 |
| 601 | 设备ID不能为空 |
| 602 | 设备ID不存在 |
| 603 | 超过最大上传数量 |

## 12. 数据结构

### 12.1 订单状态

订单状态包含以下值：
- `0`: 未支付
- `1`: 已支付
- `2`: 已取消
- `3`: 已退款
- `4`: 已打印
- `5`: 退款中
- `6`: 退款失败

### 12.2 照片类型

照片类型包含以下值：
- `1`: 大头贴
- `2`: 写真
- `3`: 证件照
- `4`: 上传打印
- `5`: 漫画风
- `6`: 手翻书
- `7`: 换背景，合拍
- `10`: 加印
- `100`: AI
- `101`: 点券充值 

## 13. 公共接口

### 13.1 获取首页设置

**接口地址**：`http://[ip]:8081/client/wxapp/common/home_set`

**请求方式**：GET

**接口说明**：获取小程序首页设置信息

**请求头**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| Authorization | String | 否 | 认证token |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | Object | 首页设置信息 |

### 13.2 获取轮播图列表

**接口地址**：`http://[ip]:8081/client/wxapp/common/carousel_list`

**请求方式**：GET

**接口说明**：获取轮播图列表

**请求头**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| Authorization | String | 否 | 认证token |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | Array | 轮播图列表 |

### 13.3 获取用户协议

**接口地址**：`http://[ip]:8081/client/wxapp/common/user_agreement`

**请求方式**：GET

**接口说明**：获取用户协议内容

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | Object | 用户协议内容 |

### 13.4 获取隐私政策

**接口地址**：`http://[ip]:8081/client/wxapp/common/privacy_policy`

**请求方式**：GET

**接口说明**：获取隐私政策内容

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | Object | 隐私政策内容 |

### 13.5 获取相框列表

**接口地址**：`http://[ip]:8081/client/wxapp/common/frame_list`

**请求方式**：GET

**接口说明**：获取相框列表

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | Array | 相框列表 |

### 13.6 获取分组相框列表

**接口地址**：`http://[ip]:8081/client/wxapp/common/frame_list_group`

**请求方式**：GET

**接口说明**：获取按系列分组的相框列表

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | Array | 分组相框列表 |

## 14. 代金券接口

### 14.1 使用代金券

**接口地址**：`http://[ip]:8081/voucher/voucher_order/wipedOut`

**请求方式**：GET

**接口说明**：使用代金券

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| voucherCode | String | 是 | 代金券代码 |
| deviceId | String | 是 | 设备ID |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | Object | 使用结果 |

### 14.2 检查代金券

**接口地址**：`http://[ip]:8081/voucher/voucher_order/check_DaiJinQuan`

**请求方式**：GET

**接口说明**：检查代金券是否有效

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| voucherCode | String | 是 | 代金券代码 |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | Object | 代金券信息 |

## 15. 美颜接口

### 15.1 美颜设备登录

**接口地址**：`http://[ip]:8081/beauty/beauty/devLogin`

**请求方式**：GET

**接口说明**：美颜设备登录

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| deviceId | String | 是 | 设备ID |
| password | String | 是 | 密码 |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | Object | 登录结果 |

### 15.2 获取美颜值列表

**接口地址**：`http://[ip]:8081/beauty/value/list`

**请求方式**：GET

**接口说明**：获取美颜值列表

**请求头**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| Authorization | String | 是 | 认证token |

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| pageNum | Integer | 是 | 页码 |
| pageSize | Integer | 是 | 每页大小 |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| total | Integer | 总记录数 |
| rows | Array | 美颜值列表 |

## 16. 视频接口

### 16.1 上传视频

**接口地址**：`http://[ip]:8081/video/upload`

**请求方式**：POST

**接口说明**：上传视频文件

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| file | MultipartFile | 是 | 视频文件 |
| deviceId | String | 是 | 设备ID |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | String | 视频URL |

## 17. 台湾AWS接口

### 17.1 上传文件到AWS

**接口地址**：`http://[ip]:8081/taiwan/aws/uploadFiles`

**请求方式**：POST

**接口说明**：上传文件到AWS存储

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| file | MultipartFile | 是 | 要上传的文件 |
| fileName | String | 是 | 文件名 |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | String | 文件URL |

### 17.2 保存AWS文件信息

**接口地址**：`http://[ip]:8081/taiwan/aws/save`

**请求方式**：POST

**接口说明**：保存AWS文件信息

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| url | String | 是 | 文件URL |
| fileName | String | 是 | 文件名 |
| fileSize | Long | 是 | 文件大小 |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | Object | 保存结果 | 

## 18. 其他支付方式接口

### 18.1 WSS支付

#### 18.1.1 创建支付码

**接口地址**：`http://[ip]:8081/WSSPay/createPayCode`

**请求方式**：GET

**接口说明**：创建WSS支付码

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| deviceId | String | 是 | 设备ID |
| orderId | String | 是 | 订单ID |
| totalAmount | int | 是 | 支付金额（单位：分） |
| photoType | int | 是 | 照片类型 |
| body | String | 是 | 商品描述 |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | Object | 支付码信息 |

#### 18.1.2 获取支付状态

**接口地址**：`http://[ip]:8081/WSSPay/getPayStatus`

**请求方式**：GET

**接口说明**：获取WSS支付状态

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| orderId | String | 是 | 订单ID |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | Object | 支付状态 |

### 18.2 MAYA支付

#### 18.2.1 创建支付码

**接口地址**：`http://[ip]:8081/MAYAPay/createPayCode`

**请求方式**：GET

**接口说明**：创建MAYA支付码

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| deviceId | String | 是 | 设备ID |
| orderId | String | 是 | 订单ID |
| totalAmount | int | 是 | 支付金额（单位：分） |
| photoType | int | 是 | 照片类型 |
| body | String | 是 | 商品描述 |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | Object | 支付码信息 |

#### 18.2.2 获取支付状态

**接口地址**：`http://[ip]:8081/MAYAPay/getPayStatus`

**请求方式**：GET

**接口说明**：获取MAYA支付状态

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| orderId | String | 是 | 订单ID |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | Object | 支付状态 |

### 18.3 VITE二维码支付

#### 18.3.1 创建支付码

**接口地址**：`http://[ip]:8081/vqr/createPayCode`

**请求方式**：GET

**接口说明**：创建VITE二维码支付码

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| deviceId | String | 是 | 设备ID |
| orderId | String | 是 | 订单ID |
| totalAmount | int | 是 | 支付金额（单位：分） |
| photoType | int | 是 | 照片类型 |
| body | String | 是 | 商品描述 |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | Object | 支付码信息 |

#### 18.3.2 获取支付状态

**接口地址**：`http://[ip]:8081/vqr/getPayStatus`

**请求方式**：GET

**接口说明**：获取VITE二维码支付状态

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| orderId | String | 是 | 订单ID |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | Object | 支付状态 |

### 18.4 MQTT支付

#### 18.4.1 获取支付状态

**接口地址**：`http://[ip]:8081/MQTTPay/getPayStatus`

**请求方式**：GET

**接口说明**：获取MQTT支付状态

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| orderId | String | 是 | 订单ID |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | Object | 支付状态 |

### 18.5 Ezetap支付

#### 18.5.1 创建支付码

**接口地址**：`http://[ip]:8081/EzetapPay/createPayCode`

**请求方式**：GET

**接口说明**：创建Ezetap支付码

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| deviceId | String | 是 | 设备ID |
| orderId | String | 是 | 订单ID |
| totalAmount | int | 是 | 支付金额（单位：分） |
| photoType | int | 是 | 照片类型 |
| body | String | 是 | 商品描述 |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | Object | 支付码信息 |

#### 18.5.2 获取支付状态

**接口地址**：`http://[ip]:8081/EzetapPay/getPayStatus`

**请求方式**：GET

**接口说明**：获取Ezetap支付状态

**请求参数**：

| 参数名 | 类型 | 是否必须 | 说明 |
| ------ | ---- | -------- | ---- |
| orderId | String | 是 | 订单ID |

**响应参数**：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| data | Object | 支付状态 | 