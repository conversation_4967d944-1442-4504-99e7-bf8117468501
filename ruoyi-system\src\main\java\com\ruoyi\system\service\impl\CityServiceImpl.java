package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.CityMapper;
import com.ruoyi.system.domain.City;
import com.ruoyi.system.service.ICityService;

/**
 * 城市信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-07-14
 */
@Service
public class CityServiceImpl implements ICityService 
{
    @Autowired
    private CityMapper cityMapper;

    /**
     * 查询城市信息
     * 
     * @param id 城市信息主键
     * @return 城市信息
     */
    @Override
    public City selectCityById(Long id)
    {
        return cityMapper.selectCityById(id);
    }

    /**
     * 查询城市信息列表
     * 
     * @param city 城市信息
     * @return 城市信息
     */
    @Override
    public List<City> selectCityList(City city)
    {
        return cityMapper.selectCityList(city);
    }

    /**
     * 新增城市信息
     * 
     * @param city 城市信息
     * @return 结果
     */
    @Override
    public int insertCity(City city)
    {
        return cityMapper.insertCity(city);
    }

    /**
     * 修改城市信息
     * 
     * @param city 城市信息
     * @return 结果
     */
    @Override
    public int updateCity(City city)
    {
        return cityMapper.updateCity(city);
    }

    /**
     * 批量删除城市信息
     * 
     * @param ids 需要删除的城市信息主键
     * @return 结果
     */
    @Override
    public int deleteCityByIds(Long[] ids)
    {
        return cityMapper.deleteCityByIds(ids);
    }

    /**
     * 删除城市信息信息
     * 
     * @param id 城市信息主键
     * @return 结果
     */
    @Override
    public int deleteCityById(Long id)
    {
        return cityMapper.deleteCityById(id);
    }

    /**
     * 查询城市信息
     *
     * @param value 城市信息父id
     * @return 结果
     */
    @Override
    public List<City> queryCityByValue(String value) {
        System.out.println(value);
        City city = new City();
        city.setParent(value);
        return cityMapper.selectCityList(city);
    }

    @Override
    public List<City> queryProvinceByValue(String province) {
        return cityMapper.queryProvinceByValue(province);
    }

    @Override
    public List<City> queryCityByProvince(String province, String city) {
        return cityMapper.queryCityByProvince(province,city);
    }

    @Override
    public List<City> queryDistrictByCity(String city, String district) {
        return cityMapper.queryDistrictByCity(city,district);
    }
}
