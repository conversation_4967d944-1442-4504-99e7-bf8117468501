package com.ruoyi.web.controller.fee;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.fee.domain.Fee;
import com.ruoyi.fee.service.IFeeService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 费用Controller
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */
@RestController
@RequestMapping("/fee/fee")
public class FeeController extends BaseController
{
    @Autowired
    private IFeeService feeService;

    /**
     * 查询费用列表
     */
    @PreAuthorize("@ss.hasPermi('fee:fee:list')")
    @GetMapping("/list")
    public TableDataInfo list(Fee fee)
    {
        startPage();
        List<Fee> list = feeService.selectFeeList(fee);
        return getDataTable(list);
    }

    /**
     * 导出费用列表
     */
    @PreAuthorize("@ss.hasPermi('fee:fee:export')")
    @Log(title = "费用", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Fee fee)
    {
        List<Fee> list = feeService.selectFeeList(fee);
        ExcelUtil<Fee> util = new ExcelUtil<Fee>(Fee.class);
        util.exportExcel(response, list, "费用数据");
    }

    /**
     * 获取费用详细信息
     */
    @PreAuthorize("@ss.hasPermi('fee:fee:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(feeService.selectFeeById(id));
    }

    /**
     * 新增费用
     */
    @PreAuthorize("@ss.hasPermi('fee:fee:add')")
    @Log(title = "费用", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Fee fee)
    {
        return toAjax(feeService.insertFee(fee));
    }

    /**
     * 修改费用
     */
    @PreAuthorize("@ss.hasPermi('fee:fee:edit')")
    @Log(title = "费用", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Fee fee)
    {
        return toAjax(feeService.updateFee(fee));
    }

    /**
     * 删除费用
     */
    @PreAuthorize("@ss.hasPermi('fee:fee:remove')")
    @Log(title = "费用", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(feeService.deleteFeeByIds(ids));
    }
}
