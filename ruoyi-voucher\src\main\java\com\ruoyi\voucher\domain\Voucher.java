package com.ruoyi.voucher.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 优惠券对象 voucher
 * 
 * <AUTHOR>
 * @date 2024-04-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("voucher")
public class Voucher extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 券id */
    @TableId(value = "id", type = IdType.INPUT)
    private Integer id;

    /** 用户id */
    @Excel(name = "用户id")
    private Long userId;

    /** 设备id */
    @Excel(name = "设备id")
    private String deviceId;

    /** 售价 */
    @Excel(name = "售价")
    private Long cellPrice;

    /** 满减条件 */
    @Excel(name = "满减条件")
    private Long requireFull;

    /** 实际价格 */
    @Excel(name = "实际价格")
    private Long actualPrice;

    /** 标题 */
    @Excel(name = "标题")
    private String title;

    /** 描述规则 */
    @Excel(name = "描述规则")
    private String voucherDescribe;

    /** 类型 */
    @Excel(name = "类型")
    private Integer type;

    /** 状态 */
    @Excel(name = "状态")
    private Integer status;

    /** 过期时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "过期时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date expirationTime;


}
