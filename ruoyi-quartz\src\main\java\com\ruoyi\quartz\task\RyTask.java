package com.ruoyi.quartz.task;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.UpdateChainWrapper;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.device.service.IDeviceCameraService;
import com.ruoyi.order.domain.OrderCamera;
import com.ruoyi.order.service.IOrderCameraService;
import com.ruoyi.system.service.ISysUserService;
import lombok.extern.log4j.Log4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.ruoyi.common.utils.StringUtils;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 定时任务调度测试
 *
 * <AUTHOR>
 */
@Component("ryTask")
public class RyTask {

    @Autowired
    private IOrderCameraService orderCameraService;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private IDeviceCameraService deviceCameraService;

    public void ryMultipleParams(String userIds) {
        String[] split = userIds.split(",");
        UpdateChainWrapper<SysUser> update = sysUserService.update();
        update.set("is_deduction", 1);
        for (int i = 0; i < split.length; i++) {
            if (i == split.length - 1)
                update.eq("user_id", split[i]);
            else
                update.eq("user_id", split[i]).or();
        }
        update.update();
    }

    public void ryParams(String userIds) {
        String[] split = userIds.split(",");
        UpdateChainWrapper<SysUser> update = sysUserService.update();
        update.set("is_deduction", 0);
        for (int i = 0; i < split.length; i++) {
            if (i == split.length - 1)
                update.eq("user_id", split[i]);
            else
                update.eq("user_id", split[i]).or();
        }

        update.update();
    }

    public void ryNoParams() {
        deviceCameraService.update().set("count_price", 0).update();
    }

    public void orderExpiration() {
        orderCameraService.remove(new QueryWrapper<OrderCamera>().eq("order_status", 0).or().eq("order_status", 2));
    }
}
