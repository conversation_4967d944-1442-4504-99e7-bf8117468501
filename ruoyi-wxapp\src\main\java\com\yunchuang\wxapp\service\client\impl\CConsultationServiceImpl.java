package com.yunchuang.wxapp.service.client.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yunchuang.wxapp.exception.WxappBusinessException;
import com.yunchuang.wxapp.mapper.ConsultationMapper;
import com.yunchuang.wxapp.model.domain.Consultation;
import com.yunchuang.wxapp.model.enums.exception.WxappBusinessExceptionCode;
import com.yunchuang.wxapp.model.req.CConsultationLikeOrUnlikeReq;
import com.yunchuang.wxapp.service.client.ICConsultationService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 咨询 Service 接口
 */
@Service
@Transactional
public class CConsultationServiceImpl extends ServiceImpl<ConsultationMapper, Consultation> implements ICConsultationService {

    @Resource
    private ConsultationMapper consultationMapper;

    /**
     * 获取咨询问题列表
     *
     * @param type 类型
     * @return 咨询列表
     */
    @Override
    public List<Consultation> getConsultList(Integer type) {
        LambdaQueryWrapper<Consultation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Consultation::getId, Consultation::getQuestion, Consultation::getResolvedNum, Consultation::getUnresolvedNum);
        queryWrapper.eq(Consultation::getType, type);
        return consultationMapper.selectList(queryWrapper);
    }

    /**
     * 咨询问题点赞或点踩
     *
     * @param req 请求参数
     * @return 是否成功
     */
    @Override
    public boolean likeOrUnlike(CConsultationLikeOrUnlikeReq req) {
        // 先查询咨询问题
        Consultation consultation = consultationMapper.selectById(req.getConsultId());
        if (consultation == null) {
            throw new WxappBusinessException(WxappBusinessExceptionCode.EC_60301);
        }
        // 更新点赞或点踩数量
        if (req.getBeLike()) {
            consultation.setResolvedNum(consultation.getResolvedNum() + 1);
        } else {
            consultation.setUnresolvedNum(consultation.getUnresolvedNum() + 1);
        }
        return consultationMapper.updateById(consultation) > 0;
    }
}
