package com.ruoyi.thali.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class Result {
    private Boolean success;
    private int code;
    private String message;
    private String timestamp;
    private Object data;

    public static Result ok(int code,String message,String timestamp,Object data){
        return new Result(true,code,message,timestamp,data);
    }
    public static Result fail(int code,String message,String timestamp){
        return new Result(false,code,message,timestamp,null);
    }
}
