package com.ruoyi.web.controller.wxapp.admin;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.yunchuang.wxapp.model.domain.HomeSet;
import com.yunchuang.wxapp.service.admin.IHomeSetService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 首页设置Controller
 *
 * <AUTHOR>
 * @date 2025-02-07
 */
@RestController
@RequestMapping("/wxapp/home_set")
public class HomeSetController extends BaseController {
    @Autowired
    private IHomeSetService homeSetService;

    /**
     * 查询首页设置列表
     */
    @PreAuthorize("@ss.hasPermi('wxapp:home_set:list')")
    @GetMapping("/list")
    public TableDataInfo list(HomeSet homeSet) {
        startPage();
        List<HomeSet> list = homeSetService.selectHomeSetList(homeSet);
        return getDataTable(list);
    }

    /**
     * 导出首页设置列表
     */
    @PreAuthorize("@ss.hasPermi('wxapp:home_set:export')")
    @Log(title = "首页设置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HomeSet homeSet) {
        List<HomeSet> list = homeSetService.selectHomeSetList(homeSet);
        ExcelUtil<HomeSet> util = new ExcelUtil<HomeSet>(HomeSet.class);
        util.exportExcel(response, list, "首页设置数据");
    }

    /**
     * 获取首页设置详细信息
     */
    @PreAuthorize("@ss.hasPermi('wxapp:home_set:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(homeSetService.selectHomeSetById(id));
    }

    /**
     * 新增首页设置
     */
    @PreAuthorize("@ss.hasPermi('wxapp:home_set:add')")
    @Log(title = "首页设置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HomeSet homeSet) {
        return toAjax(homeSetService.insertHomeSet(homeSet));
    }

    /**
     * 修改首页设置
     */
    @PreAuthorize("@ss.hasPermi('wxapp:home_set:edit')")
    @Log(title = "首页设置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HomeSet homeSet) {
        return toAjax(homeSetService.updateHomeSet(homeSet));
    }

    /**
     * 删除首页设置
     */
    @PreAuthorize("@ss.hasPermi('wxapp:home_set:remove')")
    @Log(title = "首页设置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(homeSetService.deleteHomeSetByIds(ids));
    }
}
