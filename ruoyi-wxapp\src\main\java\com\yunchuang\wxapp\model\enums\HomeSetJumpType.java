package com.yunchuang.wxapp.model.enums;

import lombok.Getter;

/**
 * 首页设置 - 跳转类型
 */
@Getter
public enum HomeSetJumpType {

    // 无跳转
    NONE(0, "无跳转"),

    // 相框系列
    PHOTO_FRAME_SERIES(1, "相框系列"),

    // 小程序页面
    MINI_APP_PAGE(2, "小程序页面");

    private final Integer value;
    private final String name;

    HomeSetJumpType(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public static HomeSetJumpType getHomeSetJumpType(Integer value) {
        for (HomeSetJumpType homeSetJumpType : HomeSetJumpType.values()) {
            if (homeSetJumpType.getValue().equals(value)) {
                return homeSetJumpType;
            }
        }
        return null;
    }
}
