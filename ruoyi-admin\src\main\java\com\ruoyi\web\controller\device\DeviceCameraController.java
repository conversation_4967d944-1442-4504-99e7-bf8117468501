package com.ruoyi.web.controller.device;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.device.domain.DeviceCamera;
import com.ruoyi.device.service.IDeviceCameraService;
import com.ruoyi.dto.GZUserLogin;
import com.ruoyi.dto.Result;
import com.ruoyi.fee.domain.Fee;
import com.ruoyi.fee.service.IFeeService;
import com.ruoyi.order.service.IOrderCameraService;
import com.ruoyi.photo.domain.UserPhoto;
import com.ruoyi.photo.service.IUserPhotoService;
import com.ruoyi.socket.WebSocketServer;
import com.ruoyi.system.domain.SysPost;
import com.ruoyi.system.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 管理拍照机设备Controller
 *
 * <AUTHOR>
 * @date 2023-07-15
 */
@Slf4j
@RestController
@RequestMapping("/device/device_camera")
public class DeviceCameraController extends BaseController {
    @Autowired
    private IDeviceCameraService deviceCameraService;
    @Resource
    private ISysUserService userService;
    @Resource
    private IUserPhotoService userPhotoService;
    @Resource
    private IOrderCameraService orderCameraService;
    @Autowired
    private IFeeService feeService;


    /**
     * 查询管理拍照机设备列表
     */
    @PreAuthorize("@ss.hasPermi('device:device_camera:list')")
    @GetMapping("/list")
    public TableDataInfo list(DeviceCamera deviceCamera, int pageNum, int pageSize) {
        TableDataInfo tableDataInfo = deviceCameraService.selectDeviceCameraList(deviceCamera, pageNum, pageSize);
        List<DeviceCamera> list = (List<DeviceCamera>) tableDataInfo.getRows();
        for (DeviceCamera row : list) {
            String deviceId = row.getDeviceId();
            row.setIsOnLine(WebSocketServer.checkOnline(deviceId) ? "在线" : "离线");
        }
        return tableDataInfo;
    }

    /**
     * 查询管理拍照机设备列表
     */
    @GetMapping("/byId")
    public Result getById(@RequestParam String deviceId) {
        DeviceCamera deviceCamera = deviceCameraService.selectDeviceCameraByDeviceId(deviceId);
        return Result.ok(200, "查询成功", "", deviceCamera);
    }

    /**
     * 导出管理拍照机设备列表
     */
    @PreAuthorize("@ss.hasPermi('device:device_camera:export')")
    @Log(title = "管理拍照机设备", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DeviceCamera deviceCamera) {
        TableDataInfo tableDataInfo = deviceCameraService.selectDeviceCameraList(deviceCamera, 1, 100);
        List<DeviceCamera> rows = (List<DeviceCamera>) tableDataInfo.getRows();
        ExcelUtil<DeviceCamera> util = new ExcelUtil<DeviceCamera>(DeviceCamera.class);
        util.exportExcel(response, rows, "管理拍照机设备数据");
    }

    /**
     * 获取管理拍照机设备详细信息
     */
    @PreAuthorize("@ss.hasPermi('device:device_camera:query')")
    @GetMapping(value = "/{deviceId}")
    public AjaxResult getInfo(@PathVariable("deviceId") String deviceId) {
        return success(deviceCameraService.selectDeviceCameraByDeviceId(deviceId));
    }

    /**
     * 新增管理拍照机设备
     */
    @PreAuthorize("@ss.hasPermi('device:device_camera:add')")
    @Log(title = "管理拍照机设备", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DeviceCamera deviceCamera) {
        return toAjax(deviceCameraService.insertDeviceCamera(deviceCamera));
    }

    /**
     * 修改管理拍照机设备
     */
    @PreAuthorize("@ss.hasPermi('device:device_camera:edit')")
    @Log(title = "管理拍照机设备", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DeviceCamera deviceCamera) {
        return toAjax(deviceCameraService.updateDeviceCamera(deviceCamera));
    }

    /**
     * 删除管理拍照机设备
     */
    @PreAuthorize("@ss.hasPermi('device:device_camera:remove')")
    @Log(title = "管理拍照机设备", businessType = BusinessType.DELETE)
    @DeleteMapping("/{deviceIds}")
    public AjaxResult remove(@PathVariable String[] deviceIds) {
        return toAjax(deviceCameraService.deleteDeviceCameraByDeviceIds(deviceIds));
    }

    /**
     * 获取用户所拥有的设备
     */
    @GetMapping("/getDeviceByUser")
    public Result getDeviceByUser() {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        LambdaQueryWrapper<DeviceCamera> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DeviceCamera::getUserId, user.getUserId());
        List<DeviceCamera> deviceCameras = deviceCameraService.list(lambdaQueryWrapper);
        return Result.ok(200, "1", "", deviceCameras);
    }

    /**
     * 获取设备软件版本号
     */
    @GetMapping("/getSoftware")
    public Result getSoftware(@RequestParam String deviceId) {
        SysPost sysPost = deviceCameraService.getSoftware(deviceId);
        if (sysPost == null) return Result.fail(500, "查询失败", "");
        return Result.ok(200, "查询成功", "", sysPost);
    }

    /**
     * 进入设备后台登录
     */
    @GetMapping("/loginDevice")
    public Result loginDevice(@RequestParam String deviceId, @RequestParam String password) {
        if (deviceId == null || password == null) return Result.fail(200, "设备id或密码不能为空", "");
        DeviceCamera deviceCamera = deviceCameraService.getById(deviceId);
        if (deviceCamera == null) return Result.fail(200, "设备不存在", "");
        SysUser sysUser = userService.selectUserById(deviceCamera.getUserId());
        if (sysUser == null) return Result.fail(200, "设备用户错误", "");
        if (sysUser.getDevicePassword().equals(password)) {
            return Result.ok(200, "登录成功", "", null);
        }
        return Result.fail(200, "密码错误", "");
    }

    /**
     * 远程监控设备
     */
    @GetMapping("/control")
    public Result controlDevice(@RequestParam String deviceId) {
        DeviceCamera deviceCamera = deviceCameraService.selectDeviceCameraByDeviceId(deviceId);
        if (deviceCamera == null) {
            return Result.fail(200, "设备id不存在", "");
        }
        int check = 0;
        check = WebSocketServer.sendInfo("check", deviceId);

        return check > 0 ? Result.ok(200, "操作成功", "", deviceCamera) : Result.fail(400, "操作成功", "");
    }

    /**
     * 远程重启设备
     */
    @GetMapping("/reboot")
    public Result reboot(@RequestParam String deviceId) {
        int check = 0;

        check = WebSocketServer.sendInfo("reboot", deviceId);

        return check > 0 ? Result.ok(200, null, "", check) : Result.fail(200, null, "");
    }

    /**
     * 拍照机上分
     */
    @GetMapping("/addDeviceMoney")
    public Result addDeviceMoney(@RequestParam String deviceId,
                                 @RequestParam String orderId,
                                 @RequestParam int money) {
        int success = 0;
        success = WebSocketServer.sendInfo("kspz," + orderId + "," + money, deviceId);
        return success > 0 ? Result.ok(200, null, "", success) : Result.fail(200, null, "");
    }

    /**
     * 远程打印照片
     */
    @GetMapping("/printPhoto")
    public Result printPhoto(@RequestParam String deviceId,
                             @RequestParam Long photoId,
                             @RequestParam Integer num) {
        if (photoId == null) {
            return Result.fail(200, "照片id不存在", "");
        }
        UserPhoto userPhoto = userPhotoService.selectUserPhotoById(photoId);
        if (userPhoto == null) {
            return Result.fail(200, "照片id不存在", "");
        }

        int success = 0;
        success = WebSocketServer.sendInfo("print," + userPhoto.getUrl() + "," + num, deviceId);
        return success > 0 ? Result.ok(200, "操作成功", "", success) : Result.fail(200, "操作成功", "");
    }

    /**
     * 远程打印照片
     */
    @GetMapping("/devicePrintPhoto")
    public Result devicePrintPhoto(@RequestParam String deviceId,
                                   @RequestParam String myDeviceId,
                                   @RequestParam String photoUrl,
                                   @RequestParam Integer num) {
        if (myDeviceId == null || deviceId == null) {
            return Result.fail(301, "设备号错误", "");
        }

        DeviceCamera camera1 = deviceCameraService.getById(deviceId);
        DeviceCamera camera2 = deviceCameraService.getById(myDeviceId);
        if (camera1 == null || camera2 == null || !Objects.equals(camera1.getUserId(), camera2.getUserId()))
            return Result.fail(301, "设备数据错误", "");

        if (photoUrl == null || photoUrl.equals("")) {
            return Result.fail(200, "照片id不存在", "");
        }

        int success = 0;
        success = WebSocketServer.sendInfo("print," + photoUrl + "," + num, deviceId);
        return success > 0 ? Result.ok(200, "操作成功", "", success) : Result.fail(200, "操作成功", "");
    }

    /**
     * 更新设备状态
     */
    @GetMapping("/updateDeviceStatus")
    public Result updateDeviceStatus(
            @RequestParam String deviceId,
            @RequestParam String printerStatus,
            @RequestParam int cameraStatus,
            @RequestParam int beautyStatus,
            @RequestParam int consumables,
            @RequestParam(required = false) int paperConsumables) {
        boolean success = deviceCameraService.updateDeviceStatus(deviceId, printerStatus, cameraStatus, beautyStatus, consumables, paperConsumables);
        return success ? Result.ok(200, "操作成功", "", null) : Result.fail(200, "操作成功", "");
    }

    /**
     * 乐高用户上传照片 回调
     */
    @GetMapping("/GZPhotoCallBack")
    public String GZPhotoCallBack(
            @RequestParam String user_id,
            @RequestParam String pic_url,
            @RequestParam String machine_id) {

        WebSocketServer.sendInfo("upload:" + pic_url, machine_id);
        return "success";
    }


    /**
     * 乐高用户登录拍照机 回调
     */
    @GetMapping("/GZLoginCallBack")
    public String GZLoginCallBack(
            @RequestParam String user_id,  //用户id
            @RequestParam String avatar,  //微信头像url
            @RequestParam String nick_name,
            @RequestParam Integer sex,  // 1男  2女
            @RequestParam String machine_id,
            @RequestParam Date login_time) {
        GZUserLogin userLogin = new GZUserLogin();
        userLogin.setUserId(user_id);
        userLogin.setAvatar(avatar);
        userLogin.setNickName(nick_name);
        userLogin.setSex(sex);
        userLogin.setDeviceId(machine_id);
        userLogin.setLoginTime(login_time);
        String jsonStr = JSONUtil.toJsonStr(userLogin);

        WebSocketServer.sendInfo("login:" + jsonStr, machine_id);

        return "success";
    }

    /**
     * 设备自动录入
     */
    @Log(title = "管理拍照机设备", businessType = BusinessType.INSERT)
    @PostMapping("/addDevice")
    public AjaxResult addDevice(DeviceCamera deviceCamera) {
        Long count = deviceCameraService.query().eq("device_id", deviceCamera.getDeviceId()).count();
        if (count > 0) {
            return new AjaxResult(200, "设备号已存在", null);
        }
        return toAjax(deviceCameraService.insertDeviceCamera(deviceCamera));
    }


    /**
     * 设备消耗点券点数
     */
    @GetMapping("/consumeStamps")
    public Result consumeStamps(@RequestParam String deviceId, @RequestParam(required = false) Integer count, @RequestParam(required = false) Integer type) {
        DeviceCamera deviceCamera = deviceCameraService.getById(deviceId);
        if (deviceCamera == null)
            return Result.fail(401, "设备id不存在", "");
        Long userId = deviceCamera.getUserId();
        SysUser user = userService.selectUserById(userId);
        if (user == null)
            return Result.fail(402, "设备无负责人或不存在", "");

        int fee = 0;
        if (type != null) {
            //如果特定类型收取手续费
            Fee photoFee = feeService.query().eq("photo_type", type).one();

            if (photoFee != null && (photoFee.getExcludeDevice() == null || !photoFee.getExcludeDevice().contains(deviceId))) {
                Integer chargeType = photoFee.getChargeType();

                if (chargeType == 1) { //金额
                    fee = photoFee.getFeeAmount();
                }
            }
        }
        if (fee > 0)
            count = fee;

        if (user.getStamps() < count)
            return Result.fail(500, "设备点数不足，请联系店主充值", "");

        boolean update = userService.update().eq("user_id", userId).setSql("stamps = stamps - " + count).update();

        if (!update)
            return Result.fail(501, "使用失败，请联系管理员", "");
        return Result.ok(200, "使用成功", "", null);
    }

}
