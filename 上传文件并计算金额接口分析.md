# 上传文件并计算金额接口详细分析

## 接口概述

**接口路径**: `POST /order/printer/uploadFile`

**功能**: 上传打印文件并根据打印参数自动计算价格，创建打印任务

## 涉及的核心文件

### 1. Controller层
**文件**: `ruoyi-admin/src/main/java/com/ruoyi/web/controller/order/OrderPrinterController.java`
- **方法**: `uploadFile()`
- **职责**: 接收文件上传请求，参数验证，文件上传处理

### 2. Service层
**文件**: `ruoyi-order/src/main/java/com/ruoyi/order/service/impl/OrderPrinterServiceImpl.java`
- **方法**: `uploadFileAndCalculatePrice()`
- **职责**: 业务逻辑处理，价格计算，任务创建

### 3. 实体类
**文件**: `ruoyi-order/src/main/java/com/ruoyi/order/domain/OrderPrinterTask.java`
- **职责**: 打印任务数据模型，包含taskPrice字段

### 4. 数据访问层
**文件**: `ruoyi-order/src/main/resources/mapper/order/OrderPrinterTaskMapper.xml`
- **职责**: 打印任务的数据库操作

## 金额计算详解

### 价格计算核心方法
```java
public Long calculatePrintPrice(int pageCount, int copies, int colorMode, int duplexMode, int paperType, String deviceId)
```

### 价格计算规则

#### 1. 基础价格（单位：分）
| 纸张类型 | 黑白打印 | 彩色打印 |
|---------|---------|---------|
| A4 (paperType=1) | 20分/页 | 50分/页 |
| A5 (paperType=2) | 15分/页 | 30分/页 |
| 照片纸 (paperType=3) | 200分/页 | 200分/页 |

#### 2. 双面打印处理
- 如果是双面打印 (duplexMode=1)：基础价格 × 1.5
- 单面打印 (duplexMode=0)：基础价格不变

#### 3. 最终价格计算公式
```
总价格 = 基础价格 × 页数 × 份数
```

### 页码解析逻辑

#### parsePageRange方法处理规则：
1. **空值处理**: 如果pageRange为空，默认返回1页
2. **全部页面**: 如果是"all"或"*"，默认按10页计算
3. **范围解析**: 支持"1-3,5,7-9"格式
   - "1-3" → 3页 (1,2,3)
   - "5" → 1页
   - "7-9" → 3页 (7,8,9)
   - 总计：7页

## 接口参数详解

### 请求参数
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| orderId | String | 是 | - | 订单ID |
| file | MultipartFile | 是 | - | 上传的文件 |
| copies | Integer | 否 | 1 | 打印份数 |
| colorMode | Integer | 否 | 0 | 颜色模式：0-黑白，1-彩色 |
| duplexMode | Integer | 否 | 0 | 双面模式：0-单面，1-双面 |
| paperType | Integer | 否 | 1 | 纸张类型：1-A4，2-A5，3-照片纸 |
| pageRange | String | 否 | null | 页码范围，如"1-3,5" |

### 返回结果
```json
{
  "code": 200,
  "msg": "上传文件成功",
  "data": {
    "taskId": "任务ID",
    "taskPrice": 价格（分）,
    "pageCount": 页数,
    "fileName": "文件名",
    "fileUrl": "文件URL"
  }
}
```

## 数据库操作

### 涉及的表
1. **order_printer_tasks**: 保存打印任务信息
   - task_price字段存储计算出的价格

### 关键字段
- `task_price`: 任务价格（分）
- `page_range`: 页码范围
- `copies`: 打印份数
- `color_mode`: 颜色模式
- `duplex_mode`: 双面模式
- `paper_type`: 纸张类型

## 价格计算示例

### 示例1：A4黑白单面打印
- 纸张类型：A4 (paperType=1)
- 颜色模式：黑白 (colorMode=0)
- 双面模式：单面 (duplexMode=0)
- 页码范围："1-3" (3页)
- 打印份数：2份

**计算过程**：
1. 基础价格：20分/页（A4黑白）
2. 双面处理：20分（单面不变）
3. 总价格：20 × 3 × 2 = 120分

### 示例2：A4彩色双面打印
- 纸张类型：A4 (paperType=1)
- 颜色模式：彩色 (colorMode=1)
- 双面模式：双面 (duplexMode=1)
- 页码范围："1-5" (5页)
- 打印份数：1份

**计算过程**：
1. 基础价格：50分/页（A4彩色）
2. 双面处理：50 × 1.5 = 75分/页
3. 总价格：75 × 5 × 1 = 375分

## 业务流程

1. **文件上传**: 客户端上传文件到服务器
2. **参数解析**: 解析打印参数（份数、颜色、纸张等）
3. **页码计算**: 根据pageRange计算实际打印页数
4. **价格计算**: 根据打印参数和页数计算价格
5. **任务创建**: 创建OrderPrinterTask对象
6. **数据保存**: 将任务信息保存到数据库
7. **结果返回**: 返回任务ID和价格信息

## 注意事项

1. **价格单位**: 所有价格都以"分"为单位存储和计算
2. **文件存储**: 文件上传到服务器指定目录
3. **任务状态**: 新创建的任务状态为0（待打印）
4. **错误处理**: 包含完整的参数验证和异常处理
5. **日志记录**: 详细的操作日志便于调试

## 扩展功能

代码中预留了设备特殊价格配置的扩展点：
```java
// 可以根据设备ID获取特殊价格配置（预留扩展）
// DevicePrinter device = devicePrinterService.selectDevicePrinterByDeviceId(deviceId);
// if (device != null && device.getPricePerPage() != null) {
//     totalPrice = device.getPricePerPage() * pageCount * copies;
// }
```

这允许未来为不同设备设置不同的价格策略。
