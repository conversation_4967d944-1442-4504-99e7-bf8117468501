package com.yunchuang.wxapp.model.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 相框系列对象 wxapp_photo_frame_series
 *
 * <AUTHOR>
 * @date 2025-02-06
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("wxapp_photo_frame_series")
public class PhotoFrameSeries extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 相框系列ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 系列名称
     */
    @Excel(name = "系列名称")
    private String seriesName;

    /**
     * 排序
     */
    @Excel(name = "排序")
    private Long sort;

    /**
     * 状态
     */
    @Excel(name = "状态")
    private Integer status;
}
