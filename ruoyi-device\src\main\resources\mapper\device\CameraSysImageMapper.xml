<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.device.mapper.CameraSysImageMapper">
    
    <resultMap type="CameraSysImage" id="CameraSysImageResult">
        <result property="id"    column="id"    />
        <result property="url"    column="url"    />
        <result property="objectName"    column="object_name"    />
        <result property="name"    column="name"    />
        <result property="type"    column="type"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectCameraSysImageVo">
        select id, url, object_name, name, type, create_by, create_time, update_by, update_time, remark from camera_sys_image
    </sql>

    <select id="selectCameraSysImageList" parameterType="CameraSysImage" resultMap="CameraSysImageResult">
        <include refid="selectCameraSysImageVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="createBy != null  and createBy != ''"> and (create_by like concat('%', #{createBy}, '%') or create_by = 'pdl_admin')</if>
            <if test="type != null "> and type = #{type}</if>
        </where>
    </select>

    <select id="selectMyCameraSysImageList" parameterType="CameraSysImage" resultMap="CameraSysImageResult">
        <include refid="selectCameraSysImageVo"/>
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="createBy != null  and createBy != ''"> and create_by like concat('%', #{createBy}, '%')</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="type == null "> or type = 28 or type = 29 or type = 30 or type = 31 or type = 32 or type = 33</if>
        </where>
    </select>

    <select id="selectCameraSysBackgroundList" resultMap="CameraSysImageResult">
        <include refid="selectCameraSysImageVo"/>
        <where>
            and type != 1 and type != 2 and type != 3 and type != 6 and type != 7 and type != 8
        </where>
    </select>
    
    <select id="selectCameraSysImageById" parameterType="Long" resultMap="CameraSysImageResult">
        <include refid="selectCameraSysImageVo"/>
        where id = #{id}
    </select>
    <select id="selectCameraSysImageByUser" resultType="com.ruoyi.device.domain.CameraSysImage">
        SELECT * FROM camera_sys_image WHERE create_by = #{createBy}
        <if test="type != null "> and type = #{type}</if>
        <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
    </select>
    <select id="selectCameraSysImageBydeviceName" resultType="com.ruoyi.device.domain.CameraSysImage">
        select * from camera_sys_image where create_by = #{userName} or type = 28  or type = 29  or type = 30  or type = 31  or type = 32
    </select>
    <select id="selectCameraSysImageListAll" resultType="com.ruoyi.device.domain.CameraSysImage">
        select * from camera_sys_image
        <where>
            <if test="type != null "> type = #{type}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
        </where>
    </select>


    <insert id="insertCameraSysImage" parameterType="CameraSysImage">
        insert into camera_sys_image
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="url != null">url,</if>
            <if test="objectName != null">object_name,</if>
            <if test="name != null">name,</if>
            <if test="type != null">type,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="url != null">#{url},</if>
            <if test="objectName != null">#{objectName},</if>
            <if test="name != null">#{name},</if>
            <if test="type != null">#{type},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateCameraSysImage" parameterType="CameraSysImage">
        update camera_sys_image
        <trim prefix="SET" suffixOverrides=",">
            <if test="url != null">url = #{url},</if>
            <if test="objectName != null">object_name = #{objectName},</if>
            <if test="name != null">name = #{name},</if>
            <if test="type != null">type = #{type},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCameraSysImageById" parameterType="Long">
        delete from camera_sys_image where id = #{id}
    </delete>

    <delete id="deleteCameraSysImageByIds" parameterType="String">
        delete from camera_sys_image where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>