package com.yunchuang.wxapp.exception;

import com.yunchuang.wxapp.model.enums.exception.WxappBusinessExceptionCode;

/**
 * 微信小程序 - 业务异常类
 */
public class WxappBusinessException extends BusinessException {

    /**
     * 构造函数
     *
     * @param EC 异常枚举
     */
    public WxappBusinessException(WxappBusinessExceptionCode EC) {
        super(false, EC.getMessage(), EC.getCode());
    }

    /**
     * 构造函数
     *
     * @param message 异常信息
     * @param EC      异常枚举
     */
    public WxappBusinessException(WxappBusinessExceptionCode EC, String message) {
        super(false, message, WxappBusinessExceptionCode.UNKNOWN_EXCEPTION.getCode());
    }
}
