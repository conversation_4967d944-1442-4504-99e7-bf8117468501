# 删除order_price字段修改总结

## 问题描述
MySQL数据库死锁问题，错误信息显示在更新order_printer表的order_price字段时发生死锁：
```
Deadlock found when trying to get lock; try restarting transaction
```

## 解决方案
删除order_printer表中的order_price字段，避免并发更新导致的死锁问题。

## 修改文件列表

### 1. 实体类修改
**文件**: `ruoyi-order/src/main/java/com/ruoyi/order/domain/OrderPrinter.java`
- 删除了orderPrice字段及其注解

### 2. Mapper XML修改
**文件**: `ruoyi-order/src/main/resources/mapper/order/OrderPrinterMapper.xml`
- 删除了resultMap中的order_price映射
- 删除了selectOrderPrinterVo中的order_price字段
- 删除了insert语句中的order_price字段
- 删除了update语句中的order_price字段

### 3. Service层修改
**文件**: `ruoyi-order/src/main/java/com/ruoyi/order/service/impl/OrderPrinterServiceImpl.java`
- `createPreOrder`方法：删除了orderPrice相关逻辑
- `createOrder`方法：删除了orderPrice初始化
- `uploadFileAndCalculatePrice`方法：删除了更新订单总金额的逻辑
- 返回结果中删除了orderPrice相关字段

### 4. Controller层修改
**文件**: `ruoyi-admin/src/main/java/com/ruoyi/web/controller/order/OrderPrinterController.java`
- `uploadAndCreateOrder`方法：删除了orderPrice参数
- 删除了相关日志记录

### 5. 数据库修改
**文件**: `sql/remove_order_price_field.sql`
- 创建了删除order_price字段的SQL脚本

### 6. 文档修改
**文件**: `接口文档-打印订单拆分接口.md`
- 更新了数据库表结构说明，删除了order_price字段

## 数据库执行步骤

1. **备份数据库**（重要！）
```sql
-- 备份order_printer表
CREATE TABLE order_printer_backup AS SELECT * FROM order_printer;
```

2. **执行字段删除**
```sql
-- 删除order_price字段
ALTER TABLE order_printer DROP COLUMN order_price;

-- 验证字段是否已删除
DESCRIBE order_printer;
```

## 业务逻辑变更

### 原有逻辑
- 订单创建时设置orderPrice
- 上传文件时累加taskPrice到orderPrice
- 支付时使用orderPrice作为订单总金额

### 新逻辑
- 订单不再维护总金额字段
- 每个打印任务独立计价（taskPrice）
- 如需订单总金额，可通过查询该订单下所有任务的taskPrice求和获得

## 影响评估

### 正面影响
1. **解决死锁问题**：消除了并发更新orderPrice导致的死锁
2. **数据一致性**：避免了orderPrice与实际任务价格不一致的问题
3. **性能提升**：减少了数据库字段更新操作

### 需要注意的点
1. **前端适配**：前端代码可能需要调整，不再依赖orderPrice字段
2. **报表统计**：涉及订单金额统计的功能需要改为从任务表聚合计算
3. **支付逻辑**：支付金额需要从任务表计算而不是直接使用orderPrice

## 测试建议

1. **功能测试**
   - 创建订单功能
   - 上传文件功能
   - 支付流程
   - 订单查询

2. **并发测试**
   - 多用户同时创建订单
   - 多用户同时上传文件
   - 验证不再出现死锁

3. **数据一致性测试**
   - 验证任务价格计算正确
   - 验证订单状态更新正常

## 部署注意事项

1. **部署顺序**
   - 先部署代码
   - 再执行数据库脚本
   - 最后验证功能

2. **回滚方案**
   - 保留数据库备份
   - 保留代码版本
   - 如需回滚，先恢复数据库再回滚代码

## 完成状态
✅ 实体类修改完成
✅ Mapper XML修改完成  
✅ Service层修改完成
✅ Controller层修改完成
✅ SQL脚本创建完成
✅ 文档更新完成

**修改已完成，可以进行测试和部署。**
