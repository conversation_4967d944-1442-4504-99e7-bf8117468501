package com.ruoyi.common.utils; // 建议放在 common/utils 包下

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

public class QrCodeUtil {

    private static final int DEFAULT_WIDTH = 200;
    private static final int DEFAULT_HEIGHT = 200;
    private static final String DEFAULT_FORMAT = "png";

    /**
     * 生成二维码并转换为 Base64 编码的字符串
     *
     * @param content 二维码内容
     * @return Base64 编码的二维码图片字符串，如果生成失败则返回 null
     */
    public static String generateQrCodeToBase64(String content) {
        return generateQrCodeToBase64(content, DEFAULT_WIDTH, DEFAULT_HEIGHT);
    }

    /**
     * 生成指定尺寸的二维码并转换为 Base64 编码的字符串
     *
     * @param content 二维码内容
     * @param width   二维码宽度
     * @param height  二维码高度
     * @return Base64 编码的二维码图片字符串，如果生成失败则返回 null
     */
    public static String generateQrCodeToBase64(String content, int width, int height) {
        return generateQrCodeToBase64(content, width, height, DEFAULT_FORMAT);
    }

    /**
     * 生成指定尺寸和格式的二维码并转换为 Base64 编码的字符串
     *
     * @param content 二维码内容
     * @param width   二维码宽度
     * @param height  二维码高度
     * @param format  图片格式，例如 "png", "jpeg"
     * @return Base64 编码的二维码图片字符串，如果生成失败则返回 null
     */
    public static String generateQrCodeToBase64(String content, int width, int height, String format) {
        if (content == null || content.isEmpty()) {
            return null;
        }
        try {
            Map<EncodeHintType, Object> hints = new HashMap<>();
            // 设置字符编码
            hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
            // 设置容错级别
            hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
            QRCodeWriter qrCodeWriter = new QRCodeWriter();
            BitMatrix bitMatrix = qrCodeWriter.encode(content, BarcodeFormat.QR_CODE, width, height, hints);
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            MatrixToImageWriter.writeToStream(bitMatrix, format, outputStream);
            byte[] qrCodeBytes = outputStream.toByteArray();
            return Base64.getEncoder().encodeToString(qrCodeBytes);
        } catch (WriterException | IOException e) {
            System.err.println("Error generating QR code: " + e.getMessage());
            return null;
        }
    }

    public static void main(String[] args) {
        String content = "https://www.ruoyi.vip";
        String base64QrCode = generateQrCodeToBase64(content);
        if (base64QrCode != null) {
            System.out.println("Base64 Encoded QR Code:\n" + base64QrCode);
            // 你可以在前端将 base64QrCode 渲染成图片
            // 例如，在 HTML 中使用 <img src="data:image/png;base64,..." />
        } else {
            System.out.println("Failed to generate QR code.");
        }

        String deviceId = "DEVICE007";
        String deviceBindQrCode = generateQrCodeToBase64("bind_device://" + deviceId, 300, 300, "jpeg");
        if (deviceBindQrCode != null) {
            System.out.println("\nBase64 Encoded Device Bind QR Code for " + deviceId + ":\n" + deviceBindQrCode);
        }
    }
}