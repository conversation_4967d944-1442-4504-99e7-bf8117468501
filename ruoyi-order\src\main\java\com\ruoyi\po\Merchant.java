package com.ruoyi.po;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**全参构造器跟无参构造器*/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Merchant {

    /**必填参数*/
    private String outTradeNo;
    /**汇联商户号*/
    private String hlMerchantId;

    /**可选参数*/
    /**路径*/
    private String path;
    /**关注公众号*/
//    private String subscribeAppId;
    /**支付appid*/
    private String paymentAppId;
    /**商户经营类型*/
    private String dealType;
    /**商户简称*/
    private String merchantShortName;
    /**
     * 商户全称
     */
    private String merchantName;
    /**
     * 经营类目
     */
    private String mccCode;
    /**
     * 商户类型
     */
    private String merchantType;

    /**
     * 开户状态 -1待提交  0审核中  1开通  2拒绝 3风险  4渠道人工审核中 10 平台审中,11 停用
     */
    private Integer status;

    private MerchantBankCard merchantBankCard;

    private List<MerchantFee> merchantFeeList;

    private MerchantDetail merchantDetail;
    /**
     * 结算类型类型
     */
    private String stlTyp;
    /**
     * 管理账户
     */
    private String username;
    /**
     * MCC
     * */
    private Integer mcc;

    public String payCompany;

    public String orderNo;

    public String settleDate;

    public String amount;

    public String summary;

    public String subAppId;

    public String settlementMode;

    private List<String> payCompanyCodeList;

    public String email;
    public Merchant(String outTradeNo, String merchantName, String merchantShortName, String mccCode, String merchantType, String dealType, String stlTyp, String username) {
        this.outTradeNo = outTradeNo;
        this.merchantName = merchantName;
        this.merchantShortName = merchantShortName;
        this.mccCode = mccCode;
        this.merchantType = merchantType;
        this.dealType = dealType;
        this.stlTyp = stlTyp;
        this.username = username;
    }
    /**
     * 资质编号
     * */
    public String certNo;

    /**
     * 云资金
     */
    private CloudBalancePay merchantBalancePay;
}
