<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.beauty.mapper.BeautyValueMapper">
    
    <resultMap type="BeautyValue" id="BeautyValueResult">
        <result property="id"    column="id"    />
        <result property="mopiMin"    column="mopi_min"    />
        <result property="mopiMax"    column="mopi_max"    />
        <result property="meibaiMin"    column="meibai_min"    />
        <result property="meibaiMax"    column="meibai_max"    />
        <result property="wuguanlitiMin"    column="wuguanliti_min"    />
        <result property="wuguanlitiMax"    column="wuguanliti_max"    />
        <result property="liangyanMin"    column="liangyan_min"    />
        <result property="liangyanMax"    column="liangyan_max"    />
        <result property="hongrunMin"    column="hongrun_min"    />
        <result property="hongrunMax"    column="hongrun_max"    />
        <result property="shoulianMin"    column="shoulian_min"    />
        <result property="shoulianMax"    column="shoulian_max"    />
        <result property="dayanMin"    column="dayan_min"    />
        <result property="dayanMax"    column="dayan_max"    />
    </resultMap>

    <sql id="selectBeautyValueVo">
        select id, mopi_min, mopi_max, meibai_min, meibai_max, wuguanliti_min, wuguanliti_max, liangyan_min, liangyan_max, hongrun_min, hongrun_max, shoulian_min, shoulian_max, dayan_min, dayan_max from beauty_value
    </sql>

    <select id="selectBeautyValueList" parameterType="BeautyValue" resultMap="BeautyValueResult">
        <include refid="selectBeautyValueVo"/>
        <where>  
            <if test="id != null "> and id = #{id}</if>
        </where>
    </select>
    
    <select id="selectBeautyValueById" parameterType="Long" resultMap="BeautyValueResult">
        <include refid="selectBeautyValueVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertBeautyValue" parameterType="BeautyValue">
        insert into beauty_value
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="mopiMin != null">mopi_min,</if>
            <if test="mopiMax != null">mopi_max,</if>
            <if test="meibaiMin != null">meibai_min,</if>
            <if test="meibaiMax != null">meibai_max,</if>
            <if test="wuguanlitiMin != null">wuguanliti_min,</if>
            <if test="wuguanlitiMax != null">wuguanliti_max,</if>
            <if test="liangyanMin != null">liangyan_min,</if>
            <if test="liangyanMax != null">liangyan_max,</if>
            <if test="hongrunMin != null">hongrun_min,</if>
            <if test="hongrunMax != null">hongrun_max,</if>
            <if test="shoulianMin != null">shoulian_min,</if>
            <if test="shoulianMax != null">shoulian_max,</if>
            <if test="dayanMin != null">dayan_min,</if>
            <if test="dayanMax != null">dayan_max,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="mopiMin != null">#{mopiMin},</if>
            <if test="mopiMax != null">#{mopiMax},</if>
            <if test="meibaiMin != null">#{meibaiMin},</if>
            <if test="meibaiMax != null">#{meibaiMax},</if>
            <if test="wuguanlitiMin != null">#{wuguanlitiMin},</if>
            <if test="wuguanlitiMax != null">#{wuguanlitiMax},</if>
            <if test="liangyanMin != null">#{liangyanMin},</if>
            <if test="liangyanMax != null">#{liangyanMax},</if>
            <if test="hongrunMin != null">#{hongrunMin},</if>
            <if test="hongrunMax != null">#{hongrunMax},</if>
            <if test="shoulianMin != null">#{shoulianMin},</if>
            <if test="shoulianMax != null">#{shoulianMax},</if>
            <if test="dayanMin != null">#{dayanMin},</if>
            <if test="dayanMax != null">#{dayanMax},</if>
         </trim>
    </insert>

    <update id="updateBeautyValue" parameterType="BeautyValue">
        update beauty_value
        <trim prefix="SET" suffixOverrides=",">
            <if test="mopiMin != null">mopi_min = #{mopiMin},</if>
            <if test="mopiMax != null">mopi_max = #{mopiMax},</if>
            <if test="meibaiMin != null">meibai_min = #{meibaiMin},</if>
            <if test="meibaiMax != null">meibai_max = #{meibaiMax},</if>
            <if test="wuguanlitiMin != null">wuguanliti_min = #{wuguanlitiMin},</if>
            <if test="wuguanlitiMax != null">wuguanliti_max = #{wuguanlitiMax},</if>
            <if test="liangyanMin != null">liangyan_min = #{liangyanMin},</if>
            <if test="liangyanMax != null">liangyan_max = #{liangyanMax},</if>
            <if test="hongrunMin != null">hongrun_min = #{hongrunMin},</if>
            <if test="hongrunMax != null">hongrun_max = #{hongrunMax},</if>
            <if test="shoulianMin != null">shoulian_min = #{shoulianMin},</if>
            <if test="shoulianMax != null">shoulian_max = #{shoulianMax},</if>
            <if test="dayanMin != null">dayan_min = #{dayanMin},</if>
            <if test="dayanMax != null">dayan_max = #{dayanMax},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBeautyValueById" parameterType="Long">
        delete from beauty_value where id = #{id}
    </delete>

    <delete id="deleteBeautyValueByIds" parameterType="String">
        delete from beauty_value where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>