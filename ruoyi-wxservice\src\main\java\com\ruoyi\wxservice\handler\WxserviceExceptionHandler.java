package com.ruoyi.wxservice.handler;

import com.ruoyi.common.utils.MyResultUtil;
import com.ruoyi.wxservice.exception.WxserviceBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.Map;

/**
 * 微信服务号 - 异常处理器
 */
@Slf4j
@RestControllerAdvice
@Order(1)
public class WxserviceExceptionHandler {


    /**
     * 处理 服务号 业务异常
     *
     * @param e 异常
     * @return JSONResult
     */
    @ResponseBody
    @ExceptionHandler(WxserviceBusinessException.class)
    public ResponseEntity<Map<String, Object>> businessExceptionHandler(WxserviceBusinessException e) {
        log.error("微信服务号 - 业务异常【 code：{} msg：{} 】", e.getCode(), e.getMessage());
        return new ResponseEntity<>(MyResultUtil.error(e.getCode(), e.getMessage()), HttpStatus.BAD_REQUEST);
    }

}