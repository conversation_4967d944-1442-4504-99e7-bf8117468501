package com.ruoyi.wxservice.model.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 咨询对象 wxapp_consultation
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("wxservice_config")
public class WxServiceConfig extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 配置ID
     */
    @TableId(type = IdType.AUTO)
    private Long configId;

    /**
     * 服务号名称
     */
    @Excel(name = "服务号名称")
    private String name;

    /**
     * 微信服务号的appId
     */
    @Excel(name = "微信服务号的appId")
    private String appId;

    /**
     * 微信服务号的appSecret
     */
    @Excel(name = "微信服务号的appSecret")
    private String appSecret;

}
