<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yunchuang.wxapp.mapper.WishListMapper">

    <resultMap type="WishList" id="WishListResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="photoFrameId" column="photo_frame_id"/>
        <result property="clockinTime" column="clockin_time"/>
    </resultMap>

    <sql id="selectWishListVo">
        select id, user_id, photo_frame_id, clockin_time
        from wxapp_wish _list
    </sql>

    <select id="selectWishListList" parameterType="WishList" resultMap="WishListResult">
        <include refid="selectWishListVo"/>
        <where>
            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="photoFrameId != null ">and photo_frame_id = #{photoFrameId}</if>
            <if test="clockinTime != null ">and clockin_time = #{clockinTime}</if>
        </where>
    </select>

    <select id="selectWishListById" parameterType="Long" resultMap="WishListResult">
        <include refid="selectWishListVo"/>
        where id = #{id}
    </select>

    <insert id="insertWishList" parameterType="WishList" useGeneratedKeys="true" keyProperty="id">
        insert into wxapp_wish _list
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="photoFrameId != null">photo_frame_id,</if>
            <if test="clockinTime != null">clockin_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="photoFrameId != null">#{photoFrameId},</if>
            <if test="clockinTime != null">#{clockinTime},</if>
        </trim>
    </insert>

    <update id="updateWishList" parameterType="WishList">
        update wxapp_wish _list
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="photoFrameId != null">photo_frame_id = #{photoFrameId},</if>
            <if test="clockinTime != null">clockin_time = #{clockinTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWishListById" parameterType="Long">
        delete
        from wxapp_wish _list
        where id = #{id}
    </delete>

    <delete id="deleteWishListByIds" parameterType="String">
        delete from wxapp_wish _list where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>