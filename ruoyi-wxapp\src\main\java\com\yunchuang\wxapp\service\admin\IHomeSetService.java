package com.yunchuang.wxapp.service.admin;

import com.yunchuang.wxapp.model.domain.HomeSet;

import java.util.List;

/**
 * 首页设置Service接口
 *
 * <AUTHOR>
 * @date 2025-02-07
 */
public interface IHomeSetService {
    /**
     * 查询首页设置
     *
     * @param id 首页设置主键
     * @return 首页设置
     */
    public HomeSet selectHomeSetById(Long id);

    /**
     * 查询首页设置列表
     *
     * @param homeSet 首页设置
     * @return 首页设置集合
     */
    public List<HomeSet> selectHomeSetList(HomeSet homeSet);

    /**
     * 新增首页设置
     *
     * @param homeSet 首页设置
     * @return 结果
     */
    public int insertHomeSet(HomeSet homeSet);

    /**
     * 修改首页设置
     *
     * @param homeSet 首页设置
     * @return 结果
     */
    public int updateHomeSet(HomeSet homeSet);

    /**
     * 批量删除首页设置
     *
     * @param ids 需要删除的首页设置主键集合
     * @return 结果
     */
    public int deleteHomeSetByIds(Long[] ids);

    /**
     * 删除首页设置信息
     *
     * @param id 首页设置主键
     * @return 结果
     */
    public int deleteHomeSetById(Long id);
}
