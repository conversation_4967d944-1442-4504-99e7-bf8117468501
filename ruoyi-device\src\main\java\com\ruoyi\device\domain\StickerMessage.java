package com.ruoyi.device.domain;

import java.util.List;

public class StickerMessage {
    private String name;
    private List<CameraSysImage> stickers;

    public StickerMessage(String name, List<CameraSysImage> stickers) {
        this.name = name;
        this.stickers = stickers;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<CameraSysImage> getStickers() {
        return stickers;
    }

    public void setStickers(List<CameraSysImage> stickers) {
        this.stickers = stickers;
    }

    @Override
    public String toString() {
        return "StickerMessage{" +
                "name='" + name + '\'' +
                ", stickers=" + stickers +
                '}';
    }
}
