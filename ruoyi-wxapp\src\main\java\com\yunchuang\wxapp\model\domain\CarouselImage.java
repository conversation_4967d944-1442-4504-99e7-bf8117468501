package com.yunchuang.wxapp.model.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 轮播图对象 wxapp_carousel_image
 *
 * <AUTHOR>
 * @date 2024-12-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("wxapp_carousel_image")
public class CarouselImage extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 轮播图id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 图片名称
     */
    @Excel(name = "图片名称")
    private String name;

    /**
     * 是否启用 0：是 1：否
     */
    @Excel(name = "是否启用 0：是 1：否")
    private Integer beEnabled;

    /**
     * 图片地址
     */
    @Excel(name = "图片地址")
    private String imageUrl;

    /**
     * 排序
     */
    @Excel(name = "排序")
    private Long sort;
}
