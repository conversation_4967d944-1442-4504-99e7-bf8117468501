package com.ruoyi.config;

public interface MyConfig {


    /**
     * 支付公众号(汇联)
     */
    String WX_APPID = "wx8f7a9346308ee56f";
    /**
     * 汇联机构号
     */
    String AgencyNo = "1228026";
    String AgencyNo_yc = "11301";
    /**
     * 商户
     */
    String HlMerchant = "1366993712427";// 付款

    /**
     * 回调通知地址
     */
    String Notice_URL = "https://fotoboxserve.cameraon.store/pay/notice";

    /**
     * 汇联公钥
     */
    String huilian_publicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAs53yaPCdlEQ7vaXwKzM8vujx8TS8vvRl98dXvk7Nbb/w0QciU8IeFwTeuhtomb4W604kbZQMlDFL8csWUr1mhndRmqmA7QvqJwX1CH0+vA+EbuBbp7cIRGjUYp4nepZQ6Wkd2sJmw9LERzcnaODN2QzOyebDzF7+0uWdmoeTu4GP0VKqCVnmugvXR80rkQ/QHDGqFRnwpKLkgCST3gVOC1pGEt7IcYXj2FYbQGLgep6EwnYF2LcIR+wF4Z/hUBHdgpcdpok0+hBQnznEIV5VHokz2le0pjrrb6fhfrsLkNOyR+PbeaBJ9wQkUIrekHOmK+9xLJHjzf7kZqJydtnZgwIDAQAB";
    /**
     * 公钥
     */
    String PublicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEArguaJbXLnJhM7+bO9kPJAIiyUFkbaoNahx0pumtvAcLRLDlXXsPXqEfgRIK5ZF9tENzRalk0yMArWUSMSzUctRScnm8YvSS+MO5yXu/m0ROPrnkxkoyoYg5xjLGi4mIGf3BzXI/7DGhWXucdWUgBiGvtFQqpkVYVdNkAlgjWCWsvF2A5mAqrzDpXGmT7wORJKuU0XuaXLPWUAlxl+ZdcgajHAjAJITtuddNK49SD/Z6nhx6NW69LSvj1mPHkG4h5riDf/CUuR5/5NHi9hO2Qq8lpEyDu155frk8ULgMPyrZsAm4GnyrXZsiA13QPVz10/ywHQ4Wx3eToii0E1R7hyQIDAQAB";

    /**
     * 私钥
     */
    String PrivateKey = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCuC5oltcucmEzv5s72Q8kAiLJQWRtqg1qHHSm6a28BwtEsOVdew9eoR+BEgrlkX20Q3NFqWTTIwCtZRIxLNRy1FJyebxi9JL4w7nJe7+bRE4+ueTGSjKhiDnGMsaLiYgZ/cHNcj/sMaFZe5x1ZSAGIa+0VCqmRVhV02QCWCNYJay8XYDmYCqvMOlcaZPvA5Ekq5TRe5pcs9ZQCXGX5l1yBqMcCMAkhO25100rj1IP9nqeHHo1br0tK+PWY8eQbiHmuIN/8JS5Hn/k0eL2E7ZCryWkTIO7Xnl+uTxQuAw/KtmwCbgafKtdmyIDXdA9XPXT/LAdDhbHd5OiKLQTVHuHJAgMBAAECggEAGQLd0hGAAy73z3nxv/4ZwpPiXB2SDQp3Vfdg6cNKnowqlxpebeXi9fHuTqoijkQQXl39UjUjmr3S/O8W4i/twjAGGdaJTZUcJ9f8Y5xCJUWUXlRYRWElSYId20QjzjicCnBUg7bFMxFDaDv6QbxRTBOGbIGtwI8IYlRU+zql15BsmdoqLhiUqO0Lt1G9OYTBsS5loGLRXWq+QOS6N8xgr/+wY8yVkcUMIikFTxpbH90pqsiGo1UnU7f/5XooPdU9hGhlX4AbssZJE5tLSg2ukngZLKO0+Hm33hhx8dPnnPTwKPgsD6CmNU1bxJOjotWlI04fYeikBdC+xS3ON2bdyQKBgQDrgFkS9882fyP1JeU6i4AmpWwQwnfMNjcIGCnNjKEJ79xd+cZE7hwkLAuOMT0w4I/PR/nmIi9rHlvzMgYoFXmVFGaRDDBbMLkN10QX6zQvyo0tgw9u6vhJUCO+fZtQ6d3ZrGqYEJiF7Z9s2d/KHQDO2xJLkX3r2Q0QT7aiJ2vMZwKBgQC9MdXzWoRW+rvnTM0LpyzsKLnPbzCbcX7JWyOrYmY+qX2uQItvU/D5hl1ceFJwds4cJhN9bCwjIIIp/kTKUlhNUKo4+iLybvjdEb0FkhY3Ed9xatwEIfYMJwkjgPMYgEfTCBnNQ7FrPj3e0wc+lQtXc1Q1y8xbBKi/4BgGqW4CTwKBgQCRJDZBqXcNCEjaAvQHJAjkSJmUrrX0pwk9tN62r1Bk9m6jgJhAzVuMs5Sfmxsyb4RSZPLQcgs1gq168j63H50iU0Nva8q1uQ6kSCLQdwZf2f8uqJKvcqN5U6nj8jfyI2C2kjvT4o6OJEhYZuM1BrEDzCK3bkDkRY52mhpeT9txSwKBgQCznEE6Qz+J94U1o9KxcmcSV+N2i1+A4rFz5SMqzEK20F9xcvMsoGsP6EBvzgatoAwx6u/rzSrlujH4Pdz+mkgcNi0z5DwTHckCzpk9VZ/jbgNLgCAZiEg250QkwYGHTBljvbgJB2/CmRdRl9Oz1Pksb1+Npsl9yyGM7ujAQk8evwKBgG0mWFoQeDHjvkmrugOD3Y+4He2lwkXlnEeDedsDtJWZKNqDqZrso3bjMB2RN3vsGx8KPJS7/bYTUTUC//GI5G9RP1W0WEKyfZwqwpGSDy6LBsOLfihrPBB2HDvkCJA+SfDHQjBxZimTK7RGITNFDuwzWgKQs5eT43qO6QbQRjgs";
    String PrivateKey_yc = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCd63oRRgnP8aryRK0Hjl+64DMl5TCnldEogi5/HtrXhz5KmftdZoO7gcSfMyJhOGsW+PkpSpKG8h9JyjHPpPVTthbByUjjuzRVOQM9WFoQ1iPgDcWmDalWVEguRl++CzlrpbQFiZQlg4DQICH+kt91CSVHY35gIiG7WTbSqaJzja+/uuF15SRpBNQiDUP4+5VN+SaUwBzezrDnK2eWohuBtOKL2oArp0QQ2Nao4uiC8vY1xBWKs/+oBF17UeyabQ3ITXx9D+2UU+CyLXlWv1W/4s1lxZpfqzJQN7cahZ63PjsmnjTd8sdhH/EeqFK89I95NaWGtTs/Q9CXr5F4ZgD9AgMBAAECggEAaS4ndU3Sxz9M/mOjYz7+n7coGftWtqfgUTyzv1NLqvWXFZUNiJ3463B5BxfFtD8hhIF7YncatOqa0EjteEGEbKcA7T0lYRiUuJZqg/dOtZwK9PX3SKwctzUXhkq1Bhzvt7T7/BwwIfQ+XWCpg6dLUeYvgCbSSzEwilnXW+REIPWX1CcKitzDaBnvO1UxpWdhhgO5pWo3BXrKTmSKKxkbycFdimGImDw53NfN2MekvVdFIOxzjJUkBUPpOzusG101+iwVq9kr4YlIe/uno10JgtIA5NGvnvg1mUlxS9qkwdDkv6KX6mnxAiVllCwvRIbMhjP5DMCzr91Q05yLSrpMOQKBgQDMNFkdsp4crB9qVej5gAo+HO3S/mvgeRyrTSYmIS4QR+6e+eKlmOumYwNhYJY4N8/ifoNTWH/TidL/1lI7yvvkzcM5hl/yT3OQkPZKw4/tgDro0SSOsnLUaM6q6KzA2TluC5uxYi4hJS/g37VHLK7sHPC/mRZxVoMK8krFeV0ABwKBgQDF+bfANZNOgnZ6XTA22F6J0gPPThk7rvvEfrt8CfSDgiPG6KTlV6VON0u2bPIvcvWNKea1B4P3OfsW93D6y4Q/g9XiN7w3lzAuITb5F+oFZ1ESUktemf8KZEQ8jGyKI2DaMVXSAffXTH9m0DkEE2vJ3/GHLvS6ukkyHdlxb4xt2wKBgQCZ8hEI+YKHfR+F69RiXCvcnbSRb2rmZIe/gL702ei/L2tugFZknz9wRY7j14wIP9NCHjxlFf/v+ySN6QY/u4xg9tppwKsOouUtpIt426vi0dwXbqxPw6ocBhNf919dV6YbFzc96BV8uWs57YzdZotMx5ib6Q5pTV4nX3gNHayF+wKBgBSBXOrnoa5MDQF3jXrgxkn4GrTqHer0oNclWT3rugu0+werKQkoHtMPHyRx/FO10IEiiebONHjy5HLohicz1SrO+ORJaZFIc5ETl6INPAv7O4ZuR6gwwx3MbNTopoVYTLvMyFrMIWOHZNq9TmqBqHRsYZPi5S5CBTrPpKn/J8hpAoGBAJk0VITa+ZQonKF8H3iUzaIegZK410eco29bDP4gdtz9SVW9uoMTMLMpeXiftS/SvDvasCHxo/1mWJod4kLdqd72hvzTpXuI6GxI1EhmdJmy7v7AyrAhHkaMeaL4WKLTj/GcfluwbdZdIRrKez7aKCZqQeQz3NAb0100D/9dEyrP";
    /**
     * 代付商户号
     */
    String dfMerchant = "";
    /**
     * 代付私钥
     */
    String daiFu_rsa_privateKey = "";


    /**
     * 获取openid
     */
    String OPENID_URL = "https://open.huilianpay.com/output/openId?redirectUrl=%s&attach=%s";

    String Url = "https://open.huilian.cloud";
//    String Url = "http://localhost:8080";

    String OrderUrl = Url + "/order";
    String PayUrl = Url + "/pay";
    String Merchant = Url + "/merchant";
    String Daifu = Url + "/daifu";
    String CloudmoneyUrl = Url + "/cloudmoney";
    String DeviceUrl = Url + "/device";

    String MerchantRegists = Merchant + "/merchantApply";
    String uploadUrl = Merchant + "/uploadPhoto";
    String IotBox = DeviceUrl + "/iotBox";
    String cloudHorn = DeviceUrl + "/cloudHorn";
    String IOT = DeviceUrl + "/iot";
}
