package com.ruoyi.web.controller.pay;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.device.domain.DeviceCamera;
import com.ruoyi.device.service.IDeviceCameraService;
import com.ruoyi.dto.EzetapPayGenerateQrReq;
import com.ruoyi.dto.Result;
import com.ruoyi.order.domain.OrderCamera;
import com.ruoyi.order.service.IOrderCameraService;
import com.ruoyi.system.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * EzetapPay
 */
@Slf4j
@RestController
@RequestMapping("/EzetapPay")
public class EzetapPay {

    @Resource
    private RestTemplate restTemplate;
    @Autowired
    private IDeviceCameraService deviceCameraService;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private IOrderCameraService orderCameraService;

    /**
     * APP KEY
     */
    private static final String API_APP_KEY = "0e528274-7284-4786-850e-4531c52e7a84";  // 测试用：827f7013-fb5c-4e1c-b042-bd8742c677bb

    /**
     * 用户名
     */
    private static final String API_USER_NAME = "7624940295"; // 测试用：9876526001

    /**
     * 创建支付码
     */
    @GetMapping("/createPayCode")
    public Result createPayCode(double amount, String orderNo, @RequestParam(required = false) String deviceId) {
        log.info("创建Ezetap付款码");
        // 创建请求参数
        OrderCamera order = new OrderCamera();
        order.setOrderId(orderNo);
        order.setDeviceId(deviceId);
        order.setOrderPrice((long) amount);
        order.setPhotoType(6);
        order.setProductDescription("大头贴");
        order.setProductQuantity(1L);


        EzetapPayGenerateQrReq ezetapPayGenerateQrReq = new EzetapPayGenerateQrReq();
        if (StrUtil.isEmpty(deviceId)) {
            ezetapPayGenerateQrReq.setAppKey(API_APP_KEY);
            ezetapPayGenerateQrReq.setUsername(API_USER_NAME);
        } else {
            DeviceCamera camera = deviceCameraService.selectDeviceCameraByDeviceId(deviceId);
            if (camera == null) {
                return Result.fail(500, "设备id不存在", String.valueOf(System.currentTimeMillis()));
            }
            order.setDeviceName(camera.getDeviceName());
            if (camera.getDeviceStatus() == 0) {
                return Result.fail(500, "设备已停用", String.valueOf(System.currentTimeMillis()));
            }

            SysUser sysUser = sysUserService.selectUserById(camera.getUserId());
            if (sysUser == null) return Result.fail(500, "设备无负责人", String.valueOf(System.currentTimeMillis()));
            if (StrUtil.isEmpty(sysUser.getMerchantId()) || StrUtil.isEmpty(sysUser.getPrivateKey()))
                return Result.fail(500, "请完善账户信息", String.valueOf(System.currentTimeMillis()));


            order.setMchid(sysUser.getMerchantId());
            order.setUserId(camera.getUserId());
            ezetapPayGenerateQrReq.setAppKey(sysUser.getPrivateKey());
            ezetapPayGenerateQrReq.setUsername(sysUser.getMerchantId());
        }

        ezetapPayGenerateQrReq.setAmount(amount);
        if (!StrUtil.isEmpty(orderNo)) {
            ezetapPayGenerateQrReq.setExternalRefNumber(orderNo);
        } else {
            return Result.fail(500, "订单号为空！", String.valueOf(System.currentTimeMillis()));
        }
        if (!orderCameraService.createOrderHL(order).getSuccess()) {
            return Result.fail(500, "订单号创建失败！", String.valueOf(System.currentTimeMillis()));
        }

        String ezetapPayGenerateQrReqStr = JSON.toJSONString(ezetapPayGenerateQrReq);
        // 设置请求头
        HttpHeaders requestHeaders = new HttpHeaders();
        requestHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> r = new HttpEntity<String>(ezetapPayGenerateQrReqStr, requestHeaders);
        // 请求地址
        String url = "https://www.ezetap.com/api/2.0/merchant/upi/qrcode/generate";
        String result = restTemplate.postForObject(url, r, String.class);
        // 将json字符串转为json对象
        JSONObject jsonObject = JSON.parseObject(result);
        // 如果返回的json对象中 success为true
        if (jsonObject != null && jsonObject.getBoolean("success")) {
            // 获取返回的数据
            Map<String, Object> data = new HashMap<>();
            data.put("txnId", jsonObject.getString("txnId"));
            data.put("qrCodeUri", jsonObject.getString("qrCodeUri"));
            return Result.ok(200, "创建支付码成功", String.valueOf(System.currentTimeMillis()), data);
        } else {
            if (jsonObject != null) {
                log.info("创建支付码失败：{}", jsonObject.getString("message"));
            }
            return Result.fail(500, "创建支付码失败", String.valueOf(System.currentTimeMillis()));
        }
    }

    /**
     * 获取支付状态
     */
    @GetMapping("/getPayStatus")
    public Result getPayStatus(String txnId, @RequestParam(required = false) String orderId) {
        log.info("获取支付状态");
        // 创建请求参数
        Map<String, Object> params = new HashMap<>();
        params.put("appKey", API_APP_KEY);
        params.put("username", API_USER_NAME);
        params.put("txnId", txnId);
        String paramsStr = JSON.toJSONString(params);
        // 设置请求头
        HttpHeaders requestHeaders = new HttpHeaders();
        requestHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> r = new HttpEntity<String>(paramsStr, requestHeaders);
        // 请求地址
        String url = "https://www.ezetap.com/api/2.0/payment/status";
        String result = restTemplate.postForObject(url, r, String.class);
        // 将json字符串转为json对象
        JSONObject jsonObject = JSON.parseObject(result);
        // 如果返回的json对象中 success为true
        if (jsonObject != null && jsonObject.getBoolean("success")) {
            // 获取返回的数据
            String status = jsonObject.getString("status");
            if (!StrUtil.isEmpty(orderId))
                orderCameraService.update().eq("order_id", orderId).set("order_status", 1).update();
            return Result.ok(200, "获取支付状态成功", String.valueOf(System.currentTimeMillis()), status);
        } else {
            if (jsonObject != null) {
                log.info("获取支付状态失败：{}", jsonObject.getString("message"));
            }
            return Result.fail(500, "获取支付状态失败", String.valueOf(System.currentTimeMillis()));
        }
    }
}
