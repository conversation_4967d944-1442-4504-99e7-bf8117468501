package com.yunchuang.wxapp.service.client;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.device.domain.DevicePrinter;

import java.util.Map;

/**
 * 客户端
 * <br />
 * 打印机设备 Service 接口
 */
public interface ICDevicePrinterService extends IService<DevicePrinter> {

    /**
     * 查询打印机设备列表（懒加载+搜索）
     * 支持按设备编号或地址搜索
     *
     * @param pageNum   页码
     * @param pageSize  每页数量
     * @param keyword   搜索关键字（设备编号或地址）
     * @param longitude 用户经度（可选，用于计算距离）
     * @param latitude  用户纬度（可选，用于计算距离）
     * @return 分页结果
     */
    Map<String, Object> getDevicePrinterList(Integer pageNum, Integer pageSize, String keyword, Double longitude, Double latitude);
}
