package com.ruoyi.web.controller.wxapp.client;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.utils.MyResultUtil;
import com.yunchuang.wxapp.model.domain.HomeSet;

import com.yunchuang.wxapp.service.client.*;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 公共Controller
 * <p>
 * 无需登录验证
 * </p>
 */
@RestController
@RequestMapping("/client/wxapp/common")
public class CCommonController extends BaseController {

    @Resource
    private ICHomeSetService cHomeSetService;

    @Resource
    private ICUserAgreementService cUserAgreementService;

    @Resource
    private ICPrivacyPolicyService cPrivacyPolicyService;

    @Resource
    private ICConsultationService cConsultationService;

    @Resource
    private ICPhotoFrameSeriesService cPhotoFrameSeriesService;

    @Resource
    private ICIdPhotoPrintParamsService cIdPhotoPrintParamsService;

    @Resource
    private ICDevicePrinterService cDevicePrinterService;

    /**
     * 获取首页设置
     */
    @GetMapping("/home_set")
    public Map<String, Object> getHomeSet() {
        return MyResultUtil.success(cHomeSetService.getHomeSetList());
    }


    /**
     * 获取轮播图列表
     */
    @GetMapping("/carousel_list")
    public Map<String, Object> getCarouselList() {
        List<HomeSet> list = cHomeSetService.getCarouselList();
        return MyResultUtil.success(list);
    }

    /**
     * 获取用户协议
     */
    @GetMapping("/user_agreement")
    public Map<String, Object> getUserAgreement() {
        return MyResultUtil.success(cUserAgreementService.getEnabledUserAgreement());
    }

    /**
     * 获取隐私政策
     */
    @GetMapping("/privacy_policy")
    public Map<String, Object> getPrivacyPolicy() {
        return MyResultUtil.success(cPrivacyPolicyService.getEnabledPrivacyPolicy());
    }

    /**
     * 获取咨询问题列表
     */
    @GetMapping("/consult_list")
    public Map<String, Object> getConsultList(@RequestParam Integer type) {
        return MyResultUtil.success(cConsultationService.getConsultList(type));
    }

    /**
     * 获取咨询信息
     */
    @GetMapping("/consult")
    public Map<String, Object> getConsultInfo(@RequestParam Integer id) {
        return MyResultUtil.success(cConsultationService.getById(id));
    }

    /**
     * 获取相框列表
     */
    @GetMapping("/frame_list")
    public Map<String, Object> getFrameList(@RequestParam(required = false) String photoFrameIdStr) {
        return MyResultUtil.success(cPhotoFrameSeriesService.getFrameList(photoFrameIdStr));
    }

    /**
     * 获取相框列表 - 系列分组
     */
    @GetMapping("/frame_list_group")
    public Map<String, Object> getFrameListGroup() {
        return MyResultUtil.success(cPhotoFrameSeriesService.getFrameListGroup());
    }

    /**
     * 获取证件照打印参数列表（按类型分组）
     */
    @GetMapping("/id_photo_print_params")
    public Map<String, Object> getIdPhotoPrintParams() {
        return MyResultUtil.success(cIdPhotoPrintParamsService.getEnabledParamsGroupByType());
    }

    /**
     * 根据类型获取证件照打印参数
     */
    @GetMapping("/id_photo_print_params_by_type")
    public Map<String, Object> getIdPhotoPrintParamsByType(@RequestParam Integer type) {
        return MyResultUtil.success(cIdPhotoPrintParamsService.getIdPhotoPrintParamsByType(type));
    }

    /**
     * 查询打印机设备列表（懒加载+搜索+距离计算）
     * 支持按设备编号或地址搜索，可计算与用户位置的距离
     */
    @GetMapping("/device_printer_list")
    public Map<String, Object> getDevicePrinterList(
            @RequestParam(required = false, defaultValue = "1") Integer pageNum,
            @RequestParam(required = false, defaultValue = "20") Integer pageSize,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Double longitude,
            @RequestParam(required = false) Double latitude) {
      
        return success(cDevicePrinterService.getDevicePrinterList(pageNum, pageSize, keyword, longitude, latitude));
    }
}
