package com.yunchuang.wxapp.model.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 用户对象 wxapp_user
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("wxapp_user")
public class User extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 头像
     */
    @Excel(name = "头像")
    private String avatar;

    /**
     * 昵称
     */
    @Excel(name = "昵称")
    private String nickname;

    /**
     * 拍摄次数
     */
    @Excel(name = "拍摄次数")
    private Long shotNum;

    /**
     * 最近拍摄时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "最近拍摄时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date lastTimeShot;

    /**
     * openID
     */
    @Excel(name = "openID")
    private String openid;

    /**
     * 手机号码
     */
    @Excel(name = "手机号码")
    private String mobile;

    /**
     * 用户状态（0-正常 1-删除）
     */
    @Excel(name = "用户状态")
    private Integer status;
}
