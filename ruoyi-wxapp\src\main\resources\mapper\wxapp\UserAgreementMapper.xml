<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yunchuang.wxapp.mapper.UserAgreementMapper">

    <resultMap type="UserAgreement" id="UserAgreementResult">
        <result property="id" column="id"/>
        <result property="releaseDate" column="release_date"/>
        <result property="effectiveDate" column="effective_date"/>
        <result property="agreementContent" column="agreement_content"/>
        <result property="beEnabled" column="be_enabled"/>
    </resultMap>

    <sql id="selectUserAgreementVo">
        select id, release_date, effective_date, agreement_content, be_enabled
        from wxapp_user_agreement
    </sql>

    <select id="selectUserAgreementList" parameterType="UserAgreement" resultMap="UserAgreementResult">
        <include refid="selectUserAgreementVo"/>
        <where>
            <if test="releaseDate != null ">and release_date = #{releaseDate}</if>
            <if test="effectiveDate != null ">and effective_date = #{effectiveDate}</if>
            <if test="agreementContent != null  and agreementContent != ''">and agreement_content like concat('%',
                #{agreementContent}, '%')
            </if>
            <if test="beEnabled != null ">and be_enabled = #{beEnabled}</if>
        </where>
    </select>

    <select id="selectUserAgreementById" parameterType="Long" resultMap="UserAgreementResult">
        <include refid="selectUserAgreementVo"/>
        where id = #{id}
    </select>

    <insert id="insertUserAgreement" parameterType="UserAgreement" useGeneratedKeys="true" keyProperty="id">
        insert into wxapp_user_agreement
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="releaseDate != null">release_date,</if>
            <if test="effectiveDate != null">effective_date,</if>
            <if test="agreementContent != null and agreementContent != ''">agreement_content,</if>
            <if test="beEnabled != null">be_enabled,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="releaseDate != null">#{releaseDate},</if>
            <if test="effectiveDate != null">#{effectiveDate},</if>
            <if test="agreementContent != null and agreementContent != ''">#{agreementContent},</if>
            <if test="beEnabled != null">#{beEnabled},</if>
        </trim>
    </insert>

    <update id="updateUserAgreement" parameterType="UserAgreement">
        update wxapp_user_agreement
        <trim prefix="SET" suffixOverrides=",">
            <if test="releaseDate != null">release_date = #{releaseDate},</if>
            <if test="effectiveDate != null">effective_date = #{effectiveDate},</if>
            <if test="agreementContent != null and agreementContent != ''">agreement_content = #{agreementContent},</if>
            <if test="beEnabled != null">be_enabled = #{beEnabled},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUserAgreementById" parameterType="Long">
        delete
        from wxapp_user_agreement
        where id = #{id}
    </delete>

    <delete id="deleteUserAgreementByIds" parameterType="String">
        delete from wxapp_user_agreement where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>