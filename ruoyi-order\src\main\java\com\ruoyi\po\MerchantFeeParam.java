package com.ruoyi.po;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: 少凡
 * @Date 2018/11/18 15:42
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MerchantFeeParam implements Serializable {

    private static final long serialVersionUID = 4194375426780382487L;
    private Integer id;
    private Integer mchId;
    //    private String providerId;
    private String channelType;
    private String feeType;
    private BigDecimal feeValue;
    private Date createTime;
    private Date updateTime;

    private String mcc;
    private String payScene;

    public MerchantFeeParam(String channelType, String feeType, BigDecimal feeValue) {
        this.channelType = channelType;
        this.feeType = feeType;
        this.feeValue = feeValue;
    }



    /*public static String genJsonBase64(List<MerchantFeeParam> list) throws JSONException {
        List<JSONObject> arr = new ArrayList<JSONObject>();
        for (MerchantFeeParam param: list) {
            JSONObject obj = new JSONObject();
            obj.put("ChannelType", param.channelType);
            obj.put("FeeType", param.feeType);
            obj.put("FeeValue", param.feeValue.toString());

            arr.add(obj);

            System.out.println(obj.toString());
        }

        return new BASE64Encoder().encode(new JSONArray(arr).toString().getBytes());
    }

    public static org.json.JSONArray genJsonByHuifu(List<MerchantFeeParam> list) throws org.json.JSONException {
        org.json.JSONArray jsonArray = new org.json.JSONArray();
        for (MerchantFeeParam param: list) {
            org.json.JSONObject obj = new org.json.JSONObject();
            obj.put("feeRate", param.feeValue);
            obj.put("mcc", param.mcc);
//            obj.put("feeType", param.feeType.getFeeCode());
            obj.put("payScene", param.payScene);
            jsonArray.put(obj);
        }
        return jsonArray;
    }*/


}
