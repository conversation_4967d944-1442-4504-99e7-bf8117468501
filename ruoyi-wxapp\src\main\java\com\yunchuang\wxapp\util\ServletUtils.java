package com.yunchuang.wxapp.util;

import com.ruoyi.common.utils.StringUtils;
import org.springframework.util.LinkedCaseInsensitiveMap;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

public class ServletUtils {
    public ServletUtils() {
    }

    public static String getParameter(String name) {
        return getRequest().getParameter(name);
    }

//    public static String getParameter(String name, String defaultValue) {
//        return Convert.toStr(getRequest().getParameter(name), defaultValue);
//    }
//
//    public static Integer getParameterToInt(String name) {
//        return Convert.toInt(getRequest().getParameter(name));
//    }
//
//    public static Integer getParameterToInt(String name, Integer defaultValue) {
//        return Convert.toInt(getRequest().getParameter(name), defaultValue);
//    }
//
//    public static Boolean getParameterToBool(String name) {
//        return Convert.toBool(getRequest().getParameter(name));
//    }
//
//    public static Boolean getParameterToBool(String name, Boolean defaultValue) {
//        return Convert.toBool(getRequest().getParameter(name), defaultValue);
//    }

    public static Map<String, String[]> getParams(ServletRequest request) {
        Map<String, String[]> map = request.getParameterMap();
        return Collections.unmodifiableMap(map);
    }

    public static Map<String, String> getParamMap(ServletRequest request) {
        Map<String, String> params = new HashMap();

        for (Map.Entry<String, String[]> entry : getParams(request).entrySet()) {
            params.put(entry.getKey(), StringUtils.join(entry.getValue(), ","));
        }

        return params;
    }

    public static HttpServletRequest getRequest() {
        try {
            return getRequestAttributes().getRequest();
        } catch (Exception var1) {
            return null;
        }
    }

    public static HttpServletResponse getResponse() {
        try {
            return getRequestAttributes().getResponse();
        } catch (Exception var1) {
            return null;
        }
    }

    public static HttpSession getSession() {
        return getRequest().getSession();
    }

    public static ServletRequestAttributes getRequestAttributes() {
        try {
            RequestAttributes attributes = RequestContextHolder.getRequestAttributes();
            return (ServletRequestAttributes) attributes;
        } catch (Exception var1) {
            return null;
        }
    }

    public static String getHeader(HttpServletRequest request, String name) {
        String value = request.getHeader(name);
        return StringUtils.isEmpty(value) ? "" : urlDecode(value);
    }

    public static Map<String, String> getHeaders(HttpServletRequest request) {
        Map<String, String> map = new LinkedCaseInsensitiveMap();
        Enumeration<String> enumeration = request.getHeaderNames();
        if (enumeration != null) {
            while (enumeration.hasMoreElements()) {
                String key = (String) enumeration.nextElement();
                String value = request.getHeader(key);
                map.put(key, value);
            }
        }

        return map;
    }

    public static void renderString(HttpServletResponse response, String string) {
        try {
            response.setStatus(200);
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            response.getWriter().print(string);
        } catch (IOException e) {
            e.printStackTrace();
        }

    }

    public static boolean isAjaxRequest(HttpServletRequest request) {
        String accept = request.getHeader("accept");
        if (accept != null && accept.contains("application/json")) {
            return true;
        } else {
            String xRequestedWith = request.getHeader("X-Requested-With");
            if (xRequestedWith != null && xRequestedWith.contains("XMLHttpRequest")) {
                return true;
            } else {
                String uri = request.getRequestURI();
                if (StringUtils.inStringIgnoreCase(uri, new String[]{".json", ".xml"})) {
                    return true;
                } else {
                    String ajax = request.getParameter("__ajax");
                    return StringUtils.inStringIgnoreCase(ajax, new String[]{"json", "xml"});
                }
            }
        }
    }

    public static String urlEncode(String str) {
        try {
            return URLEncoder.encode(str, "UTF-8");
        } catch (UnsupportedEncodingException var2) {
            return "";
        }
    }

    public static String urlDecode(String str) {
        try {
            return URLDecoder.decode(str, "UTF-8");
        } catch (UnsupportedEncodingException var2) {
            return "";
        }
    }

//    public static Mono<Void> webFluxResponseWriter(ServerHttpResponse response, Object value) {
//        return webFluxResponseWriter(response, HttpStatus.OK, value, R.FAIL);
//    }

//    public static Mono<Void> webFluxResponseWriter(ServerHttpResponse response, Object value, int code) {
//        return webFluxResponseWriter(response, HttpStatus.OK, value, code);
//    }

//    public static Mono<Void> webFluxResponseWriter(ServerHttpResponse response, HttpStatus status, Object value, int code) {
//        return webFluxResponseWriter(response, "application/json", status, value, code);
//    }

//    public static Mono<Void> webFluxResponseWriter(ServerHttpResponse response, String contentType, HttpStatus status, Object value, int code) {
//        response.setStatusCode(status);
//        response.getHeaders().add("Content-Type", contentType);
//        R<?> result = R.fail(code, value.toString());
//        DataBuffer dataBuffer = response.bufferFactory().wrap(JSON.toJSONString(result).getBytes());
//        return response.writeWith(Mono.just(dataBuffer));
//    }
}