package com.ruoyi.utils.collect;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.order.domain.OrderCamera;
import com.ruoyi.order.domain.OrderCollect;
import com.ruoyi.order.service.IOrderCollectService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class CollectOrder {
    @Autowired
    private IOrderCollectService orderCollectService;

//    public static void payOrder(OrderCamera orderCamera){
//
//        //汇总（主订单）
//        OrderCollect orderCollect = new OrderCollect();
//        orderCollect.setTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM, orderCamera.getCreateTime()));
//        orderCollect.setMerchantId(orderCamera.getMchid());
//        Integer count = orderCollectService.query().eq("merchant_id", orderCollect.getMerchantId()).eq("time", orderCollect.getTime()).count();
//        if (count > 0) {
//            orderCollectService.update()
//                    .setSql("count = count + " + orderCamera.getMoneyReceived())
//                    .eq("merchant_id", orderCollect.getMerchantId())
//                    .eq("time", orderCollect.getTime()).update();
//        } else {
//            orderCollect.setCount(orderCamera.getMoneyReceived());
//            orderCollectService.save(orderCollect);
//        }
//
//        //如果存在分账订单
//        List<OrderCamera> _orders = orderCameraService.query().like("order_id", "_" + orderCamera.getOrderId()).list();
//        if (_orders.size() > 0) {
//            //分账已支付
//            orderCameraService.update().like("order_id", "_" + orderCamera.getOrderId()).set("order_status", 1).set("pay_time", payTime).update();
//            //（汇总 分账人）
//            for (OrderCamera _order : _orders) {
//                if (_order.getMoneyReceived() == null) _order.setMoneyReceived(0L);
//                OrderCollect _orderCollect = new OrderCollect();
//                _orderCollect.setTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM, _order.getCreateTime()));
//                _orderCollect.setMerchantId(_order.getMchid());
//                Integer _count = orderCollectService.query().eq("merchant_id", _orderCollect.getMerchantId()).eq("time", _orderCollect.getTime()).count();
//                if (_count > 0) {
//                    orderCollectService.update()
//                            .setSql("count = count + " + _order.getMoneyReceived())
//                            .eq("merchant_id", _orderCollect.getMerchantId())
//                            .eq("time", _orderCollect.getTime()).update();
//                } else {
//                    _orderCollect.setCount(_order.getMoneyReceived());
//                    orderCollectService.save(_orderCollect);
//                }
//            }
//        }
//
//    }


    public static void refundOrder(){

    }

}
