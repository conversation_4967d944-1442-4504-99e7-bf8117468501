<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <title>图片上传</title>
    <script type="text/javascript" src="/js/jquery.js"></script>
    <script type="text/javascript" src="/js/conversion.js"></script>
<!--    <script type="text/javascript" src="https://cdn.jsdelivr.net/gh/WangYuLue/image-conversion/build/conversion.js"></script>-->
    <input style="display: none" id="deviceId" type="text" th:value="${deviceId}" name="deviceId">
</head>

<body style="background-image: url(/js_pay_background.jpg);background-size: cover;background-position: center center">
<div style="height: 150vw;width: 100%;" >
    <div style="font-size: 70px;text-align: center;font-style: italic"> 上传照片打印 </div>
    <div style="width: 100vw;height:110vw;display: flex;align-items: center;justify-content: center;margin-top: 50px">
        <img style="width: 100%;height: 100%;object-fit: contain;" id="img" src="" >
    </div>
    <input onchange="view()" style="height: 100px;margin-left: 100px;margin-top: 50px;font-size: 50px;text-align: center" id="demo" type="file" ><br>
    <button onclick="upload_base()" style="background: green;width: 20vw;height: 100px;font-size: 50px;margin-top: 50px;margin-left: 40%" >上传</button>
</div>

</body>

<script type="text/javascript">
    let base64Data;
    function view(){
        const file = document.getElementById('demo').files[0];
        imageConversion.compress(file,{
            quality: 0.5,
            type: "image/jpeg",
            width: 800,
            height: 800,
            scale: 0.5,
        }).then(res=>{
            console.log("res");
            console.log(res);
            var img = document.getElementById("img");
            // img.src = URL.createObjectURL(res);
            // 创建FileReader对象
            var reader = new FileReader();
            // 读取Blob内容
            reader.readAsDataURL(res);

            // 读取完成后的回调函数
            reader.onloadend = function () {
                // 获取base64编码的数据
                base64Data = reader.result;
                // 打印或使用base64Data
                img.src = base64Data
            };
        })
    }

    function upload_base() {
        if (!base64Data || base64Data.length < 1000){
            alert("data error！！！！！")
            return;
        }
        // 定义两个字符串参数
        var myDeviceId = $('#deviceId').val();

        // 发送POST请求  http://fotoboxserve.yunchuang.store/photo/photo/upload_base64
        $.post("http://fotoboxserve.yunchuang.store/photo/photo/upload_base64s", {file_base64: base64Data.substr(23), deviceId: myDeviceId}, function(data) {
            // 请求成功后的处理
            if (data){
                alert(data.message);
            }else {
                alert("fail");
            }
        }).fail(function() {
                // 请求失败后的处理
                alert("internet error!!!");
            });
    }


    // var deviceId = $('#deviceId').val();
    // $('#box').aiiUpload({
    //     // action:'http://fotoboxserve.yunchuang.store/photo/photo/upload',
    //     action: 'http://127.0.0.1:8081/photo/photo/upload_base64',
    //     max_w: 800,
    //     max_h: 1000,
    //     subText: '照片',
    //     formIdName: 'id_card', //表字段名称
    //     myDeviceId: deviceId, //表字段名称
    //     //fileText:'选择图片'
    // });

</script>
</html>
