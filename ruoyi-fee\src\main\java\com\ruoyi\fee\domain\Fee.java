package com.ruoyi.fee.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 费用对象 fee
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("fee")
public class Fee extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /** 针对的照片类型收费 */
    @Excel(name = "针对的照片类型收费")
    private Long photoType;

    /** 收费方式 1金额 2比例*/
    @Excel(name = "收费方式")
    private Integer chargeType;

    /** 收费金额 */
    @Excel(name = "收费金额")
    private Integer feeAmount;

    /** 抽成 */
    @Excel(name = "抽成")
    private Float feeRate;

    /** 成本 */
    @Excel(name = "成本")
    private Integer cost;

    /** 收费排除设备 */
    @Excel(name = "收费排除设备")
    private String excludeDevice;
}
