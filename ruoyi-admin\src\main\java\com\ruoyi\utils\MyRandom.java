package com.ruoyi.utils;

import java.security.SecureRandom;
import java.util.HashSet;
import java.util.Random;

public class MyRandom {
    // 定义包含数字和大小写字母的字符集
    private static final String CHARACTERS = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";

    // 生成指定长度的随机字符串
    public static String generateRandomString(int length) {
        StringBuilder sb = new StringBuilder(length);
        SecureRandom random = new SecureRandom();
        for (int i = 0; i < length; i++) {
            int randomIndex = random.nextInt(CHARACTERS.length());
            sb.append(CHARACTERS.charAt(randomIndex));
        }
        return sb.toString();
    }
    public static String orderId() {
        Random random = new Random();
        int randomNumber = random.nextInt(10000); // 生成0到9999的随机数
        long timeMillis = System.currentTimeMillis();
        return String.valueOf(timeMillis * 10000 + randomNumber);
    }
}
