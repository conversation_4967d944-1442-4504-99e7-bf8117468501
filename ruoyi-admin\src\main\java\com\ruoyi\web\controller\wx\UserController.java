package com.ruoyi.web.controller.wx;


import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.device.domain.CameraSysImage;
import com.ruoyi.dto.Result;
import com.ruoyi.socket.WebSocketServer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.net.ssl.HttpsURLConnection;
import java.io.*;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;

@Slf4j
@RestController
@RequestMapping("/wx/user")
public class UserController {

    @GetMapping("/getUserId")
    public int getUserId(@RequestParam String equipID, @RequestParam String linkUserID) {

        WebSocketServer.sendInfo(linkUserID, equipID);
        return 1;
    }

    @GetMapping("/login")
    public Result login(@RequestParam String jsCode) {
        JSONObject jsonObject = authCode2Session("wx679da39897326b5a", "4f963b043436bc483a991bd94bc9d7e8", jsCode);
        System.out.println("jsonObject");
        System.out.println(jsonObject);
        return Result.ok(200, "请求成功", "", jsonObject);
    }

    public JSONObject authCode2Session(String appId, String secret, String jsCode) {

        String url = "https://api.weixin.qq.com/sns/jscode2session?appid=" + appId + "&secret=" + secret + "&js_code=" + jsCode + "&grant_type=authorization_code";
        String str = httpRequest(url, "GET", null);
        log.info("api/wx-mini/getSessionKey:" + str);
        if (Objects.isNull(str)) {
            log.info("微信服务器发送的数据为空！");
            return null;
        } else {
            return JSONObject.parseObject(str);
        }
    }

    /**
     * 通过code(前端传过来的),appid ,secret三个参数向微信服务器获取openid(用户在小程序的唯一标识)
     */
    public String httpRequest(String requestUrl, String requestMethod, String output) {
        try {
            URL url = new URL(requestUrl);
            HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
            connection.setDoOutput(true);
            connection.setDoInput(true);
            connection.setUseCaches(false);
            connection.setRequestMethod(requestMethod);
            if (null != output) {
                OutputStream outputStream = connection.getOutputStream();
                outputStream.write(output.getBytes(StandardCharsets.UTF_8));
                outputStream.close();
            }
            // 从输入流读取返回内容
            InputStream inputStream = connection.getInputStream();
            InputStreamReader inputStreamReader = new InputStreamReader(inputStream, StandardCharsets.UTF_8);
            BufferedReader bufferedReader = new BufferedReader(inputStreamReader);
            String str;
            StringBuilder buffer = new StringBuilder();
            while ((str = bufferedReader.readLine()) != null) {
                buffer.append(str);
            }
            bufferedReader.close();
            inputStreamReader.close();
            inputStream.close();
            connection.disconnect();
            return buffer.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }
}
