package com.ruoyi.message.util;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.message.config.ChuangLanSmsConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * 创蓝云智短信工具类
 */
@Slf4j
@Component
@EnableConfigurationProperties(ChuangLanSmsConfig.class)
public class ChuangLanSmsUtils {

    @Resource
    private ChuangLanSmsConfig chuangLanSmsConfig;

    // 相同内容单、群发短信URL
    private static String sendSmsUrl = "https://smssh1.253.com/msg/v1/send/json";

    // 不同内容单、群发短信URL
    private static String sendVariableSmsUrl = "https://smssh1.253.com/msg/variable/json";

    /**
     * 通过 POST 方法发送消息。
     *
     * @param path        请求的 URL 路径。
     * @param postContent 请求的 JSON 数据，以字符串形式表示。
     * @return 如果发送成功，则返回服务器响应的 JSON 字符串；否则返回 null。
     */
    public static String sendSmsByPost(String path, String postContent) {
        URL url = null;
        try {
            url = new URL(path);
            HttpURLConnection httpURLConnection = (HttpURLConnection) url.openConnection();
            httpURLConnection.setRequestMethod("POST");
            httpURLConnection.setConnectTimeout(10000);
            httpURLConnection.setReadTimeout(10000);
            httpURLConnection.setDoOutput(true);
            httpURLConnection.setDoInput(true);
            httpURLConnection.setRequestProperty("Charset", "UTF-8");
            httpURLConnection.setRequestProperty("Content-Type", "application/json");
            httpURLConnection.connect();
            OutputStream os = httpURLConnection.getOutputStream();
            os.write(postContent.getBytes(StandardCharsets.UTF_8));
            os.flush();
            StringBuilder sb = new StringBuilder();
            int httpRspCode = httpURLConnection.getResponseCode();
            if (httpRspCode == HttpURLConnection.HTTP_OK) {
                BufferedReader br = new BufferedReader(
                        new InputStreamReader(httpURLConnection.getInputStream(), StandardCharsets.UTF_8));
                String line = null;
                while ((line = br.readLine()) != null) {
                    sb.append(line);
                }
                br.close();
                return sb.toString();
            } else {
                log.error("创蓝云智短信发送失败，HTTP状态码：{}", httpRspCode);
            }
        } catch (Exception e) {
            log.error("创蓝云智短信发送异常：", e);
            return null;
        }
        return null;
    }

    /**
     * 相同内容单、群发接口
     *
     * @param msg   短信内容 此处填写审核通过的签名和模板，中文括号是代表短信签名。内容长度支持1～3500个字符（含变量）。用营销账号提交短信时最末尾需带上退订语“拒收请回复R”不支持小写r，否则营销短信将进入人工审核。
     * @param phone 手机号码 接收的手机号；多个手机号使用英文逗号间隔，一次不要超过 1000 个
     */
    public boolean sendSms(String msg, String phone) {
        Map<String, String> map = new HashMap<>();
        map.put("account", chuangLanSmsConfig.getAccount()); // API账号
        map.put("password", chuangLanSmsConfig.getPassword()); // API密码
        map.put("msg", msg); // 短信内容
        map.put("phone", phone); // 手机号
        JSONObject json = (JSONObject) JSONObject.toJSON(map);
        String result = sendSmsByPost(sendSmsUrl, json.toString());
        System.out.println(result);

        if (result != null) {
            try {
                JSONObject resultJson = JSONObject.parseObject(result);
                String code = resultJson.getString("code");
                if ("0".equals(code)) {
                    log.info("创蓝云智短信发送成功，手机号：{}，内容：{}", phone, msg);
                    return true;
                } else {
                    log.error("创蓝云智短信发送失败，手机号：{}，内容：{}，错误码：{}，错误信息：{}", phone, msg, code, resultJson.getString("errorMsg"));
                    return false;
                }
            } catch (Exception e) {
                log.error("创蓝云智短信发送结果解析异常：", e);
                return false;
            }
        } else {
            log.error("创蓝云智短信发送失败，手机号：{}，内容：{}，返回结果为空", phone, msg);
            return false;
        }
    }

    /**
     * 不同内容单、群发接口
     * <p>
     * PS：params 参数组的具体格式：参数组从左往右第一个为发送的手机号码，第二个为第一个变量，第三个为第二个变量，以此类推，变量的数目需要和msg里传的变量符号$var}数量一一对应。
     * </p>
     *
     * @param msg    短信内容 此处填写审核通过的签名和模板，中文括号是代表短信签名。内容长度支持1～3500个字符（含变量），变量符号固定使用{$var}，最多 20 个。用营销账号提交短信时最末尾需带上“拒收请回复R”不支持小写r，否则营销短信将进入人工审核，退订语只支持“拒收请回复R”。
     * @param params 短信参数 手机号码和变量参数，多组参数使用英文分号区分；此字段上限支持 1000 个参数组。格式不符的参数系统自动过滤掉
     */
    public boolean sendVariableSmsUrl(String msg, String params) {
        Map<String, String> map = new HashMap<>();
        map.put("account", chuangLanSmsConfig.getAccount()); // API账号
        map.put("password", chuangLanSmsConfig.getPassword()); // API密码
        map.put("msg", msg); // 短信内容
        map.put("params", params); // 手机号和变量参数
        JSONObject json = (JSONObject) JSONObject.toJSON(map);
        String result = sendSmsByPost(sendVariableSmsUrl, json.toString());
        System.out.println(result);

        if (result != null) {
            try {
                JSONObject resultJson = JSONObject.parseObject(result);
                String code = resultJson.getString("code");
                if ("0".equals(code)) {
                    log.info("创蓝云智短信发送成功，手机号：{}，内容：{}", params.split(";")[0], msg);
                    return true;
                } else {
                    log.error("创蓝云智短信发送失败，手机号：{}，内容：{}，错误码：{}，错误信息：{}", params.split(";")[0], msg, code, resultJson.getString("errorMsg"));
                    return false;
                }
            } catch (Exception e) {
                log.error("创蓝云智短信发送结果解析异常：", e);
                return false;
            }
        } else {
            log.error("创蓝云智短信发送失败，手机号：{}，内容：{}，返回结果为空", params.split(";")[0], msg);
            return false;
        }

    }
}
