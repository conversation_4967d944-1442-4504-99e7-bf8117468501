package com.ruoyi.common.config;

import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Arrays;

/**
 * 微信 RestTemplate 配置
 */
@Configuration
public class WeiXinRestTemplateConfig {

    /**
     * 微信 RestTemplate
     *
     * @param builder RestTemplate 构建器
     * @return RestTemplate
     */
    @Bean
    public RestTemplate weixinRestTemplate(RestTemplateBuilder builder) {
        MappingJackson2HttpMessageConverter converter = new MappingJackson2HttpMessageConverter();
        converter.setSupportedMediaTypes(Arrays.asList(MediaType.TEXT_PLAIN, MediaType.TEXT_HTML, MediaType.APPLICATION_JSON));

        return builder
                .setConnectTimeout(Duration.ofSeconds(5)) // 设置连接超时为 5 秒
                .setReadTimeout(Duration.ofSeconds(10))    // 设置读取超时为 10 秒
                .additionalMessageConverters(new StringHttpMessageConverter(StandardCharsets.UTF_8), converter) // 添加消息转换器
                .build();
    }
}