package com.ruoyi.generator.util;

/**
 * 系统常量类
 *
 * @BelongsProject: cloudguard
 * @BelongsPackage: com.wteam.guard.common
 * @Author: pzy
 * @CreateTime: 2023-02-17  21:36
 * @Version: 1.0
 */

public class Constant {

    public static final String AUTHORIZATION = "Authorization";

    public static final String OPEN_ID = "openid";
    public static final String SESSION_KEY = "session_key";
    public static final String UNION_ID = "unionid";

    public static final String ERR_CODE = "500";
    public static final String ERR_MSG = "error";

    public static final String USER_TOKEN = "user_token:";

    public static final String USER_INFO = "user_info:";

    public static final String Vip_Code = "Vip_Code:";

    public static final String PAYURL = "https://openapi.alipay.com/gateway.do";

    //订单生成中
    public static final Integer ORDERING = 0;

    //订单支付完成
    public static final Integer ORDER_SUCCESS = 1;

    //订单支付失败
    public static final Integer ORDER_FAIL = 2;

    //订单超时已关闭
    public static final Integer ORDER_CLOSE = 3;

    //支付成功
    public static final Integer PAY_SUCCESS = 0;

    //其它支付
    public static final Integer PAY_OTHER_STATUS = 6;

    //支付失败
    public static final Integer PAY_FAIL = 1;

    //等待支付
    public static final Integer WAIT_PAY = 2;

    //支付超时
    public static final Integer PAY_OUTTIME = 3;

    //支付方式 微信支付
    public static final Integer PAY_WX = 0;

    //支付方式 支付宝支付
    public static final Integer PAY_ZFB = 1;

    //订单金额未到商户的账户中
    public static final Integer NOT_ACCOUNT = 0;

    //订单金额已经到了商户的账户中
    public static final Integer IN_ACCOUNT = 1;

    //获取操作微信配置 本公司的
    public static String WX_CONFIG = "WX_CONFIG";

    //获取操作微信配置 客户的
    public static final String WX_USER_CONFIG1 = "WX_USER_CONFIG1";

    //获取操作支付宝配置
    public static final String ZFB_CONFIG = "ZFB_CONFIG";

    //刷新订单编号成功
    public static final Integer REORDER_SUCCESS = 1;

    //刷新订单编号失败
    public static final Integer REORDER_FAIL = 0;

    //支付宝订单交易状态返回结果码

    //交易创建，等待买家付款
    public static final String WAIT_BUYER_PAY = "WAIT_BUYER_PAY";

    //商户签约的产品支持退款功能的前提下，买家付款成功
    public static final String TRADE_SUCCESS = "TRADE_SUCCESS";

    //在指定时间段内未支付时关闭的交易或在交易完成全额退款成功时关闭的交易
    public static final String TRADE_CLOSED = "TRADE_CLOSED";

    //支付选择
    public static final Integer SCAN_CODE_PAYMENT = 1;


}
