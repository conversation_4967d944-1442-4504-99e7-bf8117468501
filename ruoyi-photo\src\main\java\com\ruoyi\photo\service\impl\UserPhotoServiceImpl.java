package com.ruoyi.photo.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.aliyun.oss.ClientException;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.common.auth.CredentialsProvider;
import com.aliyun.oss.common.auth.DefaultCredentialProvider;
import com.aliyun.oss.model.ObjectMetadata;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.QueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.oss.ALY_OSS;
import com.ruoyi.order.domain.OrderCamera;
import com.ruoyi.photo.domain.UserPhoto;
import com.ruoyi.photo.mapper.UserPhotoMapper;
import com.ruoyi.photo.service.IUserPhotoService;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

import com.ruoyi.photo.util.HttpClientSslUtils;


import static com.ruoyi.common.constant.Constant.*;

/**
 * photoService业务层处理
 *
 * <AUTHOR>
 * @date 2023-09-05
 */
@Service
public class UserPhotoServiceImpl extends ServiceImpl<UserPhotoMapper, UserPhoto> implements IUserPhotoService {
    @Autowired
    private UserPhotoMapper userPhotoMapper;


    /**
     * 查询photo
     *
     * @param id photo主键
     * @return photo
     */
    @Override
    public UserPhoto selectUserPhotoById(Long id) {
        return userPhotoMapper.selectUserPhotoById(id);
    }

    /**
     * 查询photo列表
     *
     * @param userPhoto photo
     * @return photo
     */
    @Override
    public List<UserPhoto> selectUserPhotoList(UserPhoto userPhoto) {

        QueryChainWrapper<UserPhoto> query = query();

        // 根据用户账号高级查询
        if (userPhoto.getOpenId() != null && !userPhoto.getOpenId().equals(""))
            query.like("open_id", userPhoto.getOpenId());

        // 根据设备号高级查询
        if (userPhoto.getDeviceId() != null && !userPhoto.getDeviceId().equals(""))
            query.like("device_id", userPhoto.getDeviceId());

        // 根据订单号高级查询
        if (userPhoto.getOrderId() != null && !userPhoto.getOrderId().equals(""))
            query.like("order_id", userPhoto.getOrderId());

        // 根据照片类型查询
        if (userPhoto.getType() != null && !userPhoto.getType().equals("")) query.eq("type", userPhoto.getType());

        // 根据创建时间查询
        if (userPhoto.getStartTime() != null && userPhoto.getEndTime() != null)
            query.ge("create_time", userPhoto.getStartTime()).le("create_time", userPhoto.getEndTime());
        query.orderByDesc("id");
        return query.list();
    }

    /**
     * 新增photo
     *
     * @param userPhoto photo
     * @return 结果
     */
    @Override
    public int insertUserPhoto(UserPhoto userPhoto) {
        userPhoto.setCreateTime(DateUtils.getNowDate());
        return userPhotoMapper.insertUserPhoto(userPhoto);
    }

    /**
     * 修改photo
     *
     * @param userPhoto photo
     * @return 结果
     */
    @Override
    public int updateUserPhoto(UserPhoto userPhoto) {
        return userPhotoMapper.updateUserPhoto(userPhoto);
    }

    /**
     * 批量删除photo
     *
     * @param ids 需要删除的photo主键
     * @return 结果
     */
    @Override
    public int deleteUserPhotoByIds(Long[] ids) {
        return userPhotoMapper.deleteUserPhotoByIds(ids);
    }

    /**
     * 删除photo信息
     *
     * @param id photo主键
     * @return 结果
     */
    @Override
    public int deleteUserPhotoById(Long id) {
        return userPhotoMapper.deleteUserPhotoById(id);
    }

    @Override
    public String uploadPhoto(MultipartFile file, String deviceId, String openId) {
        String originalFilename = file.getOriginalFilename();
        String imageName = "userUploadPhoto/" + openId + "/" + deviceId;
        assert originalFilename != null;
        if (originalFilename.contains(".png")) {
            imageName += ".png";
        } else {
            imageName += ".jpg";
        }
        String url = ALY_OSS.uploadImage(file, imageName);

        // 把上传的文件路径返回 （手动拼接）
        // 这里设置图片有效时间 我设置了30年

        url = url.substring(0, url.indexOf("?"));


        try {
            mediaCheckAsync(openId, url);
        } catch (Exception e) {
            System.out.println(e);
        }

        return url;
    }


    @Override
    public String savePhoto(MultipartFile file, OrderCamera orderCamera, int index) {
        if (file == null) return "上传错误！！！";
        if (StringUtils.isEmpty(orderCamera.getOrderId())) {
            orderCamera.setOrderId("null" + System.currentTimeMillis());
        } else if (orderCamera.getOrderId().equals("null")) {
            orderCamera.setOrderId("null" + System.currentTimeMillis());
        }

        SimpleDateFormat yyyyMMdd = new SimpleDateFormat("yyyyMMdd");
        Date currentDate = new Date();
        String formattedDate = yyyyMMdd.format(currentDate);

        String originalFilename = file.getOriginalFilename();
        String imageName = "userPhoto/" + orderCamera.getDeviceId() + "/" + formattedDate + "/" + orderCamera.getOrderId() + index + originalFilename.substring(originalFilename.lastIndexOf("."));
        String url = ALY_OSS.uploadImage(file, imageName);
        String dateUrl = url;
        url = url.substring(0, url.indexOf("?"));
        UserPhoto userPhoto = new UserPhoto();
        userPhoto.setUrl(url);
        userPhoto.setObjectName(imageName);
        userPhoto.setOpenId(orderCamera.getOpenid());
        userPhoto.setDeviceId(orderCamera.getDeviceId());
        userPhoto.setDeviceName(orderCamera.getDeviceName());
        userPhoto.setOrderId(orderCamera.getOrderId());
        userPhoto.setType(orderCamera.getPhotoType());
        userPhoto.setCreateTime(DateUtils.getNowDate());
        save(userPhoto);
        return dateUrl;
    }


    @Override
    public String savePhotoNoOrder(MultipartFile file, String deviceId) {
        String originalFilename = file.getOriginalFilename();
        String imageName = "LINSHI/" + deviceId + "/" + System.currentTimeMillis() + originalFilename;

        String url = ALY_OSS.uploadImage(file, imageName);

        QueryWrapper<UserPhoto> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_id", deviceId);
        remove(queryWrapper);

        String dateUrl = url;
        url = url.substring(0, url.indexOf("?"));
        UserPhoto userPhoto = new UserPhoto();
        userPhoto.setUrl(url);
        userPhoto.setDeviceId(deviceId);
        userPhoto.setOrderId(deviceId);
        userPhoto.setObjectName(imageName);
        userPhoto.setType(100);
        userPhoto.setCreateTime(DateUtils.getNowDate());

        save(userPhoto);
        return dateUrl;
    }


    @Override
    public String savePhotoNoWX_free(MultipartFile file, String photoId) {
        System.out.println("Long.valueOf(photoId)");
        System.out.println(Long.valueOf(photoId));
        UserPhoto freePhoto = userPhotoMapper.selectUserPhotoById(Long.valueOf(photoId));
        if (freePhoto == null) {
            freePhoto = new UserPhoto();
            freePhoto.setId(Long.valueOf(photoId));
            userPhotoMapper.insertUserPhoto(freePhoto);
        }
        String originalFilename = file.getOriginalFilename();
        String imageName = "LINSHI" + "/" + System.currentTimeMillis() + originalFilename;

        String url = ALY_OSS.uploadImage(file, imageName);

        freePhoto.setUrl(url);
        freePhoto.setObjectName(imageName);
        freePhoto.setType(1);
        userPhotoMapper.updateUserPhoto(freePhoto);
        return url;
    }


    @Autowired
    private WxMaService wxMaService;

    public void mediaCheckAsync(String openid, String media_url) throws Exception {
        String accessToken = wxMaService.getAccessToken();
        Map map = new HashMap<>();
        map.put("media_url", media_url);
        map.put("media_type", 2);
        map.put("version", 2);
        map.put("scene", 1);
        map.put("openid", openid);

        String url = "https://api.weixin.qq.com/wxa/media_check_async?access_token=" + accessToken;


        String s1 = HttpClientSslUtils.doPost(url, JSON.toJSONString(map));
//        String s1 = HttpUtils.httpRequest(url,"POST", JSON.toJSONString(map));

/*       JSONObject jsonObject = JSONObject.parseObject(s1);
       JSONObject result = (JSONObject)jsonObject.get("result");
       String suggest = (String)result.get("suggest");*/
        System.out.println("-------" + s1);

    }


    @Override
    public String WxImageDetection(MultipartFile file) throws WxErrorException {
        String accessToken = wxMaService.getAccessToken();
        String url = "https://api.weixin.qq.com/wxa/img_sec_check?access_token=" + accessToken;
        Map map = new HashMap<>();
        try {
            map = uploadByte(url, file);
            System.out.println("WxImageDetection:{}" + JSON.toJSONString(map));

            if (map.get("errcode").equals(87014)) {
                throw new WxErrorException("图片含有违法违规内容");
            }

        } catch (Exception e) {
            System.out.println(e);
        }

        return JSON.toJSONString(map);
    }

    public Map uploadByte(String url, MultipartFile file) {
        File toFile = transferToFile(file);
        Map data = new HashMap();
        data.put("file", toFile);
        String body = HttpRequest.post(url)
                .form(data)
                .contentType("multipart/form-data")
                .execute()
                .body();
        Map result = new HashMap();
        if (StrUtil.isNotBlank(body)) {
            result = JSON.parseObject(body, Map.class);
        }
        return result;
    }

    public File transferToFile(MultipartFile multipartFile) {
        //选择用缓冲区来实现这个转换即使用java 创建的临时文件 使用 MultipartFile.transferto()方法 。
        File file = null;
        try {
            String originalFilename = multipartFile.getOriginalFilename();
            //获取文件后缀
            String prefix = originalFilename.substring(originalFilename.lastIndexOf("."));
            file = File.createTempFile(originalFilename, prefix);    //创建零食文件
            multipartFile.transferTo(file);
            //删除
            file.deleteOnExit();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return file;
    }

}
