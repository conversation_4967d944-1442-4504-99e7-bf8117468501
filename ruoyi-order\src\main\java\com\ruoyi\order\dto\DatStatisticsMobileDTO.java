package com.ruoyi.order.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 移动端数据统计
 */
@Data
public class DatStatisticsMobileDTO {

    /**
     * 总设备数
     */
    private Long totalDevice;

    /**
     * 在线设备数
     */
    private Long onlineDevice;

    /**
     * 营收 - 现在
     */
    private BigDecimal revenueNow;

    /**
     * 营收 - 过去
     */
    private BigDecimal revenuePast;

    /**
     * 订单数 - 现在
     */
    private Long orderNumNow;

    /**
     * 订单数 - 过去
     */
    private Long orderNumPast;

    /**
     * 营收图表数据
     */
    private RevenueChartData revenueChartData;

    /**
     * 订单图表数据
     */
    private List<OrderChartData> orderChartData;

    /**
     * 设备Id列表
     */
    private List<String> deviceIdList;

    /**
     * 营收图表数据
     */
    @Data
    public static class RevenueChartData {

        /**
         * 时间
         */
        private String[] timeArr;

        /**
         * 营收 - 现在
         */
        private BigDecimal[] nowArr;

        /**
         * 营收 - 过去
         */
        private BigDecimal[] pastArr;

        /**
         * 同比
         */
        private BigDecimal[] yoyArr;
    }

    /**
     * 订单图表数据
     */
    @Data
    public static class OrderChartData {

        /**
         * 图片类型
         */
        private Integer type;

        /**
         * 图片类型名称
         */
        private String name;

        /**
         * 订单数
         */
        private Long value;
    }
}
