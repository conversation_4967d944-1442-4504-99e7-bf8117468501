-- 修改证件照打印参数表结构
-- 将type字段从varchar改为int类型

-- 1. 修改type字段类型
ALTER TABLE `id_photo_print_params` MODIFY COLUMN `type` int NOT NULL COMMENT '类型：0-通用，1-证件，2-教育，3-从业资格，4-签证';

-- 2. 删除原有的唯一索引
DROP INDEX `idx_type` ON `id_photo_print_params`;

-- 3. 重新创建唯一索引
CREATE UNIQUE INDEX `idx_type_name` ON `id_photo_print_params` (`type`, `name`) COMMENT '类型和名称联合唯一索引';

-- 4. 更新状态索引注释
ALTER TABLE `id_photo_print_params` DROP INDEX `idx_status`;
CREATE INDEX `idx_status` ON `id_photo_print_params` (`status`) COMMENT '状态索引：0-启用，1-禁用';
