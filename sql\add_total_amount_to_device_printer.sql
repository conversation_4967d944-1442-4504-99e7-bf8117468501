-- 为设备打印机表添加总金额字段
-- 执行时间: 2025-01-29

-- 添加总金额字段到device_printer表
ALTER TABLE `device_printer`
ADD COLUMN `total_amount` DECIMAL(10,2) NULL DEFAULT 0.00 COMMENT '总金额(元)'
AFTER `count_price`;

-- 更新现有数据，将count_price的值复制到total_amount（如果需要的话）
-- UPDATE `device_printer` SET `total_amount` = `count_price` WHERE `total_amount` IS NULL;

-- 验证字段是否添加成功
-- SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT 
-- FROM INFORMATION_SCHEMA.COLUMNS 
-- WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'device_printer' AND COLUMN_NAME = 'total_amount';
