package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.City;

/**
 * 城市信息Service接口
 * 
 * <AUTHOR>
 * @date 2023-07-14
 */
public interface ICityService 
{
    /**
     * 查询城市信息
     * 
     * @param id 城市信息主键
     * @return 城市信息
     */
    public City selectCityById(Long id);

    /**
     * 查询城市信息列表
     * 
     * @param city 城市信息
     * @return 城市信息集合
     */
    public List<City> selectCityList(City city);

    /**
     * 新增城市信息
     * 
     * @param city 城市信息
     * @return 结果
     */
    public int insertCity(City city);

    /**
     * 修改城市信息
     * 
     * @param city 城市信息
     * @return 结果
     */
    public int updateCity(City city);

    /**
     * 批量删除城市信息
     * 
     * @param ids 需要删除的城市信息主键集合
     * @return 结果
     */
    public int deleteCityByIds(Long[] ids);

    /**
     * 删除城市信息信息
     * 
     * @param id 城市信息主键
     * @return 结果
     */
    public int deleteCityById(Long id);

    public List<City> queryCityByValue(String value);

    List<City> queryProvinceByValue(String province);

    List<City> queryCityByProvince(String province, String city);

    List<City> queryDistrictByCity(String city, String district);
}
