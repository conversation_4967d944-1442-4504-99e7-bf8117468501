<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yunchuang.wxapp.mapper.HomeSetMapper">

    <resultMap type="HomeSet" id="HomeSetResult">
        <result property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="name" column="name"/>
        <result property="imageUrl" column="image_url"/>
        <result property="sort" column="sort"/>
        <result property="jumpType" column="jump_type"/>
        <result property="jumpValue" column="jump_value"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectHomeSetVo">
        select id,
               type,
               name,
               image_url,
               sort,
               jump_type,
               jump_value,
               status,
               create_by,
               create_time,
               update_by,
               update_time,
               remark
        from wxapp_home_set
    </sql>

    <select id="selectHomeSetList" parameterType="HomeSet" resultMap="HomeSetResult">
        <include refid="selectHomeSetVo"/>
        <where>
            <if test="type != null ">and type = #{type}</if>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="jumpType != null ">and jump_type = #{jumpType}</if>
            <if test="status != null ">and status = #{status}</if>
        </where>
    </select>

    <select id="selectHomeSetById" parameterType="Long" resultMap="HomeSetResult">
        <include refid="selectHomeSetVo"/>
        where id = #{id}
    </select>

    <insert id="insertHomeSet" parameterType="HomeSet" useGeneratedKeys="true" keyProperty="id">
        insert into wxapp_home_set
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="type != null">type,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="imageUrl != null and imageUrl != ''">image_url,</if>
            <if test="sort != null">sort,</if>
            <if test="jumpType != null">jump_type,</if>
            <if test="jumpValue != null and jumpValue != ''">jump_value,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="type != null">#{type},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="imageUrl != null and imageUrl != ''">#{imageUrl},</if>
            <if test="sort != null">#{sort},</if>
            <if test="jumpType != null">#{jumpType},</if>
            <if test="jumpValue != null and jumpValue != ''">#{jumpValue},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateHomeSet" parameterType="HomeSet">
        update wxapp_home_set
        <trim prefix="SET" suffixOverrides=",">
            <if test="type != null">type = #{type},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="imageUrl != null and imageUrl != ''">image_url = #{imageUrl},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="jumpType != null">jump_type = #{jumpType},</if>
            <if test="jumpValue != null and jumpValue != ''">jump_value = #{jumpValue},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHomeSetById" parameterType="Long">
        delete
        from wxapp_home_set
        where id = #{id}
    </delete>

    <delete id="deleteHomeSetByIds" parameterType="String">
        delete from wxapp_home_set where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>