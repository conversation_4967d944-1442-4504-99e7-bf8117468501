package com.ruoyi.common.exception;

import com.ruoyi.common.enums.WeiXinBusinessExceptionCode;

/**
 * 微信 - 业务异常类
 */
public class WeiXinBusinessException extends BusinessException {

    /**
     * 构造函数
     *
     * @param EC 异常枚举
     */
    public WeiXinBusinessException(WeiXinBusinessExceptionCode EC) {
        super(false, EC.getMessage(), EC.getCode());
    }

    /**
     * 构造函数
     *
     * @param message 异常信息
     * @param EC      异常枚举
     */
    public WeiXinBusinessException(WeiXinBusinessExceptionCode EC, String message) {
        super(false, message, WeiXinBusinessExceptionCode.UNKNOWN_EXCEPTION.getCode());
    }
}
