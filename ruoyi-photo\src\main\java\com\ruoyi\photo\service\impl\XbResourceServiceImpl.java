package com.ruoyi.photo.service.impl;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import com.aliyun.oss.ClientException;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.common.auth.CredentialsProvider;
import com.aliyun.oss.common.auth.DefaultCredentialProvider;
import com.aliyun.oss.model.ObjectMetadata;
import com.baomidou.mybatisplus.extension.conditions.query.QueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.oss.ALY_OSS;
import com.ruoyi.device.domain.DeviceCamera;
import com.ruoyi.device.service.IDeviceCameraService;
import com.ruoyi.order.domain.OrderCamera;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.system.service.ISysDictDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.photo.mapper.XbResourceMapper;
import com.ruoyi.photo.domain.XbResource;
import com.ruoyi.photo.service.IXbResourceService;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

import static com.ruoyi.common.utils.SecurityUtils.*;
import static com.ruoyi.device.utils.Constant.*;
import static com.ruoyi.device.utils.Constant.BUCKET_NAME;

/**
 * 素材管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-29
 */
@Service
public class XbResourceServiceImpl extends ServiceImpl<XbResourceMapper, XbResource> implements IXbResourceService {
    @Resource
    private XbResourceMapper xbResourceMapper;
    @Autowired
    private IDeviceCameraService deviceCameraService;

    @Autowired
    private ISysDictDataService dictDataService;
    @Resource
    private ISysDeptService deptService;

    /**
     * 查询素材管理
     *
     * @param id 素材管理主键
     * @return 素材管理
     */
    @Override
    public XbResource selectXbResourceById(Long id) {
        return xbResourceMapper.selectXbResourceById(id);
    }

    /**
     * 查询素材管理列表
     *
     * @param xbResource 素材管理
     * @return 素材管理
     */
    @Override
    public TableDataInfo selectXbResourceList(XbResource xbResource, int pageNum, int pageSize, String deviceId) {
        TableDataInfo tableDataInfo = new TableDataInfo();
        QueryChainWrapper<XbResource> query = query();
        LoginUser user = getLoginUser();

        if (user == null) {
            tableDataInfo.setMsg("用户未登录");
            tableDataInfo.setCode(401);
            return tableDataInfo;
        }


        if (xbResource.getTitle() != null && !xbResource.getTitle().equals("")) {
            query.eq("title", xbResource.getTitle());
        }
        if (xbResource.getContent() != null && !xbResource.getContent().equals("")) {
            query.eq("content", xbResource.getContent());
        }
        if (xbResource.getPeople() != null) {
            query.eq("people", xbResource.getPeople());
        }
        if (xbResource.getStyle() != null) {
            query.eq("style", xbResource.getStyle());
        }
        if (xbResource.getStatus() != null) {
            query.eq("status", xbResource.getStatus());
        }


        {
            /**
             * 用户层级权限判断筛选
             */
            boolean isLeader = user.getUser().getDept().getLeader().equals(user.getUsername()) || user.getUser().isAdmin();
            String[] deptIds = user.getUser().getDept().getAncestors().split(",");//上级代理的id
            List<String> deptIdList = new ArrayList<>(Arrays.asList(deptIds));
            deptIdList.add(String.valueOf(user.getDeptId()));//加上本级代理的id
            List<SysDept> sysDepts = deptService.listByIds(deptIdList);//获取上述级别代理的dept信息
            List<String> leadersName = new ArrayList<>();//上述获取的dept的负责人的username
            leadersName.add(user.getUsername());
            for (SysDept sysDept : sysDepts) {
                leadersName.add(sysDept.getLeader());
            }

            List<Long> childDeptIds = new ArrayList<>();
            if (isLeader) {
                List<SysDept> depts = deptService.query().like("ancestors", user.getDeptId()).list();
                for (SysDept dept : depts) {
                    childDeptIds.add(dept.getDeptId());
                }
                childDeptIds.add(user.getDeptId());
                System.out.println(childDeptIds);

            }
            query.and(qr -> {
                qr.in("create_by", leadersName);
                if (childDeptIds.size() > 0)
                    qr.or().in("dept_id", childDeptIds);
            });
        }


        // 获取当前页数据
        Page<XbResource> page = query.orderByDesc("create_time").page(new Page<>(pageNum, pageSize));
        List<XbResource> records = page.getRecords();
        tableDataInfo.setRows(records);
        tableDataInfo.setTotal(page.getTotal());
        return tableDataInfo;
    }

    /**
     * 新增素材管理
     *
     * @param xbResource 素材管理
     * @return 结果
     */
    @Override
    public int insertXbResource(MultipartFile image, XbResource xbResource) {
        String originalFilename = image.getOriginalFilename();
        String imageName = "xb_resource/" + System.currentTimeMillis();
        if (originalFilename.contains(".png")) {
            imageName += ".png";
        } else {
            imageName += ".jpg";
        }

        String url = ALY_OSS.uploadImage(image, imageName);

        url = url.substring(0, url.indexOf("?"));
        xbResource.setUrl(url);
        xbResource.setObjectName(imageName);
        xbResource.setCreateTime(DateUtils.getNowDate());
        return save(xbResource) ? 1 : 0;
    }

    /**
     * 修改素材管理
     *
     * @param xbResource 素材管理
     * @return 结果
     */
    @Override
    public AjaxResult updateXbResource(XbResource xbResource, MultipartFile image) {
        if (!getLoginUser().getUser().isAdmin()) {
            if (!xbResource.getCreateBy().equals(getUsername())) {
                return AjaxResult.error(401, "你无权修改该素材");
            }
        }

        xbResource.setUpdateTime(DateUtils.getNowDate());
        xbResource.setUpdateBy(getUsername());

        if (image == null) {
            boolean b = updateById(xbResource);
            return b ? AjaxResult.success("修改成功") : AjaxResult.error("修改失败");
        } else {
            if (xbResource.getObjectName() != null && !xbResource.getObjectName().equals("")) {
                String url = ALY_OSS.uploadImage(image, xbResource.getObjectName());
                url = url.substring(0, url.indexOf("?"));
                xbResource.setUrl(url);
            } else {
                String originalFilename = image.getOriginalFilename();
                String imageName = "xb_resource/" + getUserId() + "/" + System.currentTimeMillis();
                if (originalFilename.contains(".png")) {
                    imageName += ".png";
                } else {
                    imageName += ".jpg";
                }
                String url = ALY_OSS.uploadImage(image, imageName);
                url = url.substring(0, url.indexOf("?"));
                xbResource.setUrl(url);
                xbResource.setObjectName(imageName);
            }
            boolean b = updateById(xbResource);
            return b ? AjaxResult.success("修改成功") : AjaxResult.error("修改失败");
        }
    }

    /**
     * 批量删除素材管理
     *
     * @param ids 需要删除的素材管理主键
     * @return 结果
     */
    @Override
    public int deleteXbResourceByIds(Long[] ids) {
        for (Long id : ids) {
            XbResource xbResource = getById(id);
            ALY_OSS.deleteImage(xbResource.getObjectName());//todo
        }
        return xbResourceMapper.deleteXbResourceByIds(ids);
    }

    /**
     * 删除素材管理信息
     *
     * @param id 素材管理主键
     * @return 结果
     */
    @Override
    public int deleteXbResourceById(Long id) {
        return xbResourceMapper.deleteXbResourceById(id);
    }
}
