package com.ruoyi.web.controller.pay;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.jwt.JWTUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONReader;
import com.ruoyi.dto.*;
import com.ruoyi.web.controller.util.MyJWTUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.http.*;
import org.springframework.util.DigestUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/vqr")
public class VITEQR {

    @Resource
    private RestTemplate restTemplate;

    private static final String VALID_USERNAME = "**********";
    private static final String VALID_PASSWORD = "111111"; // 这是来自username的base64字符串: 您的真实密码.
    private static String SECRET_KEY = ""; // JWT测试的秘密密钥Yes, this is it
    private static final String BEARER_PREFIX = "Bearer ";
    private static final String API_USERNAME = "customer-vso19898guangzhou-user24178";
    private static final String API_PASSWORD = "Y3VzdG9tZXItdnNvMTk4OThndWFuZ3pob3UtdXNlcjI0MTc4";
    private static final String BANK_ACCOUNT = "**********"; // 当前用户银行账户 prod:********** test:*************
    private static final String BANK_CODE = "MB"; // 当前用户银行代码
    private static final String USER_BANK_NAME = "VU THI HIEN"; // 当前用户账户名 prod:VU THI HIEN test:Tran Phat Dat

    /**
     * 生成Token
     * <p>
     * 该API是请求合作伙伴提供能够与Transaction Sync API进行连接和同步的访问权限（余额波动同步）
     * </p>
     * @param authHeader    授权头
     * @return  ResponseEntity
     */
    @PostMapping("/api/token_generate")
    public ResponseEntity<?> generateToken(@RequestHeader("Authorization") String authHeader) {
        // 检查请求头是否有授权
        if (authHeader != null && authHeader.startsWith("Basic ")) {
            // 将Authorization header解码Base64
            String base64Credentials = authHeader.substring("Basic ".length()).trim();
            String credentials = new String(Base64.getDecoder().decode(base64Credentials), StandardCharsets.UTF_8);

            // 用户名和密码分离
            final String[] values = credentials.split(":", 2);
            String username = values[0];
            String password = values[1];

            // 检查用户名和密码的有效性
            if (VALID_USERNAME.equals(username) && VALID_PASSWORD.equals(password)) {
                // 如果有效,请创建JWT Token
                Map<String, Object> payload = new HashMap<>();
                DateTime expireTime = DateTime.now().offsetNew(DateField.SECOND, 300); // 300秒
                payload.put("username", username);
                String token = MyJWTUtil.createJWT(payload, expireTime);
                SECRET_KEY = token;

                return ResponseEntity.ok(new TokenResponse(token, "Bearer", 300));
            } else {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid credentials");
            }
        } else {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Authorization header is missing or invalid");
        }
    }


    /**
     * 交易同步
     * <p>
     * 用于同步从VietQR返回给合作伙伴的余额波动
     * </p>
     * @param transactionCallback   交易回调
     * @param request            请求
     * @return  ResponseEntity
     */
    @PostMapping("/bank/api/transaction-sync")
    public ResponseEntity<Object> transactionSync(@RequestBody TransactionCallback transactionCallback,
                                                  HttpServletRequest request) {
        // 从请求头获取 Authorization
        String authHeader = request.getHeader("Authorization");
        if (authHeader == null || !authHeader.startsWith(BEARER_PREFIX)) {
            return new ResponseEntity<>(new ErrorResponse(true, "INVALID_AUTH_HEADER",
                    "Authorization header is missing or invalid", null), HttpStatus.UNAUTHORIZED);
        }

        String token = authHeader.substring(BEARER_PREFIX.length()).trim();

        // 验证token
        if (!validateToken(token)) {
            return new ResponseEntity<>(new ErrorResponse(true, "INVALID_TOKEN",
                    "Invalid or expired token", null), HttpStatus.UNAUTHORIZED);
        }

        try {
            // 业务处理,生成refTransactionId代码 (假设生成随机代码)
            String refTransactionId = "GeneratedRefTransactionId"; // 交易的生成ID

            // 返回200响应OK与交易信息
            TransactionResponseObject transactionResponse = new TransactionResponseObject(refTransactionId);
            return ResponseEntity.ok(new SuccessResponse(false, null,
                    "Transaction processed successfully", transactionResponse));
        } catch (Exception ex) {
            // 在存在exception的情况下返回错误
            return new ResponseEntity<>(new ErrorResponse(true, "TRANSACTION_FAILED", ex.getMessage(), null), HttpStatus.BAD_REQUEST);
        }
    }

    /**
     * 测试回调
     */
    @PostMapping("/testCallback")
    public Result testCallback(@RequestBody TestTransactionCallback transactionCallback, HttpServletRequest request) {
        log.info("交易回调：{}", transactionCallback);
        // 从请求头获取 Authorization
        String authHeader = request.getHeader("Authorization");
        String transactionCallbackStr = JSON.toJSONString(transactionCallback);
        // 设置请求头
        HttpHeaders requestHeaders = new HttpHeaders();
        requestHeaders.setContentType(MediaType.APPLICATION_JSON);
        requestHeaders.set("Authorization", authHeader);
        HttpEntity<String> r = new HttpEntity<String>(transactionCallbackStr, requestHeaders);
        // 请求地址
        String url = "https://dev.vietqr.org/vqr/bank/api/test/transaction-callback";
        String result = restTemplate.postForObject(url, r, String.class);
        // 将json字符串转为json对象
        JSONObject jsonObject = JSON.parseObject(result);
        return Result.ok(200, "交易回调成功", String.valueOf(System.currentTimeMillis()), jsonObject);
    }


    /**
     * 创建支付码
     */
    @GetMapping("/createPayCode")
    public Result createPayCode(Long amount,String cardType, String orderNo) {
        // 获取Token
        String token = getSecretKey();
        System.out.println("token: "+token);
        // 创建请求参数
        VIETQRGenerateCustomerReq generateCustomerReq = new VIETQRGenerateCustomerReq();
        generateCustomerReq.setBankCode(BANK_CODE);
        generateCustomerReq.setBankAccount(BANK_ACCOUNT);
        generateCustomerReq.setUserBankName(USER_BANK_NAME);
        generateCustomerReq.setContent("MauVanBanVN");
        generateCustomerReq.setQrType(0);
        generateCustomerReq.setAmount(amount);
        generateCustomerReq.setOrderId(orderNo);
//        generateCustomerReq.setOrderId(System.currentTimeMillis() + RandomUtil.randomNumbers(4));
        generateCustomerReq.setTransType(cardType);
        String generateCustomerReqStr= JSON.toJSONString(generateCustomerReq);
        // 设置请求头
        HttpHeaders requestHeaders = new HttpHeaders();
        requestHeaders.setContentType(MediaType.APPLICATION_JSON);
        requestHeaders.set("Authorization", "Bearer "+token);
        HttpEntity<String> r = new HttpEntity<String>(generateCustomerReqStr, requestHeaders);
        // 请求地址
        String url = "https://api.vietqr.org/vqr/api/qr/generate-customer";
        String result = restTemplate.postForObject(url, r, String.class);
        // 将json字符串转为json对象
        JSONObject jsonObject = JSON.parseObject(result);
        // 如果返回的json对象中 有qrLink这个字段 表示请求成功
        if (jsonObject != null && jsonObject.containsKey("qrCode")) {
            // 获取返回的数据
            String qrCode = jsonObject.getString("qrCode");
//            VIETQRCreatePayCodeResp resp = new VIETQRCreatePayCodeResp();
//            resp.setOrderId(generateCustomerReq.getOrderId());
//            resp.setPayCode(qrLink);
            return Result.ok(200, "创建支付码成功", String.valueOf(System.currentTimeMillis()), qrCode);
        }else{
            log.info("创建支付码失败：{}", jsonObject.getString("message"));
            return Result.fail(500, "创建支付码失败", String.valueOf(System.currentTimeMillis()));
        }

    }

    /**
     * 获取支付状态
     */
    @GetMapping("/getPayStatus")
    public Result getPayStatus(String orderId) {
        // 获取Token
        String token = getSecretKey();
        // 创建请求参数
        VIETQRCheckOrderReq checkOrderReq = new VIETQRCheckOrderReq();
        checkOrderReq.setBankAccount(BANK_ACCOUNT);
        checkOrderReq.setType("0");
        checkOrderReq.setValue(orderId);
        String checkSum=DigestUtils.md5DigestAsHex((BANK_ACCOUNT+API_USERNAME).getBytes());
        checkOrderReq.setCheckSum(checkSum);
        String checkOrderReqStr= JSON.toJSONString(checkOrderReq);
        // 设置请求头
        HttpHeaders requestHeaders = new HttpHeaders();
        requestHeaders.setContentType(MediaType.APPLICATION_JSON);
        requestHeaders.set("Authorization", "Bearer "+token);
        HttpEntity<String> r = new HttpEntity<String>(checkOrderReqStr, requestHeaders);
        // 请求地址
        String url = "https://api.vietqr.org/vqr/api/transactions/check-order";
        String result = restTemplate.postForObject(url, r, String.class);
        // 去除json开头和结尾的[]
        if (result != null) {
            result = result.substring(1, result.length() - 1);
        }
        // 字符串是多个json对象拼接的字符串，取最后一个json对象
        result = result.substring(result.lastIndexOf("{"));
        // 将json字符串转为json对象
        JSONObject jsonObject = JSON.parseObject(result);
        System.out.println("result: "+ JSONUtil.toJsonStr(jsonObject));
        // 如果返回的json对象中 有status这个字段 表示请求成功
        if (jsonObject != null && jsonObject.containsKey("status")) {
            // 获取返回的数据
            String status = jsonObject.getString("status");
            return Result.ok(200, "获取支付状态成功", String.valueOf(System.currentTimeMillis()), status);
        }else{
            log.info("获取支付状态失败：{}", jsonObject.getString("message"));
            return Result.fail(500, "获取支付状态失败", String.valueOf(System.currentTimeMillis()));
        }

    }

    /**
     * 获取Token
     */
    public String getSecretKey() {
        // 将授权[API用户名:API密码]转换为Base64
        String auth_token = "Basic " + Base64.getEncoder().encodeToString((API_USERNAME+":"+API_PASSWORD).getBytes());
        System.out.println(auth_token);
        // 设置请求头
        HttpHeaders requestHeaders = new HttpHeaders();
        requestHeaders.setContentType(MediaType.APPLICATION_JSON);
        requestHeaders.set("Authorization", auth_token);
        HttpEntity<String> r = new HttpEntity<String>(null, requestHeaders);
        // 请求地址
        String url = "https://api.vietqr.org/vqr/api/token_generate";
        String result = restTemplate.postForObject(url, r, String.class);
        // 将json字符串转为json对象
        JSONObject jsonObject = JSON.parseObject(result);
        // 如果返回的json对象中 有access_token这个字段 表示请求成功
        if (jsonObject != null && jsonObject.containsKey("access_token")) {
            // 获取返回的数据
            return jsonObject.getString("access_token");
        }else{
            log.info("获取Token失败：{}", jsonObject.getString("message"));
        }
        return null;
    }


    // Class cho response
    @Setter
    @Getter
    public static class TokenResponse {
        // Getters và Setters
        private String access_token;
        private String token_type;
        private int expires_in;

        public TokenResponse(String access_token, String token_type, int expires_in) {
            this.access_token = access_token;
            this.token_type = token_type;
            this.expires_in = expires_in;
        }

    }

    // Phương thức để xác thực token JWT
    private boolean validateToken(String token) {
        // Đây là phương pháp giả sử validate token với SECRET_KEY
        // Bạn có thể tích hợp JWT library như `io.jsonwebtoken` để validate
        try {
            // Giả sử giải mã token với SECRET_KEY
            if(!MyJWTUtil.verifyJWT(token)) {
                return false;
            }
            // 字符串转换为Map
            Map<String, Object> payload = JWTUtil.parseToken(token).getPayloads();
            if (!payload.containsKey("username")) {
                return false;
            }else if(!payload.get("username").equals(VITEQR.VALID_USERNAME)){
                return false;
            }
            return true;
            // Kiểm tra token hợp lệ (thực tế nên sử dụng JWT library như jjwt)
        } catch (Exception e) {
            return false;
        }
    }

    // Lớp model cho request body
    @Setter
    @Getter
    static
    public class TransactionCallback {
        private String transactionid;
        private long transactiontime;
        private String referencenumber;
        private double amount;
        private String content;
        private String bankaccount;
        private String orderId;
        private String sign;
        private String terminalCode;
        private String urlLink;
        private String serviceCode;
        private String subTerminalCode;

    }

    @Setter
    @Getter
    static
    public class TestTransactionCallback {
        private String bankAccount;
        private long amount;
        private String content;
        private String transType;
        private String bankCode;
    }

    // Lớp model cho success response
    @Setter
    @Getter
    class SuccessResponse {
        private boolean error;
        private String errorReason;
        private String toastMessage;
        private TransactionResponseObject object;

        public SuccessResponse(boolean error, String errorReason, String toastMessage, TransactionResponseObject object) {
            this.error = error;
            this.errorReason = errorReason;
            this.toastMessage = toastMessage;
            this.object = object;
        }

    }

    // Lớp model cho lỗi response
    @Setter
    @Getter
    class ErrorResponse {
        private boolean error;
        private String errorReason;
        private String toastMessage;
        private Object object;

        public ErrorResponse(boolean error, String errorReason, String toastMessage, Object object) {
            this.error = error;
            this.errorReason = errorReason;
            this.toastMessage = toastMessage;
            this.object = object;
        }

    }

    // Lớp model cho object trả về trong success response
    @Setter
    @Getter
    class TransactionResponseObject {
        private String reftransactionid;

        public TransactionResponseObject(String reftransactionid) {
            this.reftransactionid = reftransactionid;
        }

    }
}
