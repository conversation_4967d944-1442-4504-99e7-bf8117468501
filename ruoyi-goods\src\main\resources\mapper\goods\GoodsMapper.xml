<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.goods.mapper.GoodsMapper">
    
    <resultMap type="Goods" id="GoodsResult">
        <result property="id"    column="id"    />
        <result property="goodsImage"    column="goods_image"    />
        <result property="goodsName"    column="goods_name"    />
        <result property="price"    column="price"    />
        <result property="code"    column="code"    />
        <result property="goodsDescribe"    column="goods_describe"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectGoodsVo">
        select id, goods_image, goods_name, price, code, goods_describe, status, create_by, create_time, update_by, update_time, remark from goods
    </sql>

    <select id="selectGoodsList" parameterType="Goods" resultMap="GoodsResult">
        <include refid="selectGoodsVo"/>
        <where>  
            <if test="goodsName != null  and goodsName != ''"> and goods_name like concat('%', #{goodsName}, '%')</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectGoodsById" parameterType="Long" resultMap="GoodsResult">
        <include refid="selectGoodsVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertGoods" parameterType="Goods">
        insert into goods
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="goodsImage != null">goods_image,</if>
            <if test="goodsName != null">goods_name,</if>
            <if test="price != null">price,</if>
            <if test="code != null">code,</if>
            <if test="goodsDescribe != null">goods_describe,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="goodsImage != null">#{goodsImage},</if>
            <if test="goodsName != null">#{goodsName},</if>
            <if test="price != null">#{price},</if>
            <if test="code != null">#{code},</if>
            <if test="goodsDescribe != null">#{goodsDescribe},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateGoods" parameterType="Goods">
        update goods
        <trim prefix="SET" suffixOverrides=",">
            <if test="goodsImage != null">goods_image = #{goodsImage},</if>
            <if test="goodsName != null">goods_name = #{goodsName},</if>
            <if test="price != null">price = #{price},</if>
            <if test="code != null">code = #{code},</if>
            <if test="goodsDescribe != null">goods_describe = #{goodsDescribe},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteGoodsById" parameterType="Long">
        delete from goods where id = #{id}
    </delete>

    <delete id="deleteGoodsByIds" parameterType="String">
        delete from goods where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>