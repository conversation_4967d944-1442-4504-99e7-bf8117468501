package com.ruoyi.generator.domain;

import com.alibaba.fastjson.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 微信客服图文消息
 * @date 2020-8-4 09:22:08
 * @version 1.0
 */
public class CustomerServiceImageText {

    private String touser;

    private String msgtype;

    private Map<String,List<CustomerServiceArticle>> news;

    public String getTouser() {
        return touser;
    }

    public void setTouser(String touser) {
        this.touser = touser;
    }

    public String getMsgtype() {
        return msgtype;
    }

    public void setMsgtype(String msgtype) {
        this.msgtype = msgtype;
    }

    public Map<String, List<CustomerServiceArticle>> getNews() {
        return news;
    }

    public void setNews(Map<String, List<CustomerServiceArticle>> news) {
        this.news = news;
    }

    public static void main(String[] args) {
        CustomerServiceArticle a1 = new CustomerServiceArticle();
        a1.setTitle("你好，欢迎您的加入！");
        a1.setDescription("描述1");
        a1.setPicurl("http://res.minxueedu.cn//res//prod//SSEP/uploadfiles/knowledgeCourseImage/2020729407976775.png");
        a1.setUrl("http://www.baidu.com");
        CustomerServiceArticle a2 = new CustomerServiceArticle();
        a2.setTitle("你好，欢迎您的致电！");
        a2.setDescription("描述2");
        a2.setPicurl("http://res.minxueedu.cn//res//prod//SSEP/uploadfiles/knowledgeCourseImage/2020729407976775.png");
        a2.setUrl("http://www.jd.com");
        List<CustomerServiceArticle> list = new ArrayList<>();
        list.add(a1);
        list.add(a2);
        CustomerServiceImageText it = new CustomerServiceImageText();
        it.setTouser("11111");
        it.setMsgtype("news");
        Map<String,List<CustomerServiceArticle>> map = new HashMap<>();
        map.put("articles",list);
        it.setNews(map);

        String json =  JSONObject.toJSONString(it);
        System.out.print(json);
    }
}
