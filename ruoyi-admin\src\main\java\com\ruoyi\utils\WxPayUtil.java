package com.ruoyi.utils;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.config.WxPayConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.*;

/**
 * 微信支付工具类（API v2版本）
 *
 * <AUTHOR>
 * @date 2025-01-30
 */
@Slf4j
@Component
public class WxPayUtil {

    @Autowired
    private WxPayConfig wxPayConfig;

    /**
     * 创建JSAPI支付订单（统一下单）
     *
     * @param payRequest 支付请求参数
     * @return 支付响应
     */
    public Map<String, Object> createJsapiOrder(Map<String, Object> payRequest) throws Exception {
        // 构建统一下单请求参数
        Map<String, String> params = new HashMap<>();
        params.put("appid", wxPayConfig.getAppId());
        params.put("mch_id", wxPayConfig.getMchId());
        params.put("nonce_str", generateNonceStr());
        params.put("body", (String) payRequest.get("description"));
        params.put("out_trade_no", (String) payRequest.get("out_trade_no"));
        params.put("total_fee", String.valueOf(payRequest.get("total_fee")));
        params.put("spbill_create_ip", "127.0.0.1");
        params.put("notify_url", wxPayConfig.getNotifyUrl());
        params.put("trade_type", "JSAPI");
        params.put("openid", (String) payRequest.get("openid"));

        // 生成签名
        params.put("sign", generateSign(params));

        // 转换为XML
        String xmlRequest = mapToXml(params);
        log.info("统一下单请求: {}", maskXmlSensitiveInfo(xmlRequest));

        // 发送请求
        String xmlResponse = sendPostRequest(wxPayConfig.getUnifiedOrderUrl(), xmlRequest);
        log.info("统一下单响应: {}", maskXmlSensitiveInfo(xmlResponse));

        // 解析响应
        return xmlToMap(xmlResponse);
    }

    /**
     * 创建Native支付订单（统一下单）
     *
     * @param payRequest 支付请求参数
     * @return 支付响应
     */
    public Map<String, Object> createNativeOrder(Map<String, Object> payRequest) throws Exception {
        // 构建统一下单请求参数
        Map<String, String> params = new HashMap<>();
        params.put("appid", wxPayConfig.getAppId());
        params.put("mch_id", wxPayConfig.getMchId());
        params.put("nonce_str", generateNonceStr());
        params.put("body", (String) payRequest.get("description"));
        params.put("out_trade_no", (String) payRequest.get("out_trade_no"));
        params.put("total_fee", String.valueOf(payRequest.get("total_fee")));
        params.put("spbill_create_ip", "127.0.0.1");
        params.put("notify_url", wxPayConfig.getNotifyUrl());
        params.put("trade_type", "NATIVE");

        // 生成签名
        params.put("sign", generateSign(params));

        // 转换为XML
        String xmlRequest = mapToXml(params);
        log.info("Native统一下单请求: {}", maskXmlSensitiveInfo(xmlRequest));

        // 发送请求
        String xmlResponse = sendPostRequest(wxPayConfig.getUnifiedOrderUrl(), xmlRequest);
        log.info("Native统一下单响应: {}", maskXmlSensitiveInfo(xmlResponse));

        // 解析响应
        return xmlToMap(xmlResponse);
    }

    /**
     * 查询支付订单
     *
     * @param outTradeNo 商户订单号
     * @return 查询响应
     */
    public Map<String, Object> queryOrder(String outTradeNo) throws Exception {
        // 构建查询请求参数
        Map<String, String> params = new HashMap<>();
        params.put("appid", wxPayConfig.getAppId());
        params.put("mch_id", wxPayConfig.getMchId());
        params.put("out_trade_no", outTradeNo);
        params.put("nonce_str", generateNonceStr());

        // 生成签名
        params.put("sign", generateSign(params));

        // 转换为XML
        String xmlRequest = mapToXml(params);
        log.info("订单查询请求: {}", maskXmlSensitiveInfo(xmlRequest));

        // 发送请求
        String xmlResponse = sendPostRequest(wxPayConfig.getOrderQueryUrl(), xmlRequest);
        log.info("订单查询响应: {}", maskXmlSensitiveInfo(xmlResponse));

        // 解析响应
        return xmlToMap(xmlResponse);
    }

    /**
     * 发送POST请求
     *
     * @param url 请求URL
     * @param xmlData XML数据
     * @return 响应内容
     */
    private String sendPostRequest(String url, String xmlData) throws Exception {
        HttpURLConnection connection = null;
        try {
            URL requestUrl = new URL(url);
            connection = (HttpURLConnection) requestUrl.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "text/xml; charset=UTF-8");
            connection.setDoOutput(true);
            connection.setConnectTimeout(wxPayConfig.getConnectTimeout());
            connection.setReadTimeout(wxPayConfig.getReadTimeout());

            // 发送请求数据
            try (OutputStream os = connection.getOutputStream()) {
                os.write(xmlData.getBytes(StandardCharsets.UTF_8));
                os.flush();
            }

            // 读取响应
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                try (BufferedReader reader = new BufferedReader(
                        new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
                    StringBuilder response = new StringBuilder();
                    String line;
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                    return response.toString();
                }
            } else {
                throw new RuntimeException("HTTP请求失败，状态码: " + responseCode);
            }
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
    }

    /**
     * 生成随机字符串
     */
    private String generateNonceStr() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 生成签名（API v2 MD5签名）
     *
     * @param params 参数Map
     * @return 签名字符串
     */
    private String generateSign(Map<String, String> params) throws Exception {
        // 排序参数
        TreeMap<String, String> sortedParams = new TreeMap<>(params);

        // 构建签名字符串
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : sortedParams.entrySet()) {
            if (!"sign".equals(entry.getKey()) && entry.getValue() != null && !entry.getValue().isEmpty()) {
                sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
            }
        }
        sb.append("key=").append(wxPayConfig.getApiKey());

        // MD5签名
        String signStr = sb.toString();
        log.debug("签名字符串: {}", maskSensitiveInfo(signStr));

        MessageDigest md = MessageDigest.getInstance("MD5");
        byte[] digest = md.digest(signStr.getBytes(StandardCharsets.UTF_8));

        StringBuilder hexString = new StringBuilder();
        for (byte b : digest) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }

        return hexString.toString().toUpperCase();
    }

    /**
     * 脱敏处理敏感信息
     *
     * @param content 原始内容
     * @return 脱敏后的内容
     */
    private String maskSensitiveInfo(String content) {
        if (content == null || content.isEmpty()) {
            return content;
        }

        // 脱敏密钥信息
        content = content.replaceAll("key=[^&]+", "key=***");

        // 脱敏商户号（保留前3位和后3位）
        content = content.replaceAll("mch_id=([0-9]{3})[0-9]*([0-9]{3})", "mch_id=$1***$2");

        // 脱敏AppID（保留前6位和后4位）
        content = content.replaceAll("appid=([a-zA-Z0-9]{6})[a-zA-Z0-9]*([a-zA-Z0-9]{4})", "appid=$1***$2");

        return content;
    }

    /**
     * 脱敏处理XML内容中的敏感信息
     *
     * @param xmlContent 原始XML内容
     * @return 脱敏后的XML内容
     */
    private String maskXmlSensitiveInfo(String xmlContent) {
        if (xmlContent == null || xmlContent.isEmpty()) {
            return xmlContent;
        }

        // 脱敏签名信息
        xmlContent = xmlContent.replaceAll("(<sign><\\!\\[CDATA\\[)[^\\]]+", "$1***");

        // 脱敏商户号（保留前3位和后3位）
        xmlContent = xmlContent.replaceAll("(<mch_id><\\!\\[CDATA\\[)([0-9]{3})[0-9]*([0-9]{3})", "$1$2***$3");

        // 脱敏AppID（保留前6位和后4位）
        xmlContent = xmlContent.replaceAll("(<appid><\\!\\[CDATA\\[)([a-zA-Z0-9]{6})[a-zA-Z0-9]*([a-zA-Z0-9]{4})", "$1$2***$3");

        // 脱敏随机字符串（只显示前8位）
        xmlContent = xmlContent.replaceAll("(<nonce_str><\\!\\[CDATA\\[)([a-zA-Z0-9]{8})[a-zA-Z0-9]*", "$1$2***");

        return xmlContent;
    }

    /**
     * 生成JSAPI支付参数签名（API v2）
     *
     * @param appId 应用ID
     * @param timeStamp 时间戳
     * @param nonceStr 随机字符串
     * @param packageStr 订单详情扩展字符串
     * @return 签名
     */
    public String generateJsapiPaySign(String appId, String timeStamp, String nonceStr, String packageStr) throws Exception {
        Map<String, String> params = new HashMap<>();
        params.put("appId", appId);
        params.put("timeStamp", timeStamp);
        params.put("nonceStr", nonceStr);
        params.put("package", packageStr);
        params.put("signType", "MD5");

        return generateSign(params);
    }

    /**
     * Map转XML
     */
    private String mapToXml(Map<String, String> params) {
        StringBuilder xml = new StringBuilder();
        xml.append("<xml>");
        for (Map.Entry<String, String> entry : params.entrySet()) {
            xml.append("<").append(entry.getKey()).append(">")
               .append("<![CDATA[").append(entry.getValue()).append("]]>")
               .append("</").append(entry.getKey()).append(">");
        }
        xml.append("</xml>");
        return xml.toString();
    }

    /**
     * XML转Map
     */
    public Map<String, Object> xmlToMap(String xml) throws Exception {
        Map<String, Object> result = new HashMap<>();

        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        DocumentBuilder builder = factory.newDocumentBuilder();
        Document doc = builder.parse(new ByteArrayInputStream(xml.getBytes(StandardCharsets.UTF_8)));

        Element root = doc.getDocumentElement();
        NodeList nodeList = root.getChildNodes();

        for (int i = 0; i < nodeList.getLength(); i++) {
            Node node = nodeList.item(i);
            if (node.getNodeType() == Node.ELEMENT_NODE) {
                result.put(node.getNodeName(), node.getTextContent());
            }
        }

        return result;
    }

    /**
     * 验证微信支付回调签名（API v2使用MD5验证）
     */
    public boolean verifyNotifySignature(Map<String, String> params) {
        try {
            String receivedSign = params.get("sign");
            if (receivedSign == null) {
                return false;
            }

            // 移除sign参数后重新计算签名
            Map<String, String> signParams = new HashMap<>(params);
            signParams.remove("sign");

            String calculatedSign = generateSign(signParams);
            return receivedSign.equals(calculatedSign);
        } catch (Exception e) {
            log.error("验证回调签名失败", e);
            return false;
        }
    }
}
