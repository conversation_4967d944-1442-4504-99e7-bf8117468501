package com.ruoyi.order.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.QueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.config.MybatisPlusConfig;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.config.MyConfig;
import com.ruoyi.device.domain.DeviceCamera;
import com.ruoyi.device.service.IDeviceCameraService;
import com.ruoyi.enums.FunctionEnum;
import com.ruoyi.order.domain.OrderCamera;
import com.ruoyi.order.domain.QRCodeResult;
import com.ruoyi.order.dto.*;
import com.ruoyi.order.mapper.OrderCameraMapper;
import com.ruoyi.order.service.IOrderCameraService;
import com.ruoyi.order.service.IOrderCollectService;
import com.ruoyi.po.Order;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.system.service.ISysDictTypeService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.utils.*;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.core.exception.HttpException;
import com.wechat.pay.java.core.exception.MalformedMessageException;
import com.wechat.pay.java.core.exception.ServiceException;
import com.wechat.pay.java.service.payments.nativepay.NativePayService;
import com.wechat.pay.java.service.payments.nativepay.model.Amount;
import com.wechat.pay.java.service.payments.nativepay.model.CloseOrderRequest;
import com.wechat.pay.java.service.payments.nativepay.model.PrepayRequest;
import com.wechat.pay.java.service.payments.nativepay.model.PrepayResponse;
import com.wechat.pay.java.service.refund.RefundService;
import com.wechat.pay.java.service.refund.model.AmountReq;
import com.wechat.pay.java.service.refund.model.CreateRequest;
import com.wechat.pay.java.service.refund.model.Refund;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import static com.ruoyi.utils.OrderConstant.*;

/**
 * 订单管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-07-13
 */
@Slf4j
@Service
public class OrderCameraServiceImpl extends ServiceImpl<OrderCameraMapper, OrderCamera> implements IOrderCameraService {
    @Autowired
    private OrderCameraMapper orderCameraMapper;

    @Autowired
    private IDeviceCameraService deviceCameraService;

    @Resource
    private RedisIdWorker redisIdWorker;

    @Autowired
    private FuYouApi fuYouApi;

    @Autowired
    private ISysUserService userService;
    @Autowired
    private ISysDeptService deptService;
    @Autowired
    private IOrderCollectService orderCollectService;
    @Resource
    private ISysDictTypeService dictTypeService;


    private static final int ORDER_STATUS_PAID = 1; // 订单状态 - 已支付
    private static final int ORDER_STATUS_PRINTED = 4; // 订单状态 - 已打印

    /**
     * 查询订单管理
     *
     * @param orderId 订单管理主键
     * @return 订单管理
     */
    @Override
    public OrderCamera selectOrderCameraByOrderId(String orderId) {
//        return orderCameraMapper.selectOrderCameraByOrderId(orderId);
        return getById(orderId);
    }


    /**
     * 新增订单
     *
     * @param orderCamera 订单管理
     * @return 结果
     */
    public static Config config = null;

    static {
        // 使用自动更新平台证书的RSA配置
        // 一个商户号只能初始化一个配置，否则会因为重复的下载任务报错
        if (config == null) {
            config = new RSAAutoCertificateConfig.Builder()
                    .merchantId(MERCHANT_ID)
                    .privateKey(PRIVATE_KEY)
                    .merchantSerialNumber(MERCHANT_SERIAL_NUMBER)
                    .apiV3Key(API_V3_KEY)
                    .build();
        }
    }

    @Override
    public Result insertOrderCamera(OrderCamera orderCamera, Integer pay) {
        Random random = new Random();
        int randomNumber = random.nextInt(10000); // 生成0到9999的随机数
        long timeMillis = System.currentTimeMillis();
        long orderId = timeMillis * 10000 + randomNumber;
        // 构建service
        NativePayService service = new NativePayService.Builder().config(config).build();
        // request.setXxx(val)设置所需参数，具体参数可见Request定义
        PrepayRequest request = new PrepayRequest();
        Amount amount = new Amount();
        amount.setTotal(pay);
        request.setAmount(amount);
        request.setAppid(APP_ID);
        request.setMchid(MERCHANT_ID);
        request.setDescription(orderCamera.getProductDescription());
        request.setNotifyUrl(NOTIFY_URL);
        request.setOutTradeNo(String.valueOf(orderId));
        orderCamera.setOrderId(String.valueOf(orderId));
        orderCamera.setPayWay(1L);
        // 调用下单方法，得到应答
        PrepayResponse response = service.prepay(request);
        // 使用微信扫描 code_url 对应的二维码，即可体验Native支付
        save(orderCamera);
        return Result.ok(200, "请求成功", "time", new QRCodeResult(response.getCodeUrl(), orderId));
    }

    /**
     * 修改订单管理
     *
     * @param orderCamera 订单管理-1
     * @return 结果
     */
    @Override
    public int updateOrderCamera(OrderCamera orderCamera) {
        orderCamera.setUpdateTime(DateUtils.getNowDate());
        return updateById(orderCamera) ? 1 : 0;
    }

    /**
     * 批量删除订单管理
     *
     * @param orderIds 需要删除的订单管理主键
     * @return 结果
     */
    @Override
    public int deleteOrderCameraByOrderIds(String[] orderIds) {
        return orderCameraMapper.deleteOrderCameraByOrderIds(orderIds);
    }

    /**
     * 删除订单管理信息
     *
     * @param orderId 订单管理主键
     * @return 结果
     */
    @Override
    public int deleteOrderCameraByOrderId(String orderId) {
        return orderCameraMapper.deleteOrderCameraByOrderId(orderId);
    }

    /**
     * 订单超时取消
     *
     * @param order 取消的订单
     */
    @Override
    public void cancelOrder(OrderCamera order) {
        order.setUpdateTime(DateUtils.getNowDate());
        orderCameraMapper.updateOrderCamera(order);

        CloseOrderRequest closeRequest = new CloseOrderRequest();
        closeRequest.setMchid(MERCHANT_ID);
        closeRequest.setOutTradeNo(String.valueOf(order.getOrderId()));
        NativePayService service = new NativePayService.Builder().config(config).build();
        service.closeOrder(closeRequest);
    }

    @Override
    public Result updateStatus(OrderCamera orderCamera) {
        orderCamera.setUpdateTime(DateUtils.getNowDate());
        int i = orderCameraMapper.updateOrderCamera(orderCamera);
        if (i > 0) return Result.ok(200, null, "订单更新成功", System.currentTimeMillis());
        return Result.fail(500, "订单状态修改失败", String.valueOf(System.currentTimeMillis()));
    }

    /**
     * @param order 微信退款
     */
    @Override
    public Result refunds(OrderCamera order) {
        // 初始化服务
        RefundService service = new RefundService.Builder().config(config).build();
        // ... 调用接口
        Refund response = null;
        try {
            CreateRequest request = new CreateRequest();
            request.setOutTradeNo(String.valueOf(order.getOrderId()));
            request.setOutRefundNo(order.getOrderId() + "_refund");
            request.setNotifyUrl(REFUND_NOTIFY_URL);
            AmountReq amountReq = new AmountReq();
            amountReq.setRefund(order.getOrderPrice());
            amountReq.setTotal(order.getOrderPrice());
            amountReq.setCurrency("CNY");
            request.setAmount(amountReq);
            response = service.create(request);
        } catch (HttpException e) { // 发送HTTP请求失败
            // 调用e.getHttpRequest()获取请求打印日志或上报监控，更多方法见HttpException定义
            System.out.println("发送HTTP请求失败");
            e.printStackTrace();
        } catch (ServiceException e) { // 服务返回状态小于200或大于等于300，例如500
            // 调用e.getResponseBody()获取返回体打印日志或上报监控，更多方法见ServiceException定义
            System.out.println("服务返回状态小于200或大于等于300，例如500");
            e.printStackTrace();
        } catch (MalformedMessageException e) { // 服务返回成功，返回体类型不合法，或者解析返回体失败
            // 调用e.getMessage()获取信息打印日志或上报监控，更多方法见MalformedMessageException定义
            System.out.println("服务返回成功，返回体类型不合法，或者解析返回体失败");
            e.printStackTrace();
        }
        System.out.println("response = " + response);
        return Result.ok(200, "退款成功", "", response);
    }

    @Override
    public Result createOrderHL(OrderCamera orderCamera) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            Date createTime = dateFormat.parse(DateUtils.getTime());
            orderCamera.setCreateTime(createTime);
        } catch (Exception e) {
            log.info("时间类型异常");
            log.info(e.getMessage());
        }
        orderCamera.setAppid(MyConfig.WX_APPID);

        boolean save = save(orderCamera);
        if (save) return Result.ok(200, null, "订单创建成功", System.currentTimeMillis());
        else return Result.fail(200, "订单创建失败", String.valueOf(System.currentTimeMillis()));
    }

    @Override
    public Result createOrderCash(OrderCamera order) {
        if (getById(order.getOrderId()) != null)
            updateById(order);
        else
            save(order);
        return Result.ok(200, null, "订单创建成功", System.currentTimeMillis());
    }

    @Override
    public TableDataInfo selectOrderCameraList(OrderCamera orderCamera, int pageNum, int pageSize) {
        QueryChainWrapper<OrderCamera> query = query();
//        query.ne("hide", 1);
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser == null) return null;
        SysUser user = loginUser.getUser();
        if (user == null) return null;


        if (orderCamera.getDeviceId() != null) {
            query.eq("device_id", orderCamera.getDeviceId());
        }

        if (!loginUser.getUsername().equals("admin")) {

            List<Long> userIds = new ArrayList<>();
            userIds.add(user.getUserId());
            Long loginUserDeptId = user.getDeptId();
            SysDept loginUserDept = loginUser.getUser().getDept();
            if (loginUserDept.getLeader().equals(user.getUserName())) {  //dept的负责人
                SysUser sysUserParams = new SysUser();
                sysUserParams.setDeptId(loginUserDeptId);
                System.out.println(sysUserParams);
                List<SysUser> sysUsers = userService.selectUserList(sysUserParams);
                for (SysUser sysUser : sysUsers) {
                    userIds.add(sysUser.getUserId());
                }
            }
            query.in("user_id", userIds);
        }


        String orderId = orderCamera.getOrderId();
        if (orderId != null) {
            if (orderId.length() == 17 || orderId.contains("_")) {
                query.eq("order_id", orderId);
            } else if (orderId.length() > 17) {
                query.eq("transaction_id", orderId);
            }
        }
        if (orderCamera.getPhone() != null) {
            query.eq("phone", orderCamera.getPhone());
        }
        if (orderCamera.getPayWay() != null) {
            query.eq("pay_way", orderCamera.getPayWay());
        }
        if (orderCamera.getPhotoType() != null) {
            query.eq("photo_type", orderCamera.getPhotoType());
        }
        if (orderCamera.getStartTime() != null && orderCamera.getEndTime() != null) {
            query.between("create_time", orderCamera.getStartTime(), orderCamera.getEndTime());
        }
        if (orderCamera.getDeviceName() != null)
            query.like("device_name", orderCamera.getDeviceName());

        if (orderCamera.getOrderStatus() != null) {
            if (orderCamera.getOrderStatus() == 10) {
                query.and(qr -> {
                    qr.eq("order_status", 1).or().eq("order_status", 4);
                });
            } else {
                query.eq("order_status", orderCamera.getOrderStatus());
            }
        }


//     todo 待优化 分表
//

        try {
            if (orderCamera.getStartTime() != null && orderCamera.getEndTime() != null) {
                int year = orderCamera.getStartTime().getYear() + 1900;
                if (year < 2025)
                    MybatisPlusConfig.TABLE_NAME_HOLDER.set("order_camera_2024");
                else if (year < DateUtils.getNowDate().getYear() + 1900)
                    MybatisPlusConfig.TABLE_NAME_HOLDER.set("order_camera_" + year);
            }

            Page<OrderCamera> page = query.orderByDesc("create_time").page(new Page<>(pageNum, pageSize));
            List<OrderCamera> records = page.getRecords();
            OrderCamera one = query.select("sum(order_price) as priceCount , sum(money_received) as receivedCount , sum(commission) as commissionCount , sum(interface_fee) as interfaceFeeCount , sum(account) as accountCount , sum(product_quantity) as productQuantityCount").one();
            TableDataInfo tableDataInfo = new TableDataInfo(records, page.getTotal());
            tableDataInfo.setMsg("success");
            tableDataInfo.setData(one);
            return tableDataInfo;
        } finally {
            MybatisPlusConfig.TABLE_NAME_HOLDER.remove();
        }

        // 获取当前页数据


    }

    @Override
    public AjaxResult refundsHL(OrderCamera orderCamera) throws Exception {
        if (orderCamera.getOrderStatus() != 1 && orderCamera.getOrderStatus() != 4 && orderCamera.getOrderStatus() != 6)
            return AjaxResult.error("订单状态错误");
        if (orderCamera.getOrderId().contains("_")) {
            return AjaxResult.error("分账子订单不可发起退款");
        }
        Order order = new Order();
        order.setHlMerchantId(orderCamera.getMchid());
        order.setOutTradeNo(orderCamera.getOrderId());

        String refundId = orderCamera.getRefundId();
        if (refundId == null || refundId.equals("")) {
            refundId = "refund-0-" + orderCamera.getOrderId();
            order.setOutRefundNo(refundId);
        } else {
            String[] split = refundId.split("-");
            if (split.length < 2) return AjaxResult.error("退款订单号错误");
            refundId = "refund-" + (Integer.parseInt(split[1]) + 1) + "-" + orderCamera.getOrderId();
            order.setOutRefundNo(refundId);
        }
        order.setRefundAmount(String.valueOf(orderCamera.getOrderPrice()));
        order.setRefundReason("客户要求退款");

        boolean isPDL = false;
        SysUser user = SecurityUtils.getLoginUser().getUser();
        String ancestors = user.getDept().getAncestors();
        Long deptId = user.getDept().getDeptId();
        if (ancestors.contains("201") || deptId == 201) {
            isPDL = true;
        }

        String myAgencyNo = MyConfig.AgencyNo;
        String myPrivateKey = MyConfig.PrivateKey;
        if (!isPDL) {
            myAgencyNo = MyConfig.AgencyNo_yc;
            myPrivateKey = MyConfig.PrivateKey_yc;
        }

        String param = HttpsMain.format(order, FunctionEnum.ORDER_REFUND, myAgencyNo, myPrivateKey);
        log.info("请求报文{}", param);
        String response = HttpsMain.httpReq(MyConfig.OrderUrl, param);
        log.info("响应报文{}", response);
        if (!RsaUtil.verifyResponseSign(response)) {
            throw new Exception("验签失败");
        }

        RefundResponse refundResponse = JSON.parseObject(response, RefundResponse.class);
        if (refundResponse.getStatus().equals("S")) {
            update().like("order_id", orderCamera.getOrderId()).set("order_status", 5).set("refund_id", refundId).set("refund_time", DateUtils.getNowDate()).update();
            return AjaxResult.success(refundResponse.getMsg());
        }

        return AjaxResult.error(refundResponse.getMsg());
    }

    @Override
    public Result refundsQuery(OrderCamera orderCamera) throws Exception {
        if (orderCamera.getOrderStatus() != 5 && orderCamera.getOrderStatus() != 6) return null;
        Order order = new Order();
        order.setHlMerchantId(orderCamera.getMchid());
        order.setOutRefundNo(orderCamera.getRefundId());

        boolean isPDL = false;
        SysUser user = userService.selectUserById(orderCamera.getUserId());
        String ancestors = user.getDept().getAncestors();
        Long deptId = user.getDept().getDeptId();
        if (ancestors.contains("201") || deptId == 201) {
            isPDL = true;
        }

        String myAgencyNo = MyConfig.AgencyNo;
        String myPrivateKey = MyConfig.PrivateKey;
        if (!isPDL) {
            myAgencyNo = MyConfig.AgencyNo_yc;
            myPrivateKey = MyConfig.PrivateKey_yc;
        }

        String param = HttpsMain.format(order, FunctionEnum.ORDER_REFUND_QUERY, myAgencyNo, myPrivateKey);
        String response = HttpsMain.httpReq(MyConfig.OrderUrl, param);
        log.info("响应报文{}", response);
        QueryRefundResponse queryRefundResponse = JSON.parseObject(response, QueryRefundResponse.class);
        log.info("响应类{}", queryRefundResponse);
        if (queryRefundResponse == null) {
            return Result.fail(500, "未知错误，无返回参数", "");
        }
        if (!queryRefundResponse.getStatus().equals("S")) {
            update().like("order_id", orderCamera.getOrderId()).set("order_status", 6).update();
            return Result.fail(203, queryRefundResponse.getMsg(), "");
        }
        QueryRefundData queryRefundData = queryRefundResponse.getData();
        if (queryRefundData == null) {
            update().like("order_id", orderCamera.getOrderId()).set("order_status", 6).update();
            return Result.fail(500, "未知错误，无refundData", "");
        }

        if (queryRefundData.getTradeStatus().equals("succ")) {
            update().like("order_id", orderCamera.getOrderId()).set("order_status", 3).update();
//            orderCamera.getRefundTime()
            //todo 退款成功逻辑

            return Result.ok(200, "退款成功", "", orderCamera);
        } else if (queryRefundData.getTradeStatus().equals("fail")) {
            update().like("order_id", orderCamera.getOrderId()).set("order_status", 6).update();
            return Result.ok(201, "退款失败", "", orderCamera);
        } else {
            return Result.ok(202, "退款中", "", orderCamera);
        }


    }


    @Override
    public Long getHistoryTotalIncome(Long userId) {
        List<String> years = new ArrayList<>();
        years.add("order_camera_2024");
        years.add("order_camera");
        return orderCameraMapper.getHistoryTotalIncome(userId, years);
    }


    @Override
    public Long countMonthIncome(Long userId, String startTime, String endTime) {
        return orderCameraMapper.countMonthIncome(userId, startTime, endTime);
    }

    @Override
    public int updateOrderCameraTypeById(String orderId, Integer type, Integer modelId) {
        if (modelId == null) modelId = 0;
        boolean update;
        if (type == 2) { //取消特殊处理 只有未支付订单才有取消操作
            update = update().eq("order_id", orderId).eq("order_status", 0).set("order_status", type).set("model_id", modelId).update();
        } else {
            update = update().eq("order_id", orderId).set("order_status", type).set("model_id", modelId).update();
        }

        return update ? 1 : 0;
    }

    /**
     * 获取移动端数据统计
     *
     * @param merchantId 商户id
     * @param loginUser  登录用户
     * @param dateFilter 日期过滤
     * @return 移动端数据统计
     */
    @Override
    public DatStatisticsMobileDTO getDataStatisticsMobile(String merchantId, LoginUser loginUser, Integer dateFilter) {
        DatStatisticsMobileDTO datStatisticsMobileDTO = new DatStatisticsMobileDTO();
        SysUser user = loginUser.getUser();

        // 获取现在和过去的时间
        Date[] dateRange = getDateRange(dateFilter);
        Date nowDateEndTime = TimeUtil.getEndOfDay(dateRange[1]); // 现在的结束时间
        Date preDateStartTime = TimeUtil.getStartOfDay(dateRange[0]); // 过去的开始时间

        // 创建查询条件
        LambdaQueryWrapper<OrderCamera> orderQW = new LambdaQueryWrapper<>();
        orderQW.select(OrderCamera::getOrderPrice, OrderCamera::getCreateTime, OrderCamera::getPhotoType);
        orderQW.between(OrderCamera::getCreateTime, preDateStartTime, nowDateEndTime);
        orderQW.and(qr -> {
            qr.eq(OrderCamera::getOrderStatus, ORDER_STATUS_PAID).or().eq(OrderCamera::getOrderStatus, ORDER_STATUS_PRINTED);
        }); // 只查询已支付和已打印的订单

        // 如果不是管理员，进行部门权限过滤
        if (!loginUser.getUsername().equals("admin")) {

            // 查询用户ID列表
            List<Long> userIds = getUserIdsByDept(user);
            orderQW.in(OrderCamera::getUserId, userIds);

            // 查询用户所属设备
            LambdaQueryWrapper<DeviceCamera> deviceQW = new LambdaQueryWrapper<>();
            deviceQW.select(DeviceCamera::getDeviceId, DeviceCamera::getUserId);
            deviceQW.in(DeviceCamera::getUserId, userIds);
            List<DeviceCamera> deviceCameras = deviceCameraService.list(deviceQW);
            datStatisticsMobileDTO.setTotalDevice((long) deviceCameras.size()); // 总设备数

            // 设备ID列表
            List<String> deviceIds = deviceCameras.stream().map(DeviceCamera::getDeviceId).collect(Collectors.toList());
            datStatisticsMobileDTO.setDeviceIdList(deviceIds);
        } else {
            // 如果是管理员，直接查询所有设备和订单
            LambdaQueryWrapper<DeviceCamera> deviceQW = new LambdaQueryWrapper<>();
            deviceQW.select(DeviceCamera::getDeviceId);
            deviceQW.select(DeviceCamera::getUserId);
            List<DeviceCamera> deviceCameras = deviceCameraService.list(deviceQW);
            datStatisticsMobileDTO.setTotalDevice((long) deviceCameras.size()); //

            // 设备ID列表
            List<String> deviceIds = deviceCameras.stream().map(DeviceCamera::getDeviceId).collect(Collectors.toList());
            datStatisticsMobileDTO.setDeviceIdList(deviceIds);
        }
        List<OrderCamera> orderCameraList = list(orderQW);

        // 获取照片类型
        List<SysDictData> photoTypeList = dictTypeService.selectDictDataByType("types"); // 查询字典数据 - 图片类型
        List<DatStatisticsMobileDTO.OrderChartData> orderChartData = new ArrayList<>();
        photoTypeList.forEach(sysDictData -> {
            DatStatisticsMobileDTO.OrderChartData orderChartDataItem = new DatStatisticsMobileDTO.OrderChartData();
            orderChartDataItem.setType(Integer.valueOf(sysDictData.getDictValue()));
            orderChartDataItem.setName(sysDictData.getDictLabel());
            orderChartDataItem.setValue(0L);
            orderChartData.add(orderChartDataItem);
        });

        // 根据日期过滤获取数据
        DatStatisticsMobileDTO.RevenueChartData revenueChartData = new DatStatisticsMobileDTO.RevenueChartData();
        final BigDecimal[] totalRevenueNow = {BigDecimal.ZERO}; // 现在的总收入
        final BigDecimal[] totalRevenuePast = {BigDecimal.ZERO}; // 过去的总收入
        AtomicLong totalOrderCountNow = new AtomicLong(); // 现在的总订单数
        AtomicLong totalOrderCountPast = new AtomicLong(); // 过去的总订单数
        switch (dateFilter) {
            case 1: // 今天和昨天
                // 时间数组 分为12个 2小时一个 2:00 4:00 6:00 ...
                String[] timeArray = new String[12];
                for (int i = 0; i < 12; i++) {
                    timeArray[i] = (i + 1) * 2 + ":00";
                }
                // 现在和过去的订单金额数组
                BigDecimal[] nowPriceArray = new BigDecimal[12];
                Arrays.fill(nowPriceArray, BigDecimal.ZERO);
                BigDecimal[] prePriceArray = new BigDecimal[12];
                Arrays.fill(prePriceArray, BigDecimal.ZERO);
                orderCameraList.forEach(orderCamera -> {
                    Date createTime = orderCamera.getCreateTime();
                    BigDecimal orderPrice = new BigDecimal(orderCamera.getOrderPrice() != null ? orderCamera.getOrderPrice() : 0);
                    int hour = MyDateUtil.getHour(createTime);
                    if (DateUtils.isSameDay(createTime, nowDateEndTime)) {
                        totalRevenueNow[0] = totalRevenueNow[0].add(orderPrice);
                        nowPriceArray[hour / 2] = nowPriceArray[hour / 2].add(orderPrice);
                        totalOrderCountNow.getAndIncrement();
                        if (orderCamera.getPhotoType() != null) {
                            for (DatStatisticsMobileDTO.OrderChartData orderChartDataItem : orderChartData) {
                                if (orderCamera.getPhotoType().equals(orderChartDataItem.getType())) {
                                    orderChartDataItem.setValue(orderChartDataItem.getValue() + 1);
                                    break;
                                }
                            }
                        }
                    } else {
                        totalRevenuePast[0] = totalRevenuePast[0].add(orderPrice);
                        prePriceArray[hour / 2] = prePriceArray[hour / 2].add(orderPrice);
                        totalOrderCountPast.getAndIncrement();
                    }
                });
                // 同比数组
                BigDecimal[] yoyPriceArray = new BigDecimal[12];
                for (int i = 0; i < 12; i++) {
                    // 如果旧数据为0，新数据大于0 同比为100
                    yoyPriceArray[i] = prePriceArray[i].compareTo(BigDecimal.ZERO) == 0 && nowPriceArray[i].compareTo(BigDecimal.ZERO) > 0
                            ? new BigDecimal(100)
                            : prePriceArray[i].compareTo(BigDecimal.ZERO) == 0
                            ? new BigDecimal(0)
                            : nowPriceArray[i].subtract(prePriceArray[i]).divide(prePriceArray[i], 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
                }
                // 设置数据
                revenueChartData.setTimeArr(timeArray);
                revenueChartData.setNowArr(nowPriceArray);
                revenueChartData.setPastArr(prePriceArray);
                revenueChartData.setYoyArr(yoyPriceArray);
                datStatisticsMobileDTO.setRevenueChartData(revenueChartData);
                datStatisticsMobileDTO.setOrderChartData(orderChartData);
                break;
            case 2:  // 本周和上周
                // 时间数组 分为7天
                String[] weekArray = new String[]{"周一", "周二", "周三", "周四", "周五", "周六", "周日"};
                // 现在和过去的订单金额数组
                BigDecimal[] nowPriceWeekArray = new BigDecimal[7];
                Arrays.fill(nowPriceWeekArray, BigDecimal.ZERO);
                BigDecimal[] prePriceWeekArray = new BigDecimal[7];
                Arrays.fill(prePriceWeekArray, BigDecimal.ZERO);
                orderCameraList.forEach(orderCamera -> {
                    Date createTime = orderCamera.getCreateTime();
                    BigDecimal orderPrice = new BigDecimal(orderCamera.getOrderPrice() != null ? orderCamera.getOrderPrice() : 0);
                    int day = DateUtil.dayOfWeek(createTime);
                    // 转为 0为星期一 1为星期二 ...
                    day = day == 1 ? 6 : day - 2;
                    if (DateUtil.isSameWeek(createTime, nowDateEndTime, true)) { // 本周
                        totalRevenueNow[0] = totalRevenueNow[0].add(orderPrice);
                        nowPriceWeekArray[day] = nowPriceWeekArray[day].add(orderPrice);
                        totalOrderCountNow.getAndIncrement();
                        if (orderCamera.getPhotoType() != null) { // 图片类型
                            for (DatStatisticsMobileDTO.OrderChartData orderChartDataItem : orderChartData) {
                                if (orderCamera.getPhotoType().equals(orderChartDataItem.getType())) {
                                    orderChartDataItem.setValue(orderChartDataItem.getValue() + 1);
                                    break;
                                }
                            }
                        }
                    } else { // 上周
                        totalRevenuePast[0] = totalRevenuePast[0].add(orderPrice);
                        prePriceWeekArray[day] = prePriceWeekArray[day].add(orderPrice);
                        totalOrderCountPast.getAndIncrement();
                    }
                });
                // 同比数组
                BigDecimal[] yoyPriceWeekArray = new BigDecimal[7];
                for (int i = 0; i < 7; i++) {
                    // 如果旧数据为0，新数据大于0 同比为100
                    yoyPriceWeekArray[i] = prePriceWeekArray[i].compareTo(BigDecimal.ZERO) == 0 && nowPriceWeekArray[i].compareTo(BigDecimal.ZERO) > 0
                            ? new BigDecimal(100)
                            : prePriceWeekArray[i].compareTo(BigDecimal.ZERO) == 0
                            ? new BigDecimal(0)
                            : nowPriceWeekArray[i].subtract(prePriceWeekArray[i]).divide(prePriceWeekArray[i], 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
                }
                // 设置数据
                revenueChartData.setTimeArr(weekArray);
                revenueChartData.setNowArr(nowPriceWeekArray);
                revenueChartData.setPastArr(prePriceWeekArray);
                revenueChartData.setYoyArr(yoyPriceWeekArray);
                datStatisticsMobileDTO.setRevenueChartData(revenueChartData);
                datStatisticsMobileDTO.setOrderChartData(orderChartData);
                break;
            case 3:
                // 时间数组 分为4-5周
                String[] monthArray = new String[]{"第一周", "第二周", "第三周", "第四周", "第五周"};
                // 现在和过去的订单金额数组
                BigDecimal[] nowPriceMonthArray = new BigDecimal[5];
                Arrays.fill(nowPriceMonthArray, BigDecimal.ZERO);
                BigDecimal[] prePriceMonthArray = new BigDecimal[5];
                Arrays.fill(prePriceMonthArray, BigDecimal.ZERO);
                orderCameraList.forEach(orderCamera -> {
                    Date createTime = orderCamera.getCreateTime();
                    BigDecimal orderPrice = new BigDecimal(orderCamera.getOrderPrice() != null ? orderCamera.getOrderPrice() : 0);
                    int week = MyDateUtil.weekOfMonth(createTime);
                    if (DateUtil.isSameMonth(createTime, nowDateEndTime)) { // 本月
                        totalRevenueNow[0] = totalRevenueNow[0].add(orderPrice);
                        nowPriceMonthArray[week - 1] = nowPriceMonthArray[week - 1].add(orderPrice);
                        totalOrderCountNow.getAndIncrement();
                        if (orderCamera.getPhotoType() != null) { // 图片类型
                            for (DatStatisticsMobileDTO.OrderChartData orderChartDataItem : orderChartData) {
                                if (orderCamera.getPhotoType().equals(orderChartDataItem.getType())) {
                                    orderChartDataItem.setValue(orderChartDataItem.getValue() + 1);
                                    break;
                                }
                            }
                        }
                    } else { // 上月
                        totalRevenuePast[0] = totalRevenuePast[0].add(orderPrice);
                        prePriceMonthArray[week - 1] = prePriceMonthArray[week - 1].add(orderPrice);
                        totalOrderCountPast.getAndIncrement();
                    }
                });
                // 同比数组
                BigDecimal[] yoyPriceMonthArray = new BigDecimal[5];
                for (int i = 0; i < 5; i++) {
                    // 如果旧数据为0，新数据大于0 同比为100
                    yoyPriceMonthArray[i] = prePriceMonthArray[i].compareTo(BigDecimal.ZERO) == 0 && nowPriceMonthArray[i].compareTo(BigDecimal.ZERO) > 0
                            ? new BigDecimal(100)
                            : prePriceMonthArray[i].compareTo(BigDecimal.ZERO) == 0
                            ? new BigDecimal(0)
                            : nowPriceMonthArray[i].subtract(prePriceMonthArray[i]).divide(prePriceMonthArray[i], 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
                }
                // 设置数据
                revenueChartData.setTimeArr(monthArray);
                revenueChartData.setNowArr(nowPriceMonthArray);
                revenueChartData.setPastArr(prePriceMonthArray);
                revenueChartData.setYoyArr(yoyPriceMonthArray);
                datStatisticsMobileDTO.setRevenueChartData(revenueChartData);
                datStatisticsMobileDTO.setOrderChartData(orderChartData);
                break;
            case 4:
                // 时间数组 分为12个月
                String[] yearArray = new String[12];
                for (int i = 0; i < 12; i++) {
                    yearArray[i] = (i + 1) + "月";
                }
                // 现在和过去的订单金额数组
                BigDecimal[] nowPriceYearArray = new BigDecimal[12];
                Arrays.fill(nowPriceYearArray, BigDecimal.ZERO);
                BigDecimal[] prePriceYearArray = new BigDecimal[12];
                Arrays.fill(prePriceYearArray, BigDecimal.ZERO);
                orderCameraList.forEach(orderCamera -> {
                    Date createTime = orderCamera.getCreateTime();
                    BigDecimal orderPrice = new BigDecimal(orderCamera.getOrderPrice() != null ? orderCamera.getOrderPrice() : 0);
                    int month = MyDateUtil.monthOfYear(createTime);
                    if (MyDateUtil.isSameYear(createTime, nowDateEndTime)) { // 今年
                        totalRevenueNow[0] = totalRevenueNow[0].add(orderPrice);
                        nowPriceYearArray[month] = nowPriceYearArray[month].add(orderPrice);
                        totalOrderCountNow.getAndIncrement();
                        if (orderCamera.getPhotoType() != null) { // 图片类型
                            for (DatStatisticsMobileDTO.OrderChartData orderChartDataItem : orderChartData) {
                                if (orderCamera.getPhotoType().equals(orderChartDataItem.getType())) {
                                    orderChartDataItem.setValue(orderChartDataItem.getValue() + 1);
                                    break;
                                }
                            }
                        }
                    } else { // 去年
                        totalRevenuePast[0] = totalRevenuePast[0].add(orderPrice);
                        prePriceYearArray[month] = prePriceYearArray[month].add(orderPrice);
                        totalOrderCountPast.getAndIncrement();
                    }
                });
                // 同比数组
                BigDecimal[] yoyPriceYearArray = new BigDecimal[12];
                for (int i = 0; i < 12; i++) {
                    // 如果旧数据为0，新数据大于0 同比为100
                    yoyPriceYearArray[i] = prePriceYearArray[i].compareTo(BigDecimal.ZERO) == 0 && nowPriceYearArray[i].compareTo(BigDecimal.ZERO) > 0
                            ? new BigDecimal(100)
                            : prePriceYearArray[i].compareTo(BigDecimal.ZERO) == 0
                            ? new BigDecimal(0)
                            : nowPriceYearArray[i].subtract(prePriceYearArray[i]).divide(prePriceYearArray[i], 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
                }
                // 设置数据
                revenueChartData.setTimeArr(yearArray);
                revenueChartData.setNowArr(nowPriceYearArray);
                revenueChartData.setPastArr(prePriceYearArray);
                revenueChartData.setYoyArr(yoyPriceYearArray);
                datStatisticsMobileDTO.setRevenueChartData(revenueChartData);
                datStatisticsMobileDTO.setOrderChartData(orderChartData);
                break;
            default:
                break;
        }
        datStatisticsMobileDTO.setRevenueNow(totalRevenueNow[0]);
        datStatisticsMobileDTO.setRevenuePast(totalRevenuePast[0]);
        datStatisticsMobileDTO.setOrderNumNow(totalOrderCountNow.get());
        datStatisticsMobileDTO.setOrderNumPast(totalOrderCountPast.get());

        // 获取移动端数据统计
        return datStatisticsMobileDTO;
    }

    /**
     * 获取日期
     * <p>
     * 1: 今天和昨天, 2: 本周和上周,
     * 3: 本月和上月, 4: 本年和去年
     * </p>
     *
     * @param dateFilter 日期过滤
     * @return 日期
     */
    public static Date[] getDateRange(int dateFilter) {
        Date nowDate = null;
        Date preDate = null;
        switch (dateFilter) { // 1: 今天和昨天, 2: 本周和上周, 3: 本月和上月, 4: 本年和去年
            case 1: // 今天和昨天
                nowDate = DateUtils.getNowDate();
                preDate = DateUtils.addDays(nowDate, -1);
                break;
            case 2: // 本周和上周
                nowDate = DateUtils.getNowDate();
                Date nowDateMonday = MyDateUtil.getFirstDayOfWeek(nowDate); // 获取本周一的日期
                preDate = DateUtils.addWeeks(nowDateMonday, -1);
                break;
            case 3: // 本月和上月
                nowDate = DateUtils.getNowDate();
                Date nowDateNo1 = MyDateUtil.getFirstDayOfMonth(nowDate); // 获取本月一号的日期
                preDate = DateUtils.addMonths(nowDateNo1, -1);
                break;
            case 4: // 本年和去年
                nowDate = DateUtils.getNowDate();
                Date nowDateM1No1 = MyDateUtil.getFirstDayOfYear(nowDate); // 获取今年一月一日的日期
                preDate = DateUtils.addYears(nowDateM1No1, -1);
                break;
            default:
                break;
        }
        return new Date[]{preDate, nowDate};
    }

    /**
     * 获取移动端首页数据
     *
     * @param loginUser 登录用户
     * @param deviceId  设备id
     * @return 移动端首页数据
     */
    @Override
    public IndexMobileRespDTO getIndexMobile(LoginUser loginUser, String deviceId) {
        IndexMobileRespDTO indexMobileRespDTO = new IndexMobileRespDTO();
        SysUser user = loginUser.getUser();

        // 获取昨天和今天的时间范围
        Date today = DateUtils.getNowDate();
        Date yesterday = DateUtils.addDays(today, -1);

        // 获取今天和昨天的开始和结束时间
        Date todayStartTime = TimeUtil.getStartOfDay(today);
        Date todayEndTime = TimeUtil.getEndOfDay(today);
        Date yesterdayStartTime = TimeUtil.getStartOfDay(yesterday);
        Date yesterdayEndTime = TimeUtil.getEndOfDay(yesterday);

        // 查询订单数据
        LambdaQueryWrapper<OrderCamera> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.between(OrderCamera::getCreateTime, yesterdayStartTime, todayEndTime);
        if (deviceId != null) queryWrapper.eq(OrderCamera::getDeviceId, deviceId);

        // 如果不是管理员，进行部门权限过滤
        if (!loginUser.getUsername().equals("admin")) {

            // 查询用户ID列表
            List<Long> userIds = getUserIdsByDept(user);
            queryWrapper.in(OrderCamera::getUserId, userIds);

//            // 查询用户所属设备
//            List<DeviceCamera> deviceCameras = deviceCameraService.query().in("user_id", userIds).list();
//
//            // 设备ID列表
//            List<String> deviceIds = new ArrayList<>();
//            for (DeviceCamera deviceCamera : deviceCameras) {
//                deviceIds.add(deviceCamera.getDeviceId());
//            }
//
//            // 如果有设备，查询设备订单，否则查询所有包含下划线的订单
//            if (!deviceIds.isEmpty()) {
//                queryWrapper.and(qr -> {
//                    qr.in(OrderCamera::getDeviceId, deviceIds).or().like(OrderCamera::getOrderId, "\\_");
//                });
//            } else {
//                queryWrapper.like(OrderCamera::getOrderId, "\\_");
//            }
        }
        List<OrderCamera> orderCameraList = list(queryWrapper);

        return getIndexMobileData(orderCameraList, todayStartTime, todayEndTime, yesterdayStartTime, yesterdayEndTime);
    }

    /**
     * 获取移动端首页数据
     *
     * @param orderCameraList    订单列表
     * @param todayStartTime     今天开始时间
     * @param todayEndTime       今天结束时间
     * @param yesterdayStartTime 昨天开始时间
     * @param yesterdayEndTime   昨天结束时间
     * @return 移动端首页数据
     */
    private IndexMobileRespDTO getIndexMobileData(List<OrderCamera> orderCameraList, Date todayStartTime, Date todayEndTime, Date yesterdayStartTime, Date yesterdayEndTime) {
        IndexMobileRespDTO indexMobileRespDTO = new IndexMobileRespDTO();

        List<OrderCamera> todayOrderList = new ArrayList<>();
        List<OrderCamera> yesterdayOrderList = new ArrayList<>();
        for (OrderCamera orderCamera : orderCameraList) {
            if (orderCamera.getCreateTime().after(todayStartTime) && orderCamera.getCreateTime().before(todayEndTime)) {
                todayOrderList.add(orderCamera);
            } else if (orderCamera.getCreateTime().after(yesterdayStartTime) && orderCamera.getCreateTime().before(yesterdayEndTime)) {
                yesterdayOrderList.add(orderCamera);
            }
        }

        long todayOrderTotalPrice = 0L;
        long yesterdayOrderTotalPrice = 0L;
        long todayProfit = 0L;
        long yesterdayProfit = 0L;
        long todayOrderCount = 0L;
        long yesterdayOrderCount = 0L;
        for (OrderCamera orderCamera : todayOrderList) {
            if (orderCamera.getOrderStatus() == ORDER_STATUS_PAID || orderCamera.getOrderStatus() == ORDER_STATUS_PRINTED) {
                if (orderCamera.getOrderPrice() != null) todayOrderTotalPrice += orderCamera.getOrderPrice();
                if (orderCamera.getAccount() != null) todayProfit += orderCamera.getAccount();
                todayOrderCount++;
            }
        }
        for (OrderCamera orderCamera : yesterdayOrderList) {
            if (orderCamera.getOrderStatus() == ORDER_STATUS_PAID || orderCamera.getOrderStatus() == ORDER_STATUS_PRINTED) {
                if (orderCamera.getOrderPrice() != null) yesterdayOrderTotalPrice += orderCamera.getOrderPrice();
                if (orderCamera.getAccount() != null) yesterdayProfit += orderCamera.getAccount();
                yesterdayOrderCount++;
            }
        }

        indexMobileRespDTO.setTodayOrderTotal(BigDecimal.valueOf(todayOrderTotalPrice).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
        indexMobileRespDTO.setYesterdayOrderTotal(BigDecimal.valueOf(yesterdayOrderTotalPrice).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
        indexMobileRespDTO.setTodayProfit(BigDecimal.valueOf(todayProfit).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
        indexMobileRespDTO.setYesterdayProfit(BigDecimal.valueOf(yesterdayProfit).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
        indexMobileRespDTO.setTodayOrderCount(todayOrderCount);
        indexMobileRespDTO.setYesterdayOrderCount(yesterdayOrderCount);
        indexMobileRespDTO.setTodayOrderAvgPrice(todayOrderCount == 0 ? BigDecimal.ZERO : indexMobileRespDTO.getTodayOrderTotal().divide(BigDecimal.valueOf(todayOrderCount), 2, RoundingMode.HALF_UP));
        indexMobileRespDTO.setYesterdayOrderAvgPrice(yesterdayOrderCount == 0 ? BigDecimal.ZERO : indexMobileRespDTO.getYesterdayOrderTotal().divide(BigDecimal.valueOf(yesterdayOrderCount), 2, RoundingMode.HALF_UP));
        return indexMobileRespDTO;
    }

    /**
     * 获取用户ID列表
     * <p>
     * 获取当前用户所在部门下的所有用户ID
     * </p>
     *
     * @param user 用户
     * @return 用户ID列表
     */
    private List<Long> getUserIdsByDept(SysUser user) {
        List<Long> userIds = new ArrayList<>();
        Long loginUserDeptId = user.getDeptId();
        SysDept loginUserDept = user.getDept();

        // 如果是部门负责人，查询部门下所有用户
        if (loginUserDept.getLeader().equals(user.getUserName())) {
            SysUser sysUserParams = new SysUser();
            sysUserParams.setDeptId(loginUserDeptId);
            List<SysUser> sysUsers = userService.selectUserList(sysUserParams);
            for (SysUser sysUser : sysUsers) {
                userIds.add(sysUser.getUserId());
            }
        } else {
            userIds.add(user.getUserId());
        }
        return userIds;
    }
}

