package com.ruoyi.message.util;

import com.ruoyi.message.config.TencentSmsConfig;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.sms.v20210111.SmsClient;
import com.tencentcloudapi.sms.v20210111.models.SendSmsRequest;
import com.tencentcloudapi.sms.v20210111.models.SendSmsResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 腾讯云短信服务工具类
 */
@Slf4j
@Component
@EnableConfigurationProperties(TencentSmsConfig.class)
public class TencentSmsUtil {

    @Resource
    private TencentSmsConfig tencentSmsConfig;

    // 端点
    private final String smsEndpoint = "sms.tencentcloudapi.com";

    // 区域
    private final String smsRegion = "ap-guangzhou";

    /**
     * 创建短信客户端
     *
     * @return Client 短信客户端
     */
    public SmsClient createClient() {

        /* 必要步骤：
         * 实例化一个认证对象，入参需要传入腾讯云账户密钥对secretId，secretKey。
         * 这里采用的是从环境变量读取的方式，需要在环境变量中先设置这两个值。
         * 你也可以直接在代码中写死密钥对，但是小心不要将代码复制、上传或者分享给他人，
         * 以免泄露密钥对危及你的财产安全。
         * CAM密匙查询: https://console.cloud.tencent.com/cam/capi*/
        Credential cred = new Credential(tencentSmsConfig.getSecretId(), tencentSmsConfig.getSecretKey());
        // 实例化一个http选项，可选的，没有特殊需求可以跳过
        HttpProfile httpProfile = new HttpProfile();
        httpProfile.setEndpoint(smsEndpoint);
        // 实例化一个client选项，可选的，没有特殊需求可以跳过
        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setHttpProfile(httpProfile);
        // 实例化要请求产品的client对象,clientProfile是可选的
        return new SmsClient(cred, smsRegion, clientProfile);
    }

    /**
     * 发送短信
     *
     * @param phoneNumber   手机号码
     * @param signName      短信签名
     * @param templateCode  短信模板code
     * @param templateParam 短信模板参数
     * @return boolean 是否发送成功
     */
    public boolean sendSms(String phoneNumber, String signName, String templateCode, String templateParam) {
        // 初始化请求客户端
        SmsClient client = createClient();
        // 组装请求对象
        SendSmsRequest req = new SendSmsRequest();
        req.setSmsSdkAppId(tencentSmsConfig.getAppId());
        /* 短信签名内容: 使用 UTF-8 编码，必须填写已审核通过的签名，签名信息可登录 [短信控制台] 查看 */
        req.setSignName(signName);
        /* 用户的 session 内容: 可以携带用户侧 ID 等上下文信息，server 会原样返回 */
        String sessionContext = "xxx";
        req.setSessionContext(sessionContext);
        req.setTemplateId(templateCode);
        req.setPhoneNumberSet(new String[]{"+86" + phoneNumber});
        /* 模板参数: 若无模板参数，则设置为空 */
        req.setTemplateParamSet(new String[]{templateParam});
        /* 通过 client 对象调用 SendSms 方法发起请求。注意请求方法名与请求对象是对应的
         * 返回的 res 是一个 SendSmsResponse 类的实例，与请求对象对应 */
        SendSmsResponse res = null;
        try {
            res = client.SendSms(req);
        } catch (TencentCloudSDKException ex) {
            log.error("[短信服务] 发送短息异常，手机号码：{}", phoneNumber, ex);
            return false;
        }
        // 输出json格式的字符串回包
        System.out.println(SendSmsResponse.toJsonString(res));
        // 也可以取出单个值，你可以通过官网接口文档或跳转到response对象的定义处查看返回字段的定义
        System.out.println(res.getRequestId());
        return true;
    }
}
