package com.yunchuang.wxapp.service.admin;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yunchuang.wxapp.model.domain.PrivacyPolicy;

import java.util.List;

/**
 * 隐私政策Service接口
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
public interface IPrivacyPolicyService extends IService<PrivacyPolicy> {
    /**
     * 查询隐私政策
     *
     * @param id 隐私政策主键
     * @return 隐私政策
     */
    public PrivacyPolicy selectPrivacyPolicyById(Long id);

    /**
     * 查询隐私政策列表
     *
     * @param wxPrivacyPolicy 隐私政策
     * @return 隐私政策集合
     */
    public List<PrivacyPolicy> selectPrivacyPolicyList(PrivacyPolicy wxPrivacyPolicy);

    /**
     * 新增隐私政策
     *
     * @param wxPrivacyPolicy 隐私政策
     * @return 结果
     */
    public int insertPrivacyPolicy(PrivacyPolicy wxPrivacyPolicy);

    /**
     * 修改隐私政策
     *
     * @param wxPrivacyPolicy 隐私政策
     * @return 结果
     */
    public int updatePrivacyPolicy(PrivacyPolicy wxPrivacyPolicy);

    /**
     * 批量删除隐私政策
     *
     * @param ids 需要删除的隐私政策主键集合
     * @return 结果
     */
    public int deletePrivacyPolicyByIds(Long[] ids);

    /**
     * 删除隐私政策信息
     *
     * @param id 隐私政策主键
     * @return 结果
     */
    public int deletePrivacyPolicyById(Long id);
}
