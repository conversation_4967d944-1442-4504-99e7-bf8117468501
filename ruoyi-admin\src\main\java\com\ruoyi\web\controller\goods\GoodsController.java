package com.ruoyi.web.controller.goods;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.oss.ALY_OSS;
import com.ruoyi.device.domain.DeviceCamera;
import com.ruoyi.device.service.IDeviceCameraService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.goods.domain.Goods;
import com.ruoyi.goods.service.IGoodsService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * goodsController
 *
 * <AUTHOR>
 * @date 2024-07-29
 */
@RestController
@RequestMapping("/goods/coffee")
public class GoodsController extends BaseController {
    @Autowired
    private IGoodsService goodsService;
    @Autowired
    private IDeviceCameraService deviceCameraService;

    /**
     * 查询goods列表
     */
    @PreAuthorize("@ss.hasPermi('goods:goods:list')")
    @GetMapping("/list")
    public TableDataInfo list(Goods goods, int pageNum, int pageSize) {
        goods.setCreateBy(String.valueOf(getUserId()));
        return goodsService.selectGoodsList(goods, pageNum, pageSize);
    }

    /**
     * 导出goods列表
     */
    @PreAuthorize("@ss.hasPermi('goods:goods:export')")
    @Log(title = "goods", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Goods goods, int pageNum, int pageSize) {
        goods.setCreateBy(String.valueOf(getUserId()));
        TableDataInfo tableDataInfo = goodsService.selectGoodsList(goods, pageNum, pageSize);
        List<Goods> list = (List<Goods>) tableDataInfo.getRows();
        ExcelUtil<Goods> util = new ExcelUtil<Goods>(Goods.class);
        util.exportExcel(response, list, "goods数据");
    }

    /**
     * 获取goods详细信息
     */
    @PreAuthorize("@ss.hasPermi('goods:goods:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(goodsService.selectGoodsById(id));
    }

    /**
     * 新增goods
     */
    @PreAuthorize("@ss.hasPermi('goods:goods:add')")
    @Log(title = "goods", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(Goods goods, @RequestParam(value = "image", required = false) MultipartFile image) {
        goods.setCreateTime(DateUtils.getNowDate());
        goods.setCreateBy(String.valueOf(getUserId()));
        if (image != null) {
            String originalFilename = image.getOriginalFilename();
            String imageName = "goods/" + System.currentTimeMillis();
            if (originalFilename.contains(".png")) {
                imageName += ".png";
            } else {
                imageName += ".jpg";
            }
            String url = ALY_OSS.uploadImage(image, imageName);
            url = url.substring(0, url.indexOf("?"));
            goods.setGoodsImage(url);
            goods.setObjectName(imageName);
        }
        return toAjax(goodsService.insertGoods(goods));
    }

    /**
     * 修改goods
     */
    @PreAuthorize("@ss.hasPermi('goods:goods:edit')")
    @Log(title = "goods", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/update")
    public AjaxResult edit(Goods goods, @RequestParam(value = "image", required = false) MultipartFile image) {
        goods.setUpdateTime(DateUtils.getNowDate());
        goods.setUpdateBy(String.valueOf(getUserId()));
        if (image != null) {
            String originalFilename = image.getOriginalFilename();
            String imageName = "goods/" + System.currentTimeMillis();
            if (originalFilename.contains(".png")) {
                imageName += ".png";
            } else {
                imageName += ".jpg";
            }
            String url = ALY_OSS.uploadImage(image, imageName);
            url = url.substring(0, url.indexOf("?"));
            goods.setGoodsImage(url);
            goods.setObjectName(imageName);
        }
        return toAjax(goodsService.updateGoods(goods));
    }

    /**
     * 删除goods
     */
    @PreAuthorize("@ss.hasPermi('goods:goods:remove')")
    @Log(title = "goods", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(goodsService.deleteGoodsByIds(ids));
    }


    /**
     * 查询设备的goods列表
     */
    @GetMapping("/byDevice")
    public TableDataInfo ByDevice (@RequestParam String deviceId,@RequestParam Integer coffeeType,@RequestParam Boolean hot) {
        DeviceCamera deviceCamera = deviceCameraService.getById(deviceId);
        if (deviceCamera == null) return null;
        Long userId = deviceCamera.getUserId();
        List<Goods> list = goodsService.query().eq("create_by", userId).eq("coffee_type", coffeeType).eq("hot", hot).list();
        return new TableDataInfo(list, (long) list.size());
    }
}
