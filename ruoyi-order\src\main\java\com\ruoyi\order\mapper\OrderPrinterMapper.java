package com.ruoyi.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.yulichang.base.MPJBaseMapper;
import com.ruoyi.order.domain.OrderPrinter;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 订单打印机Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-15
 */
@Mapper
public interface OrderPrinterMapper extends MPJBaseMapper<OrderPrinter> {
    /**
     * 查询订单打印机
     *
     * @param orderId 订单打印机主键
     * @return 订单打印机
     */
    public OrderPrinter selectOrderPrinterByOrderId(String orderId);

    /**
     * 查询订单打印机列表
     *
     * @param orderPrinter 订单打印机
     * @return 订单打印机集合
     */
    public List<OrderPrinter> selectOrderPrinterList(OrderPrinter orderPrinter);

    /**
     * 新增订单打印机
     *
     * @param orderPrinter 订单打印机
     * @return 结果
     */
    public int insertOrderPrinter(OrderPrinter orderPrinter);

    /**
     * 修改订单打印机
     *
     * @param orderPrinter 订单打印机
     * @return 结果
     */
    public int updateOrderPrinter(OrderPrinter orderPrinter);

    /**
     * 删除订单打印机
     *
     * @param orderId 订单打印机主键
     * @return 结果
     */
    public int deleteOrderPrinterByOrderId(String orderId);

    /**
     * 批量删除订单打印机
     *
     * @param orderIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOrderPrinterByOrderIds(String[] orderIds);

    /**
     * 查询用户订单列表（包含总金额）
     *
     * @param openid 用户openid
     * @return 订单列表（包含总金额）
     */
    public List<OrderPrinter> selectOrderPrinterListWithTotalAmount(@Param("openid") String openid);

    /**
     * 批量计算订单总金额
     *
     * @param orderIds 订单ID列表
     * @return 订单ID和总金额的映射
     */
    public List<Map<String, Object>> batchCalculateOrderTotalAmount(@Param("orderIds") List<String> orderIds);

    /**
     * 查询用户订单列表（支持状态筛选）
     *
     * @param openid 用户openid
     * @param payStatus 支付状态筛选
     * @param printStatus 打印状态筛选
     * @return 订单列表（包含总金额和打印状态）
     */
    public List<OrderPrinter> selectOrderPrinterListWithStatusFilter(@Param("openid") String openid,
                                                                     @Param("payStatus") Integer payStatus,
                                                                     @Param("printStatus") String printStatus);
} 