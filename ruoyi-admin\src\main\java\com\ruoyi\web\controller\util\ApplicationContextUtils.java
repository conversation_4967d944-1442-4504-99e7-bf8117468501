package com.ruoyi.web.controller.util;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

//用来获取springboot创建好的工厂

/**
 * 1. 由于我们在使用分布式缓存时，需要用到'RedisTemplate'或'StringRedisTemplate'其中的一个对象。
 * 我们这里分布式缓存用到对象，所以这里使用"RedisTemplate"这个对象。
 * ➀. 如果RedisTemplate这个类是工厂管理，我们就可以直接使用@Autowired注入这个对象，但是这个类的实例化
 * 是由mybatis实例化的(创建的)-->而mybatis中的Cache对象不是由工厂去管理的对象，我们要在自定义的
 * RedisCache这个类下面去注入并使用'RedisTemplate'，是无法注入的。
 * <p>
 * ➁. 因此若要拿到'RedisTemplate'这个对象，就需要到Spring的工厂中去获取，而"ApplicationContext这个工厂"是
 * 我们最大的工厂，因此我们就需要获取到ApplicationContext这个工厂，然后根据这个工厂中的"getbean(String bean)"
 * 传入要获取的对象字符串名，去工厂中获取'RedisTemplate'这个对象。
 * <p>
 * <p>
 * 2. 所以我们可以去创建类，比如创建一个(ApplicationContextUtils)类，并让这个类去实现'ApplicationContextAware接口'，
 * 此时当我们去实现'ApplicationContextAware接口后'就代表springboot它会自动帮我们创建一个工厂，当springboot帮我们创建号
 * 工厂后，springboot会通过"setApplicationContext()"这个方法以参数的形式给我们返回创建号的这个工厂。
 * <p>
 * 3. 之后我们就可以获取到这个"ApplicationContext工厂"-->并调用getBean()获取我们需要的"RedisTemplate对象"执行
 * redis中的方法。
 * --->你日后如果给我传进来的是"RedisTemplate",那么入参就是小写的'redisTemplate'。
 */
@Component
public class ApplicationContextUtils implements ApplicationContextAware {

    //
    /**
     * 1. 将springboot给我们创建的工厂定义为全局静态变量，将此工厂对象保留下来
     * 2. 下面"setApplicationContext()"给我们返回的工厂我们就用此对象去接受springboot
     * 给我们返回的这个'ApplicationContext'工厂。
     */
    private static ApplicationContext applicationContext;

    //将创建好工厂以参数形式传递给这个类
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }


    /**
     * 1. 日后我们可以通过springboot给我们创建的这个工厂，去获取对应我们所需要的
     * 对象的方法，通过getBean()去获取
     * 2. 下面方法的入参我们定义称我们锁需要获取的对象的小写形式，他就会给我们返回具体的对象。
     */
    //提供在工厂中获取对象的方法 //RedisTemplate  redisTemplate
    public static Object getBean(String beanName) {
        return applicationContext.getBean(beanName);
    }

}

