package com.ruoyi.system.domain;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.annotation.Excel.ColumnType;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 软件版本表 sys_post
 * 
 * <AUTHOR>
 */

public class SysPost extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 版本序号 */
    @Excel(name = "版本id", cellType = ColumnType.NUMERIC)
    private Long postId;

    /** 版本编码 */
    @Excel(name = "版本号")
    private String postCode;

    /** 版本名称 */
    @Excel(name = "版本名")
    private String postName;

    /** 版本更新地址 */
    @Excel(name = "更新地址")
    private String postUrl;

    /** 软件首页ui */
    @Excel(name = "ui图片")
    private String postUi;

    /** 软件pdf */
    @Excel(name = "pdf")
    private String postPdf;

    /** 编码者 */
    @Excel(name = "编码者")
    private String creator;

    /** 版本排序 */
    @Excel(name = "排序")
    private Integer postSort;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 用户是否存在此版本标识 默认不存在 */
    private boolean flag = false;

    public String getPostUi() {
        return postUi;
    }

    public void setPostUi(String postUi) {
        this.postUi = postUi;
    }

    public Long getPostId()
    {
        return postId;
    }

    public void setPostId(Long postId)
    {
        this.postId = postId;
    }

    @NotBlank(message = "版本号不能为空")
    @Size(min = 0, max = 64, message = "版本号长度不能超过64个字符")
    public String getPostCode()
    {
        return postCode;
    }

    public void setPostCode(String postCode)
    {
        this.postCode = postCode;
    }

    @NotBlank(message = "版本名称不能为空")
    @Size(min = 0, max = 50, message = "版本名称长度不能超过50个字符")
    public String getPostName()
    {
        return postName;
    }

    public void setPostName(String postName)
    {
        this.postName = postName;
    }

    @NotNull(message = "显示顺序不能为空")
    public Integer getPostSort()
    {
        return postSort;
    }

    public void setPostSort(Integer postSort)
    {
        this.postSort = postSort;
    }

    public String getStatus()
    {
        return status;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public boolean isFlag()
    {
        return flag;
    }

    public void setFlag(boolean flag)
    {
        this.flag = flag;
    }

    public String getPostUrl() {
        return postUrl;
    }

    public void setPostUrl(String postUrl) {
        this.postUrl = postUrl;
    }

    public String getPostPdf() {
        return postPdf;
    }

    public void setPostPdf(String postPdf) {
        this.postPdf = postPdf;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    @Override
    public String toString() {
        return "SysPost{" +
                "postId=" + postId +
                ", postCode='" + postCode + '\'' +
                ", postName='" + postName + '\'' +
                ", postUrl='" + postUrl + '\'' +
                ", postUi='" + postUi + '\'' +
                ", postPdf='" + postPdf + '\'' +
                ", creator='" + creator + '\'' +
                ", postSort=" + postSort +
                ", status='" + status + '\'' +
                ", flag=" + flag +
                '}';
    }
}
