package com.yunchuang.wxapp.model.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 隐私政策对象 wxapp_privacy_policy
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("wxapp_privacy_policy")
public class PrivacyPolicy extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 政策ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 发布日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "发布日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date releaseDate;

    /**
     * 生效日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "生效日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date effectiveDate;

    /**
     * 政策内容
     */
    @Excel(name = "政策内容")
    private String policyContent;

    /**
     * 是否启用 0：是 1：否
     */
    @Excel(name = "是否启用 0：是 1：否")
    private Integer beEnabled;
}
