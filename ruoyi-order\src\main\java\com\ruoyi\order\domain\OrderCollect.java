package com.ruoyi.order.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.poi.ss.formula.functions.Count;

/**
 * collect对象 order_collect
 *
 * <AUTHOR>
 * @date 2024-01-03
 */

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("order_collect")
public class OrderCollect {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 商户号
     */
    @Excel(name = "商户号")
    private String merchantId;

    /**
     * 时间
     */
    @Excel(name = "时间", width = 30)
    private String time;

    /**
     * 收入
     */
    @Excel(name = "收入")
    private Long count = 0L;

    /**
     * 大头贴收入
     */
    @Excel(name = "大头贴收入")
    private Long dttIncome = 0L;

    /**
     * 大头贴销量
     */
    @Excel(name = "大头贴销量")
    private Long dttOrderCount = 0L;

    /**
     * 大头贴退单量
     */
    @Excel(name = "大头贴退单量")
    private Long dttRefundCount = 0L;

    /**
     * 证件照收入
     */
    @Excel(name = "证件照收入")
    private Long idIncome = 0L;

    /**
     * 证件照销量
     */
    @Excel(name = "证件照销量")
    private Long idOrderCount = 0L;

    /**
     * 证件照退单量
     */
    @Excel(name = "证件照退单量")
    private Long idRefundCount = 0L;

    /**
     * AI收入
     */
    @Excel(name = "AI收入")
    private Long aiIncome = 0L;

    /**
     * AI销量
     */
    @Excel(name = "AI销量")
    private Long aiOrderCount = 0L;

    /**
     * AI退单量
     */
    @Excel(name = "AI退单量")
    private Long aiRefundCount = 0L;

    /**
     * 手机上传收入
     */
    @Excel(name = "手机上传收入")
    private Long uploadIncome = 0L;

    /**
     * 手机上传销量
     */
    @Excel(name = "手机上传销量")
    private Long uploadOrderCount = 0L;

    /**
     * 手机上传退单量
     */
    @Excel(name = "手机上传退单量")
    private Long uploadRefundCount = 0L;

    /**
     * 加印收入
     */
    @Excel(name = "加印收入")
    private Long addIncome = 0L;

    /**
     * 加印订单量
     */
    @Excel(name = "加印订单量")
    private Long addOrderCount = 0L;

    /**
     * 加印退单量
     */
    @Excel(name = "加印退单量")
    private Long addRefundCount = 0L;

    /**
     * 其他收入
     */
    @Excel(name = "其他收入")
    private Long otherIncome = 0L;

    /**
     * 其他订单量
     */
    @Excel(name = "其他订单量")
    private Long otherOrderCount = 0L;

    /**
     * 其他退单量
     */
    @Excel(name = "其他退单量")
    private Long otherRefundCount = 0L;

}

