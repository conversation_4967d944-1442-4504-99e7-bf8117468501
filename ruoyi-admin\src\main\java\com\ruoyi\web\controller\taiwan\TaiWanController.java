package com.ruoyi.web.controller.taiwan;


import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.dto.Result;
import com.ruoyi.order.domain.OrderCamera;
import com.ruoyi.order.service.IOrderCameraService;
import com.ruoyi.photo.domain.UserPhoto;
import com.ruoyi.photo.service.IUserPhotoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;


@RestController
@RequestMapping("/taiwan/aws")
@Slf4j
public class TaiWanController {
    @Resource
    private AwsS3Service awsS3Service;
    @Autowired
    private IOrderCameraService orderCameraService;
    @Autowired
    private IUserPhotoService userPhotoService;

    @PostMapping("/uploadFiles")
    public Result uploadFiles(@RequestParam(value = "file") MultipartFile file, String orderId) {
        OrderCamera orderCamera = orderCameraService.selectOrderCameraByOrderId(orderId);
        if (orderCamera == null) return Result.fail(500, "订单错误", "");

        if (file != null) {

            String path = "";
            try {
                path = awsS3Service.uploadFile(file);
                log.info("path = " + path);
            } catch (Exception e) {
                e.printStackTrace();
            }
            UserPhoto userPhoto = new UserPhoto();
            userPhoto.setOpenId(orderCamera.getOpenid());
            userPhoto.setUrl(path);
            userPhoto.setDeviceId(orderCamera.getDeviceId());
            userPhoto.setDeviceName(orderCamera.getDeviceName());
            userPhoto.setOrderId(orderCamera.getOrderId());
            userPhoto.setObjectName("imageName");
            userPhoto.setType(orderCamera.getPhotoType());
            userPhoto.setCreateTime(DateUtils.getNowDate());

            userPhotoService.save(userPhoto);

            return Result.ok(200, "照片上传成功", String.valueOf(System.currentTimeMillis()), path);
        } else {
            return Result.fail(500, "照片上传失败，file为空", "");
        }
    }


    /**
     * 保存照片
     */
    @PostMapping("/save")
    public Result save(@RequestParam(value = "file", required = false) MultipartFile[] file, String orderId) {
        List<String> urls = new ArrayList<>();
        OrderCamera orderCamera = orderCameraService.selectOrderCameraByOrderId(orderId);
        if (orderCamera == null) return Result.fail(500, "订单错误", "");
        if (file != null && file.length > 0) {
            for (MultipartFile multipartFile : file) {
                String url = userPhotoService.savePhoto(multipartFile, orderCamera,0);
                urls.add(url);
            }
            return Result.ok(200, "照片上传成功", String.valueOf(System.currentTimeMillis()), urls);
        } else {
            return Result.fail(500, "照片上传失败，file为空", "");
        }
    }

}
