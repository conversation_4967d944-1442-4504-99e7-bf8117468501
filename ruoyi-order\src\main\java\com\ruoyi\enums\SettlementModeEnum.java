package com.ruoyi.enums;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/6/22 20:00
 */
public enum SettlementModeEnum {

    AUTOMATIC("1", "自动结算"),
    AUTONOMY("2","自主结算"),
    ;

    private String code;
    private String desc;

    SettlementModeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
