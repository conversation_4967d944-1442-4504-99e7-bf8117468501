package com.ruoyi.device.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * camera_sys_image对象 camera_sys_image
 * 
 * <AUTHOR>
 * @date 2023-07-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("camera_sys_image")
public class CameraSysImage extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 图片id */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /** 图片地址 */
    @Excel(name = "图片地址")
    private String url;

    /** 图片文件夹地址 */
    @Excel(name = "图片地址")
    private String objectName;

    /** 图标地址 */
    @Excel(name = "图标地址")
    private String icon;

    /** 图标文件夹地址 */
    @Excel(name = "图标文件夹地址")
    private String iconObjectName;

    /** 图片名称 */
    @Excel(name = "图片名称")
    private String name;

    /** 图片类型（区分拍照模板、系统背景） */
    @Excel(name = "图片类型", readConverterExp = "区=分拍照模板、系统背景")
    private String type;

    /** 代理id */
    private Long deptId;

    /** 填充位置的x轴 */
    private String positionX;

    /** 填充位置的y轴 */
    private String positionY;

    /** 填充位置的宽 */
    private String positionW;

    /** 填充位置的高 */
    private String positionH;


    @TableField(exist = false)
    private Integer useCount;



   /* *//** 是否显示*//*
    @TableField(exist = false)
    private Boolean isChoice;

    public Boolean getChoice() {
        return isChoice;
    }

    public void setChoice(Boolean choice) {
        isChoice = choice;
    }
*/
}
