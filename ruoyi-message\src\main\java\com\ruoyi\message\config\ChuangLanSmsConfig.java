package com.ruoyi.message.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 短信配置类 - 创蓝云智
 * <p>
 * 该类用于读取创蓝云智短信相关的配置属性
 * </p>
 * <p>
 * 注意：请确保在 application.yml 或 application.properties 中配置了以下属性：
 * <ul>
 *     <li>chuanglan.sms.account</li>
 *     <li>chuanglan.sms.password</li>
 * </ul>
 * </p>
 */
@Data
@Component
@ConfigurationProperties(prefix = "chuanglan.sms")
public class ChuangLanSmsConfig {

    /**
     * API账号
     */
    private String account;

    /**
     * API密码
     */
    private String password;
}
