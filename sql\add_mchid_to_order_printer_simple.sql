-- 为 order_printer 表添加 mchid 字段（生产环境版本）
-- 执行时间：2025-01-30
-- 注意：执行前请备份数据库！

-- 1. 添加 mchid 字段
ALTER TABLE order_printer 
ADD COLUMN mchid VARCHAR(50) DEFAULT NULL COMMENT '商户号';

-- 2. 为现有订单设置商户号（从设备负责人获取）
UPDATE order_printer op
INNER JOIN sys_user su ON op.user_id = su.user_id
SET op.mchid = su.merchant_id
WHERE op.mchid IS NULL 
  AND su.merchant_id IS NOT NULL 
  AND su.merchant_id != '';

-- 3. 验证更新结果
SELECT 
    COUNT(*) as total_orders,
    COUNT(mchid) as orders_with_mchid,
    COUNT(*) - COUNT(mchid) as orders_without_mchid
FROM order_printer;

-- 4. 查看商户号分布
SELECT 
    mchid,
    COUNT(*) as order_count
FROM order_printer 
WHERE mchid IS NOT NULL
GROUP BY mchid
ORDER BY order_count DESC;
