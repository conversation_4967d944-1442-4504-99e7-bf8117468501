package com.ruoyi.photo.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;

import com.ruoyi.order.domain.OrderCamera;
import com.ruoyi.photo.domain.UserPhoto;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.web.multipart.MultipartFile;

/**
 * photoService接口
 *
 * <AUTHOR>
 * @date 2023-09-05
 */
public interface IUserPhotoService extends IService<UserPhoto>
{
    /**
     * 查询photo
     *
     * @param id photo主键
     * @return photo
     */
    public UserPhoto selectUserPhotoById(Long id);

    /**
     * 查询photo列表
     *
     * @param userPhoto photo
     * @return photo集合
     */
    public List<UserPhoto> selectUserPhotoList(UserPhoto userPhoto);

    /**
     * 新增photo
     *
     * @param userPhoto photo
     * @return 结果
     */
    public int insertUserPhoto(UserPhoto userPhoto);

    /**
     * 修改photo
     *
     * @param userPhoto photo
     * @return 结果
     */
    public int updateUserPhoto(UserPhoto userPhoto);

    /**
     * 批量删除photo
     *
     * @param ids 需要删除的photo主键集合
     * @return 结果
     */
    public int deleteUserPhotoByIds(Long[] ids);

    /**
     * 删除photo信息
     *
     * @param id photo主键
     * @return 结果
     */
    public int deleteUserPhotoById(Long id);

    String uploadPhoto(MultipartFile file, String deviceId,String openId);

    String savePhoto(MultipartFile file, OrderCamera orderCamera,int index);
    String savePhotoNoOrder(MultipartFile file,String deviceId);

    String savePhotoNoWX_free(MultipartFile file,String photoId);


    String WxImageDetection(MultipartFile file) throws WxErrorException;
}
