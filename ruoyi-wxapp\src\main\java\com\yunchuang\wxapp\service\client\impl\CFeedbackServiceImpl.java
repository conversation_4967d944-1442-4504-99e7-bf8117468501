package com.yunchuang.wxapp.service.client.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yunchuang.wxapp.mapper.FeedbackMapper;
import com.yunchuang.wxapp.model.domain.Feedback;
import com.yunchuang.wxapp.model.domain.WxappLoginUser;
import com.yunchuang.wxapp.model.enums.FeedbackStatus;
import com.yunchuang.wxapp.model.req.CFeedbackAddOneReq;
import com.yunchuang.wxapp.service.client.ICFeedbackService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 意见反馈 Service 实现
 */
@Service
@Transactional
public class CFeedbackServiceImpl extends ServiceImpl<FeedbackMapper, Feedback> implements ICFeedbackService {

    @Resource
    private FeedbackMapper feedbackMapper;

    /**
     * 添加意见反馈
     *
     * @param loginUser 当前登录用户
     * @param addOneReq 新增请求
     * @return 是否添加成功
     */
    @Override
    public boolean addFeedback(WxappLoginUser loginUser, CFeedbackAddOneReq addOneReq) {
        Feedback feedback = new Feedback();
        feedback.setUserId(loginUser.getId());
        feedback.setNickname(loginUser.getNickname());
        feedback.setContent(addOneReq.getFeedbackContent());
        feedback.setStatus(FeedbackStatus.UNHANDLED.getValue());
        feedback.setCreateBy(loginUser.getNickname());
        feedback.setCreateTime(DateUtil.date());
        feedback.setFeedbackType(addOneReq.getFeedbackType());
        return feedbackMapper.insert(feedback) > 0;
    }
}
