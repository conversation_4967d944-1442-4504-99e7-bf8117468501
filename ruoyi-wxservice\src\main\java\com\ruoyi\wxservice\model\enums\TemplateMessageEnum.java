package com.ruoyi.wxservice.model.enums;

import lombok.Getter;

/**
 * 模板消息枚举类
 * <p>
 * 该类用于定义模板消息相关的枚举值
 * </p>
 */
@Getter
public enum TemplateMessageEnum {

    /**
     * 设备补货通知
     */
    DEVICE_REPLENISHMENT("oQ-7oJKFxzYR-xcmY5NVKV_WsOivcsYzbf63WoNKy0w", "设备补货通知");

    private final String id;
    private final String name;

    TemplateMessageEnum(String id, String name) {
        this.id = id;
        this.name = name;
    }

    public static TemplateMessageEnum getById(String id) {
        for (TemplateMessageEnum value : values()) {
            if (value.id.equals(id)) {
                return value;
            }
        }
        return null;
    }
}
