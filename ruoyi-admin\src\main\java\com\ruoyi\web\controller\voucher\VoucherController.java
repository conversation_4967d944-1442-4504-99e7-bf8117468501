package com.ruoyi.web.controller.voucher;

import java.util.List;
import java.util.Random;
import java.util.UUID;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.voucher.domain.Voucher;
import com.ruoyi.voucher.service.IVoucherService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 优惠券Controller
 * 
 * <AUTHOR>
 * @date 2024-04-15
 */
@RestController
@RequestMapping("/voucher/voucher")
public class VoucherController extends BaseController
{
    @Autowired
    private IVoucherService voucherService;

    /**
     * 查询优惠券列表
     */
    @PreAuthorize("@ss.hasPermi('voucher:voucher:list')")
    @GetMapping("/list")
    public TableDataInfo list(Voucher voucher)
    {
        startPage();
        List<Voucher> list = voucherService.selectVoucherList(voucher);
        return getDataTable(list);
    }

    /**
     * 导出优惠券列表
     */
    @PreAuthorize("@ss.hasPermi('voucher:voucher:export')")
    @Log(title = "优惠券", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Voucher voucher)
    {
        List<Voucher> list = voucherService.selectVoucherList(voucher);
        ExcelUtil<Voucher> util = new ExcelUtil<Voucher>(Voucher.class);
        util.exportExcel(response, list, "优惠券数据");
    }

    /**
     * 获取优惠券详细信息
     */
    @PreAuthorize("@ss.hasPermi('voucher:voucher:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id)
    {
        return success(voucherService.selectVoucherById(id));
    }

    /**
     * 新增优惠券
     */
    @PreAuthorize("@ss.hasPermi('voucher:voucher:add')")
    @Log(title = "优惠券", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Voucher voucher)
    {
        return toAjax(voucherService.insertVoucher(voucher));
    }

    /**
     * 修改优惠券
     */
    @PreAuthorize("@ss.hasPermi('voucher:voucher:edit')")
    @Log(title = "优惠券", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Voucher voucher)
    {
        return toAjax(voucherService.updateVoucher(voucher));
    }

    /**
     * 删除优惠券
     */
    @PreAuthorize("@ss.hasPermi('voucher:voucher:remove')")
    @Log(title = "优惠券", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids)
    {
        return toAjax(voucherService.deleteVoucherByIds(ids));
    }
}
