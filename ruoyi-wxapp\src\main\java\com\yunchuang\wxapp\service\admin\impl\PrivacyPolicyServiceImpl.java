package com.yunchuang.wxapp.service.admin.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yunchuang.wxapp.mapper.PrivacyPolicyMapper;
import com.yunchuang.wxapp.model.domain.PrivacyPolicy;
import com.yunchuang.wxapp.service.admin.IPrivacyPolicyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 隐私政策Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@Service
public class PrivacyPolicyServiceImpl extends ServiceImpl<PrivacyPolicyMapper, PrivacyPolicy> implements IPrivacyPolicyService {

    @Autowired
    private PrivacyPolicyMapper wxPrivacyPolicyMapper;

    /**
     * 查询隐私政策
     *
     * @param id 隐私政策主键
     * @return 隐私政策
     */
    @Override
    public PrivacyPolicy selectPrivacyPolicyById(Long id) {
        return wxPrivacyPolicyMapper.selectPrivacyPolicyById(id);
    }

    /**
     * 查询隐私政策列表
     *
     * @param wxPrivacyPolicy 隐私政策
     * @return 隐私政策
     */
    @Override
    public List<PrivacyPolicy> selectPrivacyPolicyList(PrivacyPolicy wxPrivacyPolicy) {
        return wxPrivacyPolicyMapper.selectPrivacyPolicyList(wxPrivacyPolicy);
    }

    /**
     * 新增隐私政策
     *
     * @param wxPrivacyPolicy 隐私政策
     * @return 结果
     */
    @Override
    public int insertPrivacyPolicy(PrivacyPolicy wxPrivacyPolicy) {
        return wxPrivacyPolicyMapper.insertPrivacyPolicy(wxPrivacyPolicy);
    }

    /**
     * 修改隐私政策
     *
     * @param wxPrivacyPolicy 隐私政策
     * @return 结果
     */
    @Override
    public int updatePrivacyPolicy(PrivacyPolicy wxPrivacyPolicy) {
        return wxPrivacyPolicyMapper.updatePrivacyPolicy(wxPrivacyPolicy);
    }

    /**
     * 批量删除隐私政策
     *
     * @param ids 需要删除的隐私政策主键
     * @return 结果
     */
    @Override
    public int deletePrivacyPolicyByIds(Long[] ids) {
        return wxPrivacyPolicyMapper.deletePrivacyPolicyByIds(ids);
    }

    /**
     * 删除隐私政策信息
     *
     * @param id 隐私政策主键
     * @return 结果
     */
    @Override
    public int deletePrivacyPolicyById(Long id) {
        return wxPrivacyPolicyMapper.deletePrivacyPolicyById(id);
    }
}
