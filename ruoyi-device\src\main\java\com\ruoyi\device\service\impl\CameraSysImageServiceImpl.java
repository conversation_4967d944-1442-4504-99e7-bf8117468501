package com.ruoyi.device.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.QueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.query.MPJQueryWrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.ruoyi.common.core.domain.TreeSelect;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.oss.ALY_OSS;
import com.ruoyi.device.domain.CameraSysImage;
import com.ruoyi.device.domain.DeviceCamera;
import com.ruoyi.device.domain.StickerMessage;
import com.ruoyi.device.mapper.CameraSysImageMapper;
import com.ruoyi.device.service.ICameraSysImageService;
import com.ruoyi.device.service.IDeviceCameraService;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * camera_sys_imageService业务层处理
 *
 * <AUTHOR>
 * @date 2023-07-19
 */
@Service
public class CameraSysImageServiceImpl extends ServiceImpl<CameraSysImageMapper, CameraSysImage> implements ICameraSysImageService {
    @Autowired
    private CameraSysImageMapper cameraSysImageMapper;
    @Autowired
    private IDeviceCameraService deviceCameraService;

    @Resource
    private ISysUserService userService;
    @Resource
    private ISysDeptService deptService;

    /**
     * 查询camera_sys_image
     *
     * @param id camera_sys_image主键
     * @return camera_sys_image
     */
    @Override
    public CameraSysImage selectCameraSysImageById(Long id) {
        return getById(id);
//        return cameraSysImageMapper.selectCameraSysImageById(id);
    }

    /**
     * 查询camera_sys_image列表
     *
     * @param cameraSysImage camera_sys_image
     * @return camera_sys_image
     */
    @Override
    public TableDataInfo selectCameraSysImageList(CameraSysImage cameraSysImage, int pageNum, int pageSize, Boolean isOwn) {
        LoginUser user = SecurityUtils.getLoginUser();
        if (user == null) return null;
        boolean isLeader = user.getUser().getDept().getLeader().equals(user.getUsername()) || user.getUser().isAdmin();


        MPJQueryWrapper<CameraSysImage> lambdaWrapper = new MPJQueryWrapper<>();

        lambdaWrapper.select("(SELECT COUNT(*) FROM `order_camera`  WHERE order_camera.model_id = t.id) AS useCount");
        lambdaWrapper.selectAll(CameraSysImage.class);

        if (cameraSysImage.getName() != null && !cameraSysImage.getName().equals("")) {
            lambdaWrapper.like("name", cameraSysImage.getName());
        }
        if (cameraSysImage.getType() != null && !cameraSysImage.getType().equals("")) {
            lambdaWrapper.eq("type", cameraSysImage.getType());
        }
        if (cameraSysImage.getCreateBy() != null && !cameraSysImage.getCreateBy().equals("")) {
            lambdaWrapper.like("create_by", cameraSysImage.getCreateBy());
        }


        if (isOwn != null && isOwn) {
            lambdaWrapper.eq("create_by", user.getUsername());
        } else {
            String[] deptIds = user.getUser().getDept().getAncestors().split(",");//上级代理的id
            List<String> deptIdList = new ArrayList<>(Arrays.asList(deptIds));
            deptIdList.add(String.valueOf(user.getDeptId()));//加上本级代理的id
            List<SysDept> sysDepts = deptService.listByIds(deptIdList);//获取上述级别代理的dept信息
            List<String> leadersName = new ArrayList<>();//上述获取的dept的负责人的username
            leadersName.add(user.getUsername());
            for (SysDept sysDept : sysDepts) {
                leadersName.add(sysDept.getLeader());
            }

            List<Long> childDeptIds = new ArrayList<>();
            if (isLeader) {
                List<SysDept> depts = deptService.query().like("ancestors", user.getDeptId()).list();
                for (SysDept dept : depts) {
                    childDeptIds.add(dept.getDeptId());
                }
                childDeptIds.add(user.getDeptId());

            }

            lambdaWrapper.and(lw -> {
                lw.in("create_by", leadersName);
                if (childDeptIds.size() > 0)
                    lw.or().in("dept_id", childDeptIds);
            });
        }

        lambdaWrapper.orderByDesc("create_time");
        Page<CameraSysImage> sysImagePage = cameraSysImageMapper.selectPage(new Page<>(pageNum, pageSize), lambdaWrapper);

        return new TableDataInfo(sysImagePage.getRecords(), sysImagePage.getTotal());
    }


    /**
     * 查询私人camera_sys_image列表
     *
     * @param cameraSysImage camera_sys_image
     * @return camera_sys_image
     * <p>
     * 旧AI版本用的  ！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！
     */
    @Override
    public List<CameraSysImage> selectMyCameraSysImageList(CameraSysImage cameraSysImage) {
        return cameraSysImageMapper.selectMyCameraSysImageList(cameraSysImage);
    }

    /**
     * 新增camera_sys_image
     *
     * @param cameraSysImage camera_sys_image
     * @return 结果
     */
    @Override
    public int insertCameraSysImage(CameraSysImage cameraSysImage) {
        cameraSysImage.setCreateTime(DateUtils.getNowDate());
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser == null) return 0;
        cameraSysImage.setCreateBy(loginUser.getUsername());
        cameraSysImage.setDeptId(loginUser.getDeptId());
        return save(cameraSysImage) ? 1 : 0;
    }

    /**
     * 修改camera_sys_image
     *
     * @param cameraSysImage camera_sys_image
     * @return 结果
     */
    @Override
    public int updateCameraSysImage(CameraSysImage cameraSysImage) {
        cameraSysImage.setUpdateTime(DateUtils.getNowDate());
        return updateById(cameraSysImage) ? 1 : 0;
//        return cameraSysImageMapper.updateCameraSysImage(cameraSysImage);
    }

    /**
     * 批量删除camera_sys_image
     *
     * @param ids 需要删除的camera_sys_image主键
     * @return 结果
     */
    @Override
    public int deleteCameraSysImageByIds(Long[] ids) {
        for (Long id : ids) {
            CameraSysImage cameraSysImage = cameraSysImageMapper.selectCameraSysImageById(id);
            if (SecurityUtils.getLoginUser() == null) {
                return 0;
            }
            if (SecurityUtils.getLoginUser().getUser().isAdmin()) {
                return cameraSysImageMapper.deleteCameraSysImageByIds(ids);
            }
            if (cameraSysImage.getCreateBy() == null || !cameraSysImage.getCreateBy().equals(SecurityUtils.getLoginUser().getUsername())) {
                return 0;
            }
            if (cameraSysImage.getObjectName() != null)
                ALY_OSS.deleteImage(cameraSysImage.getObjectName());
        }
        return cameraSysImageMapper.deleteCameraSysImageByIds(ids);
    }

    /**
     * 删除camera_sys_image信息
     *
     * @param id camera_sys_image主键
     * @return 结果
     */
    @Override
    public int deleteCameraSysImageById(Long id) {
        return cameraSysImageMapper.deleteCameraSysImageById(id);
    }


    @Override
    public List<CameraSysImage> selectCameraSysBackgroundList() {
        return cameraSysImageMapper.selectCameraSysBackgroundList();
    }


    @Override
    public List<CameraSysImage> userModelType(String deviceId) {
        int userId = deviceCameraService.selectUserIDByDeviceId(deviceId);
        String userName = userService.selectUserNameByUserId(userId);
        if (userName == null) return null;
        List<CameraSysImage> ModelType = cameraSysImageMapper.selectCameraSysImageBydeviceName(userName);
        return ModelType;

    }

    @Override
    public List<StickerMessage> getSticker(String deviceId, Integer who) {
        System.out.println(deviceId);
        System.out.println(who);
        List<StickerMessage> stickerMessages = new ArrayList<>();
        List<CameraSysImage> type1;
        List<CameraSysImage> type2;
        List<CameraSysImage> type3;
        List<CameraSysImage> type4;
        List<CameraSysImage> type6;
        if (deviceId != null && who != null) {
            DeviceCamera deviceCamera = deviceCameraService.getById(deviceId);
            Long userId = deviceCamera.getUserId();
            SysUser user = userService.selectUserById(userId);
            String userName = user.getUserName();
            if (who == 1) {         //官方
                type1 = query().eq("type", 28).and(qr -> {
                    qr.eq("create_by", "admin").or().eq("create_by", "pdl_admin");
                }).list();
                type2 = query().eq("type", 29).and(qr -> {
                    qr.eq("create_by", "admin").or().eq("create_by", "pdl_admin");
                }).list();
                type3 = query().eq("type", 30).and(qr -> {
                    qr.eq("create_by", "admin").or().eq("create_by", "pdl_admin");
                }).list();
                type4 = query().eq("type", 31).and(qr -> {
                    qr.eq("create_by", "admin").or().eq("create_by", "pdl_admin");
                }).list();
                type6 = query().eq("type", 33).and(qr -> {
                    qr.eq("create_by", "admin").or().eq("create_by", "pdl_admin");
                }).list();
            } else if (who == 2) {   // 自己
                type1 = query().eq("type", 28).eq("create_by", userName).list();
                type2 = query().eq("type", 29).eq("create_by", userName).list();
                type3 = query().eq("type", 30).eq("create_by", userName).list();
                type4 = query().eq("type", 31).eq("create_by", userName).list();
                type6 = query().eq("type", 33).eq("create_by", userName).list();
            } else {   //   官方 + 自己
                type1 = query().eq("type", 28).and(qr -> {
                    qr.eq("create_by", userName).or().eq("create_by", "admin").or().eq("create_by", "pdl_admin");
                }).list();
                type2 = query().eq("type", 29).and(qr -> {
                    qr.eq("create_by", userName).or().eq("create_by", "admin").or().eq("create_by", "pdl_admin");
                }).list();
                type3 = query().eq("type", 30).and(qr -> {
                    qr.eq("create_by", userName).or().eq("create_by", "admin").or().eq("create_by", "pdl_admin");
                }).list();
                type4 = query().eq("type", 31).and(qr -> {
                    qr.eq("create_by", userName).or().eq("create_by", "admin").or().eq("create_by", "pdl_admin");
                }).list();
                type6 = query().eq("type", 33).and(qr -> {
                    qr.eq("create_by", userName).or().eq("create_by", "admin").or().eq("create_by", "pdl_admin");
                }).list();
            }
        } else {
            type1 = query().eq("type", 28).list();
            type2 = query().eq("type", 29).list();
            type3 = query().eq("type", 30).list();
            type4 = query().eq("type", 31).list();
            type6 = query().eq("type", 33).list();
        }

        stickerMessages.add(new StickerMessage("氛围", type1));
        stickerMessages.add(new StickerMessage("头饰", type2));
        stickerMessages.add(new StickerMessage("文字", type3));
        stickerMessages.add(new StickerMessage("星座", type4));
        stickerMessages.add(new StickerMessage("中国风", type6));
        return stickerMessages;
    }

    /**
     * @param deviceId
     * @param type
     * @param who      1官方  2自己  3官方+自己
     * @return
     */
    @Override
    public List<CameraSysImage> getModel(String deviceId, String type, int who) {
        if (who == 1) {
            return query().eq("create_by", "pdl_admin").eq("type", type).list();
        } else if (who == 2 || who == 3) {
            DeviceCamera deviceCamera = deviceCameraService.getById(deviceId);
            if (deviceCamera == null) return null;
            Long userId = deviceCamera.getUserId();
            SysUser sysUser = userService.selectUserById(userId);

            if (who == 2) {
                return query().eq("create_by", sysUser.getUserName()).eq("type", type).list();
            }
            return query().eq("create_by", sysUser.getUserName()).eq("type", type).or().eq("create_by", "pdl_admin").eq("type", type).list();
        } else {
            DeviceCamera deviceCamera = deviceCameraService.getById(deviceId);
            if (deviceCamera == null) return null;
            Long userId = deviceCamera.getUserId();
            SysUser user = userService.selectUserById(userId);

            String[] deptIds = user.getDept().getAncestors().split(",");//上级代理的id
            List<String> deptIdList = new ArrayList<>(Arrays.asList(deptIds));
            deptIdList.add(String.valueOf(user.getDeptId()));//加上本级代理的id
            List<SysDept> sysDepts = deptService.listByIds(deptIdList);//获取上述级别代理的dept信息
            List<String> leadersName = new ArrayList<>();//上述获取的dept的负责人的username
            leadersName.add(user.getUserName());
            for (SysDept sysDept : sysDepts) {
                leadersName.add(sysDept.getLeader());
            }
            return query().in("create_by", leadersName).eq("type", type).list();
        }
    }
}
