<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.order.mapper.OrderPrinterTaskMapper">
    
    <resultMap type="OrderPrinterTask" id="OrderPrinterTaskResult">
        <result property="taskId"    column="task_id"    />
        <result property="orderId"    column="order_id"    />
        <result property="deviceId"    column="device_id"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="fileName"    column="file_name"    />
        <result property="originalFileName"    column="original_file_name"    />
        <result property="fileType"    column="file_type"    />
        <result property="fileSize"    column="file_size"    />
        <result property="printStatus"    column="print_status"    />
        <result property="pageRange"    column="page_range"    />
        <result property="copies"    column="copies"    />
        <result property="colorMode"    column="color_mode"    />
        <result property="duplexMode"    column="duplex_mode"    />
        <result property="paperType"    column="paper_type"    />
        <result property="taskPrice"    column="task_price"    />
        <result property="errorMsg"    column="error_msg"    />
        <result property="retryCount"    column="retry_count"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="status"    column="status"    />
        <result property="isPhoto"    column="is_photo"    />
        <result property="sizeSpec"    column="size_spec"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectOrderPrinterTaskVo">
        select task_id, order_id, device_id, file_url, file_name, original_file_name, file_type, file_size, print_status, page_range,
        copies, color_mode, duplex_mode, paper_type, task_price, error_msg, retry_count, start_time, end_time, status,
        is_photo, size_spec, create_by, create_time, update_by, update_time, remark
        from order_printer_tasks
    </sql>

    <select id="selectOrderPrinterTaskList" parameterType="OrderPrinterTask" resultMap="OrderPrinterTaskResult">
        <include refid="selectOrderPrinterTaskVo"/>
        <where>
            <choose>
                <when test="status != null">
                    and status = #{status}
                </when>
                <otherwise>
                    and status = 0
                </otherwise>
            </choose>
            <if test="orderId != null  and orderId != ''"> and order_id = #{orderId}</if>
            <if test="deviceId != null  and deviceId != ''"> and device_id = #{deviceId}</if>
            <if test="printStatus != null "> and print_status = #{printStatus}</if>
            <if test="fileType != null  and fileType != ''"> and file_type = #{fileType}</if>
            <if test="paperType != null "> and paper_type = #{paperType}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectOrderPrinterTaskByTaskId" parameterType="String" resultMap="OrderPrinterTaskResult">
        <include refid="selectOrderPrinterTaskVo"/>
        where task_id = #{taskId} and status = 0
    </select>
    
    <select id="selectOrderPrinterTasksByOrderId" parameterType="String" resultMap="OrderPrinterTaskResult">
        <include refid="selectOrderPrinterTaskVo"/>
        where order_id = #{orderId} and status = 0
        order by create_time desc
    </select>
        
    <insert id="insertOrderPrinterTask" parameterType="OrderPrinterTask">
        insert into order_printer_tasks
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null">task_id,</if>
            <if test="orderId != null">order_id,</if>
            <if test="deviceId != null">device_id,</if>
            <if test="fileUrl != null">file_url,</if>
            <if test="fileName != null">file_name,</if>
            <if test="originalFileName != null">original_file_name,</if>
            <if test="fileType != null">file_type,</if>
            <if test="fileSize != null">file_size,</if>
            <if test="printStatus != null">print_status,</if>
            <if test="pageRange != null">page_range,</if>
            <if test="copies != null">copies,</if>
            <if test="colorMode != null">color_mode,</if>
            <if test="duplexMode != null">duplex_mode,</if>
            <if test="paperType != null">paper_type,</if>
            <if test="taskPrice != null">task_price,</if>
            <if test="errorMsg != null">error_msg,</if>
            <if test="retryCount != null">retry_count,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="status != null">status,</if>
            <if test="isPhoto != null">is_photo,</if>
            <if test="sizeSpec != null">size_spec,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null">#{taskId},</if>
            <if test="orderId != null">#{orderId},</if>
            <if test="deviceId != null">#{deviceId},</if>
            <if test="fileUrl != null">#{fileUrl},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="originalFileName != null">#{originalFileName},</if>
            <if test="fileType != null">#{fileType},</if>
            <if test="fileSize != null">#{fileSize},</if>
            <if test="printStatus != null">#{printStatus},</if>
            <if test="pageRange != null">#{pageRange},</if>
            <if test="copies != null">#{copies},</if>
            <if test="colorMode != null">#{colorMode},</if>
            <if test="duplexMode != null">#{duplexMode},</if>
            <if test="paperType != null">#{paperType},</if>
            <if test="taskPrice != null">#{taskPrice},</if>
            <if test="errorMsg != null">#{errorMsg},</if>
            <if test="retryCount != null">#{retryCount},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="status != null">#{status},</if>
            <if test="isPhoto != null">#{isPhoto},</if>
            <if test="sizeSpec != null">#{sizeSpec},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateOrderPrinterTask" parameterType="OrderPrinterTask">
        update order_printer_tasks
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="fileUrl != null">file_url = #{fileUrl},</if>
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="originalFileName != null">original_file_name = #{originalFileName},</if>
            <if test="fileType != null">file_type = #{fileType},</if>
            <if test="fileSize != null">file_size = #{fileSize},</if>
            <if test="printStatus != null">print_status = #{printStatus},</if>
            <if test="pageRange != null">page_range = #{pageRange},</if>
            <if test="copies != null">copies = #{copies},</if>
            <if test="colorMode != null">color_mode = #{colorMode},</if>
            <if test="duplexMode != null">duplex_mode = #{duplexMode},</if>
            <if test="paperType != null">paper_type = #{paperType},</if>
            <if test="taskPrice != null">task_price = #{taskPrice},</if>
            <if test="errorMsg != null">error_msg = #{errorMsg},</if>
            <if test="retryCount != null">retry_count = #{retryCount},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="isPhoto != null">is_photo = #{isPhoto},</if>
            <if test="sizeSpec != null">size_spec = #{sizeSpec},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where task_id = #{taskId} and (status = 0 or #{status} is not null)
    </update>

    <delete id="deleteOrderPrinterTaskByTaskId" parameterType="String">
        delete from order_printer_tasks where task_id = #{taskId}
    </delete>

    <delete id="deleteOrderPrinterTaskByTaskIds" parameterType="String">
        delete from order_printer_tasks where task_id in 
        <foreach item="taskId" collection="array" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </delete>
</mapper> 