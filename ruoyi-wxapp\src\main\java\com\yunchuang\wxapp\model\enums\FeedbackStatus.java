package com.yunchuang.wxapp.model.enums;

import lombok.Getter;

/**
 * 反馈状态
 */
@Getter
public enum FeedbackStatus {

    /**
     * 未处理
     */
    UNHANDLED(0, "未处理"),

    /**
     * 处理中
     */
    HANDLING(1, "处理中"),

    /**
     * 已处理
     */
    HANDLED(2, "已处理");

    private final Integer value;
    private final String name;

    FeedbackStatus(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public static FeedbackStatus getFeedbackStatus(Integer value) {
        for (FeedbackStatus feedbackStatus : FeedbackStatus.values()) {
            if (feedbackStatus.getValue().equals(value)) {
                return feedbackStatus;
            }
        }
        return null;
    }
}
