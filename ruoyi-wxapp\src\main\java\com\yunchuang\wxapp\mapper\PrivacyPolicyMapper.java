package com.yunchuang.wxapp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yunchuang.wxapp.model.domain.PrivacyPolicy;

import java.util.List;

/**
 * 隐私政策Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
public interface PrivacyPolicyMapper extends BaseMapper<PrivacyPolicy> {
    /**
     * 查询隐私政策
     *
     * @param id 隐私政策主键
     * @return 隐私政策
     */
    public PrivacyPolicy selectPrivacyPolicyById(Long id);

    /**
     * 查询隐私政策列表
     *
     * @param privacyPolicy 隐私政策
     * @return 隐私政策集合
     */
    public List<PrivacyPolicy> selectPrivacyPolicyList(PrivacyPolicy privacyPolicy);

    /**
     * 新增隐私政策
     *
     * @param privacyPolicy 隐私政策
     * @return 结果
     */
    public int insertPrivacyPolicy(PrivacyPolicy privacyPolicy);

    /**
     * 修改隐私政策
     *
     * @param privacyPolicy 隐私政策
     * @return 结果
     */
    public int updatePrivacyPolicy(PrivacyPolicy privacyPolicy);

    /**
     * 删除隐私政策
     *
     * @param id 隐私政策主键
     * @return 结果
     */
    public int deletePrivacyPolicyById(Long id);

    /**
     * 批量删除隐私政策
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePrivacyPolicyByIds(Long[] ids);
}
