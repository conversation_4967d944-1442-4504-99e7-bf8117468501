import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.device.domain.CameraSysImage;
import com.ruoyi.device.mapper.CameraSysImageMapper;

import java.util.Date;
import java.util.HashSet;
import java.util.List;

public class test {


    public static void main(String[] args) {

//        System.out.println("15098765432112309".matches("[0-9]+"));
//        HashSet<String> merchantIds = new HashSet<>();
//        System.out.println(merchantIds.add("121"));
//        System.out.println(merchantIds.add("121"));

        float v = 1990 * 0.006f;
        int round = Math.round(0.5f);
        System.out.println(round);
    }

}
