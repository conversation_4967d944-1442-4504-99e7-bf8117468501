package com.ruoyi.message.service;

import com.alibaba.fastjson.JSON;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.message.util.AliSmsUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 阿里云 消息服务
 */
@Slf4j
@Service
public class AliSmsService {

    @Resource
    private RedisCache redisCache;

    @Resource
    private AliSmsUtils aliSmsUtils;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    // 短信手机号保存到 Redis 中的前缀
    private final String SMS_PER = "sms:phone:";
    // 短信验证码过期时间 单位:秒 5分钟
    private final int SMS_EXPIRE_TIME = 5 * 60;
    // 短信验证码重复限制时间 单位:秒 1分钟
    private final int SMS_LIMIT_TIME = 60;

    /**
     * 清理可能存在的错误缓存数据
     *
     * @param phoneNumber 手机号
     */
    private void cleanupErrorCache(String phoneNumber) {
        String cacheKey = SMS_PER + phoneNumber;
        try {
            // 删除可能存在的错误数据
            redisCache.deleteObject(cacheKey);
            log.info("清理缓存数据 - key: {}", cacheKey);
        } catch (Exception e) {
            log.warn("清理缓存数据失败 - key: {}, 错误: {}", cacheKey, e.getMessage());
        }
    }

    /**
     * 发送短信验证码（模拟模式）
     *
     * @param phoneNumber 手机号码
     * @param code        验证码
     * @return 是否发送成功
     */
    public boolean sendSmsVerifyCodeMock(String phoneNumber, String code) {
        log.info("=== 模拟发送短信验证码 ===");
        log.info("手机号: {}", phoneNumber);
        log.info("验证码: {}", code);

        // 先清理可能存在的错误缓存数据
        cleanupErrorCache(phoneNumber);

        // 检查发送频率（使用与原方法一致的逻辑）
        String frequencyKey = "sms:frequency:" + phoneNumber;
        String lastSendTime = redisCache.getCacheObject(frequencyKey);

        if (lastSendTime != null) {
            log.warn("发送频率限制 - 手机号: {}, 上次发送时间: {}", phoneNumber, lastSendTime);
            return false;
        }

        try {
            // 模拟发送成功，直接在控制台输出
            System.out.println("==========================================");
            System.out.println("📱 模拟短信发送");
            System.out.println("📞 手机号: " + phoneNumber);
            System.out.println("🔢 验证码: " + code);
            System.out.println("⏰ 发送时间: " + new Date());
            System.out.println("⏳ 有效期: 5分钟");
            System.out.println("==========================================");

            // 存储验证码到Redis，设置5分钟过期（使用与校验方法一致的存储方式）
            Map<String, Object> smsData = new HashMap<>();
            smsData.put("code", code);
            smsData.put("currentTime", System.currentTimeMillis());

            // 使用RedisCache存储Map对象，与校验方法保持一致
            redisCache.setCacheObject(SMS_PER + phoneNumber, smsData, SMS_EXPIRE_TIME, TimeUnit.SECONDS);

            // 设置发送频率限制，1分钟内不能重复发送
            redisCache.setCacheObject(frequencyKey, String.valueOf(System.currentTimeMillis()), SMS_LIMIT_TIME, TimeUnit.SECONDS);

            log.info("验证码已存储到Redis - key: {}, 过期时间: {}秒", SMS_PER + phoneNumber, SMS_EXPIRE_TIME);
            log.info("发送频率限制已设置 - key: {}, 过期时间: 1分钟", frequencyKey);



            return true;

        } catch (Exception e) {
            log.error("模拟发送短信验证码异常", e);
            return false;
        }
    }

    /**
     * 发送短信验证码
     *
     * @param phoneNumber 手机号
     * @param code        验证码
     * @return 发送结果
     */
    public boolean sendSmsVerifyCode(String phoneNumber, String code) {
        // 限流，限制1分钟内不能重复发送短信
        Map<String, Object> smsMap = redisCache.getCacheMap(SMS_PER + phoneNumber);
        if (smsMap != null && !smsMap.isEmpty()) {
            long currentTime = (long) smsMap.get("currentTime");
            long nowTime = System.currentTimeMillis();
            // 如果当前时间减去上次发送时间小于1分钟，则不允许发送
            if (nowTime - currentTime < SMS_LIMIT_TIME * 1000) {
                log.info("请勿频繁发送短信验证码");
                return false;
            }
        }
        Map<String, Object> param = new HashMap<>();
        param.put("code", code);
        // 发送短信
        aliSmsUtils.sendSms(phoneNumber, "测试专用模板", "SMS_154950909", JSON.toJSONString(param));

        // 将发送时间存入 Redis
        Map<String, Object> newMap = new HashMap<>();
        newMap.put("code", code);
        newMap.put("currentTime", System.currentTimeMillis());
        // 发送短信成功，写入redis，设置生存时间为5分钟
        redisCache.setCacheObject(SMS_PER + phoneNumber, newMap, SMS_EXPIRE_TIME, TimeUnit.SECONDS);

        return true;
    }

    /**
     * 校验短信验证码
     *
     * @param phoneNumber 手机号码
     * @param code        验证码
     * @return 是否校验成功
     */
    public boolean checkSmsVerifyCode(String phoneNumber, String code) {
        String cacheKey = SMS_PER + phoneNumber;
        log.info("开始校验验证码 - 手机号: {}, 验证码: {}, Redis Key: {}", phoneNumber, code, cacheKey);

        try {
            // 先尝试获取缓存对象
            Object cacheObject = redisCache.getCacheObject(cacheKey);
            if (cacheObject == null) {
                log.info("验证码已过期或不存在 - 手机号: {}", phoneNumber);
                return false;
            }

            Map<String, Object> smsMap = null;

            // 判断缓存对象类型
            if (cacheObject instanceof Map) {
                smsMap = (Map<String, Object>) cacheObject;
            } else if (cacheObject instanceof String) {
                // 如果是字符串，尝试解析为JSON
                try {
                    smsMap = JSON.parseObject((String) cacheObject, Map.class);
                } catch (Exception e) {
                    log.error("解析验证码JSON失败 - 手机号: {}, 数据: {}", phoneNumber, cacheObject, e);
                    // 删除无效数据
                    redisCache.deleteObject(cacheKey);
                    return false;
                }
            } else {
                log.error("验证码数据类型错误 - 手机号: {}, 类型: {}", phoneNumber, cacheObject.getClass().getName());
                // 删除无效数据
                redisCache.deleteObject(cacheKey);
                return false;
            }

            if (smsMap == null || smsMap.isEmpty()) {
                log.info("验证码数据为空 - 手机号: {}", phoneNumber);
                return false;
            }

            String redisCode = (String) smsMap.get("code");
            if (redisCode == null) {
                log.error("验证码字段不存在 - 手机号: {}", phoneNumber);
                redisCache.deleteObject(cacheKey);
                return false;
            }

            // 校验验证码
            if (!code.equals(redisCode)) {
                log.info("验证码错误 - 手机号: {}, 输入: {}, 期望: {}", phoneNumber, code, redisCode);
                return false;
            }

            // 验证成功，删除 Redis 中的验证码
            redisCache.deleteObject(cacheKey);
            log.info("验证码校验成功 - 手机号: {}", phoneNumber);
            return true;

        } catch (Exception e) {
            log.error("校验验证码异常 - 手机号: {}, 错误: {}", phoneNumber, e.getMessage(), e);
            // 发生异常时删除可能有问题的缓存数据
            try {
                redisCache.deleteObject(cacheKey);
            } catch (Exception deleteEx) {
                log.error("删除异常缓存数据失败 - 手机号: {}", phoneNumber, deleteEx);
            }
            return false;
        }
    }


}
