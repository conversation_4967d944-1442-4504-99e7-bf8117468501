package com.ruoyi.dto;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * VIETQR创建支付码请求
 */
public class VIETQRCreatePayCodeReq {

    /**
     * 金额
     */
    @Min(value = 0, message = "付款金额不能小于0")
    private Long amount;

    /**
     * 支付卡 C:信用卡 D:借记卡
     */
    @NotNull(message = "支付卡类型不能为空")
    private String cardType;

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    @Override
    public String toString() {
        return "VIETQRGenerateCustomerReq{" +
                ", amount=" + amount +
                ", cardType='" + cardType + '\'' +
                '}';
    }
}
