package com.ruoyi.web.controller.resource;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.resource.domain.ModelPackage;
import com.ruoyi.resource.service.IModelPackageService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 模板库Controller
 * 
 * <AUTHOR>
 * @date 2024-12-10
 */
@RestController
@RequestMapping("/resource/package")
public class ModelPackageController extends BaseController
{
    @Autowired
    private IModelPackageService modelPackageService;

    /**
     * 查询模板库列表
     */
    @PreAuthorize("@ss.hasPermi('resource:package:list')")
    @GetMapping("/list")
    public TableDataInfo list(ModelPackage modelPackage)
    {
        startPage();
        List<ModelPackage> list = modelPackageService.selectModelPackageList(modelPackage);
        return getDataTable(list);
    }

    /**
     * 导出模板库列表
     */
    @PreAuthorize("@ss.hasPermi('resource:package:export')")
    @Log(title = "模板库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ModelPackage modelPackage)
    {
        List<ModelPackage> list = modelPackageService.selectModelPackageList(modelPackage);
        ExcelUtil<ModelPackage> util = new ExcelUtil<ModelPackage>(ModelPackage.class);
        util.exportExcel(response, list, "模板库数据");
    }

    /**
     * 获取模板库详细信息
     */
    @PreAuthorize("@ss.hasPermi('resource:package:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(modelPackageService.selectModelPackageById(id));
    }

    /**
     * 新增模板库
     */
    @PreAuthorize("@ss.hasPermi('resource:package:add')")
    @Log(title = "模板库", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ModelPackage modelPackage)
    {
        return toAjax(modelPackageService.insertModelPackage(modelPackage));
    }

    /**
     * 修改模板库
     */
    @PreAuthorize("@ss.hasPermi('resource:package:edit')")
    @Log(title = "模板库", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ModelPackage modelPackage)
    {
        return toAjax(modelPackageService.updateModelPackage(modelPackage));
    }

    /**
     * 删除模板库
     */
    @PreAuthorize("@ss.hasPermi('resource:package:remove')")
    @Log(title = "模板库", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(modelPackageService.deleteModelPackageByIds(ids));
    }
}
