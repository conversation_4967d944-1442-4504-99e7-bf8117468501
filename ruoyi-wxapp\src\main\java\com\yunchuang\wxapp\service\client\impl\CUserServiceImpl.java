package com.yunchuang.wxapp.service.client.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanUtils;

import com.yunchuang.wxapp.exception.WxappBusinessException;
import com.yunchuang.wxapp.mapper.UserMapper;
import com.yunchuang.wxapp.model.domain.User;
import com.yunchuang.wxapp.model.domain.WxappLoginUser;
import com.yunchuang.wxapp.model.enums.exception.WxappBusinessExceptionCode;
import com.yunchuang.wxapp.model.req.CUserLoginOrRegisterReq;
import com.yunchuang.wxapp.model.resp.CUserLoginOrRegisterResp;
import com.yunchuang.wxapp.service.WxAppAuthService;
import com.yunchuang.wxapp.service.WxAppOpenApiService;
import com.yunchuang.wxapp.service.client.ICUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 客户端
 * <br />
 * 用户 Service 实现类
 */
@Service
@Transactional
public class CUserServiceImpl extends ServiceImpl<UserMapper, User> implements ICUserService {

    private static final Logger log = LoggerFactory.getLogger(CUserServiceImpl.class);

    @Resource
    private UserMapper userMapper;

    @Resource
    private WxAppOpenApiService wxAppOpenApiService;

    @Resource
    private WxAppAuthService wxappAuthService;

    @Value("${wxapp.static.default.avatar:/profile/default/avatar.jpg}")
    private String defaultAvatar; // 默认头像

    /**
     * 登录或注册
     *
     * @param req 请求参数
     * @return 登录或注册响应
     */
    @Override
    public CUserLoginOrRegisterResp loginOrRegister(CUserLoginOrRegisterReq req) {
        // 请求微信接口 获取openid
        String openid = wxAppOpenApiService.getOpenid(req.getCode());
        // 查询用户是否存在（Mapper.xml已自动过滤status=0）
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getOpenid, openid);
        User user = userMapper.selectOne(queryWrapper);
        if (user == null) {
            // 不存在则注册
            user = new User();
            user.setNickname("用户" + openid.substring(openid.length() - 5));
            user.setAvatar(defaultAvatar);
            user.setOpenid(openid);
            user.setShotNum(0L);
            user.setStatus(0); // 设置默认状态为正常
            if (userMapper.insert(user) == 0) {
                throw new WxappBusinessException(WxappBusinessExceptionCode.EC_60201);
            }
        }
        // 保存用户信息到redis 并获取token
        WxappLoginUser loginUser = new WxappLoginUser();
        BeanUtils.copyBeanProp(loginUser, user);
        String token = wxappAuthService.setClientUser(loginUser);

        // 封装返回参数
        CUserLoginOrRegisterResp resp = new CUserLoginOrRegisterResp();
        CUserLoginOrRegisterResp.CUserInfo userInfo = new CUserLoginOrRegisterResp.CUserInfo();
        BeanUtils.copyBeanProp(userInfo, user);
        resp.setUserInfo(userInfo);
        resp.setToken(token);
        return resp;
    }

    /**
     * 绑定手机号
     *
     * @param openid 用户openid
     * @param mobile 手机号
     * @return 绑定结果
     */
    @Override
    public boolean bindMobile(String openid, String mobile) {
        // 查询用户是否存在
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getOpenid, openid);
        User user = userMapper.selectOne(queryWrapper);

        if (user == null) {
            return false; // 用户不存在
        }

        // 更新用户手机号
        user.setMobile(mobile);
        return userMapper.updateUser(user) > 0;
    }

    /**
     * 绑定手机号（包含业务验证逻辑）
     *
     * @param openid 用户openid
     * @param mobile 手机号
     * @return 绑定结果消息，成功返回"绑定成功"，失败返回具体错误信息
     */
    @Override
    public String bindMobileWithValidation(String openid, String mobile) {
        log.info("开始绑定手机号业务逻辑处理 - openid: {}, mobile: {}", openid, mobile);

        // 1. 参数验证
        if (StringUtils.isEmpty(openid)) {
            log.error("业务验证失败: openid不能为空");
            return "用户信息无效";
        }

        if (StringUtils.isEmpty(mobile)) {
            log.error("业务验证失败: 手机号不能为空");
            return "手机号不能为空";
        }

        // 2. 手机号格式验证
        if (!mobile.matches("^1[3-9]\\d{9}$")) {
            log.error("业务验证失败: 手机号格式不正确 - {}", mobile);
            return "手机号格式不正确";
        }

        // 3. 查询用户是否存在（Mapper.xml已自动过滤status=0）
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getOpenid, openid);
        User user = userMapper.selectOne(queryWrapper);

        if (user == null) {
            log.error("业务验证失败: 用户不存在 - openid: {}", openid);
            return "用户不存在";
        }

        // 4. 检查手机号是否已被其他用户绑定（Mapper.xml已自动过滤status=0）
        LambdaQueryWrapper<User> mobileQueryWrapper = new LambdaQueryWrapper<>();
        mobileQueryWrapper.eq(User::getMobile, mobile);
        mobileQueryWrapper.ne(User::getOpenid, openid); // 排除当前用户
        User existingUser = userMapper.selectOne(mobileQueryWrapper);

        if (existingUser != null) {
            log.error("业务验证失败: 手机号已被其他用户绑定 - mobile: {}", mobile);
            return "该手机号已被其他用户绑定";
        }

        // 5. 检查用户是否已绑定手机号
        if (StringUtils.isNotEmpty(user.getMobile())) {
            log.warn("用户已绑定手机号，将更新为新手机号 - 原手机号: {}, 新手机号: {}", user.getMobile(), mobile);
        }

        // 6. 执行绑定操作
        try {
            user.setMobile(mobile);
            int updateResult = userMapper.updateUser(user);

            if (updateResult > 0) {
                log.info("手机号绑定成功 - openid: {}, mobile: {}", openid, mobile);
                return "绑定成功";
            } else {
                log.error("手机号绑定失败: 数据库更新失败 - openid: {}, mobile: {}", openid, mobile);
                return "绑定失败，请稍后重试";
            }
        } catch (Exception e) {
            log.error("手机号绑定异常 - openid: {}, mobile: {}, error: {}", openid, mobile, e.getMessage(), e);
            return "系统异常，请稍后重试";
        }
    }

    /**
     * 注销用户账号（逻辑删除相关数据）
     *
     * @param openid 用户openid
     * @return 注销结果消息，成功返回"注销成功"，失败返回具体错误信息
     */
    @Override
    public String deleteUserAccount(String openid) {
        log.info("开始注销用户账号 - openid: {}", openid);

        // 1. 参数验证
        if (StringUtils.isEmpty(openid)) {
            log.error("注销失败: openid不能为空");
            return "用户信息无效";
        }

        // 2. 查询用户是否存在（Mapper.xml已自动过滤status=0）
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getOpenid, openid);
        User user = userMapper.selectOne(queryWrapper);

        if (user == null) {
            log.error("注销失败: 用户不存在 - openid: {}", openid);
            return "用户不存在";
        }

        try {
            // 3. 逻辑删除用户记录（设置status=1）
            user.setStatus(1); // 设置为删除状态
            int updateResult = userMapper.updateUser(user);

            if (updateResult > 0) {
                log.info("用户账号注销成功 - openid: {}, userId: {}", openid, user.getId());
                return "注销成功";
            } else {
                log.error("用户账号注销失败: 数据库更新失败 - openid: {}", openid);
                return "注销失败，请稍后重试";
            }
        } catch (Exception e) {
            log.error("用户账号注销异常 - openid: {}, error: {}", openid, e.getMessage(), e);
            return "系统异常，请稍后重试";
        }
    }

    /**
     * 检查手机号是否可以发送验证码
     *
     * @param openid 用户openid
     * @param mobile 手机号
     * @return 检查结果消息，可以发送返回"可以发送"，否则返回具体原因
     */
    @Override
    public String checkMobileForSms(String openid, String mobile) {
        log.info("检查手机号是否可以发送验证码 - openid: {}, mobile: {}", openid, mobile);

        // 1. 参数验证
        if (StringUtils.isEmpty(openid)) {
            log.error("检查失败: openid不能为空");
            return "用户信息无效";
        }

        if (StringUtils.isEmpty(mobile)) {
            log.error("检查失败: 手机号不能为空");
            return "手机号不能为空";
        }

        // 2. 手机号格式验证
        if (!mobile.matches("^1[3-9]\\d{9}$")) {
            log.error("检查失败: 手机号格式不正确 - {}", mobile);
            return "手机号格式不正确";
        }

        try {
            // 3. 查询当前用户信息
            LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(User::getOpenid, openid);
            User user = userMapper.selectOne(queryWrapper);

            if (user == null) {
                log.error("检查失败: 用户不存在 - openid: {}", openid);
                return "用户不存在";
            }

            // 4. 检查手机号是否与当前绑定的手机号一致
            String currentMobile = user.getMobile();
            if (StringUtils.isNotEmpty(currentMobile)) {
                if (mobile.equals(currentMobile)) {
                    log.warn("手机号与当前绑定的手机号一致 - openid: {}, mobile: {}", openid, mobile);
                    return "该手机号已绑定到当前账号，无需重复绑定";
                }
                log.info("用户要更换手机号 - openid: {}, 原手机号: {}, 新手机号: {}", openid, currentMobile, mobile);
            } else {
                log.info("用户首次绑定手机号 - openid: {}, mobile: {}", openid, mobile);
            }

            // 5. 检查手机号是否已被其他用户绑定
            LambdaQueryWrapper<User> mobileQueryWrapper = new LambdaQueryWrapper<>();
            mobileQueryWrapper.eq(User::getMobile, mobile);
            mobileQueryWrapper.ne(User::getOpenid, openid); // 排除当前用户
            User existingUser = userMapper.selectOne(mobileQueryWrapper);

            if (existingUser != null) {
                log.error("检查失败: 手机号已被其他用户绑定 - mobile: {}", mobile);
                return "该手机号已被其他用户绑定";
            }

            log.info("手机号检查通过，可以发送验证码 - openid: {}, mobile: {}", openid, mobile);
            return "可以发送";

        } catch (Exception e) {
            log.error("检查手机号异常 - openid: {}, mobile: {}, error: {}", openid, mobile, e.getMessage(), e);
            return "系统异常，请稍后重试";
        }
    }

}
