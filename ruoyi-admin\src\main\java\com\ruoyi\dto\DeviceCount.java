package com.ruoyi.dto;


import com.ruoyi.order.domain.OrderCamera;
import com.ruoyi.order.domain.OrderCollect;
import lombok.Data;

import java.util.List;

@Data
public class DeviceCount {
    private Long deviceCount = 0L;//总设备量
    private Long onLineCount = 0L;//在线设备
    private Long revenueCount = 0L;//总营收
    private Long todayCount = 0L;//日总营收
    private Long yesterdayCount = 0L;//昨日总营收
    private Integer todayOrderCount = 0;//今日总订单量
    private Integer yesterdayOrderCount = 0;//昨日总订单量
    private Integer todayRefundCount = 0;//今日总退单量
    private Integer yesterdayRefundCount = 0;//昨日总退单量

    private Long dttCount = 0L;
    private Long idCount = 0L;
    private Long uploadCount = 0L;
    private Long aiCount = 0L;
    private Long sfsCount = 0L;
    private Long addCount = 0L;



    private List<OrderCollect> orderCollectList;//每个月营业汇总 size = 12

    private List<OrderCamera> todayOrderList;
    private List<OrderCamera> yesterdayOrderList;
}
