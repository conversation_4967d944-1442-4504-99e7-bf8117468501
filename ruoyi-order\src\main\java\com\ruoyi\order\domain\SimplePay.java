package com.ruoyi.order.domain;

import lombok.Data;


@Data
public class SimplePay {

    //外部订单号
    /**必填参数*/
    //外部订单号
    private String outTradeNo;
    //支付金额,单位(分)，例：1 表示 1分
    private String totalAmount;
    private String body;
    //汇联商户号
    private String hlMerchantId;
    //支付方式
    private String channelType;

    private String openId;

    private String wxAppId;

    /**可选参数*/
    private String payCompany;
    //设备ID
    private String deviceId;
    //门店ID
    private String storeId;
    //支付成功后通知地址
    private String notifyUrl;
    //附加参数，原样返回
    private String attach;
    //订单有效时间
    private String expireExpress;
    //参与分账商户
    private String splitBunch;

    private String successUrl;

    private String sucUrl;
}
