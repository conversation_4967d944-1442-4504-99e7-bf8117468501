package com.ruoyi.web.controller.beauty;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.beauty.domain.BeautyValue;
import com.ruoyi.beauty.service.IBeautyValueService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 美颜强度Controller
 * 
 * <AUTHOR>
 * @date 2023-10-15
 */
@RestController
@RequestMapping("/beauty/value")
public class BeautyValueController extends BaseController
{
    @Autowired
    private IBeautyValueService beautyValueService;

    /**
     * 查询美颜强度列表
     */
    @PreAuthorize("@ss.hasPermi('beauty:value:list')")
    @GetMapping("/list")
    public TableDataInfo list(BeautyValue beautyValue)
    {
        startPage();
        List<BeautyValue> list = beautyValueService.selectBeautyValueList(beautyValue);
        return getDataTable(list);
    }

    /**
     * 导出美颜强度列表
     */
    @PreAuthorize("@ss.hasPermi('beauty:value:export')")
    @Log(title = "美颜强度", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BeautyValue beautyValue)
    {
        List<BeautyValue> list = beautyValueService.selectBeautyValueList(beautyValue);
        ExcelUtil<BeautyValue> util = new ExcelUtil<BeautyValue>(BeautyValue.class);
        util.exportExcel(response, list, "美颜强度数据");
    }

    /**
     * 获取美颜强度详细信息
     */
    @PreAuthorize("@ss.hasPermi('beauty:value:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(beautyValueService.selectBeautyValueById(id));
    }

    /**
     * 新增美颜强度
     */
    @PreAuthorize("@ss.hasPermi('beauty:value:add')")
    @Log(title = "美颜强度", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BeautyValue beautyValue)
    {
        return toAjax(beautyValueService.insertBeautyValue(beautyValue));
    }

    /**
     * 修改美颜强度
     */
    @PreAuthorize("@ss.hasPermi('beauty:value:edit')")
    @Log(title = "美颜强度", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BeautyValue beautyValue)
    {
        return toAjax(beautyValueService.updateBeautyValue(beautyValue));
    }

    /**
     * 删除美颜强度
     */
    @PreAuthorize("@ss.hasPermi('beauty:value:remove')")
    @Log(title = "美颜强度", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(beautyValueService.deleteBeautyValueByIds(ids));
    }
}
