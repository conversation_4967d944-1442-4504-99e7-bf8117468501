package com.yunchuang.wxapp.interceptor;

import com.yunchuang.wxapp.exception.WxappAuthenticationException;
import com.yunchuang.wxapp.model.domain.WxappLoginUser;
import com.yunchuang.wxapp.model.enums.exception.WxappAuthenticationExceptionCode;
import com.yunchuang.wxapp.service.WxAppAuthService;
import com.yunchuang.wxapp.util.UserContext;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 微信小程序 - 认证拦截器
 */
@Component
public class WxappAuthInterceptor implements HandlerInterceptor {

    @Resource
    private WxAppAuthService wxappAuthService;

    /**
     * 请求处理前
     *
     * @param request  当前HTTP请求
     * @param response 当前HTTP响应
     * @param handler  当前处理器
     * @return 是否继续执行后续操作
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // 获取登录用户信息
        WxappLoginUser loginUser = wxappAuthService.getClientUser(request);
        // 判断用户是否为空
        if (loginUser == null) {
            throw new WxappAuthenticationException(WxappAuthenticationExceptionCode.AUTHENTICATION_FAILED);
        }
        // 判断令牌是否过期
        if (loginUser.isTokenExpired()) {
            throw new WxappAuthenticationException(WxappAuthenticationExceptionCode.AUTHENTICATION_EXPIRED);
        }
        // 刷新令牌
        wxappAuthService.refreshToken(loginUser);
        // 将用户信息存入ThreadLocal
        UserContext.setCurrentUser(loginUser);
        return true;
    }

    /**
     * 请求结束后
     *
     * @param request  当前HTTP请求
     * @param response 当前HTTP响应
     * @param handler  当前处理器
     * @param ex       异常
     */
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        UserContext.removeCurrentUser(); // 请求结束后清除ThreadLocal
    }
}