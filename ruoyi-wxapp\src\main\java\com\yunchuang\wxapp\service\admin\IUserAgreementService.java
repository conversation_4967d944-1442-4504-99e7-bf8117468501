package com.yunchuang.wxapp.service.admin;


import com.yunchuang.wxapp.model.domain.UserAgreement;

import java.util.List;

/**
 * 用户协议Service接口
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
public interface IUserAgreementService {
    /**
     * 查询用户协议
     *
     * @param id 用户协议主键
     * @return 用户协议
     */
    public UserAgreement selectUserAgreementById(Long id);

    /**
     * 查询用户协议列表
     *
     * @param userAgreement 用户协议
     * @return 用户协议集合
     */
    public List<UserAgreement> selectUserAgreementList(UserAgreement userAgreement);

    /**
     * 新增用户协议
     *
     * @param userAgreement 用户协议
     * @return 结果
     */
    public int insertUserAgreement(UserAgreement userAgreement);

    /**
     * 修改用户协议
     *
     * @param userAgreement 用户协议
     * @return 结果
     */
    public int updateUserAgreement(UserAgreement userAgreement);

    /**
     * 批量删除用户协议
     *
     * @param ids 需要删除的用户协议主键集合
     * @return 结果
     */
    public int deleteUserAgreementByIds(Long[] ids);

    /**
     * 删除用户协议信息
     *
     * @param id 用户协议主键
     * @return 结果
     */
    public int deleteUserAgreementById(Long id);
}
