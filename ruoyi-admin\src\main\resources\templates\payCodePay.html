<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0"/>
    <link rel="stylesheet" href="css/index.css">
    <title>付款码支付</title>
</head>
<body>

<div class="wrap">
    <div>付款码支付样例</div>
    <form>
        <div class="item">
            <div class="left">金额：</div>
            <div class="right">
                <input id="totalAmount" type="text" value="0.1" name="totalAmount">
            </div>
        </div>
        <div class="item">
            <div class="left">付款码：</div>
            <div class="right">
                <input id="authCode" type="text" name="authCode" placeholder="输入微信支付宝或银联的付款码">
            </div>
        </div>
        <div class="btn">
            <button onclick="doPay()" class="submit_btn" type="button">支付</button>
        </div>
    </form>
</div>

</body>
<script type="text/javascript" src="js/jquery.js"></script>
<script type="text/javascript">

    function doPay() {
        var totalAmount = $('#totalAmount').val();
        var authCode = $('#authCode').val();
        $.ajax({
            url: "/payCodePay",
            type: "POST",
            dataType: "json",
            data: {"totalAmount": totalAmount, "authCode": authCode},
            success: function (data) {
                console.log(data);
                if (data.status == "U") {
                    //在这里进行订单状态查询，一般建议 3-5秒一次轮询，查60秒
                }
            }
        });
    }
</script>
</html>