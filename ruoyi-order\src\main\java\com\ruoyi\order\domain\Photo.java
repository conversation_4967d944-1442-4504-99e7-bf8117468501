package com.ruoyi.order.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * photo对象 user_photo
 * 
 * <AUTHOR>
 * @date 2023-09-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_photo")
public class Photo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 照片id */
    @JsonSerialize(using = ToStringSerializer.class)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /** 用户账号 */
    @Excel(name = "用户账号")
    private String openId;

    /** 照片文件夹地址 */
    @Excel(name = "照片文件夹地址")
    private String objectName;

    /** 照片地址 */
    @Excel(name = "照片地址")
    private String url;

    /** 照片类型 */
    @Excel(name = "照片类型")
    private Integer type;

    /** 设备id */
    @Excel(name = "设备id")
    private String deviceId;

    /** 订单id */
    @Excel(name = "订单id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    /** 设备名字 */
    @Excel(name = "设备名字")
    @TableField(exist = false)
    private String deviceName;


    /** 开始时间 */
    @TableField(exist = false)
    private Date startTime;

    /** 结束时间 */
    @TableField(exist = false)
    private Date endTime;


    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setOpenId(String openId) 
    {
        this.openId = openId;
    }

    public String getOpenId() 
    {
        return openId;
    }
    public void setObjectName(String objectName) 
    {
        this.objectName = objectName;
    }

    public String getObjectName() 
    {
        return objectName;
    }
    public void setUrl(String url) 
    {
        this.url = url;
    }

    public String getUrl() 
    {
        return url;
    }
    public void setType(Integer type) 
    {
        this.type = type;
    }

    public Integer getType() 
    {
        return type;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("openId", getOpenId())
            .append("objectName", getObjectName())
            .append("url", getUrl())
            .append("type", getType())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("deviceId", getDeviceId())
            .append("orderId", getOrderId())
            .append("deviceName", getDeviceName())
            .toString();
    }
}
