package com.yunchuang.wxapp.service.client;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yunchuang.wxapp.model.domain.User;
import com.yunchuang.wxapp.model.req.CUserLoginOrRegisterReq;
import com.yunchuang.wxapp.model.resp.CUserLoginOrRegisterResp;

/**
 * 客户端
 * <br />
 * 用户 Service 接口
 */
public interface ICUserService extends IService<User> {

    /**
     * 登录或注册
     *
     * @param req 请求参数
     * @return 登录或注册响应
     */
    CUserLoginOrRegisterResp loginOrRegister(CUserLoginOrRegisterReq req);

    /**
     * 绑定手机号
     *
     * @param openid 用户openid
     * @param mobile 手机号
     * @return 绑定结果
     */
    boolean bindMobile(String openid, String mobile);

    /**
     * 绑定手机号（包含业务验证逻辑）
     *
     * @param openid 用户openid
     * @param mobile 手机号
     * @return 绑定结果消息，成功返回"绑定成功"，失败返回具体错误信息
     */
    String bindMobileWithValidation(String openid, String mobile);

    /**
     * 注销用户账号（逻辑删除相关数据）
     *
     * @param openid 用户openid
     * @return 注销结果消息，成功返回"注销成功"，失败返回具体错误信息
     */
    String deleteUserAccount(String openid);

    /**
     * 检查手机号是否可以发送验证码
     *
     * @param openid 用户openid
     * @param mobile 手机号
     * @return 检查结果消息，可以发送返回"可以发送"，否则返回具体原因
     */
    String checkMobileForSms(String openid, String mobile);
}
