<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0"/>
    <link rel="stylesheet" href="/css/index.css">
    <title>拍照支付</title>
</head>
<body id="body"
      style="background-image: url(/lego_background.png);background-size: cover;background-position: center center">
<img style="width: 60vw;position: absolute;top: 10%;left: 20%" src="/lego_logo.png">
<div class="wrap" style="display: flex;flex-direction: column;align-items: center;justify-content: center">
    <form>
        <div class="item">
            <div class="right">

                <span style="font-size: 2vh;font-weight: bold;color: #f3c31e;position: absolute;top: 43%;left: 37%">¥</span>
                <input id="totalAmount" th:value="${totalAmount}/100"
                       style=";font-size: 5vh;text-align: left;width: 60vw;background: none;border: none;color: #f3c31e;font-weight: 550;position: absolute;top: 40%;left: 40%;"
                       disabled type="text" name="totalAmount">
                <div style="border-bottom: 2px solid #ffffff77;width: 25vw;height: 1px;position: absolute;left: 37vw;top: 47vh"></div>
                <div style="font-weight: bold;color: white;font-size: 5vw;position: absolute;left: 37.5vw;top: 50vh">
                    艺术像素画
                </div>
                <input style="display: none" id="openId" type="text" th:value="${openId}" name="openId">
                <input style="display: none" id="userId" type="text" th:value="${userId}" name="userId">
                <input style="display: none" id="orderId" type="text" th:value="${orderId}" name="orderId">
                <input style="display: none" id="mchid" type="text" th:value="${mchid}" name="mchid">
            </div>
        </div>

        <img style="font-size: 3vh;width: 76%;height: 10%;font-weight: 550;border-radius: 150px;border: none;position: absolute;left: 12%;bottom: 10%"
             onclick="doPay()" type="button"
             src="/sure_pay_lego.png">

    </form>
</div>

</body>
<!--<script type="text/javascript" src="https://cdn.bootcss.com/vConsole/3.3.0/vconsole.min.js"></script>-->
<script type="text/javascript" src="/js/jquery.js"></script>
<script type="text/javascript">
    // var vConsole = new VConsole();
    var payInfo = null;

    function doPay() {
        var totalAmount = $('#totalAmount').val();
        var openId = $('#openId').val();
        var userId = $('#userId').val();
        var orderId = $('#orderId').val();
        var mchid = $('#mchid').val();
        $.ajax({
            url: "/pay/jsPay",
            type: "POST",
            dataType: "json",
            data: {
                "totalAmount": Math.round(Number(totalAmount * 100)),
                "openId": openId,
                "userId": userId,
                "outTradeNo": orderId,
                "mchid": mchid,
            },
            success: function (data) {
                if (data.channelType == "WX") {
                    payInfo = JSON.parse(data.data);
                    onBridgeReady();
                } else {
                    tradePay(data.data);
                }
            }, error: function (msg) {
                alert(msg);
                alert(JSON.parse(msg));
            }
        });
    }

    // 由于js的载入是异步的，所以可以通过该方法，当AlipayJSBridgeReady事件发生后，再执行callback方法
    function ready(callback) {
        if (window.AlipayJSBridge) {
            callback && callback();
        } else {
            document.addEventListener('AlipayJSBridgeReady', callback, false);
        }
    }

    function tradePay(tradeNO) {
        ready(function () {
            // 通过传入交易号唤起快捷调用方式(注意tradeNO大小写严格)
            AlipayJSBridge.call("tradePay", {
                tradeNO: tradeNO
            }, function (data) {
                $(".safe-pay").css({"pointer-events": "auto"});
                if ("9000" == data.resultCode) {
                    window.location = "/success";
                }
            });
        });
    }

    function onBridgeReady() {
        WeixinJSBridge.invoke(
            'getBrandWCPayRequest', {
                "appId": payInfo.appId,
                "timeStamp": payInfo.timeStamp,
                "nonceStr": payInfo.nonceStr,
                "package": payInfo.package,
                "signType": payInfo.signType,
                "paySign": payInfo.paySign
            },
            function (res) {
                $(".safe-pay").css({"pointer-events": "auto"});
                if (res.err_msg == "get_brand_wcpay_request:ok") {
                    window.location = "/success";
                } else {
                    alert(JSON.stringify(res));
                }
            });
    }
</script>
</html>
