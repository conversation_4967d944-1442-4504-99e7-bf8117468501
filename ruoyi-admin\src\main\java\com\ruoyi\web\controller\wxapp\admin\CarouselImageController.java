package com.ruoyi.web.controller.wxapp.admin;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.yunchuang.wxapp.model.domain.CarouselImage;
import com.yunchuang.wxapp.service.admin.ICarouselImageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 轮播图Controller
 *
 * <AUTHOR>
 * @date 2024-12-21
 */
@RestController
@RequestMapping("/wxapp/carousel")
public class CarouselImageController extends BaseController {
    @Autowired
    private ICarouselImageService carouselImageService;

    /**
     * 查询轮播图列表
     */
    @PreAuthorize("@ss.hasPermi('wxapp:carousel:list')")
    @GetMapping("/list")
    public TableDataInfo list(CarouselImage carouselImage) {
        startPage();
        List<CarouselImage> list = carouselImageService.selectCarouselImageList(carouselImage);
        return getDataTable(list);
    }

    /**
     * 导出轮播图列表
     */
    @PreAuthorize("@ss.hasPermi('wxapp:carousel:export')")
    @Log(title = "轮播图", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CarouselImage carouselImage) {
        List<CarouselImage> list = carouselImageService.selectCarouselImageList(carouselImage);
        ExcelUtil<CarouselImage> util = new ExcelUtil<CarouselImage>(CarouselImage.class);
        util.exportExcel(response, list, "轮播图数据");
    }

    /**
     * 获取轮播图详细信息
     */
    @PreAuthorize("@ss.hasPermi('wxapp:carousel:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(carouselImageService.selectCarouselImageById(id));
    }

    /**
     * 新增轮播图
     */
    @PreAuthorize("@ss.hasPermi('wxapp:carousel:add')")
    @Log(title = "轮播图", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CarouselImage carouselImage) {
        return toAjax(carouselImageService.insertCarouselImage(carouselImage));
    }

    /**
     * 修改轮播图
     */
    @PreAuthorize("@ss.hasPermi('wxapp:carousel:edit')")
    @Log(title = "轮播图", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CarouselImage carouselImage) {
        return toAjax(carouselImageService.updateCarouselImage(carouselImage));
    }

    /**
     * 删除轮播图
     */
    @PreAuthorize("@ss.hasPermi('wxapp:carousel:remove')")
    @Log(title = "轮播图", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(carouselImageService.deleteCarouselImageByIds(ids));
    }
}
